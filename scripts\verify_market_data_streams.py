#!/usr/bin/env python3
"""
Script to verify market data streams are active and working
"""
import redis
import json
import time
from datetime import datetime

def check_redis_streams():
    """Check if market data streams are active"""
    try:
        # Connect to Redis
        redis_client = redis.Redis(host='**************', port=6379, db=0, decode_responses=True)
        redis_client.ping()
        print("✅ Connected to Redis successfully")
        
        # Streams to check
        streams_to_check = [
            'testrade:internal:market-data:raw-quotes',
            'testrade:internal:market-data:raw-trades',
            'testrade:position-updates',
            'testrade:enriched-position-updates',
            'testrade:market-data:summary',
            'testrade:market-data:volume'
        ]
        
        print("\n📊 Checking Redis streams...")
        print("=" * 60)
        
        for stream_name in streams_to_check:
            try:
                # Check if stream exists and get info
                stream_info = redis_client.xinfo_stream(stream_name)
                length = stream_info.get('length', 0)
                last_generated_id = stream_info.get('last-generated-id', 'N/A')
                
                # Get latest message if available
                latest_messages = redis_client.xrevrange(stream_name, count=1)
                latest_time = "No messages"
                if latest_messages:
                    latest_id = latest_messages[0][0]
                    # Extract timestamp from Redis stream ID (format: timestamp-sequence)
                    timestamp_ms = int(latest_id.split('-')[0])
                    latest_time = datetime.fromtimestamp(timestamp_ms / 1000).strftime('%Y-%m-%d %H:%M:%S')
                
                status = "🟢 ACTIVE" if length > 0 else "🟡 EMPTY"
                print(f"{status} {stream_name}")
                print(f"   Length: {length} messages")
                print(f"   Last ID: {last_generated_id}")
                print(f"   Latest: {latest_time}")
                
                # Show sample message if available
                if latest_messages:
                    sample_data = latest_messages[0][1]
                    if 'data' in sample_data:
                        try:
                            message_data = json.loads(sample_data['data'])
                            payload = message_data.get('payload', {})
                            if 'symbol' in payload:
                                print(f"   Sample: {payload.get('symbol', 'N/A')} - {payload.get('event_type', 'N/A')}")
                        except:
                            print(f"   Sample: {str(sample_data)[:50]}...")
                print()
                
            except redis.ResponseError as e:
                if "no such key" in str(e).lower():
                    print(f"🔴 MISSING {stream_name}")
                    print(f"   Error: Stream does not exist")
                else:
                    print(f"🔴 ERROR {stream_name}")
                    print(f"   Error: {e}")
                print()
            except Exception as e:
                print(f"🔴 ERROR {stream_name}")
                print(f"   Error: {e}")
                print()
        
        # Check consumer groups
        print("\n👥 Checking consumer groups...")
        print("=" * 60)
        
        consumer_groups_to_check = [
            ('testrade:position-updates', 'position_enrichment_service'),
            ('testrade:enriched-position-updates', 'gui_backend_consumers'),
            ('testrade:internal:market-data:raw-quotes', 'gui_backend_consumers'),
            ('testrade:internal:market-data:raw-trades', 'gui_backend_consumers')
        ]
        
        for stream_name, group_name in consumer_groups_to_check:
            try:
                groups = redis_client.xinfo_groups(stream_name)
                group_found = False
                for group in groups:
                    if group['name'] == group_name:
                        group_found = True
                        consumers = group.get('consumers', 0)
                        pending = group.get('pending', 0)
                        last_delivered_id = group.get('last-delivered-id', 'N/A')
                        
                        status = "🟢 ACTIVE" if consumers > 0 else "🟡 NO CONSUMERS"
                        print(f"{status} {stream_name} -> {group_name}")
                        print(f"   Consumers: {consumers}")
                        print(f"   Pending: {pending}")
                        print(f"   Last delivered: {last_delivered_id}")
                        break
                
                if not group_found:
                    print(f"🔴 MISSING {stream_name} -> {group_name}")
                    print(f"   Error: Consumer group does not exist")
                print()
                
            except redis.ResponseError as e:
                if "no such key" in str(e).lower():
                    print(f"🔴 MISSING {stream_name} -> {group_name}")
                    print(f"   Error: Stream does not exist")
                else:
                    print(f"🔴 ERROR {stream_name} -> {group_name}")
                    print(f"   Error: {e}")
                print()
            except Exception as e:
                print(f"🔴 ERROR {stream_name} -> {group_name}")
                print(f"   Error: {e}")
                print()
        
        # Check if PriceRepository is publishing market data
        print("\n💰 Market Data Activity Check...")
        print("=" * 60)
        
        # Monitor for new messages for 10 seconds
        print("Monitoring for new market data messages (10 seconds)...")
        start_time = time.time()
        initial_counts = {}
        
        for stream_name in ['testrade:internal:market-data:raw-quotes', 'testrade:internal:market-data:raw-trades']:
            try:
                stream_info = redis_client.xinfo_stream(stream_name)
                initial_counts[stream_name] = stream_info.get('length', 0)
            except:
                initial_counts[stream_name] = 0
        
        time.sleep(10)
        
        for stream_name in ['testrade:internal:market-data:raw-quotes', 'testrade:internal:market-data:raw-trades']:
            try:
                stream_info = redis_client.xinfo_stream(stream_name)
                final_count = stream_info.get('length', 0)
                initial_count = initial_counts.get(stream_name, 0)
                new_messages = final_count - initial_count
                
                if new_messages > 0:
                    print(f"🟢 {stream_name}: {new_messages} new messages")
                else:
                    print(f"🟡 {stream_name}: No new messages (may be normal if markets closed)")
            except:
                print(f"🔴 {stream_name}: Error checking activity")
        
        print("\n✅ Market data stream verification complete!")
        
    except redis.ConnectionError:
        print("❌ Failed to connect to Redis at **************:6379")
        print("   Make sure Redis is running and accessible")
    except Exception as e:
        print(f"❌ Error checking Redis streams: {e}")

if __name__ == "__main__":
    print("🔍 TESTRADE Market Data Stream Verification")
    print("=" * 60)
    check_redis_streams()
