#!/usr/bin/env python3
"""
Check what symbols are exported from the .pyd file
"""

import os
import sys
import platform
import ctypes

def check_pyd_symbols():
    if platform.system() != "Windows":
        print("This diagnostic requires Windows")
        return
    
    pyd_path = os.path.abspath("ocr_accelerator/x64/Release/ocr_accelerator.pyd")
    
    if not os.path.exists(pyd_path):
        print(f"❌ PYD file not found: {pyd_path}")
        return
        
    print(f"✅ PYD file: {pyd_path}")
    print(f"Size: {os.path.getsize(pyd_path):,} bytes")
    
    # Try to load as a DLL to check basic structure
    try:
        dll = ctypes.CDLL(pyd_path)
        print("✅ DLL loaded successfully")
        
        # Check for the main Python module init function
        init_function_name = "PyInit_ocr_accelerator"
        try:
            init_func = getattr(dll, init_function_name)
            print(f"✅ Found {init_function_name}")
        except AttributeError:
            print(f"❌ {init_function_name} not found")
            print("This suggests the pybind11 module isn't correctly built")
            
    except Exception as e:
        print(f"❌ Failed to load DLL: {e}")
        return
    
    # Try Python import with detailed debugging
    print("\n=== Python Import Test ===")
    
    # Setup environment
    build_dir = os.path.dirname(pyd_path)
    original_cwd = os.getcwd()
    
    try:
        # Add DLL directory to PATH
        current_path = os.environ.get('PATH', '')
        if build_dir not in current_path:
            os.environ['PATH'] = build_dir + os.pathsep + current_path
            print(f"Added to PATH: {build_dir}")
        
        # Change to build directory
        os.chdir(build_dir)
        print(f"Changed to: {build_dir}")
        
        # Import the module
        import ocr_accelerator
        print("✅ Module imported successfully")
        
        # Check module attributes
        attrs = [attr for attr in dir(ocr_accelerator) if not attr.startswith('_')]
        print(f"Module attributes: {attrs}")
        
        # Test specific functions
        expected_funcs = ['test_function', 'process_image_and_ocr']
        for func_name in expected_funcs:
            if hasattr(ocr_accelerator, func_name):
                print(f"✅ {func_name} found")
                
                if func_name == 'test_function':
                    try:
                        result = ocr_accelerator.test_function()
                        print(f"  test_function() = {result}")
                    except Exception as e:
                        print(f"  ❌ Error calling test_function: {e}")
            else:
                print(f"❌ {func_name} NOT found")
        
        # Check module docstring
        if hasattr(ocr_accelerator, '__doc__'):
            print(f"Module doc: {ocr_accelerator.__doc__}")
            
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        os.chdir(original_cwd)

if __name__ == "__main__":
    check_pyd_symbols()