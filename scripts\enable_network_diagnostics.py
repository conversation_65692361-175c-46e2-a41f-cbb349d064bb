"""
Network Diagnostics Enabler

This script enables network diagnostics in the application.
It should be imported at the start of main.py to enable diagnostics.

Usage:
    # In main.py, add:
    import scripts.enable_network_diagnostics
"""

import sys
import os
import logging

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the network diagnostics module
try:
    from utils.network_diagnostics import enable_network_diagnostics
    
    # Enable network diagnostics
    enable_network_diagnostics(True)
    
    print("Network diagnostics enabled successfully.")
    logging.critical("Network diagnostics enabled via scripts/enable_network_diagnostics.py")
except ImportError as e:
    print(f"Error importing network_diagnostics module: {e}")
    logging.error(f"Error importing network_diagnostics module: {e}")
except Exception as e:
    print(f"Error enabling network diagnostics: {e}")
    logging.error(f"Error enabling network diagnostics: {e}")
