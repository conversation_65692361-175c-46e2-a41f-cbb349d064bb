#!/usr/bin/env python3
"""
Alpaca Data Analyzer

Analyzes downloaded tick data from Alpaca Markets and provides various data processing utilities.

Usage:
    python analyze_alpaca_data.py --data-dir data/alpaca_downloads
    python analyze_alpaca_data.py --file data/alpaca_downloads/AAPL_trades_20250530_0945_1045.csv --summary
"""

import os
import sys
import argparse
import logging
import glob
from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AlpacaDataAnalyzer:
    """Analyzes Alpaca tick data and provides various statistics and conversions."""
    
    def __init__(self):
        """Initialize the data analyzer."""
        pass
    
    def analyze_trades_file(self, filepath: str) -> Dict[str, Any]:
        """
        Analyze a single trades CSV file.
        
        Args:
            filepath: Path to the trades CSV file
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            df = pd.read_csv(filepath)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # Basic statistics
            stats = {
                'file': os.path.basename(filepath),
                'symbol': self._extract_symbol_from_filename(filepath),
                'total_trades': len(df),
                'time_range': {
                    'start': df['timestamp'].min().isoformat(),
                    'end': df['timestamp'].max().isoformat(),
                    'duration_minutes': (df['timestamp'].max() - df['timestamp'].min()).total_seconds() / 60
                },
                'price_stats': {
                    'min': float(df['price'].min()),
                    'max': float(df['price'].max()),
                    'mean': float(df['price'].mean()),
                    'std': float(df['price'].std()),
                    'first': float(df['price'].iloc[0]),
                    'last': float(df['price'].iloc[-1]),
                    'change': float(df['price'].iloc[-1] - df['price'].iloc[0]),
                    'change_pct': float((df['price'].iloc[-1] - df['price'].iloc[0]) / df['price'].iloc[0] * 100)
                },
                'volume_stats': {
                    'total_volume': int(df['size'].sum()),
                    'avg_trade_size': float(df['size'].mean()),
                    'max_trade_size': int(df['size'].max()),
                    'min_trade_size': int(df['size'].min())
                },
                'trading_activity': {
                    'trades_per_minute': len(df) / ((df['timestamp'].max() - df['timestamp'].min()).total_seconds() / 60),
                    'avg_seconds_between_trades': (df['timestamp'].max() - df['timestamp'].min()).total_seconds() / len(df)
                }
            }
            
            # Exchange distribution
            if 'exchange' in df.columns:
                exchange_counts = df['exchange'].value_counts().to_dict()
                stats['exchange_distribution'] = exchange_counts
            
            return stats
            
        except Exception as e:
            logger.error(f"Error analyzing trades file {filepath}: {e}")
            return {'error': str(e)}
    
    def analyze_quotes_file(self, filepath: str) -> Dict[str, Any]:
        """
        Analyze a single quotes CSV file.
        
        Args:
            filepath: Path to the quotes CSV file
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            df = pd.read_csv(filepath)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df['spread'] = df['ask_price'] - df['bid_price']
            df['mid_price'] = (df['bid_price'] + df['ask_price']) / 2
            df['spread_pct'] = (df['spread'] / df['mid_price']) * 100
            
            stats = {
                'file': os.path.basename(filepath),
                'symbol': self._extract_symbol_from_filename(filepath),
                'total_quotes': len(df),
                'time_range': {
                    'start': df['timestamp'].min().isoformat(),
                    'end': df['timestamp'].max().isoformat(),
                    'duration_minutes': (df['timestamp'].max() - df['timestamp'].min()).total_seconds() / 60
                },
                'bid_stats': {
                    'min': float(df['bid_price'].min()),
                    'max': float(df['bid_price'].max()),
                    'mean': float(df['bid_price'].mean()),
                    'first': float(df['bid_price'].iloc[0]),
                    'last': float(df['bid_price'].iloc[-1])
                },
                'ask_stats': {
                    'min': float(df['ask_price'].min()),
                    'max': float(df['ask_price'].max()),
                    'mean': float(df['ask_price'].mean()),
                    'first': float(df['ask_price'].iloc[0]),
                    'last': float(df['ask_price'].iloc[-1])
                },
                'spread_stats': {
                    'min_spread': float(df['spread'].min()),
                    'max_spread': float(df['spread'].max()),
                    'avg_spread': float(df['spread'].mean()),
                    'min_spread_pct': float(df['spread_pct'].min()),
                    'max_spread_pct': float(df['spread_pct'].max()),
                    'avg_spread_pct': float(df['spread_pct'].mean())
                },
                'quote_activity': {
                    'quotes_per_minute': len(df) / ((df['timestamp'].max() - df['timestamp'].min()).total_seconds() / 60),
                    'avg_seconds_between_quotes': (df['timestamp'].max() - df['timestamp'].min()).total_seconds() / len(df)
                }
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error analyzing quotes file {filepath}: {e}")
            return {'error': str(e)}
    
    def convert_to_ohlc(self, trades_filepath: str, interval: str = '1min') -> pd.DataFrame:
        """
        Convert tick data to OHLC bars.
        
        Args:
            trades_filepath: Path to trades CSV file
            interval: Time interval for bars (e.g., '1min', '5min', '1s')
            
        Returns:
            DataFrame with OHLC data
        """
        try:
            df = pd.read_csv(trades_filepath)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            
            # Create OHLC bars
            ohlc = df['price'].resample(interval).ohlc()
            volume = df['size'].resample(interval).sum()
            trade_count = df['price'].resample(interval).count()
            
            # Combine into single DataFrame
            result = pd.concat([ohlc, volume, trade_count], axis=1)
            result.columns = ['open', 'high', 'low', 'close', 'volume', 'trade_count']
            
            # Remove rows with no data
            result = result.dropna()
            
            return result
            
        except Exception as e:
            logger.error(f"Error converting to OHLC: {e}")
            return pd.DataFrame()
    
    def _extract_symbol_from_filename(self, filepath: str) -> str:
        """Extract symbol from filename."""
        filename = os.path.basename(filepath)
        return filename.split('_')[0]
    
    def analyze_directory(self, data_dir: str) -> Dict[str, Any]:
        """
        Analyze all CSV files in a directory.
        
        Args:
            data_dir: Directory containing CSV files
            
        Returns:
            Dictionary containing analysis results for all files
        """
        results = {
            'trades': {},
            'quotes': {},
            'summary': {
                'total_files': 0,
                'symbols': set(),
                'date_range': None
            }
        }
        
        # Find all CSV files
        csv_files = glob.glob(os.path.join(data_dir, "*.csv"))
        results['summary']['total_files'] = len(csv_files)
        
        for filepath in csv_files:
            filename = os.path.basename(filepath)
            symbol = self._extract_symbol_from_filename(filepath)
            results['summary']['symbols'].add(symbol)
            
            if '_trades_' in filename:
                logger.info(f"Analyzing trades file: {filename}")
                results['trades'][filename] = self.analyze_trades_file(filepath)
            elif '_quotes_' in filename:
                logger.info(f"Analyzing quotes file: {filename}")
                results['quotes'][filename] = self.analyze_quotes_file(filepath)
        
        results['summary']['symbols'] = list(results['summary']['symbols'])
        
        return results

def print_summary(analysis_results: Dict[str, Any]):
    """Print a summary of analysis results."""
    print("\n" + "="*80)
    print("ALPACA DATA ANALYSIS SUMMARY")
    print("="*80)
    
    summary = analysis_results.get('summary', {})
    print(f"Total files analyzed: {summary.get('total_files', 0)}")
    print(f"Symbols: {', '.join(summary.get('symbols', []))}")
    
    # Trades summary
    trades = analysis_results.get('trades', {})
    if trades:
        print(f"\nTRADES DATA ({len(trades)} files):")
        print("-" * 40)
        for filename, stats in trades.items():
            if 'error' not in stats:
                symbol = stats.get('symbol', 'Unknown')
                total_trades = stats.get('total_trades', 0)
                duration = stats.get('time_range', {}).get('duration_minutes', 0)
                price_change = stats.get('price_stats', {}).get('change_pct', 0)
                volume = stats.get('volume_stats', {}).get('total_volume', 0)
                
                print(f"  {symbol}: {total_trades:,} trades, {duration:.1f}min, "
                      f"{price_change:+.2f}%, Vol: {volume:,}")
    
    # Quotes summary
    quotes = analysis_results.get('quotes', {})
    if quotes:
        print(f"\nQUOTES DATA ({len(quotes)} files):")
        print("-" * 40)
        for filename, stats in quotes.items():
            if 'error' not in stats:
                symbol = stats.get('symbol', 'Unknown')
                total_quotes = stats.get('total_quotes', 0)
                duration = stats.get('time_range', {}).get('duration_minutes', 0)
                avg_spread = stats.get('spread_stats', {}).get('avg_spread_pct', 0)
                
                print(f"  {symbol}: {total_quotes:,} quotes, {duration:.1f}min, "
                      f"Avg spread: {avg_spread:.3f}%")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Analyze Alpaca tick data')
    parser.add_argument('--data-dir', help='Directory containing CSV files to analyze')
    parser.add_argument('--file', help='Single CSV file to analyze')
    parser.add_argument('--summary', action='store_true', help='Print summary only')
    parser.add_argument('--convert-ohlc', help='Convert trades file to OHLC and save')
    parser.add_argument('--interval', default='1min', help='OHLC interval (default: 1min)')
    parser.add_argument('--output', help='Output file for converted data')
    
    args = parser.parse_args()
    
    analyzer = AlpacaDataAnalyzer()
    
    if args.convert_ohlc:
        # Convert to OHLC
        logger.info(f"Converting {args.convert_ohlc} to OHLC with {args.interval} intervals")
        ohlc_df = analyzer.convert_to_ohlc(args.convert_ohlc, args.interval)
        
        if not ohlc_df.empty:
            output_file = args.output or args.convert_ohlc.replace('.csv', f'_ohlc_{args.interval}.csv')
            ohlc_df.to_csv(output_file)
            logger.info(f"OHLC data saved to {output_file}")
            print(f"\nOHLC Preview ({args.interval} bars):")
            print(ohlc_df.head(10))
        else:
            logger.error("Failed to convert to OHLC")
        
        return 0
    
    if args.file:
        # Analyze single file
        if '_trades_' in args.file:
            results = analyzer.analyze_trades_file(args.file)
        elif '_quotes_' in args.file:
            results = analyzer.analyze_quotes_file(args.file)
        else:
            logger.error("Cannot determine file type (trades or quotes)")
            return 1
        
        if args.summary:
            print(f"\nAnalysis for {os.path.basename(args.file)}:")
            print(json.dumps(results, indent=2, default=str))
        else:
            print(json.dumps(results, indent=2, default=str))
    
    elif args.data_dir:
        # Analyze directory
        results = analyzer.analyze_directory(args.data_dir)
        
        if args.summary:
            print_summary(results)
        else:
            import json
            print(json.dumps(results, indent=2, default=str))
    
    else:
        parser.print_help()
        return 1
    
    return 0

if __name__ == "__main__":
    import json
    sys.exit(main())
