# Config Reload Solution for IntelliSense Visibility

## Problem Statement
When manually editing `control.json` to tune parameters (MAF thresholds, risk settings, etc.), IntelliSense has no visibility into these changes because:
1. TESTRADE doesn't know the file changed
2. No config change events are published
3. IntelliSense and TESTRADE have different views of reality

## Solution Implemented
Enhanced the existing "Save Configuration" button in the GUI to:
1. **Reload from disk first** - Picks up manual edits
2. **Compare old vs new** - Detects what changed
3. **Publish detailed events** - IntelliSense sees all changes

## How It Works

### Workflow
1. **Edit `control.json`** - Change MAF thresholds, risk params, etc.
   ```json
   "cost_basis_settling_duration_seconds": 15.0,  // was 30.0
   ```

2. **Click "Save Configuration" in GUI**
   - GUI sends `SAVE_CONFIGURATION` command
   - TESTRADE reloads config from disk
   - Publishes event to `testrade:config-changes`

3. **IntelliSense sees the change**
   ```json
   {
     "changed_params": {
       "cost_basis_settling_duration_seconds": {
         "old": 30.0,
         "new": 15.0
       }
     }
   }
   ```

### Implementation Details

**Modified**: `/modules/gui_commands/gui_command_service.py`
```python
def _handle_save_configuration(self, parameters: Dict[str, Any]) -> CommandResult:
    """Handle SAVE_CONFIGURATION command - now reloads from disk first."""
    # 1. Capture old state
    old_config_dict = asdict(config)
    
    # 2. RELOAD from disk (picks up manual edits)
    reload_global_config()
    
    # 3. Calculate changes
    changed_params = {}
    for key, new_value in new_config_dict.items():
        if old_value != new_value:
            changed_params[key] = {"old": old_value, "new": new_value}
    
    # 4. Save and publish event
    save_global_config(config, ipc_client=ipc_client, 
                      change_source_override="gui_save_configuration_with_reload")
```

## Benefits
- ✅ **No risk to TESTRADE** - Uses existing infrastructure
- ✅ **No polling/watchers** - Explicit user control
- ✅ **All params supported** - Not limited to GUI-exposed settings
- ✅ **IntelliSense visibility** - Sees all config changes with old/new values
- ✅ **Atomic operation** - Config reload and event are synchronized

## Event Format
Published to `testrade:config-changes`:
```json
{
  "metadata": {
    "eventType": "TESTRADE_CONFIG_CHANGE",
    "sourceComponent": "gui_save_configuration_with_reload"
  },
  "payload": {
    "full_config_snapshot": { /* entire config */ },
    "change_details": {
      "changed_parameters": {
        "cost_basis_settling_duration_seconds": {"old": 30.0, "new": 15.0},
        "rPnL_settling_duration_seconds": {"old": 45.0, "new": 20.0}
      },
      "change_timestamp": 1751265180.123,
      "change_source": "gui_save_configuration_with_reload",
      "change_method": "save_global_config"
    }
  }
}
```

## Testing Verification
1. Changed `cost_basis_settling_duration_seconds` from 30.0 to 15.0
2. Would click "Save Configuration" in GUI
3. Event appears in `testrade:config-changes` stream
4. IntelliSense can query via MCP:
   ```python
   mcp__intellisense-trading__query_redis_stream(
     stream_name="testrade:config-changes",
     count=10
   )
   ```

## Future Enhancements
- Add "Reload Only" button (no save) for testing
- Show changed parameters in GUI response
- Add config diff visualization
- Implement config history/rollback

## Critical for IntelliSense
This solution ensures IntelliSense always knows:
- Current MAF thresholds when analyzing suppressions
- Risk parameters when evaluating trading decisions
- Any config that affects trading behavior

Without this, IntelliSense analysis would be based on stale/incorrect configuration values.