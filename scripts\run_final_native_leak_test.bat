@echo off
echo ========================================================
echo Running FINAL-NATIVE-LEAK-TEST
echo ========================================================
echo This test will run for 15 minutes to detect memory leaks
echo in the application when running WITHOUT Tkinter widgets.
echo.
echo Test specifically checks if the Tkinter window creation
echo and widget setup in App.__init__ is the trigger for the
echo ~70MB native leak.
echo.
echo Started at: %date% %time%
echo ========================================================

mkdir "mem leak" 2>nul

python scripts/final_native_leak_test.py

echo ========================================================
echo Test completed at: %date% %time%
echo Results can be found in the 'mem leak' directory.
echo ========================================================
pause
