#!/usr/bin/env python3
"""
Scientific OCR Timing Measurement Script

This script captures and analyzes precise timing measurements from the OCR pipeline:
- OpenCV preprocessing
- OpenCV contour processing  
- Tesseract setup
- Tesseract OCR execution
- Total C++ processing time

Results are measured in milliseconds with microsecond precision.
"""

import subprocess
import time
import sys
import os
from datetime import datetime

def run_ocr_and_measure():
    """Run OCR process and capture timing measurements from logs."""
    
    print("🔬 SCIENTIFIC OCR TIMING MEASUREMENT")
    print("=" * 60)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Clear recent logs to get fresh timing data
    log_file = "/mnt/c/TESTRADE/logs/testrade_main.log"
    
    if os.path.exists(log_file):
        print(f"📊 Monitoring log file: {log_file}")
        
        # Get current log position
        with open(log_file, 'r') as f:
            f.seek(0, 2)  # Go to end
            start_pos = f.tell()
        
        print("🚀 Starting OCR process...")
        print("⏱️  Collecting timing measurements...")
        print("📝 Look for [OCR_TIMING] entries in the logs")
        print()
        
        # Monitor for new OCR timing logs for 30 seconds
        timeout = 30
        start_time = time.time()
        timing_entries = []
        
        while time.time() - start_time < timeout:
            try:
                with open(log_file, 'r') as f:
                    f.seek(start_pos)
                    new_lines = f.readlines()
                    
                for line in new_lines:
                    if "[OCR_TIMING]" in line:
                        timing_entries.append(line.strip())
                        print(f"📊 {line.strip()}")
                    elif "Missing timing data" in line and "OCR_TIMING" in line:
                        print(f"❌ {line.strip()}")
                    elif "Available keys in cpp_result" in line:
                        print(f"🔍 {line.strip()}")
                
                # Update position
                with open(log_file, 'r') as f:
                    f.seek(0, 2)
                    start_pos = f.tell()
                    
                time.sleep(0.5)
                
            except Exception as e:
                print(f"Error reading log: {e}")
                break
        
        print()
        print("📈 TIMING ANALYSIS:")
        print("-" * 40)
        
        if timing_entries:
            print(f"✅ Captured {len(timing_entries)} timing measurements")
            
            # Parse timing data
            opencv_preproc_times = []
            opencv_contour_times = []
            tesseract_setup_times = []
            tesseract_ocr_times = []
            total_cpp_times = []
            
            for entry in timing_entries:
                try:
                    # Extract timing values using string parsing
                    parts = entry.split(" | ")
                    for part in parts:
                        if "OpenCV_Preproc:" in part:
                            opencv_preproc_times.append(float(part.split(":")[1].replace("ms", "")))
                        elif "OpenCV_Contours:" in part:
                            opencv_contour_times.append(float(part.split(":")[1].replace("ms", "")))
                        elif "Tesseract_Setup:" in part:
                            tesseract_setup_times.append(float(part.split(":")[1].replace("ms", "")))
                        elif "Tesseract_OCR:" in part:
                            tesseract_ocr_times.append(float(part.split(":")[1].replace("ms", "")))
                        elif "Total_C++:" in part:
                            total_cpp_times.append(float(part.split(":")[1].replace("ms", "")))
                except Exception as e:
                    print(f"Error parsing timing entry: {e}")
            
            # Calculate statistics
            def calc_stats(times, name):
                if times:
                    avg = sum(times) / len(times)
                    min_t = min(times)
                    max_t = max(times)
                    print(f"{name:20}: Avg={avg:6.2f}ms  Min={min_t:6.2f}ms  Max={max_t:6.2f}ms  Count={len(times)}")
                else:
                    print(f"{name:20}: No data collected")
            
            calc_stats(opencv_preproc_times, "OpenCV Preprocessing")
            calc_stats(opencv_contour_times, "OpenCV Contours") 
            calc_stats(tesseract_setup_times, "Tesseract Setup")
            calc_stats(tesseract_ocr_times, "Tesseract OCR")
            calc_stats(total_cpp_times, "Total C++ Time")
            
            if total_cpp_times:
                print()
                print("🎯 PERFORMANCE BREAKDOWN:")
                avg_total = sum(total_cpp_times) / len(total_cpp_times)
                avg_opencv = (sum(opencv_preproc_times) / len(opencv_preproc_times) + 
                             sum(opencv_contour_times) / len(opencv_contour_times)) if opencv_preproc_times and opencv_contour_times else 0
                avg_tesseract = (sum(tesseract_setup_times) / len(tesseract_setup_times) + 
                               sum(tesseract_ocr_times) / len(tesseract_ocr_times)) if tesseract_setup_times and tesseract_ocr_times else 0
                
                if avg_total > 0:
                    opencv_pct = (avg_opencv / avg_total) * 100
                    tesseract_pct = (avg_tesseract / avg_total) * 100
                    print(f"OpenCV time:    {avg_opencv:6.2f}ms ({opencv_pct:5.1f}%)")
                    print(f"Tesseract time: {avg_tesseract:6.2f}ms ({tesseract_pct:5.1f}%)")
                    print(f"Total time:     {avg_total:6.2f}ms (100.0%)")
        else:
            print("❌ No timing measurements captured!")
            print("   Possible issues:")
            print("   - OCR process not running")
            print("   - C++ module not being used")
            print("   - Log level too high")
            print("   - Timing data not being returned from C++ module")
    
    else:
        print(f"❌ Log file not found: {log_file}")
        print("   Make sure TESTRADE is running and logging is enabled")

if __name__ == "__main__":
    run_ocr_and_measure()