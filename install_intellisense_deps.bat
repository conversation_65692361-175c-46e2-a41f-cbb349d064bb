@echo off
echo === Installing IntelliSense API Dependencies ===
echo.

cd /d C:\TESTRADE
echo Installing from directory: %cd%
echo.

REM Activate virtual environment
echo Activating virtual environment...
call .venv\Scripts\activate.bat

REM Install dependencies
echo Installing required packages...
pip install fastapi uvicorn pydantic redis

echo.
echo Dependencies installed. You can now start the API server with:
echo C:\TESTRADE\start_intellisense_api.bat
echo.
pause