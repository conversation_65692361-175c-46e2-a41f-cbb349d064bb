#!/usr/bin/env python3
"""
Headless Core Runner Script

This script runs ApplicationCore in headless mode for video replay testing.
It provides proper signal handling for graceful shutdown and manages the
ApplicationCore lifecycle.

Usage:
    python run_headless_core.py

Configuration:
    Configure video input and other settings in utils/control.json:
    {
        "OCR_INPUT_VIDEO_FILE_PATH": "path/to/your/video.mp4",
        "ROI_COORDINATES": [x1, y1, x2, y2],
        ...
    }
"""

import logging
import logging.handlers
import os
import signal
import sys
import time
import threading
from pathlib import Path
from typing import Optional, Any

# Import clean DI architecture
from core.dependency_injection import DIContainer
from core.di_registration import register_all_services
from interfaces.core.application import IApplicationCore as DI_ApplicationCore
from utils.global_config import CONFIG_FILE_PATH

# Global shutdown flag for signal handling
shutdown_flag = threading.Event()
main_logger: Optional[logging.Logger] = None
app_core_instance: Optional[Any] = None


def setup_logging():
    """Configure optimized logging for the headless runner."""
    global main_logger

    # Ensure logs directory exists
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

    # Configure root logger with normal verbosity
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)  # Show INFO level and above

    # Remove any existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Create console handler (for INFO and above)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)

    # Create file handler (non-rotating to avoid Windows file lock issues)
    log_file_path = logs_dir / "headless_core_runner.log"
    file_handler = logging.FileHandler(
        log_file_path,
        mode='a',  # Append mode
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)  # File gets more detail than console
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)

    # Create dedicated logger for HeadlessCoreRunner with INFO level
    main_logger = logging.getLogger('HeadlessCoreRunner')
    main_logger.setLevel(logging.INFO)  # This logger shows INFO and above

    # Reduce verbosity of only the most noisy loggers
    noisy_loggers = [
        'websockets',
        'urllib3',
        'requests'
    ]

    for logger_name in noisy_loggers:
        logging.getLogger(logger_name).setLevel(logging.ERROR)

    main_logger.info("Headless Core Runner logging initialized (normal verbosity mode)")


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    global main_logger, shutdown_flag

    signal_name = signal.Signals(signum).name if hasattr(signal, 'Signals') else str(signum)
    if main_logger:
        main_logger.info(f"Received signal {signal_name} ({signum}). Initiating graceful shutdown...")
    else:
        print(f"Received signal {signal_name} ({signum}). Initiating graceful shutdown...")

    shutdown_flag.set()


def setup_signal_handlers():
    """Set up signal handlers for graceful shutdown."""
    # Handle common shutdown signals
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Termination request

    # Windows-specific signal handling
    if hasattr(signal, 'SIGBREAK'):
        signal.signal(signal.SIGBREAK, signal_handler)  # Ctrl+Break on Windows


def main():
    """Main function for the headless core runner."""
    global main_logger, app_core_instance, shutdown_flag

    # Set up logging first
    setup_logging()
    main_logger.info("Headless Core Runner Starting")

    # Set up signal handlers
    setup_signal_handlers()

    try:
        # 1. Create the Dependency Injection Container
        main_logger.info("Creating DI container...")
        container = DIContainer()
        
        # 2. Register all services and their dependencies
        register_all_services(container)
        
        # 3. Resolve the top-level application coordinator
        # The DI container will build the entire application graph from here.
        main_logger.info("Resolving ApplicationCore from DI container...")
        app_core_instance = container.resolve(DI_ApplicationCore)
        
        # 4. Start the application
        main_logger.info("Starting ApplicationCore...")
        app_core_instance.start()
        main_logger.info("ApplicationCore started successfully")

        # Log input mode
        if hasattr(app_core_instance.config, 'OCR_INPUT_VIDEO_FILE_PATH') and app_core_instance.config.OCR_INPUT_VIDEO_FILE_PATH:
            main_logger.info(f"Video mode: {os.path.basename(app_core_instance.config.OCR_INPUT_VIDEO_FILE_PATH)}")
        else:
            main_logger.info("Live capture mode")

        main_logger.info("System running. Press Ctrl+C to stop.")

        # Main loop - keep the application alive
        while not shutdown_flag.is_set() and app_core_instance.is_ready:
            # Clean readiness checking - no internal event access needed
            time.sleep(1)

        main_logger.info("Shutdown initiated")

    except KeyboardInterrupt:
        main_logger.info("Interrupted by user")
        shutdown_flag.set()
    except Exception as e:
        main_logger.critical(f"Critical error: {e}", exc_info=True)
        shutdown_flag.set()
    finally:
        # Graceful shutdown
        if app_core_instance:
            try:
                main_logger.info("Stopping ApplicationCore...")
                app_core_instance.stop()
                main_logger.info("ApplicationCore stopped")
            except Exception as e:
                main_logger.error(f"Error during shutdown: {e}")

        main_logger.info("Headless Core Runner finished")


if __name__ == "__main__":
    main()
