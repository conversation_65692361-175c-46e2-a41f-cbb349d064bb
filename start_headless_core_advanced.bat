@echo off
REM TESTRADE Headless Core Startup Script (Advanced)
REM Activates virtual environment, checks dependencies, and starts run_headless_core.py

title TESTRADE Headless Core

echo ========================================
echo TESTRADE Headless Core Startup (Advanced)
echo ========================================
echo Timestamp: %date% %time%
echo.

REM Change to TESTRADE directory
cd /d "C:\TESTRADE"
echo Current directory: %CD%

REM Check if virtual environment exists
if not exist ".venv\Scripts\activate.bat" (
    echo [ERROR] Virtual environment not found at .venv\Scripts\activate.bat
    echo Please ensure the virtual environment is set up correctly.
    echo.
    pause
    exit /b 1
)

echo [INFO] Activating virtual environment...
call .venv\Scripts\activate.bat

REM Verify Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not available after activating virtual environment
    pause
    exit /b 1
)

echo [INFO] Python version:
python --version

REM Check if run_headless_core.py exists
if not exist "run_headless_core.py" (
    echo [ERROR] run_headless_core.py not found in current directory
    echo Current directory: %CD%
    echo.
    pause
    exit /b 1
)

REM Check if control.json exists (important config file)
if not exist "utils\control.json" (
    echo [WARNING] control.json not found at utils\control.json
    echo TESTRADE may not start properly without configuration
    echo.
)

REM Optional: Check if Redis is running (comment out if not needed)
REM echo [INFO] Checking Redis connection...
REM python -c "import redis; r=redis.Redis(host='127.0.0.1', port=6379, db=0); r.ping(); print('Redis connection: OK')" 2>nul
REM if errorlevel 1 (
REM     echo [WARNING] Redis connection failed - TESTRADE may have issues
REM     echo Make sure Redis is running on 127.0.0.1:6379
REM     echo.
REM )

echo.
echo ========================================
echo Starting TESTRADE Headless Core...
echo ========================================
echo Press Ctrl+C to stop TESTRADE
echo.

REM Start the headless core
python run_headless_core.py

REM Capture exit code
set EXIT_CODE=%errorlevel%

echo.
echo ========================================
if %EXIT_CODE% equ 0 (
    echo TESTRADE Headless Core shutdown normally
) else (
    echo TESTRADE Headless Core exited with error
    echo Exit code: %EXIT_CODE%
)
echo Timestamp: %date% %time%
echo ========================================

REM Always pause so user can see the result
pause
