{"missing_data_categories": {"CONFIGURATION_CHANGES": {"priority": "HIGH", "description": "Configuration changes and system state transitions", "missing_streams": [{"stream_name": "testrade:config-changes", "data_content": "Configuration updates from GUI, control.json changes", "source_locations": ["gui/gui_backend.py:2109 - /config endpoint", "utils/global_config.py - update_config_key()", "utils/control.json - Direct file changes"], "intellisense_value": "Track configuration impact on trading behavior", "implementation_effort": "MEDIUM"}]}, "BROKER_COMMUNICATIONS": {"priority": "HIGH", "description": "Raw broker messages and communication errors", "missing_streams": [{"stream_name": "testrade:broker-raw-messages", "data_content": "Raw C++ broker messages before processing", "source_locations": ["modules/broker_bridge/lightspeed_broker.py:805 - _handle_inbound_message()", "BrokerRawMessageRelayEvent publishing"], "intellisense_value": "Debug broker communication issues and timing", "implementation_effort": "LOW"}, {"stream_name": "testrade:broker-errors", "data_content": "Broker connection errors, communication failures", "source_locations": ["modules/broker_bridge/lightspeed_broker.py:1405 - _handle_broker_error()", "BrokerErrorEvent publishing"], "intellisense_value": "Correlate trading issues with broker problems", "implementation_effort": "LOW"}]}, "PERFORMANCE_METRICS": {"priority": "MEDIUM", "description": "System performance and throughput metrics", "missing_streams": [{"stream_name": "testrade:performance-metrics", "data_content": "Processing times, queue sizes, throughput metrics", "source_locations": ["core/application_core.py:147 - key_performance_metric_names", "utils/performance_tracker/real.py:862 - capture_throughput_metric()", "Performance benchmarker data"], "intellisense_value": "Identify performance bottlenecks affecting trading", "implementation_effort": "MEDIUM"}, {"stream_name": "testrade:ipc-stats", "data_content": "ZMQ queue depths, IPC communication stats", "source_locations": ["core/babysitter_service.py - IPC queue monitoring", "core/bulletproof_ipc_client.py - Buffer statistics"], "intellisense_value": "Monitor system communication health", "implementation_effort": "MEDIUM"}]}, "TRADE_LIFECYCLE": {"priority": "HIGH", "description": "Internal trade decisions and lifecycle events", "missing_streams": [{"stream_name": "testrade:trade-lifecycle-events", "data_content": "Trade creation, state transitions, closure decisions", "source_locations": ["modules/trade_management/trade_lifecycle_manager_service.py:349 - lifecycle state changes", "modules/trade_management/trade_lifecycle_manager_service.py:511 - trade opened callbacks"], "intellisense_value": "Track complete trade lifecycle for analysis", "implementation_effort": "MEDIUM"}, {"stream_name": "testrade:signal-generation", "data_content": "Signal generation logic, OCR interpretation decisions", "source_locations": ["modules/trade_management/ocr_scalping_signal_orchestrator_service.py - signal logic", "Signal generation from OCR analysis"], "intellisense_value": "Understand why signals were generated or suppressed (NOTE: MAF decisions now available in testrade:maf-decisions stream)", "implementation_effort": "HIGH"}]}, "ACCOUNT_STATE": {"priority": "MEDIUM", "description": "Account summary and portfolio state changes", "missing_streams": [{"stream_name": "testrade:account-summary", "data_content": "Account balance, buying power, portfolio value", "source_locations": ["modules/broker_bridge/lightspeed_broker.py - AccountUpdateEvent", "Account summary data from broker"], "intellisense_value": "Track account state impact on trading decisions", "implementation_effort": "LOW"}, {"stream_name": "testrade:position-snapshots", "data_content": "Complete position snapshots from broker", "source_locations": ["modules/broker_bridge/lightspeed_broker.py:843 - positions_update handler", "AllPositionsEvent publishing"], "intellisense_value": "Verify position accuracy and detect discrepancies", "implementation_effort": "LOW"}]}, "ERROR_CONDITIONS": {"priority": "MEDIUM", "description": "System errors and alert conditions", "missing_streams": [{"stream_name": "testrade:system-alerts", "data_content": "Critical errors, kill switch activations, emergency stops", "source_locations": ["utils/flags.py - kill switch operations", "Emergency close all operations", "Critical error conditions"], "intellisense_value": "Understand system failure modes and recovery", "implementation_effort": "MEDIUM"}, {"stream_name": "testrade:data-quality-issues", "data_content": "OCR confidence issues, data validation failures", "source_locations": ["OCR confidence thresholds", "Data validation failures", "Serialization errors"], "intellisense_value": "Identify data quality impact on trading", "implementation_effort": "MEDIUM"}]}}, "current_streams": ["testrade:roi-updates", "testrade:order-fills", "testrade:market-data:active-quotes", "testrade:order-rejections", "testrade:health:babysitter", "testrade:raw-ocr-events", "testrade:order-status", "testrade:enriched-position-updates", "testrade:risk-actions", "testrade:market-data:quotes", "testrade:market-data:trades", "testrade:health:core", "testrade:commands:from_gui", "testrade:image-grabs", "testrade:position-updates", "testrade:order-requests", "testrade:cleaned-ocr-snapshots", "testrade:market-data:active-trades", "testrade:responses:to_gui", "testrade:validated-orders", "testrade:maf-decisions"]}