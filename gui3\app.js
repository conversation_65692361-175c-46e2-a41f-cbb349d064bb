// --- Application Configuration & Global State ---
const APP_CONFIG = {
    apiUrl: 'http://localhost:8001',
    maxReconnectAttempts: 5,
    healthPollingIntervalMs: 5000, // Back to 5 seconds for responsive latency
    coreStatusCheckIntervalMs: 10000, // 10 seconds for status checks
    reconnectDelayMs: 2000,
    // Memory management constants
    maxImageSizeBytes: 2000000, // 2MB limit for base64 images
    healthUpdateTimeoutMs: 15000, // 15 seconds timeout - reasonable balance
    maxImageHistoryCount: 5, // Keep only last 5 images of each type
    // UI constants
    maxStreamLines: 30, // Maximum lines in stream displays
    buttonResetDelayMs: 2000, // Success button reset delay
    buttonErrorDelayMs: 3000, // Error button reset delay
    maxSharesLimit: 10000, // Maximum shares for manual trades
    // Reconnection constants
    reconnectionTimeoutMs: 5000, // Fallback reconnection timeout
    // Debug mode
    debugMode: false, // Set to true for verbose logging
    // Image update debouncing
    imageUpdateDebounceMs: 50, // Debounce rapid image updates
    // Memory monitoring
    memoryWarningThresholdMB: 50, // Warn when total image memory exceeds 50MB
    maxImageHistoryItems: 100, // Maximum items in rawImageHistory and processedImageHistory arrays
};

// --- CRITICAL IMPROVEMENTS: DOM Caching, Memory Management & Error Handling ---

// 1. DOM ELEMENT CACHING - Performance Optimization
class DOMElementManager {
    constructor() {
        this.elements = new Map();
        this.cacheElements();
    }
    
    get(elementId) {
        // Return cached element or fall back to getElementById
        return this.elements.get(elementId) || document.getElementById(elementId);
    }
    
    cacheElements() {
        // Cache frequently accessed elements
        const commonElements = [
            'previewImage', 'imageModeToggle', 'roiBox', 
            'apiStatusDot', 'coreStatusDot', 'redisStatusDot', 'babysitterStatusDot', 'zmqStatusDot',
            'latencyDisplay', 'confidenceDisplay', 'accountValue', 'dayTotalPnL', 'buyingPower', 'buyingPowerLeft', 'buyingPowerUsageBar',
            'positionsContainer', 'historicalTradesContainer', 'systemMessages',
            'manualTradeSymbol', 'manualTradeShares', 'manualTradeAction',
            'ocrModeDisplay', 'streamOutput', 'historyContent', 'historyToggle'
        ];
        
        commonElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                this.elements.set(id, element);
            }
        });
        
        console.log(`DOMElementManager: Cached ${this.elements.size} elements`);
    }
    
    refreshCache() {
        this.elements.clear();
        this.cacheElements();
    }
}

// Initialize global DOM manager
const domManager = new DOMElementManager();

// 2. MEMORY LEAK PREVENTION - Critical Issue Fix
class ImageMemoryManager {
    constructor() {
        this.imageCache = new Map();
        this.maxCacheSize = 10; // Limit cached images
        this.totalMemoryUsed = 0;
        this.maxMemoryMB = 50; // 50MB limit
    }

    addImage(key, imageData, type = 'raw') {
        // Clean up old images if cache is full
        if (this.imageCache.size >= this.maxCacheSize) {
            this.cleanupOldestImages(3);
        }

        // Estimate memory usage
        const estimatedSize = imageData.length * 0.75; // Base64 overhead

        if (this.totalMemoryUsed + estimatedSize > this.maxMemoryMB * 1024 * 1024) {
            this.forceCleanup();
        }

        this.imageCache.set(key, {
            data: imageData,
            type: type,
            timestamp: Date.now(),
            size: estimatedSize
        });

        this.totalMemoryUsed += estimatedSize;
    }

    cleanupOldestImages(count) {
        const sortedEntries = Array.from(this.imageCache.entries())
            .sort((a, b) => a[1].timestamp - b[1].timestamp);

        for (let i = 0; i < Math.min(count, sortedEntries.length); i++) {
            const [key, entry] = sortedEntries[i];
            this.totalMemoryUsed -= entry.size;
            this.imageCache.delete(key);
        }
    }

    forceCleanup() {
        console.warn('Force cleaning image cache due to memory pressure');
        this.cleanupOldestImages(Math.floor(this.imageCache.size / 2));
    }

    getImage(key) {
        return this.imageCache.get(key);
    }

    getMemoryUsage() {
        return {
            totalMB: this.totalMemoryUsed / 1024 / 1024,
            cacheSize: this.imageCache.size,
            maxCacheSize: this.maxCacheSize
        };
    }
}

// Initialize global image manager
const imageManager = new ImageMemoryManager();

// 2. IMPROVED ERROR HANDLING - Critical Issue Fix
class ErrorHandler {
    constructor() {
        this.errorCount = 0;
        this.errorHistory = [];
        this.maxHistorySize = 100;
    }

    handleError(error, context = '', severity = 'error') {
        this.errorCount++;

        const errorInfo = {
            id: this.errorCount,
            message: error.message || error,
            context: context,
            severity: severity,
            timestamp: new Date().toISOString(),
            stack: error.stack || null
        };

        this.errorHistory.push(errorInfo);

        // Keep history manageable
        if (this.errorHistory.length > this.maxHistorySize) {
            this.errorHistory.shift();
        }

        // Log to console with appropriate level
        const logMethod = severity === 'warning' ? console.warn :
                         severity === 'info' ? console.info : console.error;

        logMethod(`[${severity.toUpperCase()}] ${context}: ${errorInfo.message}`, error);

        // Update UI
        this.updateErrorDisplay(errorInfo);

        // Report critical errors
        if (severity === 'critical') {
            this.reportCriticalError(errorInfo);
        }
    }

    updateErrorDisplay(errorInfo) {
        const { severity, message, context } = errorInfo;

        // Use existing addSystemMessage function
        if (typeof addSystemMessage === 'function') {
            addSystemMessage(`${context}: ${message}`, severity);
        }

        // Update error indicator if it exists
        const errorIndicator = domManager.get('errorIndicator');
        if (errorIndicator) {
            errorIndicator.textContent = `Errors: ${this.errorCount}`;
            errorIndicator.className = `error-indicator ${severity}`;
        }
    }

    reportCriticalError(errorInfo) {
        console.error('CRITICAL ERROR REPORTED:', errorInfo);

        // Show user notification for critical errors
        if (typeof addSystemMessage === 'function') {
            addSystemMessage('Critical error occurred. Check console for details.', 'error');
        }
    }

    getErrorSummary() {
        const recent = this.errorHistory.slice(-10);
        const byCategory = recent.reduce((acc, error) => {
            acc[error.context] = (acc[error.context] || 0) + 1;
            return acc;
        }, {});

        return {
            total: this.errorCount,
            recent: recent.length,
            categories: byCategory
        };
    }
}

const errorHandler = new ErrorHandler();

// 4. PERFORMANCE MONITORING - New Feature
class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.isEnabled = APP_CONFIG.debugMode || false;
    }

    startTiming(label) {
        if (!this.isEnabled) return;

        this.metrics.set(label, {
            start: performance.now(),
            memory: this.getMemoryUsage()
        });
    }

    endTiming(label) {
        if (!this.isEnabled) return;

        const metric = this.metrics.get(label);
        if (!metric) return;

        const duration = performance.now() - metric.start;
        const memoryAfter = this.getMemoryUsage();

        console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms, Memory: ${memoryAfter.used - metric.memory.used}MB`);

        this.metrics.delete(label);
    }

    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return { used: 0, total: 0, limit: 0 };
    }

    logPerformanceReport() {
        if (!this.isEnabled) return;

        const memory = this.getMemoryUsage();
        console.group('📊 Performance Report');
        console.log(`Memory Usage: ${memory.used}MB / ${memory.total}MB (Limit: ${memory.limit}MB)`);
        console.log(`Active trades: ${state.active_trades?.length || 0}`);
        console.log(`Historical trades: ${tradeState.historicalTrades?.length || 0}`);
        console.log(`WebSocket connected: ${state.connected}`);
        console.log(`Image cache: ${imageManager.getMemoryUsage().totalMB.toFixed(1)}MB`);
        console.groupEnd();
    }
}

const performanceMonitor = new PerformanceMonitor();

// 3. MESSAGE ROUTING - Organized WebSocket Message Handling
class MessageRouter {
    constructor() {
        this.handlers = {
            'health_update': this.handleHealthUpdate.bind(this),
            'order_status_update': this.handleOrderStatusUpdate.bind(this),
            'order_fill_update': this.handleOrderFillUpdate.bind(this),
            'trade_history_update': this.handleTradeHistoryUpdate.bind(this),
            'position_summary': this.handlePositionSummary.bind(this),
            'position_update': this.handlePositionUpdate.bind(this),
            'account_summary_update': this.handleAccountSummaryUpdate.bind(this),
            'ocr_update': this.handleOCRUpdate.bind(this),
            'processed_ocr': this.handleProcessedOCR.bind(this),
            'image_grab': this.handleImageGrab.bind(this),
            'preview_frame': this.handlePreviewFrame.bind(this),
            'roi_update': this.handleROIUpdate.bind(this),
            'core_status_update': this.handleCoreStatusUpdate.bind(this),
            'trade_opened': this.handleTradeOpened.bind(this),
            'trade_closed': this.handleTradeClosed.bind(this),
            'daily_summary_update': this.handleDailySummaryUpdate.bind(this),
            'orders_update': this.handleOrdersUpdate.bind(this),
            'open_order_update': this.handleOpenOrderUpdate.bind(this),
            'price_quote_update': this.handlePriceQuoteUpdate.bind(this),
            'market_quote_update': this.handleMarketQuoteUpdate.bind(this),
            'price_trade_update': this.handlePriceTradeUpdate.bind(this),
            'volume_data_update': this.handleVolumeDataUpdate.bind(this),
            'api_fallback_status': this.handleAPIFallbackStatus.bind(this),
            'market_data_summary': this.handleMarketDataSummary.bind(this),
            'generic_update': this.handleGenericUpdate.bind(this)
        };
    }
    
    route(data) {
        const handler = this.handlers[data.type];
        if (handler) {
            try {
                handler(data);
                return true;
            } catch (error) {
                errorHandler.handleError(error, `MessageRouter.${data.type}`, 'error');
                return false;
            }
        }
        
        // Log unhandled message types for debugging
        if (APP_CONFIG.debugMode || data.type === 'processed_ocr') {
            console.warn(`MessageRouter: Unhandled message type: ${data.type}`, data);
        }
        return false;
    }
    
    // Health Update Handler
    handleHealthUpdate(data) {
        const component = data.component || 'Unknown';
        const status = data.status || 'unknown';
        
        // Update status indicators
        this.updateComponentStatus(component, status);
        
        // Handle health issues
        if (status !== 'healthy' && data.issues?.length > 0) {
            console.warn(`Health issues in ${component}:`, data.issues);
        }
        
        // Check for latency in health updates
        if (data.latency_ms && typeof updateLatencyDisplay === 'function') {
            updateLatencyDisplay(data.latency_ms);
        } else if (data.metrics && data.metrics.response_time_ms && typeof updateLatencyDisplay === 'function') {
            // Try metrics.response_time_ms as fallback
            updateLatencyDisplay(data.metrics.response_time_ms);
        }
    }
    
    // Order Status Update Handler
    handleOrderStatusUpdate(data) {
        if (data.payload) {
            console.log(`Order Update: ${data.payload.symbol} - ${data.payload.status}`);
            
            // Add to system messages
            if (typeof addSystemMessage === 'function') {
                const symbol = data.payload.symbol || 'UNKNOWN';
                const status = data.payload.status || 'UNKNOWN';
                addSystemMessage(`Order ${symbol}: ${status}`, 'info');
            }
        }
    }
    
    // Order Fill Update Handler
    handleOrderFillUpdate(data) {
        if (data.fill) {
            const fill = data.fill;
            console.log(`Fill: ${fill.symbol} ${fill.side} ${fill.quantity}@${fill.price}`);
            
            if (typeof addSystemMessage === 'function') {
                addSystemMessage(`Fill: ${fill.symbol} ${fill.quantity}@${fill.price}`, 'success');
            }
        }
    }
    
    // Trade History Update Handler
    handleTradeHistoryUpdate(data) {
        if (data.trades && Array.isArray(data.trades)) {
            // Update trade state
            if (typeof updateTradeHistoryData === 'function') {
                updateTradeHistoryData(data.trades);
            }
        }
    }
    
    // Position Summary Handler
    handlePositionSummary(data) {
        if (data.data && typeof updateCurrentPositions === 'function') {
            updateCurrentPositions(data.data);
        }
    }
    
    // Position Update Handler - for individual position updates
    handlePositionUpdate(data) {
        console.log('[MessageRouter] Position update received:', data);
        
        // Update internal state
        if (data.symbol && data.position) {
            const existingIndex = state.activeTrades.findIndex(t => t.symbol === data.symbol);
            if (data.is_open) {
                if (existingIndex >= 0) {
                    // Update existing position but PRESERVE market data
                    const existing = state.activeTrades[existingIndex];
                    
                    // Preserve market data if not provided in update
                    data.position.bid_price = data.position.bid_price || existing.bid_price;
                    data.position.ask_price = data.position.ask_price || existing.ask_price;
                    data.position.last_price = data.position.last_price || existing.last_price;
                    data.position.volume = data.position.volume || existing.volume;
                    
                    state.activeTrades[existingIndex] = data.position;
                } else {
                    // Add new position
                    state.activeTrades.push(data.position);
                }
            } else {
                // Remove closed position
                if (existingIndex >= 0) {
                    state.activeTrades.splice(existingIndex, 1);
                }
            }
            
            // Trigger UI update
            updateActiveTrades(state.activeTrades);
        }
    }
    
    // Account Summary Update Handler
    handleAccountSummaryUpdate(data) {
        if (data.account && typeof updateAccountDisplay === 'function') {
            updateAccountDisplay(data.account);
        }
    }
    
    // OCR Update Handler
    handleOCRUpdate(data) {
        // Track frame timestamp for synchronization
        if (data.frame_number !== undefined && data.timestamp) {
            ocrTimestampSync.updateTimestamp(data.frame_number, data.timestamp);
        }
        
        if (typeof handleRawOCRMessage === 'function') {
            handleRawOCRMessage(data);
        }
    }
    
    // Processed OCR Handler
    handleProcessedOCR(data) {
        console.log('Processed OCR received:', data);
        
        // Update the processed OCR table
        if (data.snapshots && typeof updateProcessedOcrTable === 'function') {
            updateProcessedOcrTable(data.snapshots, data.timestamp);
        }
        
        // Also update OCR display manager if available
        if (ocrDisplay && typeof ocrDisplay.showProcessedData === 'function') {
            ocrDisplay.showProcessedData(data.snapshots);
        }
        
        // Clear the table if no trade data
        if (data.no_trade_data && typeof updateProcessedOcrTable === 'function') {
            updateProcessedOcrTable({}, data.timestamp);
        }
    }
    
    // Image Grab Handler
    handleImageGrab(data) {
        // Simple latency tracking - use message counter approach
        if (wsLatencyTracker) {
            wsLatencyTracker.trackMessage();
            // Update latency based on message frequency
            const latency = wsLatencyTracker.getAverageLatency();
            if (latency && latency > 0) {
                updateLatencyDisplay(latency);
            }
        }
        
        if (data.image_data && typeof updatePreviewImage === 'function') {
            updatePreviewImage(data.image_data, 'raw');
        }
    }
    
    // Preview Frame Handler (Processed images)
    handlePreviewFrame(data) {
        console.log('Preview frame received in MessageRouter:', !!data.image_data, !!data.content);
        
        // Simple latency tracking - use message counter approach
        if (wsLatencyTracker) {
            wsLatencyTracker.trackMessage();
            // Update latency based on message frequency
            const latency = wsLatencyTracker.getAverageLatency();
            if (latency && latency > 0) {
                updateLatencyDisplay(latency);
            }
        }
        
        if (data.image_data && typeof updatePreviewImage === 'function') {
            updatePreviewImage(data.image_data, 'processed');
        }
        
        // Handle OCR content from preview frame
        if (data.content) {
            const rawOcrStream = domManager.get('rawOcrStream');
            if (rawOcrStream) {
                rawOcrStream.textContent = data.content;
            }
        }
        
        // Update confidence if provided
        if (data.confidence !== undefined && typeof updateConfidence === 'function') {
            updateConfidence(data.confidence);
        }
    }
    
    // ROI Update Handler
    handleROIUpdate(data) {
        if (data.roi && typeof updateROIDisplay === 'function') {
            updateROIDisplay(data.roi);
        }
    }
    
    // Core Status Update Handler
    handleCoreStatusUpdate(data) {
        if (typeof addSystemMessage === 'function') {
            const message = data.message || data.status_event || 'Core status update';
            addSystemMessage(`Core: ${message}`, 'info');
        }
    }
    
    // Trade Opened Handler
    handleTradeOpened(data) {
        if (data.trade && typeof addSystemMessage === 'function') {
            const trade = data.trade;
            addSystemMessage(`Trade Opened: ${trade.symbol} ${trade.side} ${trade.quantity}`, 'success');
        }
    }
    
    // Trade Closed Handler
    handleTradeClosed(data) {
        if (data.trade && typeof addSystemMessage === 'function') {
            const trade = data.trade;
            const pnl = trade.realized_pnl || 0;
            const pnlColor = pnl >= 0 ? 'success' : 'error';
            addSystemMessage(`Trade Closed: ${trade.symbol} P&L $${pnl.toFixed(2)}`, pnlColor);
        }
    }
    
    // Daily Summary Update Handler
    handleDailySummaryUpdate(data) {
        if (data.summary && typeof updateDailySummaryDisplay === 'function') {
            updateDailySummaryDisplay(data.summary);
        }
    }
    
    // Orders Update Handler
    handleOrdersUpdate(data) {
        if (data.orders && typeof updateOrdersDisplay === 'function') {
            updateOrdersDisplay(data.orders);
        }
    }
    
    // Open Order Update Handler - for bootstrap and real-time open orders
    handleOpenOrderUpdate(data) {
        console.log('[MessageRouter] Open order update received:', data);
        
        if (data.order) {
            const order = data.order;
            const localOrderId = order.local_order_id;
            
            if (localOrderId) {
                // Update tradeState.openOrders
                const existingIndex = tradeState.openOrders.findIndex(o => o.local_order_id === localOrderId);
                
                if (existingIndex >= 0) {
                    // Update existing order
                    tradeState.openOrders[existingIndex] = order;
                } else {
                    // Add new order
                    tradeState.openOrders.push(order);
                }
                
                // If this is a child order with parent_trade_id, trigger position update
                if (order.parent_trade_id && order.is_child) {
                    // Find parent position and trigger update to refresh child orders display
                    const parentPosition = state.activeTrades.find(t => t.trade_id === order.parent_trade_id);
                    if (parentPosition) {
                        // Trigger refresh of position display to show new child orders
                        updateActiveTrades(state.activeTrades);
                    }
                }
                
                // Log bootstrap info
                if (data.is_bootstrap) {
                    console.log(`[Bootstrap] Loaded open order: ${order.symbol} ${order.side} ${order.quantity} @ ${order.price}`);
                }
            }
        }
    }
    
    // Price Quote Update Handler
    handlePriceQuoteUpdate(data) {
        // Update price displays if needed
        if (typeof updatePriceDisplay === 'function') {
            updatePriceDisplay(data.symbol, data.bid, data.ask);
        }
    }
    
    // Market Quote Update Handler - updates market data for positions
    handleMarketQuoteUpdate(data) {
        console.log('[MessageRouter] Market quote update:', data);
        
        if (data.symbol && state.activeTrades) {
            // Find position with this symbol
            const position = state.activeTrades.find(t => t.symbol === data.symbol);
            if (position) {
                // Update market data
                position.bid_price = data.bid_price || position.bid_price;
                position.ask_price = data.ask_price || position.ask_price;
                position.last_price = data.last_price || position.last_price;
                position.bid_size = data.bid_size || position.bid_size;
                position.ask_size = data.ask_size || position.ask_size;
                position.volume = data.volume || position.volume;
                
                console.log(`[MessageRouter] Updated market data for ${data.symbol}: bid=${data.bid_price}, ask=${data.ask_price}`);
                
                // Trigger live update only (no DOM rebuild)
                updateLiveDataOnly(state.activeTrades);
            }
        }
    }
    
    // Price Trade Update Handler
    handlePriceTradeUpdate(data) {
        // Update last trade displays if needed
        if (typeof updateLastTradeDisplay === 'function') {
            updateLastTradeDisplay(data.symbol, data.last_price, data.last_size);
        }
    }
    
    // Volume Data Update Handler
    handleVolumeDataUpdate(data) {
        // Update volume displays if needed
        if (typeof updateVolumeDisplay === 'function') {
            updateVolumeDisplay(data.symbol, data.volume);
        }
    }
    
    // API Fallback Status Handler
    handleAPIFallbackStatus(data) {
        if (data.fallback_active && typeof addSystemMessage === 'function') {
            addSystemMessage(`API Fallback: ${data.symbol} - ${data.reason}`, 'warning');
        }
    }
    
    // Market Data Summary Handler
    handleMarketDataSummary(data) {
        if (data.symbols && typeof updateMarketDataSummary === 'function') {
            updateMarketDataSummary(data.symbols);
        }
    }
    
    // Generic Update Handler
    handleGenericUpdate(data) {
        if (APP_CONFIG.debugMode) {
            console.log('Generic update:', data);
        }
    }
    
    // Helper method to update component status
    updateComponentStatus(component, status) {
        const statusMap = {
            'ApplicationCore': 'coreStatusDot',
            'Core': 'coreStatusDot', 
            'Babysitter': 'babysitterStatusDot',
            'Redis': 'redisStatusDot',
            'API': 'apiStatusDot'
        };
        
        const elementId = statusMap[component];
        if (elementId) {
            const statusDot = domManager.get(elementId);
            if (statusDot) {
                const color = status === 'healthy' || status === 'RUNNING' ? 
                    'var(--accent-success)' : 'var(--accent-danger)';
                statusDot.style.background = color;
            }
        }
    }
}

// Initialize global message router
const messageRouter = new MessageRouter();

// 4. COMPONENT CLASSES - Organized Display Management
class OCRDisplayManager {
    constructor() {
        this.confidenceElement = domManager.get('confidenceDisplay');
        this.previewImage = domManager.get('previewImage');
        this.roiBox = domManager.get('roiBox');
    }
    
    updateConfidence(confidence) {
        if (this.confidenceElement) {
            this.confidenceElement.textContent = `${confidence.toFixed(1)}%`;
            
            // Color coding based on confidence level
            this.confidenceElement.className = confidence > 80 ? 'high-confidence' :
                                             confidence > 60 ? 'medium-confidence' : 'low-confidence';
        }
    }
    
    updateImage(imageData, type = 'raw') {
        if (this.previewImage && imageData) {
            const src = imageData.startsWith('data:') ? imageData : `data:image/png;base64,${imageData}`;
            this.previewImage.src = src;
            
            // Store in image manager
            imageManager.addImage(`${type}_${Date.now()}`, imageData, type);
        }
    }
    
    updateROI(roi) {
        if (this.roiBox && roi && roi.length === 4) {
            const [x1, y1, x2, y2] = roi;
            const width = x2 - x1;
            const height = y2 - y1;
            
            this.roiBox.style.left = `${x1}px`;
            this.roiBox.style.top = `${y1}px`;
            this.roiBox.style.width = `${width}px`;
            this.roiBox.style.height = `${height}px`;
            this.roiBox.style.display = 'block';
        }
    }
    
    showProcessedData(snapshots) {
        // Update processed OCR data display
        if (snapshots && Object.keys(snapshots).length > 0) {
            console.log('OCR processed data:', snapshots);
            
            // Update any processed data displays
            const processedContainer = domManager.get('processedDataContainer');
            if (processedContainer) {
                let html = '<div class="processed-symbols">';
                Object.entries(snapshots).forEach(([symbol, data]) => {
                    html += `<div class="symbol-data">
                        <strong>${symbol}</strong>: ${JSON.stringify(data)}
                    </div>`;
                });
                html += '</div>';
                processedContainer.innerHTML = html;
            }
        }
    }
}

class TradingDisplayManager {
    constructor() {
        this.accountValueElement = domManager.get('accountValue');
        this.dayPnlElement = domManager.get('dayPnl');
        this.buyingPowerElement = domManager.get('buyingPower');
        this.positionsContainer = domManager.get('positionsContainer');
    }
    
    updateAccountSummary(accountData) {
        if (this.accountValueElement && accountData.account_value !== undefined) {
            this.accountValueElement.textContent = `$${this.formatCurrency(accountData.account_value)}`;
        }
        
        if (this.dayPnlElement && accountData.day_pnl !== undefined) {
            const pnl = parseFloat(accountData.day_pnl);
            this.dayPnlElement.textContent = `$${this.formatCurrency(pnl)}`;
            this.dayPnlElement.className = pnl >= 0 ? 'positive-pnl' : 'negative-pnl';
        }
        
        if (this.buyingPowerElement && accountData.buying_power !== undefined) {
            this.buyingPowerElement.textContent = `$${this.formatCurrency(accountData.buying_power)}`;
        }
    }
    
    updatePositionSummary(summary) {
        if (this.positionsContainer && summary) {
            let html = '';
            
            if (summary.positions && Object.keys(summary.positions).length > 0) {
                Object.entries(summary.positions).forEach(([symbol, position]) => {
                    const pnl = parseFloat(position.pnl || 0);
                    const pnlClass = pnl >= 0 ? 'positive-pnl' : 'negative-pnl';
                    
                    html += `
                        <div class="position-item">
                            <div class="position-symbol">${symbol}</div>
                            <div class="position-shares">${position.shares || 0}</div>
                            <div class="position-pnl ${pnlClass}">$${this.formatCurrency(pnl)}</div>
                        </div>
                    `;
                });
            } else {
                html = '<div class="no-positions">No active positions</div>';
            }
            
            this.positionsContainer.innerHTML = html;
        }
    }
    
    formatCurrency(value) {
        if (typeof value !== 'number') {
            value = parseFloat(value) || 0;
        }
        return value.toFixed(2);
    }
    
    updateOrderStatus(orderData) {
        // Handle order status updates
        if (orderData && typeof addSystemMessage === 'function') {
            const symbol = orderData.symbol || 'UNKNOWN';
            const status = orderData.status || 'UNKNOWN';
            const action = orderData.side || orderData.action || '';
            const quantity = orderData.quantity || 0;
            
            addSystemMessage(`${symbol} ${action} ${quantity}: ${status}`, 'info');
        }
    }
}

// Initialize component managers
const ocrDisplay = new OCRDisplayManager();
const tradingDisplay = new TradingDisplayManager();

// OCR Event Timestamp Synchronization
const ocrTimestampSync = {
    currentFrameTimestamp: null,
    frameNumber: null,
    
    updateTimestamp(frameNumber, timestamp) {
        this.frameNumber = frameNumber;
        this.currentFrameTimestamp = timestamp;
        console.log(`OCR Timestamp Sync: Frame ${frameNumber} at ${new Date(timestamp).toLocaleTimeString()}`);
    },
    
    getCurrentTimestamp() {
        return this.currentFrameTimestamp || Date.now();
    }
};

// Simple WebSocket latency tracker
const wsLatencyTracker = {
    lastMessageTime: 0,
    messageCount: 0,
    latencySamples: [],
    maxSamples: 10,
    
    trackMessage() {
        const now = performance.now();
        if (this.lastMessageTime > 0) {
            const timeSinceLastMessage = now - this.lastMessageTime;
            // Only track reasonable intervals (ignore very long gaps)
            if (timeSinceLastMessage < 5000) {
                this.latencySamples.push(timeSinceLastMessage);
                if (this.latencySamples.length > this.maxSamples) {
                    this.latencySamples.shift();
                }
                
                // Update display every 5 messages
                this.messageCount++;
                if (this.messageCount % 5 === 0 && this.latencySamples.length > 0) {
                    const avgLatency = this.latencySamples.reduce((a, b) => a + b, 0) / this.latencySamples.length;
                    updateLatencyDisplay(avgLatency);
                }
            }
        }
        this.lastMessageTime = now;
    },
    
    getAverageLatency() {
        if (this.latencySamples.length === 0) return null;
        return this.latencySamples.reduce((a, b) => a + b, 0) / this.latencySamples.length;
    }
};

const state = {
    ocrActive: false,
    recordingActive: false,
    coreActive: false,
    currentROI: [64, 159, 681, 296], // Will be loaded from backend control.json
    ocrSettingsExpanded: false,
    developmentSettingsExpanded: false,
    allSymbols: false,
    confidenceMode: false,
    videoMode: false,
    perfTracking: false,
    usePreprocessedImage: false,
    imageMode: 'raw', // 'raw' or 'processed'
    lastRawImage: null,
    lastProcessedImage: null,
    rawImageHistory: [], // Keep history for memory management
    processedImageHistory: [], // Keep history for memory management
    imageUpdateTimeout: null, // Debounce rapid image updates
    ws: null,
    apiUrl: APP_CONFIG.apiUrl,
    wsUrl: APP_CONFIG.apiUrl.replace('http', 'ws') + '/ws',
    connected: false,
    sessions: [],
    selectedSession: null,
    lastHealthUpdate: 0, // Initialize to 0 so core starts as disconnected
    coreConnected: false, // Track core connection state
    roiAdjustmentMode: false, // Lock image during ROI adjustments
    lockedImage: null, // Store locked image during ROI adjustments
    healthPollingInterval: null, // System health polling interval
    lastHealthStatus: {}, // Track last health status for change detection
    reconnecting: false, // Prevent multiple simultaneous reconnection attempts
    activeTrades: [], // Active trades array for position updates
    active_trades: [] // Alternative property name used in some places
};

const tradeState = {
    openPositions: [],
    historicalTrades: [],
    openOrders: [], // Bootstrap pattern: stores current open orders
    totalPnL: 0,
    dayPnL: 0,
    accountValue: 0,
    buyingPower: 0
};

const uiState = {
    historyExpanded: false,
    activeHistoryTab: 'today'
};

// --- END Application Configuration & Global State ---

// --- Utility Functions ---
function formatTime(timestampInSeconds) { // Expects seconds
    if (!timestampInSeconds) return new Date().toLocaleTimeString();
    return new Date(timestampInSeconds * 1000).toLocaleTimeString();
}

// Helper to convert backend timestamp (assumed seconds) to JS ms for Date()
function getJsTimestamp(backendTimestamp) {
    if (!backendTimestamp) return Date.now(); // Fallback to current time
    // If backendTimestamp is already in ms (e.g., > year 2033 in seconds), use as is
    return backendTimestamp > ********** ? backendTimestamp * 1000 : backendTimestamp;
}

function formatCurrency(value) {
    if (value === null || value === undefined || isNaN(parseFloat(value))) return '$0.00';
    if (typeof value === 'string') {
        if (value.includes('$')) return value;
        const cleaned = value.replace(/[^-\d.]/g, '');
        const num = parseFloat(cleaned);
        return isNaN(num) ? '$0.00' : `$${num.toFixed(2)}`;
    }
    if (typeof value === 'number') return `$${value.toFixed(2)}`;
    return '$0.00';
}

function formatNumber(value) { // For quantities, counts
    if (value === null || value === undefined || isNaN(parseFloat(value))) return '0';
    // For whole numbers like quantity or fills, avoid decimals
    if (Number.isInteger(parseFloat(value))) return String(parseInt(value));
    return parseFloat(value).toFixed(2); // For things like avg price if not currency
}

function formatVolume(volume) {
    if (!volume || isNaN(parseInt(volume))) return '-';
    const num = parseInt(volume);
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toLocaleString();
}

function getPnLClass(value) {
    if (value === null || value === undefined || isNaN(parseFloat(value))) return 'pnl-neutral';
    const numValue = typeof value === 'string' ? parseFloat(value.replace(/[^-\d.]/g, '')) : parseFloat(value);
    if (numValue > 0) return 'pnl-positive';
    if (numValue < 0) return 'pnl-negative';
    return 'pnl-neutral'; // For P&L of 0
}

function formatPnLWithClass(value) {
    const formatted = formatCurrency(value);
    const className = getPnLClass(value);
    return `<span class="${className}">${formatted}</span>`;
}

function getActionStatusClass(status) {
    if (!status) return '';
    const statusUpper = status.toUpperCase();
    if (statusUpper.includes('FILLED')) return 'status-filled';
    if (statusUpper.includes('PENDING') || statusUpper.includes('SUBMITTED')) return 'status-pending';
    if (statusUpper.includes('CANCELLED') || statusUpper.includes('CANCELED')) return 'status-cancelled';
    if (statusUpper.includes('PARTIAL')) return 'status-partial';
    if (statusUpper.includes('OPEN') || statusUpper.includes('ACTIVE')) return 'status-active';
    if (statusUpper.includes('CLOSED')) return 'status-closed';
    return '';
}

function formatMarketDisplay(bid, ask) {
    const b = bid ? formatCurrency(bid) : '-';
    const a = ask ? formatCurrency(ask) : '-';
    return `${b}×${a}`;
}

function createPositionSummaryCardHTML(position) {
    if (!position || !position.symbol) {
        console.error("createPositionSummaryCardHTML: Invalid position data", position);
        return '<div class="position-summary-card error">Error: Invalid position data</div>';
    }

    const symbol = position.symbol || 'UNKNOWN';
    const quantity = position.quantity || 0;
    const avgPrice = position.average_price || position.avg_price || 0;
    const currentPrice = position.current_price || position.last_price || avgPrice;
    const unrealizedPnL = position.unrealized_pnl || 0;
    const realizedPnL = position.realized_pnl || 0;
    const pnlPerShare = position.pnl_per_share || (quantity !== 0 ? unrealizedPnL / Math.abs(quantity) : 0);
    const bid = position.bid || currentPrice;
    const ask = position.ask || currentPrice;
    const volume = position.volume || position.day_volume || 0;
    const strategy = position.strategy || 'UNKNOWN';

    return `
        <div class="position-summary-card">
            <div class="position-summary-header">
                <div class="top-row">
                    <div class="summary-section symbol-section primary-section">
                        <div class="summary-section-label">Symbol</div>
                        <div class="summary-section-value">${symbol}</div>
                        <div class="summary-section-subtitle">${strategy}</div>
                    </div>
                    <div class="summary-section primary-section">
                        <div class="summary-section-label">Quantity</div>
                        <div class="summary-section-value">${formatNumber(quantity)}</div>
                        <div class="summary-section-subtitle">shares</div>
                    </div>
                    <div class="summary-section primary-section">
                        <div class="summary-section-label">Avg Price</div>
                        <div class="summary-section-value">${formatCurrency(avgPrice)}</div>
                        <div class="summary-section-subtitle">cost basis</div>
                    </div>
                    <div class="summary-section pnl-section primary-section">
                        <div class="summary-section-label">Unrealized P&L</div>
                        <div class="summary-section-value ${getPnLClass(unrealizedPnL)}">${formatCurrency(unrealizedPnL)}</div>
                        <div class="summary-section-subtitle">mark to market</div>
                    </div>
                    <div class="summary-section pnl-section primary-section">
                        <div class="summary-section-label">Realized P&L</div>
                        <div class="summary-section-value ${getPnLClass(realizedPnL)}">${formatCurrency(realizedPnL)}</div>
                        <div class="summary-section-subtitle">closed trades</div>
                    </div>
                    <div class="summary-section pnl-section primary-section">
                        <div class="summary-section-label">P&L/Share</div>
                        <div class="summary-section-value ${getPnLClass(pnlPerShare)}">${formatCurrency(pnlPerShare)}</div>
                        <div class="summary-section-subtitle">per share</div>
                    </div>
                    <div class="summary-section primary-section">
                        <div class="summary-section-label">Current Price</div>
                        <div class="summary-section-value">${formatCurrency(currentPrice)}</div>
                        <div class="summary-section-subtitle">last trade</div>
                    </div>
                    <div class="summary-section primary-section">
                        <div class="summary-section-label">Bid×Ask</div>
                        <div class="summary-section-value">${formatMarketDisplay(bid, ask)}</div>
                        <div class="summary-section-subtitle">market</div>
                    </div>
                </div>
                <div class="bottom-row">
                    <div class="summary-section secondary-section">
                        <div class="summary-section-label">Market Value</div>
                        <div class="summary-section-value">${formatCurrency(Math.abs(quantity) * currentPrice)}</div>
                    </div>
                    <div class="summary-section secondary-section">
                        <div class="summary-section-label">Cost Basis</div>
                        <div class="summary-section-value">${formatCurrency(Math.abs(quantity) * avgPrice)}</div>
                    </div>
                    <div class="summary-section secondary-section">
                        <div class="summary-section-label">Day Change</div>
                        <div class="summary-section-value ${getPnLClass(currentPrice - avgPrice)}">${formatCurrency(currentPrice - avgPrice)}</div>
                    </div>
                    <div class="summary-section secondary-section">
                        <div class="summary-section-label">Day Change %</div>
                        <div class="summary-section-value ${getPnLClass(currentPrice - avgPrice)}">${avgPrice !== 0 ? ((currentPrice - avgPrice) / avgPrice * 100).toFixed(2) + '%' : '0.00%'}</div>
                    </div>
                    <div class="summary-section secondary-section">
                        <div class="summary-section-label">Total P&L</div>
                        <div class="summary-section-value ${getPnLClass(unrealizedPnL + realizedPnL)}">${formatCurrency(unrealizedPnL + realizedPnL)}</div>
                    </div>
                    <div class="summary-section secondary-section">
                        <div class="summary-section-label">Total P&L %</div>
                        <div class="summary-section-value ${getPnLClass(unrealizedPnL + realizedPnL)}">${avgPrice !== 0 ? ((unrealizedPnL + realizedPnL) / (Math.abs(quantity) * avgPrice) * 100).toFixed(2) + '%' : '0.00%'}</div>
                    </div>
                    <div class="summary-section secondary-section">
                        <div class="summary-section-label">Volume</div>
                        <div class="summary-section-value">${formatVolume(volume)}</div>
                    </div>
                    <div class="summary-section secondary-section">
                        <div class="summary-section-label">Last Update</div>
                        <div class="summary-section-value">${formatTime()}</div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getStrategyFromRawData(data) {
    // Try to determine strategy from raw OCR data
    if (data.action_intent_str) {
        const action = data.action_intent_str.toUpperCase();
        if (action.includes('BUY') || action.includes('LONG')) return 'LONG';
        if (action.includes('SELL') || action.includes('SHORT')) return 'SHORT';
        if (action.includes('CLOSE')) return 'CLOSED';
    }

    // Try to infer from quantity
    if (data.quantity_str) {
        const qty = parseFloat(data.quantity_str);
        if (qty > 0) return 'LONG';
        if (qty < 0) return 'SHORT';
    }

    return 'UNKNOWN';
}

// Calculate derived fields for trades
function calculateDerivedTradeFields(trade) {
    // Ensure we have valid data
    trade.quantity = trade.quantity || 0;
    trade.price = trade.price || 0;
    trade.average_price = trade.average_price || trade.price || 0;
    
    // Market data
    const bid = trade.bid_price || 0;
    const ask = trade.ask_price || 0;
    const last = trade.last_price || trade.current_price || 0;
    
    // Calculate spread
    trade.spread = bid && ask ? (ask - bid).toFixed(4) : 0;
    trade.spread_percent = bid && ask && bid > 0 ? ((ask - bid) / bid * 100).toFixed(2) : 0;
    
    // Calculate P&L per share
    if (last && trade.average_price) {
        trade.pnl_per_share = last - trade.average_price;
        trade.pnl_percent = trade.average_price > 0 ? 
            ((last - trade.average_price) / trade.average_price * 100).toFixed(2) : 0;
    } else {
        trade.pnl_per_share = 0;
        trade.pnl_percent = 0;
    }
    
    // Calculate market value
    trade.market_value = Math.abs(trade.quantity) * (last || trade.average_price);
    trade.cost_basis = Math.abs(trade.quantity) * trade.average_price;
    
    // Calculate unrealized P&L if not provided
    if (trade.unrealized_pnl === undefined && last && trade.average_price) {
        trade.unrealized_pnl = (last - trade.average_price) * trade.quantity;
    }
    
    // Total traded (for positions with multiple fills)
    trade.total_traded = trade.total_fills_count ? 
        Math.abs(trade.quantity) : Math.abs(trade.quantity);
    
    // Commission per share
    trade.commission_per_share = trade.commission && trade.quantity ? 
        (trade.commission / Math.abs(trade.quantity)).toFixed(4) : 0;
    
    // Time calculations
    // Use last_fill_timestamp as the position open time, fallback to timestamp
    const openTimestamp = trade.last_fill_timestamp || trade.timestamp;
    if (openTimestamp) {
        const tradeTime = new Date(getJsTimestamp(openTimestamp));
        trade.opened_time = tradeTime.toLocaleTimeString();
        trade.opened_date = tradeTime.toLocaleDateString();
        
        // Calculate trade duration
        if (trade.close_timestamp || trade.status === 'CLOSED') {
            const closeTime = new Date(getJsTimestamp(trade.close_timestamp || trade.timestamp));
            trade.trade_duration_ms = closeTime - tradeTime;
            trade.trade_duration_formatted = formatDuration(trade.trade_duration_ms);
        } else {
            // For open positions, calculate duration from open time to now
            trade.trade_duration_ms = Date.now() - tradeTime;
            trade.trade_duration_formatted = formatDuration(trade.trade_duration_ms);
        }
    }
    
    // Format latencies
    if (trade.signal_to_fill_latency_ms !== undefined) {
        trade.signal_latency_formatted = trade.signal_to_fill_latency_ms + 'ms';
    }
    if (trade.order_to_fill_latency_ms !== undefined) {
        trade.order_latency_formatted = trade.order_to_fill_latency_ms + 'ms';
    }
    
    // Strategy display
    trade.strategy_display = trade.strategy ? trade.strategy.toUpperCase() : 'MANUAL';
    
    // Side/Direction
    trade.direction = trade.quantity > 0 ? 'LONG' : trade.quantity < 0 ? 'SHORT' : 'FLAT';
    
    return trade;
}

// Format duration helper
function formatDuration(ms) {
    if (!ms || ms < 0) return 'N/A';
    
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ${hours % 24}h`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
}

// Toggle functions for collapsible sections
function toggleHistory(event) {
    // Prevent event propagation if event is passed
    if (event) {
        event.stopPropagation();
    }

    uiState.historyExpanded = !uiState.historyExpanded;
    const historyContent = domManager.get('historyContent');
    const historyToggle = domManager.get('historyToggle');

    if (historyContent && historyToggle) {
        if (uiState.historyExpanded) {
            historyContent.classList.remove('collapsed');
            historyToggle.textContent = '×';
            historyToggle.classList.add('expanded');
        } else {
            historyContent.classList.add('collapsed');
            historyToggle.textContent = '+';
            historyToggle.classList.remove('expanded');
        }
    }
}

function switchHistoryTab(tabElement, tabType) {
    // Remove active class from all tabs
    document.querySelectorAll('.history-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Add active class to clicked tab (with null check)
    if (tabElement) {
        tabElement.classList.add('active');
    }

    // Update UI state
    uiState.activeHistoryTab = tabType;

    // Update historical trades display
    updateHistoricalTradesDisplay();
}



function updateHistoricalTradesDisplay(filterType = 'today') {
    const container = domManager.get('historicalTradesContainer');
    const noTradesMessage = domManager.get('noHistoricalTradesMessage');

    if (!container) return;

    // Filter trades based on type
    let filteredTrades = tradeState.historicalTrades || [];

    switch (filterType) {
        case 'today':
            const today = new Date().toDateString();
            filteredTrades = filteredTrades.filter(trade => {
                const tradeDate = new Date(getJsTimestamp(trade.timestamp || trade.close_timestamp)).toDateString();
                return tradeDate === today;
            });
            break;
        case 'winners':
            filteredTrades = filteredTrades.filter(trade => {
                const pnl = trade.realized_pnl || trade.pnl || 0;
                return parseFloat(pnl) > 0;
            });
            break;
        case 'losers':
            filteredTrades = filteredTrades.filter(trade => {
                const pnl = trade.realized_pnl || trade.pnl || 0;
                return parseFloat(pnl) < 0;
            });
            break;
        case 'all_time':
            // Show all trades (no filter)
            break;
    }

    // Sort trades in ascending order (oldest first)
    filteredTrades.sort((a, b) => {
        const timeA = getJsTimestamp(a.timestamp || a.close_timestamp || 0);
        const timeB = getJsTimestamp(b.timestamp || b.close_timestamp || 0);
        return timeA - timeB;
    });

    // Clear container
    container.innerHTML = '';

    if (filteredTrades.length === 0) {
        if (noTradesMessage) {
            noTradesMessage.style.display = 'block';
        }
    } else {
        if (noTradesMessage) {
            noTradesMessage.style.display = 'none';
        }

        // Group trades by parent/child relationships
        const groupedTrades = groupTradesByHierarchy(filteredTrades);

        // Render each group
        Object.values(groupedTrades).forEach(group => {
            if (group.parent) {
                container.innerHTML += createTradeHierarchyEntryHTML(group.parent, 'PARENT');
            }
            group.children.forEach(child => {
                container.innerHTML += createTradeHierarchyEntryHTML(child, 'CHILD');
            });
        });
    }

    // Update stats
    updateHistoricalStats(filteredTrades);
}

function updateHistoricalStats(trades) {
    const totalTrades = trades.length;
    const winners = trades.filter(trade => {
        const pnl = trade.realized_pnl || trade.pnl || 0;
        return parseFloat(pnl) > 0;
    }).length;
    const losers = trades.filter(trade => {
        const pnl = trade.realized_pnl || trade.pnl || 0;
        return parseFloat(pnl) < 0;
    }).length;

    const winRate = totalTrades > 0 ? (winners / totalTrades * 100).toFixed(1) : '0.0';

    const winningTrades = trades.filter(trade => {
        const pnl = trade.realized_pnl || trade.pnl || 0;
        return parseFloat(pnl) > 0;
    });
    const losingTrades = trades.filter(trade => {
        const pnl = trade.realized_pnl || trade.pnl || 0;
        return parseFloat(pnl) < 0;
    });

    const avgWin = winningTrades.length > 0 ?
        winningTrades.reduce((sum, trade) => sum + parseFloat(trade.realized_pnl || trade.pnl || 0), 0) / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ?
        losingTrades.reduce((sum, trade) => sum + parseFloat(trade.realized_pnl || trade.pnl || 0), 0) / losingTrades.length : 0;

    // Update DOM elements
    const elements = {
        histStatTotal: totalTrades,
        histStatWinners: winners,
        histStatLosers: losers,
        histStatWinRate: `${winRate}%`,
        histStatAvgWin: formatCurrency(avgWin),
        histStatAvgLoss: formatCurrency(avgLoss)
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

function groupTradesByHierarchy(trades) {
    const groups = {};

    trades.forEach(trade => {
        const parentId = trade.parent_trade_id || trade.trade_id;

        if (!groups[parentId]) {
            groups[parentId] = {
                parent: null,
                children: []
            };
        }

        if (trade.parent_trade_id) {
            // This is a child trade
            groups[parentId].children.push(trade);
        } else {
            // This is a parent trade
            groups[parentId].parent = trade;
        }
    });

    return groups;
}

// =============================================================================
// FIX 3: FIELD MAPPING ISSUES
// =============================================================================
// Fixed normalizeTradeData function with proper field mapping

function normalizeTradeData(rawTrade) {
    if (!rawTrade || typeof rawTrade !== 'object') {
        console.warn('Invalid trade data received:', rawTrade);
        return null;
    }

    // Helper function to safely get field values
    function getField(fieldName, defaultValue = null) {
        if (rawTrade[fieldName] !== undefined && rawTrade[fieldName] !== null) {
            return rawTrade[fieldName];
        }
        return defaultValue;
    }

    const normalized = {
        // Core identifiers
        // CRITICAL FIX: Backend sets trade_id to position_uuid, so prioritize trade_id
        trade_id: getField('trade_id') || getField('position_uuid') || getField('tradeId') || getField('id') || generateTradeId(),
        symbol: (getField('symbol') || 'UNKNOWN').toString().toUpperCase(),

        // Quantities and pricing
        quantity: parseFloat(getField('quantity') || 0),
        average_price: parseFloat(getField('average_price') || getField('avg_price') || getField('price') || 0),
        price: parseFloat(getField('price') || getField('average_price') || 0),

        // Market data
        last_price: parseFloat(getField('last_price') || getField('current_price') || getField('price') || 0),
        bid_price: parseFloat(getField('bid_price') || getField('bid') || 0),
        ask_price: parseFloat(getField('ask_price') || getField('ask') || 0),

        // P&L
        realized_pnl: parseFloat(getField('realized_pnl') || getField('realizedPnl') || 0),
        unrealized_pnl: parseFloat(getField('unrealized_pnl') || getField('unrealizedPnl') || 0),
        pnl_per_share: parseFloat(getField('pnl_per_share') || 0),

        // Status and flags
        status: (getField('status') || 'UNKNOWN').toString().toUpperCase(),
        is_open: Boolean(getField('is_open', true)),
        side: (getField('side') || getField('action') || 'UNKNOWN').toString().toUpperCase(),

        // Metadata
        strategy: getField('strategy') || 'UNKNOWN',
        timestamp: normalizeTimestamp(getField('timestamp') || getField('time') || Date.now()),

        // Additional fields
        parent_trade_id: getField('parent_trade_id'),
        is_parent: Boolean(getField('is_parent')),
        is_child: Boolean(getField('is_child')),
        commission_total: parseFloat(getField('commission_total') || getField('commission') || 0),
        total_volume: parseInt(getField('total_volume') || getField('volume') || 0),
    };

    // Calculate missing fields
    if (!normalized.pnl_per_share && normalized.quantity && normalized.unrealized_pnl) {
        normalized.pnl_per_share = normalized.unrealized_pnl / Math.abs(normalized.quantity);
    }

    return normalized;
}

function normalizeTimestamp(timestamp) {
    if (!timestamp) return Date.now();

    // Handle various timestamp formats
    const ts = parseFloat(timestamp);

    // If timestamp is in seconds (before year 2100), convert to milliseconds
    if (ts < 4000000000) {
        return ts * 1000;
    }

    return ts;
}

function generateTradeId() {
    return `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// --- END Utility Functions ---

// --- UI Manager (DOM Updates & UI Logic) ---
// Functions responsible for rendering data to the screen and handling UI state.
function setButtonState(buttonIdOrElement, state, originalText) {
    const button = typeof buttonIdOrElement === 'string' 
        ? domManager.get(buttonIdOrElement) 
        : buttonIdOrElement;
    if (!button) return;

    switch(state) {
        case 'loading':
            button.disabled = true;
            button.style.opacity = '0.6';
            button.textContent = 'Processing...';
            break;
        case 'success':
            button.disabled = false;
            button.style.opacity = '1';
            button.textContent = 'Done';
            setTimeout(() => {
                button.textContent = originalText;
            }, APP_CONFIG.buttonResetDelayMs);
            break;
        case 'error':
            button.disabled = false;
            button.style.opacity = '1';
            button.textContent = 'Failed';
            setTimeout(() => {
                button.textContent = originalText;
            }, APP_CONFIG.buttonErrorDelayMs);
            break;
        default:
            button.disabled = false;
            button.style.opacity = '1';
            button.textContent = originalText;
    }
}

function addSystemMessage(message, type = 'info') {
    const timestamp = formatTime();
    const color = type === 'error' ? 'var(--accent-danger)' :
                 type === 'warning' ? 'var(--accent-warning)' :
                 type === 'success' ? 'var(--accent-primary)' : 'var(--text-secondary)';

    const messagesElement = domManager.get('systemMessages');
    if (messagesElement) {
        messagesElement.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
        messagesElement.scrollTop = messagesElement.scrollHeight;
    }
}

function updateStream(elementId, content) {
    const element = domManager.get(elementId);
    if (!element) return;
    const timestamp = formatTime();
    const safeContent = String(content).replace(/</g, "&lt;").replace(/>/g, "&gt;");
    element.innerHTML += `<div style="margin-bottom: 2px;">[${timestamp}] ${safeContent}</div>`;
    element.scrollTop = element.scrollHeight;

    const lines = element.children;
    if (lines.length > APP_CONFIG.maxStreamLines) {
        element.removeChild(lines[0]);
    }
}

function updateProcessedOcrTable(snapshots, timestamp) {
    console.log('updateProcessedOcrTable called with:', snapshots, 'timestamp:', timestamp);
    
    const tableBody = domManager.get('processedOcrTableBody');
    if (!tableBody) {
        console.error('processedOcrTableBody element not found!');
        return;
    }

    if (!snapshots || Object.keys(snapshots).length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #888;">No trade or summary lines detected</td></tr>';
        return;
    }

    // Clear existing rows
    tableBody.innerHTML = '';

    // Process each symbol - Map to match master trader format exactly
    Object.entries(snapshots).forEach(([symbolKey, data]) => {
        console.log(`Processing symbol ${symbolKey}:`, data);
        
        // Extract values using proper field mapping to match master trader
        const strategy = (data.strategy_hint || data.strategy || 'UNKNOWN').toUpperCase();
        const symbol = symbolKey; // Use the key as symbol (YIBO)
        const quantity = data.quantity || data.shares || data.position || 0;
        const avgPrice = data.avg_price || data.average_price || data.cost_basis || data.price || 0;
        const realizedPnl = data.realized_pnl || data.pnl || data.total_pnl || 0;
        const lastUpdate = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${strategy}</td>
            <td>${symbol}</td>
            <td>${quantity}</td>
            <td>${formatCurrency(avgPrice)}</td>
            <td class="${getPnLClass(realizedPnl)}">${formatCurrency(realizedPnl)}</td>
            <td style="font-size: 10px; color: var(--text-secondary);">${lastUpdate}</td>
        `;
        tableBody.appendChild(row);
    });
    
    console.log('Processed OCR table updated with', Object.keys(snapshots).length, 'rows');
}

function updateRawOcrTable(snapshots) {
    const tableBody = document.getElementById('rawOcrTableBody');
    const summaryBody = document.getElementById('rawOcrSummaryBody');

    if (!snapshots || Object.keys(snapshots).length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="no-data">No OCR data</td></tr>';
        summaryBody.innerHTML = '';
        return;
    }

    // Clear existing rows
    tableBody.innerHTML = '';
    summaryBody.innerHTML = '';

    // Process each symbol
    Object.entries(snapshots).forEach(([symbol, data]) => {
        if (symbol === 'ALL') {
            // Handle summary row separately
            const summaryRow = document.createElement('tr');
            summaryRow.innerHTML = `
                <td style="font-weight: 600;">TOTAL</td>
                <td>${data.price_str || '0.00'}</td>
                <td>-</td>
                <td>-</td>
                <td>-</td>
                <td class="${getPnLClass(data.pnl_str)}">${data.pnl_str || '0.00'}</td>
            `;
            summaryBody.appendChild(summaryRow);
        } else {
            // Regular symbol row
            const row = document.createElement('tr');
            row.innerHTML = `
                <td style="font-weight: 600;">${symbol}</td>
                <td>${data.price_str || '-'}</td>
                <td>${data.quantity_str || '-'}</td>
                <td>${data.action_intent_str || '-'}</td>
                <td>${data.cost_basis_str || '-'}</td>
                <td class="${getPnLClass(data.pnl_str)}">${data.pnl_str || '-'}</td>
            `;
            tableBody.appendChild(row);
        }
    });

    // If no regular symbols, show message
    if (tableBody.children.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="no-data">No position data</td></tr>';
    }
}

function updatePreviewImage(imageData, imageType = 'raw') {
    console.log(`[updatePreviewImage] Called. Type: ${imageType}. Image data present: ${!!imageData}. Length: ${imageData ? imageData.length : 0}. Current mode: ${state.imageMode}`);

    if (!imageData) return;

    // Debounce rapid image updates to prevent UI lag
    clearTimeout(state.imageUpdateTimeout);
    state.imageUpdateTimeout = setTimeout(() => {
        updatePreviewImageImmediate(imageData, imageType);
    }, APP_CONFIG.imageUpdateDebounceMs);
}

function updatePreviewImageImmediate(imageData, imageType = 'raw') {
    try {
        // Check image size for memory management
        const imageSizeBytes = imageData.length;
        debugLog(`updatePreviewImage: ${imageType}, ${(imageSizeBytes / 1024).toFixed(1)}KB, mode=${state.imageMode}`);

        if (imageSizeBytes > APP_CONFIG.maxImageSizeBytes) {
            console.warn(`Large image detected: ${(imageSizeBytes / 1024 / 1024).toFixed(2)}MB. Consider implementing compression.`);
            addSystemMessage(`⚠️ Large image received (${(imageSizeBytes / 1024 / 1024).toFixed(1)}MB)`, 'warning');
        }

        // Use improved ImageMemoryManager instead of manual arrays
        const imageKey = `${imageType}_${Date.now()}`;
        imageManager.addImage(imageKey, imageData, imageType);

        // Update state references for backward compatibility
        if (imageType === 'raw') {
            state.lastRawImage = imageData;
        } else {
            state.lastProcessedImage = imageData;
        }

        // Log memory usage
        const memoryUsage = imageManager.getMemoryUsage();
        debugLog(`Image memory: ${memoryUsage.totalMB.toFixed(1)}MB (${memoryUsage.cacheSize}/${memoryUsage.maxCacheSize} images)`);

        // Always display the image if it matches the current mode
        if (state.imageMode === imageType) {
            displayImage(imageData);
        }

    } catch (error) {
        errorHandler.handleError(error, 'Image Update', 'error');
    }
}

function displayImage(imageData) {
    const previewImage = domManager.get('previewImage');
    console.log(`[displayImage] Called. Image data present: ${!!imageData}. Preview element: ${!!previewImage}`);

    if (previewImage && imageData) {
        const renderStart = performance.now();
        previewImage.src = imageData.startsWith('data:image') ? imageData : `data:image/png;base64,${imageData}`;
        const renderSetTime = performance.now() - renderStart;

        console.log(`[displayImage] Set image src. First 100 chars of imageData: ${imageData.substring(0,100)}`);
        console.log(`[displayImage] Image src assignment took: ${renderSetTime.toFixed(2)}ms`);
        console.log('displayImage called:', !!imageData, imageData ? imageData.length : 0);
        console.log('Image src set, length:', imageData.length);
    } else if (previewImage) {
        console.log('No image data, setting placeholder');
        previewImage.src = initializePreview(true);
    } else {
        console.log('No preview image element found');
    }
}

function updateConfidence(confidence) {
    // Confidence comes as percentage (77), limit to 2 decimal places
    const confidencePercent = typeof confidence === 'number' ? Math.round(confidence * 100) / 100 : 0;
    const confidenceText = document.getElementById('confidenceText');
    const confidenceFill = document.getElementById('confidenceFill');
    const ocrConfidence = document.getElementById('ocrConfidence');

    if (confidenceText) confidenceText.textContent = `${confidencePercent}%`;
    if (confidenceFill) confidenceFill.style.width = `${Math.min(confidencePercent, 100)}%`;
    if (ocrConfidence) ocrConfidence.textContent = `${confidencePercent}%`;
}

function updateROIDisplay() {
    const roiCoordsEl = document.getElementById('roiCoords');
    if (roiCoordsEl) {
        if (state.currentROI && state.currentROI.length === 4) {
            roiCoordsEl.textContent = state.currentROI.join(', ');
        } else {
            roiCoordsEl.textContent = 'N/A';
        }
    }

    const previewImage = document.getElementById('previewImage');
    const roiBox = document.getElementById('roiBox');

    if (previewImage && roiBox && previewImage.naturalWidth > 0 && previewImage.naturalHeight > 0 && state.currentROI && state.currentROI.length === 4) {
        // The image now shows only the ROI area, so the ROI box should cover the entire image
        const rect = previewImage.getBoundingClientRect();

        roiBox.style.left = '0px';
        roiBox.style.top = '0px';
        roiBox.style.width = `${rect.width}px`;
        roiBox.style.height = `${rect.height}px`;

        // Make the ROI box a subtle border to show the selected area
        roiBox.style.border = '2px solid var(--accent-primary)';
        roiBox.style.boxShadow = '0 0 10px rgba(0, 255, 136, 0.3)';
        roiBox.style.backgroundColor = 'transparent';
    } else {
        if (roiBox) {
            roiBox.style.width = '0px';
            roiBox.style.height = '0px';
        }
    }
}

function initializePreview(returnPlaceholderDataUrl = false) {
    const previewImage = document.getElementById('previewImage');
    const canvas = document.createElement('canvas');
    canvas.width = 500;
    canvas.height = 120;
    const ctx = canvas.getContext('2d');

    // Resolve CSS custom properties for canvas use
    const accentPrimary = getComputedStyle(document.documentElement)
        .getPropertyValue('--accent-primary').trim() || '#00ff88';

    ctx.fillStyle = '#1a1a2e';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = accentPrimary;
    ctx.font = '16px JetBrains Mono';
    ctx.textAlign = 'center';
    ctx.fillText('Waiting for OCR feed...', canvas.width/2, canvas.height/2 - 10);
    ctx.font = '12px JetBrains Mono';
    ctx.fillText('ROI controls active when connected.', canvas.width/2, canvas.height/2 + 15);

    const placeholderDataUrl = canvas.toDataURL();
    if (returnPlaceholderDataUrl) {
        return placeholderDataUrl;
    }

    if (previewImage) {
        previewImage.src = placeholderDataUrl;
        previewImage.onload = () => {
            updateROIDisplay();
        };
    }
    updateROIDisplay();
}

function updateOCRButtonState(isActive) {
    const btn = document.getElementById('ocrToggle');
    if (!btn) return;

    state.ocrActive = isActive;
    btn.disabled = false;

    if (isActive) {
        btn.textContent = 'Stop OCR';
        btn.className = 'btn btn-danger';
        btn.title = 'Click to stop OCR processing';
    } else {
        btn.textContent = 'Start OCR';
        btn.className = 'btn btn-primary';
        btn.title = 'Click to start OCR processing';
    }
}

// ===== INDEPENDENT STATUS PILL SYSTEM - NO MORE CONFLICTS! =====

// Replace the existing status pill system with independent pills
class StatusPill {
    constructor(pillId, name) {
        this.pillId = pillId;
        this.name = name;
        this.element = document.getElementById(`${pillId}StatusDot`);
        this.lastStatus = null;
        this.lastUpdate = 0;
        this.updateTimer = null;

        if (!this.element) {
            console.warn(`Status pill element not found: ${pillId}StatusDot`);
        }
    }

    update(status, tooltip = '', immediate = false) {
        // Clear any pending update
        if (this.updateTimer) {
            clearTimeout(this.updateTimer);
            this.updateTimer = null;
        }

        // Skip if same status and recent update (within 1 second)
        const now = Date.now();
        if (this.lastStatus === status && (now - this.lastUpdate) < 1000 && !immediate) {
            return;
        }

        // Apply update immediately or after brief delay
        const delay = immediate ? 0 : 100;

        this.updateTimer = setTimeout(() => {
            this._applyUpdate(status, tooltip);
            this.lastStatus = status;
            this.lastUpdate = now;
            this.updateTimer = null;
        }, delay);
    }

    _applyUpdate(status, tooltip) {
        if (!this.element) return;

        const color = this._getStatusColor(status);
        this.element.style.background = color;

        if (tooltip && this.element.parentElement) {
            this.element.parentElement.title = tooltip;
        }

        console.debug(`[${this.name}] Status updated: ${status} (${tooltip})`);
    }

    _getStatusColor(status) {
        switch (status) {
            case 'healthy': return 'var(--accent-primary)';
            case 'warning': return 'var(--accent-warning)';
            case 'error': return 'var(--accent-danger)';
            default: return 'var(--accent-danger)';
        }
    }

    getStatus() {
        return {
            status: this.lastStatus,
            lastUpdate: this.lastUpdate,
            name: this.name
        };
    }
}

// Create independent status pills
const statusPills = {
    core: new StatusPill('core', 'Core'),
    api: new StatusPill('api', 'API'),
    redis: new StatusPill('redis', 'Redis'),
    babysitter: new StatusPill('babysitter', 'Babysitter'),
    zmq: new StatusPill('zmq', 'ZMQ')
};

// Core status management - independent of other pills
class CoreStatusManager {
    constructor() {
        this.lastHealthUpdate = 0;
        this.timeoutMs = APP_CONFIG.healthUpdateTimeoutMs || 15000;
        this.checkInterval = null;
    }

    start() {
        // Check core status every 10 seconds
        this.checkInterval = setInterval(() => {
            this.checkStatus();
        }, 10000);

        // Initial check
        setTimeout(() => this.checkStatus(), 1000);
    }

    stop() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
    }

    updateHealth() {
        this.lastHealthUpdate = Date.now();
        state.lastHealthUpdate = this.lastHealthUpdate; // Keep state in sync
        statusPills.core.update('healthy', 'Core: Recent health update', false);
    }

    checkStatus() {
        const now = Date.now();
        const timeSinceLastUpdate = now - this.lastHealthUpdate;

        if (timeSinceLastUpdate > this.timeoutMs) {
            const secondsSince = Math.round(timeSinceLastUpdate / 1000);
            statusPills.core.update('error', `Core: No updates for ${secondsSince}s`, false);

            // Reset latency display
            const latencyEl = domManager.get('latencyDisplay');
            if (latencyEl && latencyEl.textContent !== '--ms') {
                latencyEl.textContent = '--ms';
                latencyEl.style.color = 'var(--text-secondary)';
            }
        } else {
            // Don't update if already healthy - let other sources handle it
            if (statusPills.core.lastStatus !== 'healthy') {
                statusPills.core.update('healthy', 'Core: Active', false);
            }
        }
    }
}

// API/WebSocket status management - independent
class APIStatusManager {
    constructor() {
        this.connected = false;
        this.reconnectAttempts = 0;
    }

    setConnected(connected) {
        this.connected = connected;
        state.connected = connected; // Keep state in sync
        if (connected) {
            this.reconnectAttempts = 0;
            statusPills.api.update('healthy', 'API: WebSocket connected', true);
        } else {
            statusPills.api.update('error', 'API: WebSocket disconnected', true);
        }
    }

    onReconnectAttempt() {
        this.reconnectAttempts++;
        statusPills.api.update('warning', `API: Reconnecting (attempt ${this.reconnectAttempts})`, true);
    }
}

// ZMQ status management - independent with special logic
class ZMQStatusManager {
    update(zmqLinks) {
        const tradingData = zmqLinks.trading || {};
        const systemData = zmqLinks.system || {};
        const bulkData = zmqLinks.bulk || {};

        // Get the worst status across all sockets
        const worstStatus = this._getWorstStatus([tradingData, systemData, bulkData]);
        const color = this._getStatusColor(worstStatus);
        const tooltip = this._getTooltip(tradingData, systemData, bulkData);

        // Update ZMQ pill directly (bypass the status pill class for custom logic)
        const zmqDot = document.getElementById('zmqStatusDot');
        if (zmqDot) {
            zmqDot.style.background = color;
            if (zmqDot.parentElement) {
                zmqDot.parentElement.title = tooltip;
            }
        }
    }

    _getWorstStatus(socketDataArray) {
        let worstStatus = { connected: true, emergency_buffer_fill_percent: 0 };

        for (const socketData of socketDataArray) {
            if (!socketData.connected) {
                return { connected: false, emergency_buffer_fill_percent: 100 };
            }

            const bufferFill = socketData.emergency_buffer_fill_percent || 0;
            if (bufferFill > worstStatus.emergency_buffer_fill_percent) {
                worstStatus = socketData;
            }
        }

        return worstStatus;
    }

    _getStatusColor(zmqData) {
        if (!zmqData.connected) {
            return 'var(--accent-danger)';  // Red: Disconnected
        }

        const bufferFillPercent = zmqData.emergency_buffer_fill_percent || 0;

        if (bufferFillPercent === 0) {
            return 'var(--accent-primary)';  // Green: Healthy (0% buffer)
        } else if (bufferFillPercent <= 10) {
            return '#ffd700';  // Yellow: Caution (1-10% buffer)
        } else if (bufferFillPercent <= 50) {
            return 'var(--accent-warning)';  // Orange: Warning (10-50% buffer)
        } else {
            return 'var(--accent-danger)';  // Red: Critical (>50% buffer)
        }
    }

    _getTooltip(tradingData, systemData, bulkData) {
        const sockets = [
            { name: 'Trading', data: tradingData },
            { name: 'System', data: systemData },
            { name: 'Bulk', data: bulkData }
        ];

        let tooltip = 'ZMQ Status:\n';

        for (const socket of sockets) {
            const status = socket.data.connected ?
                `${(socket.data.emergency_buffer_fill_percent || 0).toFixed(1)}% buffer` :
                'Disconnected';
            tooltip += `${socket.name}: ${status}\n`;
        }

        return tooltip.trim();
    }
}

// Create independent managers
const coreStatusManager = new CoreStatusManager();
const apiStatusManager = new APIStatusManager();
const zmqStatusManager = new ZMQStatusManager();

// =============================================================================
// FIX 4: STATUS PILL CONFLICTS
// =============================================================================
// REMOVE all the conflicting updateStatusPill functions and replace with:
function updateStatusPillSafe(pillType, status, tooltip = '') {
    const pill = statusPills[pillType];
    if (pill) {
        pill.update(status, tooltip, false);
    } else {
        console.warn(`Unknown status pill type: ${pillType}`);
    }
}

// REMOVED: _applyStatusPill function - not needed with independent pills

// Fix the health update function:
function updateSystemHealthStatus(healthData) {
    if (!healthData || !healthData.components) return;

    const components = healthData.components;

    // Core status
    if (components.core) {
        if (components.core.status === 'healthy') {
            if (coreStatusManager) {
                coreStatusManager.updateHealth();
            } else {
                updateStatusPillSafe('core', 'healthy', 'Core: Healthy');
            }
        } else {
            updateStatusPillSafe('core', components.core.status, `Core: ${components.core.status}`);
        }
    }

    // Other components
    ['redis', 'babysitter'].forEach(component => {
        if (components[component]) {
            updateStatusPillSafe(component, components[component].status, `${component}: ${components[component].status}`);
        }
    });

    // ZMQ status
    if (healthData.zmq_links && zmqStatusManager) {
        zmqStatusManager.update(healthData.zmq_links);
    }
}

// Initialize the independent system
function initializeIndependentStatusPills() {
    // Remove old debounce system if it exists
    if (window.statusUpdateDebounce) {
        Object.keys(window.statusUpdateDebounce).forEach(key => {
            if (window.statusUpdateDebounce[key].timer) {
                clearTimeout(window.statusUpdateDebounce[key].timer);
            }
        });
        delete window.statusUpdateDebounce;
    }

    // Start independent managers
    coreStatusManager.start();

    // Set initial states
    statusPills.core.update('error', 'Core: Initializing...', true);
    statusPills.api.update('error', 'API: Connecting...', true);
    statusPills.redis.update('error', 'Redis: Checking...', true);
    statusPills.babysitter.update('error', 'Babysitter: Checking...', true);
    statusPills.zmq.update('error', 'ZMQ: Checking...', true);

    console.log('✅ Independent status pills initialized');
    addSystemMessage('✅ Status pills now independent - no more conflicts', 'success');
}

// Debug function for independent pills
function debugIndependentPills() {
    console.log('=== INDEPENDENT STATUS PILLS DEBUG ===');
    Object.keys(statusPills).forEach(key => {
        const pill = statusPills[key];
        console.log(`${key}:`, pill.getStatus());
    });
    console.log('Core manager:', {
        lastHealthUpdate: new Date(coreStatusManager.lastHealthUpdate).toISOString(),
        timeoutMs: coreStatusManager.timeoutMs
    });
    console.log('API manager:', {
        connected: apiStatusManager.connected,
        reconnectAttempts: apiStatusManager.reconnectAttempts
    });
    console.log('=====================================');
    return 'Independent pills debug logged';
}

// Export for global access
window.initializeIndependentStatusPills = initializeIndependentStatusPills;
window.debugIndependentPills = debugIndependentPills;
window.statusPills = statusPills;

function updateZMQStatusPill(zmqLinks) {
    const zmqDot = document.getElementById('zmqStatusDot');
    if (!zmqDot) return;

    const tradingData = zmqLinks.trading || {};
    const systemData = zmqLinks.system || {};
    const bulkData = zmqLinks.bulk || {};

    // Get the worst status across all sockets
    const worstStatus = getWorstZMQStatus([tradingData, systemData, bulkData]);
    const color = getZMQStatusColor(worstStatus);

    zmqDot.style.background = color;
    zmqDot.parentElement.title = getZMQOverallTooltip(tradingData, systemData, bulkData);
}

function getWorstZMQStatus(socketDataArray) {
    let worstStatus = { connected: true, emergency_buffer_fill_percent: 0 };

    for (const socketData of socketDataArray) {
        if (!socketData.connected) {
            return { connected: false, emergency_buffer_fill_percent: 100 };
        }

        const bufferFill = socketData.emergency_buffer_fill_percent || 0;
        if (bufferFill > worstStatus.emergency_buffer_fill_percent) {
            worstStatus = socketData;
        }
    }

    return worstStatus;
}

function getZMQStatusColor(zmqData) {
    if (!zmqData.connected) {
        return 'var(--accent-danger)';  // Red: Disconnected
    }

    const bufferFillPercent = zmqData.emergency_buffer_fill_percent || 0;

    if (bufferFillPercent === 0) {
        return 'var(--accent-primary)';  // Green: Healthy (0% buffer)
    } else if (bufferFillPercent <= 10) {
        return '#ffd700';  // Yellow: Caution (1-10% buffer)
    } else if (bufferFillPercent <= 50) {
        return 'var(--accent-warning)';  // Orange: Warning (10-50% buffer)
    } else {
        return 'var(--accent-danger)';  // Red: Critical (>50% buffer)
    }
}

function getZMQOverallTooltip(tradingData, systemData, bulkData) {
    const sockets = [
        { name: 'Trading', data: tradingData },
        { name: 'System', data: systemData },
        { name: 'Bulk', data: bulkData }
    ];

    let tooltip = 'ZMQ Status:\n';

    for (const socket of sockets) {
        const status = socket.data.connected ?
            `${(socket.data.emergency_buffer_fill_percent || 0).toFixed(1)}% buffer` :
            'Disconnected';
        tooltip += `${socket.name}: ${status}\n`;
    }

    return tooltip.trim();
}

function updateDevelopmentStatusDisplays() {
    const developmentMode = document.getElementById('developmentMode').checked;
    const statusText = document.getElementById('developmentStatusText');
    if (statusText) {
        if (developmentMode) {
            statusText.textContent = 'Active: 90% less storage, all tools still work';
            statusText.style.color = 'var(--accent-primary)';
        } else {
            statusText.textContent = 'Disabled: Full logging/storage (Large files!)';
            statusText.style.color = 'var(--accent-warning)';
        }
    }
}

function updateRecordingStatusDisplays() {
    const imageRecording = document.getElementById('enableImageRecording').checked;
    const rawOcrRecording = document.getElementById('enableRawOcrRecording').checked;
    const intelliSenseLogging = document.getElementById('enableIntelliSenseLogging').checked;
    const observabilityLogging = document.getElementById('enableObservabilityLogging').checked;

    const statusText = document.getElementById('recordingStatusText');
    if (statusText) {
        const activeSettings = [];
        if (imageRecording) activeSettings.push('Images');
        if (rawOcrRecording) activeSettings.push('Raw OCR');
        if (intelliSenseLogging) activeSettings.push('IntelliSense');
        if (observabilityLogging) activeSettings.push('Observability');

        if (activeSettings.length === 0) {
            statusText.textContent = 'Development Mode: No Recording';
            statusText.style.color = 'var(--accent-primary)';
        } else {
            statusText.textContent = `Recording: ${activeSettings.join(', ')}`;
            statusText.style.color = observabilityLogging ? 'var(--accent-danger)' :
                                    (imageRecording ? 'var(--accent-warning)' : 'var(--text-muted)');
        }
    }
}

function updateLoggingStatusDisplays() {
    const logLevel = document.getElementById('logLevel').value;
    const ocrDebugLogging = document.getElementById('enableOcrDebugLogging').checked;

    const statusText = document.getElementById('loggingStatusText');
    if (statusText) {
        statusText.textContent = `Current: ${logLevel} level, OCR Debug ${ocrDebugLogging ? 'ON' : 'OFF'}`;

        if (logLevel === 'DEBUG' || ocrDebugLogging) {
            statusText.style.color = 'var(--accent-warning)';
        } else if (logLevel === 'ERROR') {
            statusText.style.color = 'var(--accent-primary)';
        } else {
            statusText.style.color = 'var(--text-muted)';
        }
    }
}

function updateTime() {
    const el = document.getElementById('lastUpdate');
    if (el) el.textContent = formatTime();
}

// Enhanced latency tracking and display with debouncing
const latencyDisplayDebouncer = {
    timeout: null,
    lastUpdate: 0,
    minInterval: 500, // Only update every 500ms max
    samples: [],
    maxSamples: 10
};

function updateLatencyDisplay(latencyMs) {
    const now = Date.now();
    
    // Add to samples for averaging
    if (latencyMs && latencyMs > 0 && latencyMs < 10000) {
        latencyDisplayDebouncer.samples.push(latencyMs);
        if (latencyDisplayDebouncer.samples.length > latencyDisplayDebouncer.maxSamples) {
            latencyDisplayDebouncer.samples.shift();
        }
    }
    
    // Check if enough time has passed since last update
    if (now - latencyDisplayDebouncer.lastUpdate < latencyDisplayDebouncer.minInterval) {
        // Schedule an update if not already scheduled
        if (!latencyDisplayDebouncer.timeout) {
            latencyDisplayDebouncer.timeout = setTimeout(() => {
                latencyDisplayDebouncer.timeout = null;
                updateLatencyDisplayImmediate();
            }, latencyDisplayDebouncer.minInterval);
        }
        return;
    }
    
    updateLatencyDisplayImmediate();
}

function updateLatencyDisplayImmediate() {
    const latencyEl = domManager.get('latencyDisplay');
    if (!latencyEl) return;
    
    const now = Date.now();
    latencyDisplayDebouncer.lastUpdate = now;
    
    // Calculate average from samples
    if (latencyDisplayDebouncer.samples.length === 0) {
        latencyEl.textContent = '--ms';
        latencyEl.style.color = 'var(--text-secondary)';
        return;
    }
    
    const avgLatency = latencyDisplayDebouncer.samples.reduce((a, b) => a + b, 0) / latencyDisplayDebouncer.samples.length;
    
    // Format latency display
    const rounded = Math.round(avgLatency);
    latencyEl.textContent = `${rounded}ms`;

    // Color code based on latency
    if (avgLatency < 100) {
        latencyEl.style.color = 'var(--accent-success)'; // Green
    } else if (avgLatency < 500) {
        latencyEl.style.color = 'var(--accent-warning)'; // Orange
    } else {
        latencyEl.style.color = 'var(--accent-danger)'; // Red
    }
}

// Enhanced trade status classification
function classifyTradeStatus(trade) {
    const status = (trade.status || '').toUpperCase();
    const isOpen = trade.is_open;
    const quantity = trade.quantity || 0;

    // Explicit closed/rejected statuses
    const closedStatuses = ['CLOSED', 'FILLED', 'CANCELLED', 'REJECTED', 'FAILED', 'EXPIRED'];
    const openStatuses = ['OPEN', 'ACTIVE', 'PARTIALLY_FILLED', 'PENDING', 'SUBMITTED'];

    if (closedStatuses.includes(status)) {
        return {
            isOpen: false,
            category: 'closed',
            displayStatus: status,
            shouldShowInOpen: false,
            shouldShowInHistory: true
        };
    }

    if (openStatuses.includes(status)) {
        return {
            isOpen: true,
            category: 'open',
            displayStatus: status,
            shouldShowInOpen: true,
            shouldShowInHistory: false
        };
    }

    // Fallback to is_open flag if status is unclear
    if (isOpen === false) {
        return {
            isOpen: false,
            category: 'closed',
            displayStatus: status || 'CLOSED',
            shouldShowInOpen: false,
            shouldShowInHistory: true
        };
    }

    // Default to open if uncertain
    return {
        isOpen: true,
        category: 'open',
        displayStatus: status || 'OPEN',
        shouldShowInOpen: true,
        shouldShowInHistory: false
    };
}

// Updated trade filtering function
function filterTradesForDisplay(trades, displayType = 'open') {
    return (trades || []).filter(trade => {
        const classification = classifyTradeStatus(trade);

        if (displayType === 'open') {
            return classification.shouldShowInOpen;
        } else if (displayType === 'history') {
            return classification.shouldShowInHistory;
        }

        return true; // Show all if type not specified
    });
}

// Handle rejected trades specifically
function handleRejectedTrade(trade) {
    const classification = classifyTradeStatus(trade);

    if (classification.category === 'closed' &&
        ['REJECTED', 'FAILED', 'CANCELLED'].includes(classification.displayStatus)) {

        // Add to historical trades immediately
        addHistoricalTrade({
            ...trade,
            status: classification.displayStatus,
            is_open: false,
            pnl: 0, // Rejected trades have no P&L
            realized_pnl: 0,
            rejection_reason: trade.rejection_reason || trade.message || 'Order rejected by broker'
        });

        // Show system message
        addSystemMessage(
            `Trade rejected: ${trade.symbol} ${trade.side} ${trade.quantity} - ${trade.rejection_reason || 'Broker rejection'}`,
            'warning'
        );

        return true; // Handled
    }

    return false; // Not a rejection
}

// DEBUG FUNCTIONS FOR TROUBLESHOOTING
function debugStatusPills() {
    console.log('=== STATUS PILLS DEBUG ===');
    console.log('Current state.lastHealthUpdate:', state.lastHealthUpdate);
    console.log('Current time:', Date.now());
    console.log('Time since last update:', Date.now() - (state.lastHealthUpdate || 0), 'ms');
    console.log('Health timeout setting:', APP_CONFIG.healthUpdateTimeoutMs || 15000, 'ms');

    console.log('\nStatus pill debounce info:');
    Object.keys(statusUpdateDebounce).forEach(pillType => {
        const info = statusUpdateDebounce[pillType];
        console.log(`${pillType}:`, {
            lastStatus: info.lastStatus,
            source: info.source,
            lastUpdateTime: info.lastUpdateTime,
            timeSinceUpdate: Date.now() - info.lastUpdateTime
        });
    });

    console.log('\nDOM elements:');
    console.log('Core pill:', document.getElementById('coreStatusPill'));
    console.log('Latency display:', domManager.get('latencyDisplay'));

    return 'Debug info logged to console';
}

function forceStatusUpdate() {
    console.log('=== FORCING STATUS UPDATE ===');

    // Force core status check
    checkCoreStatus();

    // Force manual status updates
    updateStatusPill('core', 'healthy', 'Manual test update', 'manual', 100);

    // Test latency display
    updateLatencyDisplay(123);

    console.log('Status updates forced');
    return 'Status updates completed';
}

function debugLatency() {
    console.log('=== LATENCY DEBUG ===');

    const latencyEl = domManager.get('latencyDisplay');
    console.log('Latency element:', latencyEl);
    console.log('Current text:', latencyEl?.textContent);
    console.log('Current style:', latencyEl?.style.cssText);

    // Test latency update
    console.log('Testing latency update with 456ms...');
    updateLatencyDisplay(456);

    console.log('After update:');
    console.log('New text:', latencyEl?.textContent);
    console.log('New style:', latencyEl?.style.cssText);

    // Test different latency values
    console.log('Testing different latency values...');
    setTimeout(() => updateLatencyDisplay(50), 1000);   // Green
    setTimeout(() => updateLatencyDisplay(250), 2000);  // Orange
    setTimeout(() => updateLatencyDisplay(750), 3000);  // Red

    return 'Latency debug completed';
}

// Additional debug function to test WebSocket latency extraction
function testLatencyExtraction() {
    console.log('=== TESTING LATENCY EXTRACTION ===');

    // Test different message formats
    const testMessages = [
        {
            type: 'health_update',
            metrics: { gui_latency_ms: 123 }
        },
        {
            type: 'health_update',
            metrics: { response_time_ms: 234 }
        },
        {
            type: 'ocr_update',
            metrics: { processing_time_ms: 345 }
        },
        {
            latency_ms: 456
        }
    ];

    testMessages.forEach((msg, i) => {
        console.log(`Testing message ${i + 1}:`, msg);

        // Simulate the latency extraction logic
        if (msg.metrics) {
            const latency = msg.metrics.gui_latency_ms ||
                           msg.metrics.response_time_ms ||
                           msg.metrics.latency_ms ||
                           msg.metrics.processing_time_ms ||
                           msg.metrics.message_latency;

            if (latency) {
                console.log(`  → Extracted latency: ${latency}ms`);
                updateLatencyDisplay(latency);
            } else {
                console.log('  → No latency found in metrics');
            }
        } else if (msg.latency_ms) {
            console.log(`  → Extracted direct latency: ${msg.latency_ms}ms`);
            updateLatencyDisplay(msg.latency_ms);
        } else {
            console.log('  → No latency data found');
        }

        // Wait between tests
        if (i < testMessages.length - 1) {
            setTimeout(() => {}, 500);
        }
    });

    return 'Latency extraction test completed';
}

// Replace your existing checkCoreStatus function with this fixed version
function checkCoreStatus() {
    // This function is now handled by the CoreStatusManager automatically
    // The independent system doesn't need manual checking
    if (APP_CONFIG.debugMode) {
        console.log('checkCoreStatus called - now handled by CoreStatusManager');
    }

    // Optional: You can add manual override here if needed
    // But generally, the CoreStatusManager handles this automatically
}

// If you want to manually trigger a core status check, use:
function manualCoreStatusCheck() {
    if (coreStatusManager) {
        coreStatusManager.checkStatus();
        console.log('Manual core status check triggered');
    }
}

// Add to window for console access
window.manualCoreStatusCheck = manualCoreStatusCheck;

// Create enhanced position card matching the mockup
function createEnhancedPositionCard(trade, type = 'OPEN') {
    // Calculate all derived fields
    trade = calculateDerivedTradeFields(trade);
    
    const symbol = trade.symbol || 'UNKNOWN';
    const isHistorical = type === 'HISTORICAL' || trade.status === 'CLOSED' || trade.status === 'REJECTED';
    const cardId = `${type.toLowerCase()}-${symbol.toLowerCase()}-${trade.trade_id || Date.now()}`;
    const hierarchyId = `${cardId}-hierarchy`;
    
    // Determine styling based on status
    let cardClass = 'position-summary-card';
    if (trade.is_rejection || trade.status === 'REJECTED') {
        cardClass += ' rejection-card';
    } else if (trade.pnl > 0 || trade.unrealized_pnl > 0) {
        cardClass += ' profit-card';
    } else if (trade.pnl < 0 || trade.unrealized_pnl < 0) {
        cardClass += ' loss-card';
    }
    
    return `
        <div class="${cardClass}" data-trade-id="${trade.trade_id}">
            <div class="position-summary-header">
                <!-- TOP ROW - Primary Trading Info (8 elements) -->
                <div class="summary-section symbol-section primary-section">
                    <div class="summary-section-label">SYMBOL</div>
                    <div class="summary-section-value">${symbol}</div>
                    <div class="summary-section-subtitle">${trade.strategy_display}</div>
                </div>
                <div class="summary-section primary-section">
                    <div class="summary-section-label">QUANTITY</div>
                    <div class="summary-section-value">${formatNumber(Math.abs(trade.quantity))}</div>
                    <div class="summary-section-subtitle">${trade.total_fills_count || 1} fills</div>
                </div>
                <div class="summary-section primary-section">
                    <div class="summary-section-label">AVG COST</div>
                    <div class="summary-section-value">${formatCurrency(trade.average_price)}</div>
                    <div class="summary-section-subtitle">per share</div>
                </div>
                <div class="summary-section primary-section">
                    <div class="summary-section-label">MARKET</div>
                    <div class="summary-section-value">${formatCurrency(trade.bid_price || 0)}×${formatCurrency(trade.ask_price || 0)}</div>
                    <div class="summary-section-subtitle">bid × ask</div>
                </div>
                <div class="summary-section primary-section">
                    <div class="summary-section-label">LAST PRICE</div>
                    <div class="summary-section-value">${formatCurrency(trade.last_price || trade.current_price || 0)}</div>
                    <div class="summary-section-subtitle">current</div>
                </div>
                <div class="summary-section pnl-section primary-section">
                    <div class="summary-section-label">REALIZED P&L</div>
                    <div class="summary-section-value ${getPnLClass(trade.realized_pnl || trade.pnl || 0)}">${formatCurrency(trade.realized_pnl || trade.pnl || 0)}</div>
                    <div class="summary-section-subtitle">session</div>
                </div>
                <div class="summary-section pnl-section primary-section">
                    <div class="summary-section-label">UNREALIZED P&L</div>
                    <div class="summary-section-value ${getPnLClass(trade.unrealized_pnl || 0)}">${formatCurrency(trade.unrealized_pnl || 0)}</div>
                    <div class="summary-section-subtitle">current</div>
                </div>
                <div class="summary-section primary-section">
                    <div class="summary-section-label">P&L PER SHARE</div>
                    <div class="summary-section-value ${getPnLClass(trade.pnl_per_share || 0)}">${trade.pnl_per_share ? (trade.pnl_per_share > 0 ? '+' : '') + formatCurrency(trade.pnl_per_share) : '$0.00'}</div>
                    <div class="summary-section-subtitle">${trade.pnl_percent || 0}%</div>
                </div>

                <!-- BOTTOM ROW - Secondary Details (8 elements) -->
                <div class="summary-section secondary-section">
                    <div class="summary-section-label">TOTAL TRADED</div>
                    <div class="summary-section-value">${formatNumber(trade.total_traded)}</div>
                    <div class="summary-section-subtitle">shares</div>
                </div>
                <div class="summary-section secondary-section">
                    <div class="summary-section-label">COMMISSIONS</div>
                    <div class="summary-section-value">${formatCurrency(trade.commission || 0)}</div>
                    <div class="summary-section-subtitle">${trade.commission_per_share ? '$' + trade.commission_per_share + '/sh' : 'estimated'}</div>
                </div>
                <div class="summary-section secondary-section">
                    <div class="summary-section-label">SPREAD</div>
                    <div class="summary-section-value">${formatCurrency(trade.spread || 0)}</div>
                    <div class="summary-section-subtitle">${trade.spread_percent || 0}%</div>
                </div>
                <div class="summary-section secondary-section">
                    <div class="summary-section-label">VOLUME</div>
                    <div class="summary-section-value">${formatNumber(trade.volume || 0)}</div>
                    <div class="summary-section-subtitle">market</div>
                </div>
                <div class="summary-section secondary-section">
                    <div class="summary-section-label">SIGNAL LAT</div>
                    <div class="summary-section-value">${trade.signal_latency_formatted || 'N/A'}</div>
                    <div class="summary-section-subtitle">to fill</div>
                </div>
                <div class="summary-section secondary-section">
                    <div class="summary-section-label">ORDER LAT</div>
                    <div class="summary-section-value">${trade.order_latency_formatted || 'N/A'}</div>
                    <div class="summary-section-subtitle">to fill</div>
                </div>
                <div class="summary-section secondary-section">
                    <div class="summary-section-label">MARKET VALUE</div>
                    <div class="summary-section-value">${formatCurrency(trade.market_value || 0)}</div>
                    <div class="summary-section-subtitle">current</div>
                </div>
                <div class="summary-section secondary-section">
                    <div class="summary-section-label">${isHistorical ? 'CLOSED' : 'OPENED'}</div>
                    <div class="summary-section-value">${trade.opened_time || 'N/A'}</div>
                    <div class="summary-section-subtitle">${trade.trade_duration_formatted || 'active'}</div>
                </div>
            </div>
            
            ${trade.is_rejection ? `
            <div class="rejection-details-banner">
                <span class="rejection-icon">⚠️</span>
                <span class="rejection-text">${trade.rejection_reason || 'Order Rejected'}</span>
                <span class="rejection-source">${trade.source_component || 'System'}</span>
            </div>` : ''}
            
            <!-- Expandable hierarchy section for child orders/trades -->
            <div class="trade-hierarchy-section">
                <div class="hierarchy-header" onclick="toggleHierarchy('${hierarchyId}', event)">
                    <span class="hierarchy-title">Order Details & Fills</span>
                    <button class="hierarchy-toggle">▼</button>
                </div>
                <div id="${hierarchyId}" class="hierarchy-content collapsed">
                    <!-- Child orders/fills will be inserted here -->
                </div>
            </div>
        </div>
    `;
}

function createTradeHierarchyEntryHTML(item, type) { // 🚨 CHUNK 16.2: Renamed 'trade' to 'item' for clarity
    // Defensive coding: Ensure 'item' is an object
    if (typeof item !== 'object' || item === null) {
        console.error("createTradeHierarchyEntryHTML: Invalid item data", item);
        return '<div class="trade-entry error-entry">Error: Invalid item data</div>';
    }
    console.log(`Creating hierarchy entry: Type=${type}, ID=${item.trade_id || item.order_id}, Symbol=${item.symbol}`, item);

    // 🚨 CHUNK 16.2: Enhanced type detection and field mapping
    const isOrderType = type.includes('ORDER');
    const isPositionType = type.includes('POSITION');

    // Common fields
    const symbol = item.symbol || 'UNKNOWN';
    let idToDisplay, fullId;

    // 🚨 CHUNK 16.2: Type-specific field mapping and variable declarations
    let entryClass = '';
    let action, status, quantity, price, filledQty, avgFillPrice, commission, strategy, ocrConfidence, masterTraderData;
    let jsRequestTimestamp, jsFilledTimestamp, signalLat, orderLat, fillDurationMs, netAmount;

    if (type === 'PARENT_POSITION' || type === 'CHILD_POSITION') {
        // 🚨 CHUNK 16.2: Position-specific field mapping
        entryClass = type === 'PARENT_POSITION' ? 'parent-trade position-entry' : 'child-trade position-entry';
        idToDisplay = item.trade_id || 'N/A_POS_ID';
        fullId = item.trade_id;
        action = item.action || (item.quantity > 0 ? 'OPEN LONG' : (item.quantity < 0 ? 'OPEN SHORT' : 'FLAT'));
        status = item.status || (item.is_open ? 'OPEN' : 'CLOSED');
        quantity = item.quantity || 0;
        price = item.average_price || 0; // Avg cost for positions
        filledQty = Math.abs(item.quantity || 0); // For positions, quantity is the filled/current holding
        avgFillPrice = item.average_price || 0;
        commission = item.commission_total || (item.total_fills_count || 0) * 1.00; // Estimated
        strategy = item.strategy || 'N/A';
        ocrConfidence = item.ocr_confidence !== undefined ? `${parseFloat(item.ocr_confidence).toFixed(1)}%` : 'N/A';
        masterTraderData = item.master_trader_snapshot || 'N/A';
        jsRequestTimestamp = getJsTimestamp(item.position_opened_timestamp || item.timestamp);
        jsFilledTimestamp = getJsTimestamp(item.last_update_timestamp || item.timestamp);
        signalLat = item.signal_to_fill_latency_ms;
        orderLat = item.order_to_fill_latency_ms;
        fillDurationMs = item.fill_duration_ms;
        netAmount = item.realized_pnl || 0; // Position P&L

    } else if (type === 'CHILD_ORDER') {
        // 🚨 CHUNK 16.2: Order-specific field mapping
        entryClass = 'child-trade order-entry';
        idToDisplay = item.order_id || item.local_order_id || 'N/A_ORD_ID';
        fullId = item.order_id || item.local_order_id;
        action = item.side ? item.side.toUpperCase() : 'ORDER';
        if (item.order_type) {
            action += ` ${item.order_type.toUpperCase()}`;
        }
        status = item.status || 'PENDING';
        quantity = item.quantity || 0; // Original requested quantity
        price = item.price || 0; // Limit price
        filledQty = item.filled_quantity || 0;
        avgFillPrice = item.avg_fill_price || 0;
        commission = item.commission || 0;
        strategy = item.strategy || 'N/A_STRAT';
        ocrConfidence = item.ocr_confidence !== undefined ? `${parseFloat(item.ocr_confidence).toFixed(1)}%` : 'N/A';
        masterTraderData = 'N/A_SNAP'; // Usually not on orders
        jsRequestTimestamp = getJsTimestamp(item.timestamp || item.created_at);
        jsFilledTimestamp = getJsTimestamp(item.filled_at || item.updated_at || jsRequestTimestamp);
        signalLat = item.signal_to_fill_latency_ms;
        orderLat = item.order_to_fill_latency_ms;
        fillDurationMs = item.fill_duration_ms;
        const costOrProceeds = filledQty * avgFillPrice;
        const isSellActionOrder = action.includes('SELL') || action.includes('CLOSE');
        netAmount = (status === 'FILLED' || status.includes('PARTIAL')) ?
                    (isSellActionOrder ? (costOrProceeds - commission) : (-costOrProceeds - commission)) : 0;

    } else {
        // 🚨 CHUNK 16.2: Fallback for legacy types (PARENT, CHILD) and REJECTIONS
        entryClass = type.toLowerCase().includes('parent') ? 'parent-trade generic-entry' : 'child-trade generic-entry';
        
        // Special handling for rejections
        if (item.is_rejection || item.status === 'REJECTED') {
            entryClass = 'child-trade rejection-entry';
        }
        
        // Special handling for fills
        if (item.is_fill || item.status === 'FILLED') {
            entryClass = 'child-trade fill-entry';
        }
        
        idToDisplay = item.trade_id || item.order_id || item.local_order_id || 'N/A_GEN_ID';
        fullId = item.trade_id || item.order_id || item.local_order_id;
        action = item.action || item.side || 'N/A';
        status = item.status || 'UNKNOWN';
        quantity = item.quantity || 0;
        price = item.price || item.average_price || 0;
        filledQty = Math.abs(quantity);
        avgFillPrice = price;
        commission = item.commission || 0;
        strategy = item.strategy || 'N/A';
        ocrConfidence = 'N/A';
        masterTraderData = 'N/A';
        jsRequestTimestamp = getJsTimestamp(item.timestamp);
        jsFilledTimestamp = getJsTimestamp(item.timestamp);
        signalLat = item.signal_to_fill_latency_ms;
        orderLat = item.order_to_fill_latency_ms;
        fillDurationMs = item.fill_duration_ms;
        netAmount = 0;
    }

    // 🚨 CHUNK 16.2: Calculate fill duration if not provided
    if (fillDurationMs === undefined && (status === 'FILLED' || status === 'CLOSED')) {
        fillDurationMs = jsFilledTimestamp - jsRequestTimestamp;
    }

    const displayIdShort = String(idToDisplay).length > 15 ? `${String(idToDisplay).substring(0,6)}...${String(idToDisplay).substring(String(idToDisplay).length-6)}` : idToDisplay;

    // 🚨 CHUNK 16.2: Enhanced HTML generation with type-specific display logic
    return `
        <div class="trade-entry ${entryClass}">
            <div class="trade-header">
                <span class="trade-type">${type.replace('_', ' ')}</span>
                <span class="trade-id" title="${fullId}">${displayIdShort}</span>
                <span class="trade-symbol">${symbol}</span>
                <span class="trade-action">${action.toUpperCase()}</span>
                <span class="trade-status ${getActionStatusClass(status)}">${status.toUpperCase()}</span>
            </div>
            <div class="trade-details">
                <div class="detail-row">
                    <span class="detail-label">Req/Time:</span>
                    <span class="detail-value">${new Date(jsRequestTimestamp).toLocaleTimeString()} | ${quantity > 0 ? '+' : ''}${formatNumber(quantity)} @ ${formatCurrency(price)}</span>
                </div>
                ${(type === 'CHILD_ORDER' && (status === 'FILLED' || status.includes('PARTIAL'))) ||
                  (type !== 'CHILD_ORDER' && (status === 'CLOSED' || status === 'FILLED')) ? `
                <div class="detail-row">
                    <span class="detail-label">Fill/Time:</span>
                    <span class="detail-value">${new Date(jsFilledTimestamp).toLocaleTimeString()} | ${filledQty > 0 ? '+' : ''}${formatNumber(filledQty)} @ ${formatCurrency(avgFillPrice)} | Net: ${formatCurrency(netAmount)}</span>
                </div>` : ''}
                ${(type === 'CHILD_ORDER' && (signalLat || orderLat || fillDurationMs)) ? `
                <div class="detail-row">
                    <span class="detail-label">Perf:</span>
                    <span class="detail-value">Sig: ${signalLat ? signalLat.toFixed(0)+'ms' : 'N/A'} | Ord: ${orderLat ? orderLat.toFixed(0)+'ms' : 'N/A'} | Fill: ${fillDurationMs ? (fillDurationMs/1000).toFixed(1)+'s' : 'N/A'}</span>
                </div>` : ''}
                ${type.includes('POSITION') ? `
                <div class="detail-row">
                    <span class="detail-label">P&L Real/Unreal:</span>
                    <span class="detail-value">${formatCurrency(item.realized_pnl || 0)} / ${formatCurrency(item.unrealized_pnl || 0)}</span>
                </div>` : ''}
                ${type === 'CHILD_ORDER' && item.remaining_quantity !== undefined ? `
                <div class="detail-row">
                    <span class="detail-label">Remaining:</span>
                    <span class="detail-value">${formatNumber(item.remaining_quantity)} shares | ${((item.remaining_quantity / quantity) * 100).toFixed(1)}% unfilled</span>
                </div>` : ''}
                ${commission ? `
                <div class="detail-row">
                    <span class="detail-label">Comm:</span>
                    <span class="detail-value">${formatCurrency(commission)} | Broker: LSPD</span>
                </div>` : ''}
                ${type !== 'CHILD_ORDER' && masterTraderData !== 'N/A' && masterTraderData !== 'N/A_SNAP' ? `
                <div class="detail-row">
                    <span class="detail-label">Master Snap:</span>
                    <span class="detail-value" style="font-size:8px; white-space:pre-wrap; word-break:break-all; max-height: 30px; overflow-y: auto;">${masterTraderData}</span>
                </div>` : ''}
                <div class="detail-row">
                    <span class="detail-label">OCR/Strat:</span>
                    <span class="detail-value">${ocrConfidence} | ${(strategy || 'N/A').toUpperCase()} | Src: LIVE</span>
                </div>
                ${(item.is_rejection || status === 'REJECTED') ? `
                <div class="detail-row rejection-details">
                    <span class="detail-label">Rejection:</span>
                    <span class="detail-value" style="color: var(--accent-danger);">${item.rejection_reason || 'Unknown reason'}</span>
                </div>
                ${item.broker_order_id ? `
                <div class="detail-row">
                    <span class="detail-label">Broker ID:</span>
                    <span class="detail-value">${item.broker_order_id}</span>
                </div>` : ''}
                ${item.source_component ? `
                <div class="detail-row">
                    <span class="detail-label">Rejected By:</span>
                    <span class="detail-value">${item.source_component}</span>
                </div>` : ''}
                ${item.order_type ? `
                <div class="detail-row">
                    <span class="detail-label">Order Type:</span>
                    <span class="detail-value">${item.order_type} | TIF: ${item.time_in_force || 'DAY'}</span>
                </div>` : ''}
                ${item.correlation_id ? `
                <div class="detail-row">
                    <span class="detail-label">Correlation:</span>
                    <span class="detail-value" style="font-size: 10px;">${item.correlation_id}</span>
                </div>` : ''}` : ''}
                ${(item.is_fill || status === 'FILLED') && item.broker_fill_id ? `
                <div class="detail-row">
                    <span class="detail-label">Fill ID:</span>
                    <span class="detail-value">${item.broker_fill_id}</span>
                </div>` : ''}
                ${item.exchange ? `
                <div class="detail-row">
                    <span class="detail-label">Exchange:</span>
                    <span class="detail-value">${item.exchange}</span>
                </div>` : ''}
                ${item.account ? `
                <div class="detail-row">
                    <span class="detail-label">Account:</span>
                    <span class="detail-value">${item.account}</span>
                </div>` : ''}
            </div>
        </div>
    `;
}

// SOLUTION 1: Fix the root cause - prevent redundant calls to updateActiveTrades

// SOLUTION: Live real-time data with intelligent DOM management

// Separate structural changes from data updates
const updateStrategy = {
    lastStructuralHash: null,
    lastDataUpdate: 0,
    currentPositions: new Map(), // Store current position data
    pendingDataUpdate: null
};

// Legacy tracker for compatibility
const updateTracker = {
    lastDataHash: null,
    lastMarketDataState: null,
    updateInProgress: false,
    pendingUpdate: null
};

// Create hash for structural changes only (positions added/removed/status changed)
function createStructuralHash(trades) {
    if (!trades || !Array.isArray(trades) || trades.length === 0) return 'empty';

    return trades.map(trade => {
        if (!trade) return 'null';

        // HASH COMPONENT DEBUGGING: Log all fields for specific symbols
        if (trade.symbol === 'IBIO' || trade.symbol === 'YIBO' || trade.symbol === 'AAPL') { // Monitor common symbols
            console.log(
                `[HASH_FIELDS_FOR_${trade.symbol}] Symbol: ${trade.symbol}, ` +
                `TradeID: ${trade.trade_id}, ` +
                `Qty: ${Math.round(trade.quantity || 0)}, ` +
                `IsOpen: ${trade.is_open !== undefined ? trade.is_open : true}, ` +
                `Strategy: ${trade.strategy || 'UNKNOWN'}, ` +
                `Action/Side: ${trade.action || trade.side || 'UNKNOWN'} [EXCLUDED FROM HASH], ` +
                `IsParent: ${trade.is_parent || false}, ` +
                `IsChild: ${trade.is_child || false}, ` +
                `ParentTradeID: ${trade.parent_trade_id || 'none'}`
            );
        }

        // Only fields that represent structural changes
        const hashComponents = [
            trade.symbol || 'UNKNOWN',
            trade.trade_id || 'unknown',                      // Should be stable now
            Math.round(trade.quantity || 0),                  // This IS structural
            trade.is_open !== undefined ? trade.is_open : true, // This IS structural
            trade.strategy || 'UNKNOWN',                      // Keep for now, but monitor if it also causes instability
            // trade.action || trade.side || 'UNKNOWN',       // <<<< MODIFIED: Line REMOVED - was causing instability (BUY/UNKNOWN changes)
            trade.is_parent || false,                         // Should be stable
            trade.is_child || false,                          // Should be stable
            trade.parent_trade_id || 'none'                   // Should be stable
        ];

        const hashString = hashComponents.join('-');

        // Log the complete hash string for monitored symbols
        if (trade.symbol === 'IBIO' || trade.symbol === 'YIBO' || trade.symbol === 'AAPL') {
            console.log(`[HASH_STRING_FOR_${trade.symbol}] Complete hash: "${hashString}"`);
        }

        return hashString;
    }).sort().join('|');
}

// Legacy hash function for compatibility
function createTradesHash(trades) {
    return createStructuralHash(trades);
}

// Lightweight price-only update function - no DOM rebuild
function updatePositionPrices(priceData) {
    // Find the position card for this symbol
    const symbol = priceData.symbol;
    const positionCard = document.querySelector(`[data-symbol="${symbol}"]`);
    
    if (!positionCard) return; // Position not currently displayed
    
    // Update price fields directly in the DOM
    if (priceData.bid_price !== undefined && priceData.ask_price !== undefined) {
        const marketEl = positionCard.querySelector('[data-live="market"]');
        if (marketEl) {
            marketEl.textContent = `${formatCurrency(priceData.bid_price)}×${formatCurrency(priceData.ask_price)}`;
        }
    }
    
    if (priceData.last_price !== undefined) {
        const lastPriceEl = positionCard.querySelector('[data-live="last-price"]');
        if (lastPriceEl) {
            lastPriceEl.textContent = formatCurrency(priceData.last_price);
        }
        
        // Update unrealized P&L if we have quantity and avg price
        const quantityEl = positionCard.querySelector('[data-live="quantity"]');
        const avgPriceEl = positionCard.querySelector('[data-live="avg-price"]');
        if (quantityEl && avgPriceEl) {
            const quantity = parseFloat(quantityEl.getAttribute('data-raw-value') || quantityEl.textContent.replace(/,/g, ''));
            const avgPrice = parseFloat(avgPriceEl.getAttribute('data-raw-value') || avgPriceEl.textContent.replace(/[$,]/g, ''));
            if (quantity && avgPrice) {
                const unrealizedPnl = (priceData.last_price - avgPrice) * quantity;
                const unrealizedEl = positionCard.querySelector('[data-live="unrealized-pnl"]');
                if (unrealizedEl) {
                    unrealizedEl.textContent = formatCurrency(unrealizedPnl);
                    unrealizedEl.className = `summary-section-value ${getPnLClass(unrealizedPnl)}`;
                }
            }
        }
    }
    
    // Update timestamp
    const timestampEl = positionCard.querySelector('[data-live="timestamp"]');
    if (timestampEl) {
        timestampEl.textContent = formatTime();
    }
}

// Main updateActiveTrades function - corrected logic for structural vs live updates
function updateActiveTrades(trades) {
    try {
        if (!Array.isArray(trades)) {
            console.warn('updateActiveTrades called with non-array:', trades);
            trades = [];
        }
        performanceMonitor.startTiming('updateActiveTrades');

        const normalizedTrades = trades
            .map(trade => normalizeTestradeDataComplete(trade))
            .filter(trade => trade !== null);

        const openTrades = normalizedTrades.filter(trade => {
            if (trade.is_open !== undefined) {
                return trade.is_open === true;
            }
            const closedStatuses = ['CLOSED', 'FILLED', 'CANCELLED', 'REJECTED', 'FAILED', 'EXPIRED'];
            return !closedStatuses.includes((trade.status || 'UNKNOWN').toUpperCase());
        });

        const newStructuralHash = createStructuralHash(openTrades); // This is now stable!

        // Store the latest openTrades in the global state immediately.
        // updateLiveDataOnly and rebuildPositionsDOM will use this state.
        state.active_trades = openTrades;
        state.activeTrades = openTrades; // Also update activeTrades for BP calculation

        if (updateStrategy.lastStructuralHash !== newStructuralHash) {
            console.log(`[updateActiveTrades] Structural change detected (Hash: ${newStructuralHash.substring(0,50)}...). Rebuilding DOM for ${openTrades.length} trades.`);
            updatePositionsDisplay(openTrades); // This calls rebuildPositionsDOM
            updateStrategy.lastStructuralHash = newStructuralHash;
            updateMarketDataIndicatorWithMessages(openTrades); // Show messages for structural changes
        } else {
            // console.log(`[updateActiveTrades] No structural change (Hash: ${newStructuralHash.substring(0,50)}...). Updating live data for ${openTrades.length} trades.`);
            // The console log above from createStructuralHash already tells us this.
            updateLiveDataOnly(openTrades); // <<<< CALL THIS TO UPDATE EXISTING CARDS
            updateMarketDataIndicatorQuiet(openTrades); // Quiet update for non-structural changes
        }

        updatePositionTotals(openTrades); // Update totals regardless
        updateStrategy.lastDataUpdate = Date.now();
        performanceMonitor.endTiming('updateActiveTrades');
        
        // Update buying power usage whenever positions change
        updateBuyingPowerUsage();

    } catch (error) {
        errorHandler.handleError(error, 'updateActiveTrades', 'critical');
    }
}

// =============================================================================
// FIX 2: MISSING FUNCTIONS
// =============================================================================

function updatePositionsDisplay(openTrades) {
    // This function is now only called when structural changes are detected
    // No need for hash comparison here - it's already done in updateActiveTrades
    console.log(`[updatePositionsDisplay] Rebuilding DOM for structural change with ${openTrades.length} positions`);
    rebuildPositionsDOM(openTrades);
}

// Removed createSimplePositionCard - using comprehensive createLivePositionCard instead

// Full DOM rebuild (only when structure changes)
function rebuildPositionsDOM(openTrades) {
    console.log(`[rebuildPositionsDOM] Rebuilding DOM for ${openTrades.length} positions`);

    const container = document.getElementById('openPositionsContainer');
    const noOpenMessage = document.getElementById('noOpenPositionsMessage');

    if (!container) {
        console.error("! CRITICAL ERROR: 'openPositionsContainer' not found!");
        console.error("Available elements with 'position' in ID:",
            Array.from(document.querySelectorAll('[id*="position"]')).map(el => el.id));
        console.error("Available elements with 'container' in ID:",
            Array.from(document.querySelectorAll('[id*="container"]')).map(el => el.id));

        // Try to find alternative containers
        const altContainer = document.querySelector('.positions-container, .trades-container, #positions, #trades');
        if (altContainer) {
            console.log("Found alternative container:", altContainer.id || altContainer.className);
            // Use the alternative container
            altContainer.innerHTML = `<div style="color: red; padding: 20px;">
                ⚠️ Using fallback container. Expected 'openPositionsContainer' not found.
                <br>Positions: ${openTrades.length}
                <br>Container: ${altContainer.id || altContainer.className}
            </div>`;
        }
        return;
    }

    // Handle empty state
    if (openTrades.length === 0) {
        container.innerHTML = '';
        if (noOpenMessage) noOpenMessage.style.display = 'block';
        return;
    }

    if (noOpenMessage) noOpenMessage.style.display = 'none';

    // Store current expanded states before clearing
    const expandedStates = new Set();
    document.querySelectorAll('.hierarchy-content:not(.collapsed)').forEach(el => {
        expandedStates.add(el.id);
    });

    // Clear and rebuild
    container.innerHTML = '';

    // Group trades
    const groupedTrades = {};
    openTrades.forEach(trade => {
        const isGroupParent = trade.is_parent || (!trade.parent_trade_id && !trade.is_child);
        const groupId = isGroupParent ? trade.trade_id : (trade.parent_trade_id || trade.trade_id);

        if (!groupedTrades[groupId]) {
            groupedTrades[groupId] = { parent: null, children: [] };
        }

        if (isGroupParent) {
            groupedTrades[groupId].parent = trade;
        } else {
            groupedTrades[groupId].children.push(trade);
        }
    });

    // Render each position group
    Object.values(groupedTrades).forEach(group => {
        let parentPosition = group.parent;

        if (!parentPosition && group.children.length > 0) {
            parentPosition = group.children[0];
            group.parent = parentPosition;
        }

        if (!parentPosition) return;

        // Create position card with live data containers
        const card = createLivePositionCard(parentPosition, group.children);
        container.appendChild(card);
    });

    // Restore expanded states
    setTimeout(() => {
        expandedStates.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.classList.remove('collapsed');
                const button = element.closest('.trade-hierarchy-section')?.querySelector('.hierarchy-toggle');
                if (button) {
                    button.textContent = '▲';
                    button.setAttribute('aria-expanded', 'true');
                }
                hierarchyState.expandedSections.add(id);
            }
        });
    }, 0);

    console.log(`[rebuildPositionsDOM] DOM rebuilt successfully`);
}

// Create position card with live-update containers
function createLivePositionCard(parentPosition, childPositions) {
    // Calculate all derived fields
    const position = calculateDerivedTradeFields(parentPosition);
    
    const symbol = position.symbol || 'UNKNOWN';
    const tradeId = position.trade_id || 'unknown';
    const uniqueId = `${symbol}-${tradeId}`.replace(/[^a-z0-9]/gi, '');

    const card = document.createElement('div');
    card.className = 'position-summary-card';
    
    // Add profit/loss styling
    if (position.unrealized_pnl > 0) {
        card.className += ' profit-card';
    } else if (position.unrealized_pnl < 0) {
        card.className += ' loss-card';
    }
    
    card.setAttribute('data-symbol', symbol);
    card.setAttribute('data-trade-id', tradeId);

    // Create hierarchy ID
    const hierarchyId = `pos-${symbol.toLowerCase()}-${tradeId.replace(/[^a-z0-9]/g, '')}-hierarchy`;
    const isExpanded = hierarchyState.expandedSections.has(hierarchyId);
    const toggleText = isExpanded ? '▲' : '▼';
    const collapsedClass = isExpanded ? '' : 'collapsed';

    card.innerHTML = `
        <div class="position-summary-header">
            <!-- TOP ROW - Primary Trading Info (8 elements) -->
            <div class="summary-section symbol-section primary-section">
                <div class="summary-section-label">SYMBOL</div>
                <div class="summary-section-value">${symbol}</div>
                <div class="summary-section-subtitle">${position.strategy_display}</div>
            </div>
            <div class="summary-section primary-section">
                <div class="summary-section-label">QUANTITY</div>
                <div class="summary-section-value" data-live="quantity">${formatNumber(Math.abs(position.quantity || 0))}</div>
                <div class="summary-section-subtitle" data-live="fills-count">${position.total_fills_count || 1} fills</div>
            </div>
            <div class="summary-section primary-section">
                <div class="summary-section-label">AVG COST</div>
                <div class="summary-section-value" data-live="avg-price">${formatCurrency(position.average_price || 0)}</div>
                <div class="summary-section-subtitle">per share</div>
            </div>
            <div class="summary-section primary-section">
                <div class="summary-section-label">MARKET</div>
                <div class="summary-section-value" data-live="market">${formatCurrency(position.bid_price || 0)}×${formatCurrency(position.ask_price || 0)}</div>
                <div class="summary-section-subtitle">bid × ask</div>
            </div>
            <div class="summary-section primary-section">
                <div class="summary-section-label">LAST PRICE</div>
                <div class="summary-section-value" data-live="last-price">${formatCurrency(position.last_price || position.current_price || 0)}</div>
                <div class="summary-section-subtitle">current</div>
            </div>
            <div class="summary-section pnl-section primary-section">
                <div class="summary-section-label">REALIZED P&L</div>
                <div class="summary-section-value ${getPnLClass(position.realized_pnl || 0)}" data-live="realized-pnl">${formatCurrency(position.realized_pnl || 0)}</div>
                <div class="summary-section-subtitle">session</div>
            </div>
            <div class="summary-section pnl-section primary-section">
                <div class="summary-section-label">UNREALIZED P&L</div>
                <div class="summary-section-value ${getPnLClass(position.unrealized_pnl || 0)}" data-live="unrealized-pnl">${formatCurrency(position.unrealized_pnl || 0)}</div>
                <div class="summary-section-subtitle">current</div>
            </div>
            <div class="summary-section primary-section">
                <div class="summary-section-label">P&L PER SHARE</div>
                <div class="summary-section-value ${getPnLClass(position.pnl_per_share || 0)}" data-live="pnl-per-share">${position.pnl_per_share ? (position.pnl_per_share > 0 ? '+' : '') + formatCurrency(position.pnl_per_share) : '$0.00'}</div>
                <div class="summary-section-subtitle" data-live="pnl-percent">${position.pnl_percent || 0}%</div>
            </div>

            <!-- BOTTOM ROW - Secondary Details (8 elements) -->
            <div class="summary-section secondary-section">
                <div class="summary-section-label">TOTAL TRADED</div>
                <div class="summary-section-value" data-live="total-traded">${formatNumber(position.total_traded || Math.abs(position.quantity || 0))}</div>
                <div class="summary-section-subtitle">shares</div>
            </div>
            <div class="summary-section secondary-section">
                <div class="summary-section-label">COMMISSIONS</div>
                <div class="summary-section-value" data-live="commission">${formatCurrency(position.commission || position.commission_total || 0)}</div>
                <div class="summary-section-subtitle" data-live="commission-per-share">${position.commission_per_share ? '$' + position.commission_per_share + '/sh' : 'estimated'}</div>
            </div>
            <div class="summary-section secondary-section">
                <div class="summary-section-label">SPREAD</div>
                <div class="summary-section-value" data-live="spread">${formatCurrency(position.spread || 0)}</div>
                <div class="summary-section-subtitle" data-live="spread-percent">${position.spread_percent || 0}%</div>
            </div>
            <div class="summary-section secondary-section">
                <div class="summary-section-label">VOLUME</div>
                <div class="summary-section-value" data-live="volume">${formatNumber(position.volume || 0)}</div>
                <div class="summary-section-subtitle">market</div>
            </div>
            <div class="summary-section secondary-section">
                <div class="summary-section-label">SIGNAL LAT</div>
                <div class="summary-section-value" data-live="signal-lat">${position.signal_latency_formatted || 'N/A'}</div>
                <div class="summary-section-subtitle">to fill</div>
            </div>
            <div class="summary-section secondary-section">
                <div class="summary-section-label">ORDER LAT</div>
                <div class="summary-section-value" data-live="order-lat">${position.order_latency_formatted || 'N/A'}</div>
                <div class="summary-section-subtitle">to fill</div>
            </div>
            <div class="summary-section secondary-section">
                <div class="summary-section-label">MARKET VALUE</div>
                <div class="summary-section-value" data-live="market-value">${formatCurrency(position.market_value || 0)}</div>
                <div class="summary-section-subtitle">current</div>
            </div>
            <div class="summary-section secondary-section">
                <div class="summary-section-label">OPENED</div>
                <div class="summary-section-value">${position.opened_time || 'N/A'}</div>
                <div class="summary-section-subtitle" data-live="duration">${position.trade_duration_formatted || 'active'}</div>
            </div>
        </div>

        <!-- Hierarchy Section -->
        <div class="trade-hierarchy-section">
            <div class="hierarchy-header" onclick="toggleHierarchy('${hierarchyId}', event)">
                <span class="hierarchy-title">Order Details & Fills</span>
                <button class="hierarchy-toggle" aria-expanded="${isExpanded}">${toggleText}</button>
            </div>
            <div id="${hierarchyId}" class="hierarchy-content ${collapsedClass}" onclick="event.stopPropagation()">
                ${createTradeHierarchyEntryHTML(position, 'PARENT_POSITION')}
                ${childPositions.map(child => createTradeHierarchyEntryHTML(child, 'CHILD_POSITION')).join('')}
                ${getChildOrdersHTML(position.trade_id)}
            </div>
        </div>
    `;

    return card;
}

// Get child orders HTML
function getChildOrdersHTML(parentTradeId) {
    if (!tradeState.openOrders || !Array.isArray(tradeState.openOrders) || !parentTradeId) {
        return '';
    }

    const relatedChildOrders = tradeState.openOrders.filter(
        order => order.parent_trade_id === parentTradeId && order.is_child
    );

    return relatedChildOrders
        .sort((a,b) => (getJsTimestamp(b.timestamp) || 0) - (getJsTimestamp(a.timestamp) || 0))
        .map(childOrder => createTradeHierarchyEntryHTML(childOrder, 'CHILD_ORDER'))
        .join('');
}

// Live data updates (no DOM rebuild)
function updateLiveDataOnly(openTrades) {
    const startTime = performance.now();

    openTrades.forEach(pos => {
        // Calculate all derived fields
        const position = calculateDerivedTradeFields(pos);
        const symbol = position.symbol;
        const tradeId = position.trade_id;

        // Find the card for this position
        const card = document.querySelector(`[data-symbol="${symbol}"][data-trade-id="${tradeId}"]`);
        if (!card) return;
        
        // Update card styling based on P&L
        if (position.unrealized_pnl > 0) {
            card.classList.add('profit-card');
            card.classList.remove('loss-card');
        } else if (position.unrealized_pnl < 0) {
            card.classList.add('loss-card');
            card.classList.remove('profit-card');
        } else {
            card.classList.remove('profit-card', 'loss-card');
        }

        // Update all live data elements
        updateLiveElement(card, 'quantity', formatNumber(Math.abs(position.quantity || 0)));
        updateLiveElement(card, 'fills-count', `${position.total_fills_count || 1} fills`);
        updateLiveElement(card, 'avg-price', formatCurrency(position.average_price || 0));
        updateLiveElement(card, 'market', `${formatCurrency(position.bid_price || 0)}×${formatCurrency(position.ask_price || 0)}`);
        updateLiveElement(card, 'last-price', formatCurrency(position.last_price || position.current_price || 0));

        // Update P&L with color classes
        updateLivePnLElement(card, 'realized-pnl', position.realized_pnl || 0);
        updateLivePnLElement(card, 'unrealized-pnl', position.unrealized_pnl || 0);
        
        // P&L per share with sign
        const pnlPerShareEl = card.querySelector('[data-live="pnl-per-share"]');
        if (pnlPerShareEl) {
            const pnlPerShareValue = position.pnl_per_share || 0;
            const formattedPnlPerShare = pnlPerShareValue ? 
                (pnlPerShareValue > 0 ? '+' : '') + formatCurrency(pnlPerShareValue) : '$0.00';
            if (pnlPerShareEl.textContent !== formattedPnlPerShare) {
                pnlPerShareEl.textContent = formattedPnlPerShare;
                pnlPerShareEl.className = pnlPerShareEl.className.replace(/pnl-\w+/g, '') + ' ' + getPnLClass(pnlPerShareValue);
            }
        }
        updateLiveElement(card, 'pnl-percent', `${position.pnl_percent || 0}%`);

        // Update secondary fields
        updateLiveElement(card, 'total-traded', formatNumber(position.total_traded || Math.abs(position.quantity || 0)));
        updateLiveElement(card, 'commission', formatCurrency(position.commission || position.commission_total || 0));
        updateLiveElement(card, 'commission-per-share', position.commission_per_share ? `$${position.commission_per_share}/sh` : 'estimated');
        updateLiveElement(card, 'spread', formatCurrency(position.spread || 0));
        updateLiveElement(card, 'spread-percent', `${position.spread_percent || 0}%`);
        updateLiveElement(card, 'volume', formatNumber(position.volume || 0));
        updateLiveElement(card, 'signal-lat', position.signal_latency_formatted || 'N/A');
        updateLiveElement(card, 'order-lat', position.order_latency_formatted || 'N/A');
        updateLiveElement(card, 'market-value', formatCurrency(position.market_value || 0));
        
        // Update duration if trade is still open
        // Use last_fill_timestamp as the position open time
        const openTimestamp = position.last_fill_timestamp || position.timestamp;
        if (position.status !== 'CLOSED' && openTimestamp) {
            const duration = formatDuration(Date.now() - getJsTimestamp(openTimestamp));
            updateLiveElement(card, 'duration', duration || 'active');
        }
    });

    const endTime = performance.now();
    console.log(`[updateLiveDataOnly] Updated live data in ${(endTime - startTime).toFixed(2)}ms`);
}

// Helper to update live elements
function updateLiveElement(card, dataType, newValue) {
    const element = card.querySelector(`[data-live="${dataType}"]`);
    if (element && element.textContent !== newValue) {
        element.textContent = newValue;
    }
}

// Helper to update P&L elements with color classes
function updateLivePnLElement(card, dataType, value) {
    const element = card.querySelector(`[data-live="${dataType}"]`);
    if (element) {
        const formattedValue = formatCurrency(value);
        if (element.textContent !== formattedValue) {
            element.textContent = formattedValue;
            // Update color class
            element.className = element.className.replace(/pnl-\w+/g, '') + ' ' + getPnLClass(value);
        }
    }
}

// Update position totals
function updatePositionTotals(openTrades) {
    const totalUnrealizedPnl = openTrades.reduce((sum, trade) => sum + (trade.unrealized_pnl || 0), 0);
    const openPositionsCount = openTrades.length;
    
    // Calculate total shares from open positions
    const totalShares = openTrades.reduce((sum, trade) => sum + Math.abs(trade.quantity || 0), 0);
    
    // Calculate open P&L (unrealized)
    const openPnL = totalUnrealizedPnl;

    // Update GUI elements
    const guiOpenCountEl = document.getElementById('guiOpenPositionsCount');
    if (guiOpenCountEl) guiOpenCountEl.textContent = openPositionsCount;

    const unrealizedEl = document.getElementById('guiUnrealizedPnlTotal');
    if (unrealizedEl) {
        unrealizedEl.innerHTML = `Unrealized: <strong>${formatCurrency(totalUnrealizedPnl)}</strong>`;
        unrealizedEl.className = 'pnl-positive ' + getPnLClass(totalUnrealizedPnl);
    }
    
    // Update Position Summary panel
    const totalSharesEl = document.getElementById('totalSharesTraded');
    if (totalSharesEl) totalSharesEl.textContent = formatNumber(totalShares);
    
    const openPositionsEl = document.getElementById('openPositionsCount');
    if (openPositionsEl) openPositionsEl.textContent = openPositionsCount;
    
    const openPnLEl = document.getElementById('openPnL');
    if (openPnLEl) {
        openPnLEl.textContent = formatCurrency(openPnL);
        openPnLEl.className = 'metric-value ' + getPnLClass(openPnL);
    }
    
    // Update closed positions stats from tradeState.historicalTrades
    updateClosedPositionStats();
}

// Update closed position statistics from historical trades
function updateClosedPositionStats() {
    const todayTrades = tradeState.historicalTrades.filter(trade => {
        // Filter for today's closed trades
        const tradeDate = new Date(trade.timestamp);
        const today = new Date();
        return tradeDate.toDateString() === today.toDateString() && 
               (trade.status === 'CLOSED' || trade.status === 'FILLED');
    });
    
    // Calculate stats
    const closedToday = todayTrades.length;
    const totalTrades = tradeState.historicalTrades.length;
    const closedPnL = todayTrades.reduce((sum, trade) => sum + (trade.realized_pnl || 0), 0);
    
    // Calculate win rate
    const winningTrades = todayTrades.filter(trade => (trade.realized_pnl || 0) > 0);
    const losingTrades = todayTrades.filter(trade => (trade.realized_pnl || 0) < 0);
    const winRate = todayTrades.length > 0 ? (winningTrades.length / todayTrades.length) * 100 : 0;
    
    // Calculate average win/loss
    const avgWin = winningTrades.length > 0 
        ? winningTrades.reduce((sum, trade) => sum + trade.realized_pnl, 0) / winningTrades.length 
        : 0;
    const avgLoss = losingTrades.length > 0 
        ? Math.abs(losingTrades.reduce((sum, trade) => sum + trade.realized_pnl, 0) / losingTrades.length)
        : 0;
    
    // Get open P&L from active trades
    const openPnL = state.activeTrades.reduce((sum, trade) => sum + (trade.unrealized_pnl || 0), 0);
    const dayTotalPnL = openPnL + closedPnL;
    
    // Update UI elements
    const closedTodayEl = document.getElementById('closedPositionsCount');
    if (closedTodayEl) closedTodayEl.textContent = closedToday;
    
    const totalTradesEl = document.getElementById('totalTradesCount');
    if (totalTradesEl) totalTradesEl.textContent = totalTrades;
    
    const closedPnLEl = document.getElementById('closedPnL');
    if (closedPnLEl) {
        closedPnLEl.textContent = formatCurrency(closedPnL);
        closedPnLEl.className = 'metric-value ' + getPnLClass(closedPnL);
    }
    
    const dayTotalPnLEl = document.getElementById('totalDayPnL');
    if (dayTotalPnLEl) {
        dayTotalPnLEl.textContent = formatCurrency(dayTotalPnL);
        dayTotalPnLEl.className = 'metric-value ' + getPnLClass(dayTotalPnL);
    }
    
    const winRateEl = document.getElementById('winRate');
    if (winRateEl) winRateEl.textContent = winRate.toFixed(1) + '%';
    
    const avgWinEl = document.getElementById('avgWin');
    if (avgWinEl) avgWinEl.textContent = formatCurrency(avgWin);
    
    const avgLossEl = document.getElementById('avgLoss');
    if (avgLossEl) avgLossEl.textContent = formatCurrency(avgLoss);
    
    // Update status message
    const statusEl = document.getElementById('positionSummaryStatus');
    if (statusEl) {
        if (state.activeTrades.length > 0 || todayTrades.length > 0) {
            statusEl.textContent = `${state.activeTrades.length} open, ${closedToday} closed today`;
        } else {
            statusEl.textContent = 'Waiting for position summary...';
        }
    }
}

// Market data indicator with messages (only for structural changes)
function updateMarketDataIndicatorWithMessages(trades) {
    const hasMarketData = trades.some(t => t.market_data_available);
    const hasHierarchyData = trades.some(t => t.hierarchy_data_available);

    const newState = `${hasMarketData}-${hasHierarchyData}`;

    if (updateTracker.lastMarketDataState !== newState) {
        updateTracker.lastMarketDataState = newState;

        // Update UI
        const marketDataIndicator = document.getElementById('guiMarketDataIndicator');
        if (marketDataIndicator) {
            const liveDot = marketDataIndicator.querySelector('.live-dot');
            const liveText = marketDataIndicator.querySelector('span');

            let statusText = "Market Data: ";
            let dotColor = 'var(--accent-danger)';

            if (hasMarketData && hasHierarchyData) {
                statusText += "Live & Complete";
                dotColor = 'var(--accent-primary)';
            } else if (hasMarketData) {
                statusText += "Live (Hierarchy Missing)";
                dotColor = 'var(--accent-warning)';
            } else if (hasHierarchyData) {
                statusText += "Stale (Hierarchy OK)";
                dotColor = 'var(--accent-warning)';
            } else {
                statusText += "Unavailable";
            }

            if (liveDot) liveDot.style.backgroundColor = dotColor;
            if (liveText) liveText.textContent = statusText;
        }

        // Show messages only for structural changes
        if (!hasMarketData) {
            addSystemMessage('📉 Position data missing market quotes (bid/ask/last)', 'warning');
        }
        if (!hasHierarchyData) {
            addSystemMessage('📉 Position data missing parent/child trade hierarchy', 'warning');
        }
    }
}

// Quiet market data indicator update (for live data updates)
function updateMarketDataIndicatorQuiet(trades) {
    const hasMarketData = trades.some(t => t.market_data_available);
    const hasHierarchyData = trades.some(t => t.hierarchy_data_available);

    const newState = `${hasMarketData}-${hasHierarchyData}`;

    if (updateTracker.lastMarketDataState !== newState) {
        updateTracker.lastMarketDataState = newState;

        // Update UI only, no messages
        const marketDataIndicator = document.getElementById('guiMarketDataIndicator');
        if (marketDataIndicator) {
            const liveDot = marketDataIndicator.querySelector('.live-dot');
            const liveText = marketDataIndicator.querySelector('span');

            let statusText = "Market Data: ";
            let dotColor = 'var(--accent-danger)';

            if (hasMarketData && hasHierarchyData) {
                statusText += "Live & Complete";
                dotColor = 'var(--accent-primary)';
            } else if (hasMarketData) {
                statusText += "Live (Hierarchy Missing)";
                dotColor = 'var(--accent-warning)';
            } else if (hasHierarchyData) {
                statusText += "Stale (Hierarchy OK)";
                dotColor = 'var(--accent-warning)';
            } else {
                statusText += "Unavailable";
            }

            if (liveDot) liveDot.style.backgroundColor = dotColor;
            if (liveText) liveText.textContent = statusText;
        }
    }
}

// Function is already defined above - no need to reassign
console.log('✅ Clean updateActiveTrades function loaded');

// Debug function
function debugLiveUpdates() {
    console.log('=== LIVE UPDATES DEBUG ===');
    console.log('Structural hash:', updateStrategy.lastStructuralHash?.substring(0, 20) + '...');
    console.log('Last data update:', new Date(updateStrategy.lastDataUpdate));
    console.log('Active positions:', state.active_trades?.length || 0);
    console.log('Live elements found:', document.querySelectorAll('[data-live]').length);
    console.log('==========================');

    return {
        structuralHash: updateStrategy.lastStructuralHash,
        lastUpdate: updateStrategy.lastDataUpdate,
        positions: state.active_trades?.length || 0,
        liveElements: document.querySelectorAll('[data-live]').length
    };
}

window.debugLiveUpdates = debugLiveUpdates;

// COMPLETE 60+ FIELD MAPPING SYSTEM - Based on your actual_position_fields

// Field mapping groups from your testrade_stream_mappings.json
const TESTRADE_FIELD_MAPPINGS = {
    // Core identifiers
    symbol: ['symbol'],
    // REVISED FOCUSED FIX: Prioritize trade_id (already set by backend), then position_uuid_sent as fallback
    trade_id: ['trade_id', 'position_uuid_sent', 'order_id', 'local_order_id'],
    parent_trade_id: ['parent_trade_id', 'lcm_parent_trade_id', 'master_correlation_id'],

    // Quantities
    quantity: ['quantity', 'shares', 'total_shares', 'startup_shares', 'session_buy_quantity', 'session_sell_quantity'],

    // Pricing
    average_price: ['average_price', 'avg_price', 'total_avg_price', 'startup_avg_price', 'potential_avg_price', 'cost_basis'],
    last_price: ['last_price', 'current_price', 'price'],
    bid_price: ['bid_price', 'bid'],
    ask_price: ['ask_price', 'ask'],

    // P&L
    unrealized_pnl: ['unrealized_pnl'],
    realized_pnl: ['realized_pnl', 'overall_realized_pnl'],
    realized_pnl_session: ['realized_pnl_session'],
    pnl_per_share: ['pnl_per_share'],

    // Volume
    total_volume: ['total_volume', 'volume', 'day_volume'],
    volume: ['volume', 'total_volume', 'day_volume'],

    // Status and flags
    status: ['status', 'trading_status', 'lifecycle_state'],
    is_open: ['is_open', 'open'],
    is_parent: ['is_parent'],
    is_child: ['is_child'],

    // Actions
    action: ['action', 'side'],
    side: ['side', 'action'],

    // Strategy and metadata
    strategy: ['strategy', 'strategy_hint'],
    origin: ['origin'],

    // Timestamps
    timestamp: ['timestamp', 'last_update_timestamp'],
    position_opened_timestamp: ['position_opened_timestamp', 'start_time'],
    last_update_timestamp: ['last_update_timestamp', 'timestamp'],
    last_fill_timestamp: ['last_fill_timestamp'],

    // Commission
    commission: ['commission', 'commission_total'],
    commission_total: ['commission_total', 'commission'],

    // Fill metrics
    total_fills_count: ['total_fills_count', 'fills_count'],
    fills_count: ['fills_count', 'total_fills_count'],

    // Performance metrics
    signal_to_fill_latency_ms: ['signal_to_fill_latency_ms'],
    order_to_fill_latency_ms: ['order_to_fill_latency_ms'],
    fill_duration_ms: ['fill_duration_ms'],

    // Market data availability
    market_data_available: ['market_data_available'],
    hierarchy_data_available: ['hierarchy_data_available'],

    // TESTRADE specific fields
    position_uuid: ['position_uuid'],
    master_correlation_id: ['master_correlation_id', 'correlation_id', 'causing_correlation_id'],
    lcm_parent_trade_id: ['lcm_parent_trade_id'],
    startup_shares: ['startup_shares'],
    startup_avg_price: ['startup_avg_price'],
    last_sync_details: ['last_sync_details'],
    last_update_source: ['last_update_source', 'update_source_trigger'],
    trading_status: ['trading_status'],
    luld_high: ['luld_high'],
    luld_low: ['luld_low'],
    last_broker_fingerprint: ['last_broker_fingerprint'],
    retry_count: ['retry_count'],
    cooldown_until: ['cooldown_until'],
    potential_avg_price: ['potential_avg_price'],
    session_system_fills_ledger: ['session_system_fills_ledger'],
    lifecycle_state: ['lifecycle_state'],
    session_buy_quantity: ['session_buy_quantity'],
    session_sell_quantity: ['session_sell_quantity'],
    session_buy_value: ['session_buy_value'],
    session_sell_value: ['session_sell_value'],
    local_orders: ['local_orders'],
    fills: ['fills'],
    comment: ['comment'],
    child_trades: ['child_trades'],
    ocr_confidence: ['ocr_confidence'],
    master_trader_snapshot: ['master_trader_snapshot'],

    // Market value (calculated)
    market_value: ['market_value'], // Will be calculated if not present

    // Additional fields from your mapping
    end_time: ['end_time'],
    start_time: ['start_time']
};

// Dynamic field mapper that handles all 60+ fields
function mapFieldValue(rawTrade, fieldGroup, defaultValue = null) {
    const fieldOptions = TESTRADE_FIELD_MAPPINGS[fieldGroup];
    if (!fieldOptions) {
        console.warn(`Unknown field group: ${fieldGroup}`);
        return defaultValue;
    }

    // Try each field option in order
    for (const fieldName of fieldOptions) {
        if (rawTrade[fieldName] !== undefined && rawTrade[fieldName] !== null) {
            return rawTrade[fieldName];
        }
    }

    return defaultValue;
}

// Generate trade ID if missing
function generateTradeId() {
    return `trade-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// Normalize timestamp to consistent format
function normalizeTimestamp(timestamp) {
    if (!timestamp) return null;

    // Handle different timestamp formats
    if (typeof timestamp === 'string') {
        const parsed = Date.parse(timestamp);
        return isNaN(parsed) ? null : parsed;
    }

    if (typeof timestamp === 'number') {
        // Handle both seconds and milliseconds
        return timestamp < 1e12 ? timestamp * 1000 : timestamp;
    }

    return null;
}

// Comprehensive normalization using all 60+ fields
function normalizeTestradeDataComplete(rawTrade) {
    if (!rawTrade || typeof rawTrade !== 'object') {
        console.warn('Invalid trade data received:', rawTrade);
        return null;
    }

    // REVISED FOCUSED DEBUG: Enhanced trade_id source debugging
    if (rawTrade.symbol) { // Only log for valid trades
        const debugIdSources = {
            raw_trade_id: rawTrade.trade_id,
            raw_position_uuid_sent: rawTrade.position_uuid_sent,
            raw_position_uuid: rawTrade.position_uuid,
            raw_order_id: rawTrade.order_id,
            raw_local_order_id: rawTrade.local_order_id
        };
        console.log(`[TRADE_ID_SOURCES_DEBUG] ${rawTrade.symbol}:`, debugIdSources);
    }

    // Debug: Show all available fields in raw data
    if (APP_CONFIG.debugMode) {
        const availableFields = Object.keys(rawTrade);
        console.log(`[normalizeTestradeDataComplete] Processing ${rawTrade.symbol} with ${availableFields.length} fields:`, availableFields);
    }

    // Get the final trade_id using field mapping
    const final_trade_id = mapFieldValue(rawTrade, 'trade_id', generateTradeId());
    console.log(`[FINAL_TRADE_ID_DEBUG] ${rawTrade.symbol}: final_trade_id="${final_trade_id}" (derived from above sources)`);

    const normalized = {
        // === CORE IDENTIFIERS ===
        symbol: (mapFieldValue(rawTrade, 'symbol', 'UNKNOWN')).toString().toUpperCase(),
        trade_id: final_trade_id, // Use the derived one
        parent_trade_id: mapFieldValue(rawTrade, 'parent_trade_id'),

        // === QUANTITIES ===
        quantity: parseFloat(mapFieldValue(rawTrade, 'quantity', 0)),
        startup_shares: parseFloat(mapFieldValue(rawTrade, 'startup_shares', 0)),
        session_buy_quantity: parseFloat(mapFieldValue(rawTrade, 'session_buy_quantity', 0)),
        session_sell_quantity: parseFloat(mapFieldValue(rawTrade, 'session_sell_quantity', 0)),

        // === PRICING ===
        average_price: parseFloat(mapFieldValue(rawTrade, 'average_price', 0)),
        startup_avg_price: parseFloat(mapFieldValue(rawTrade, 'startup_avg_price', 0)),
        potential_avg_price: parseFloat(mapFieldValue(rawTrade, 'potential_avg_price', 0)),
        last_price: parseFloat(mapFieldValue(rawTrade, 'last_price', 0)),
        bid_price: parseFloat(mapFieldValue(rawTrade, 'bid_price', 0)),
        ask_price: parseFloat(mapFieldValue(rawTrade, 'ask_price', 0)),

        // === P&L DATA ===
        unrealized_pnl: parseFloat(mapFieldValue(rawTrade, 'unrealized_pnl', 0)),
        realized_pnl: parseFloat(mapFieldValue(rawTrade, 'realized_pnl', 0)),
        realized_pnl_session: parseFloat(mapFieldValue(rawTrade, 'realized_pnl_session', 0)),
        pnl_per_share: parseFloat(mapFieldValue(rawTrade, 'pnl_per_share', 0)),

        // === VOLUME DATA ===
        total_volume: parseInt(mapFieldValue(rawTrade, 'total_volume', 0)),
        volume: parseInt(mapFieldValue(rawTrade, 'volume', 0)),
        session_buy_value: parseFloat(mapFieldValue(rawTrade, 'session_buy_value', 0)),
        session_sell_value: parseFloat(mapFieldValue(rawTrade, 'session_sell_value', 0)),

        // === STATUS & FLAGS ===
        status: (mapFieldValue(rawTrade, 'status', 'UNKNOWN')).toString().toUpperCase(),
        is_open: Boolean(mapFieldValue(rawTrade, 'is_open', true)),
        is_parent: Boolean(mapFieldValue(rawTrade, 'is_parent', false)),
        is_child: Boolean(mapFieldValue(rawTrade, 'is_child', false)),
        trading_status: mapFieldValue(rawTrade, 'trading_status', 'UNKNOWN'),
        lifecycle_state: mapFieldValue(rawTrade, 'lifecycle_state', 'ACTIVE'),

        // === ACTIONS ===
        action: (mapFieldValue(rawTrade, 'action', 'UNKNOWN')).toString().toUpperCase(),
        side: (mapFieldValue(rawTrade, 'side', 'UNKNOWN')).toString().toUpperCase(),

        // === STRATEGY & METADATA ===
        strategy: mapFieldValue(rawTrade, 'strategy', 'UNKNOWN'),
        origin: mapFieldValue(rawTrade, 'origin', 'UNKNOWN'),
        comment: mapFieldValue(rawTrade, 'comment', null),

        // === TIMESTAMPS ===
        timestamp: normalizeTimestamp(mapFieldValue(rawTrade, 'timestamp', Date.now())),
        position_opened_timestamp: normalizeTimestamp(mapFieldValue(rawTrade, 'position_opened_timestamp', Date.now())),
        last_update_timestamp: normalizeTimestamp(mapFieldValue(rawTrade, 'last_update_timestamp', Date.now())),
        last_fill_timestamp: normalizeTimestamp(mapFieldValue(rawTrade, 'last_fill_timestamp', null)),
        start_time: normalizeTimestamp(mapFieldValue(rawTrade, 'start_time', null)),
        end_time: normalizeTimestamp(mapFieldValue(rawTrade, 'end_time', null)),

        // === COMMISSION & COSTS ===
        commission: parseFloat(mapFieldValue(rawTrade, 'commission', 0)),
        commission_total: parseFloat(mapFieldValue(rawTrade, 'commission_total', 0)),

        // === FILL METRICS ===
        total_fills_count: parseInt(mapFieldValue(rawTrade, 'total_fills_count', 0)),
        fills_count: parseInt(mapFieldValue(rawTrade, 'fills_count', 0)),

        // === PERFORMANCE METRICS ===
        signal_to_fill_latency_ms: parseFloat(mapFieldValue(rawTrade, 'signal_to_fill_latency_ms', 0)),
        order_to_fill_latency_ms: parseFloat(mapFieldValue(rawTrade, 'order_to_fill_latency_ms', 0)),
        fill_duration_ms: parseFloat(mapFieldValue(rawTrade, 'fill_duration_ms', 0)),

        // === TESTRADE SPECIFIC FIELDS ===
        position_uuid: mapFieldValue(rawTrade, 'position_uuid', null),
        master_correlation_id: mapFieldValue(rawTrade, 'master_correlation_id', null),
        lcm_parent_trade_id: mapFieldValue(rawTrade, 'lcm_parent_trade_id', null),
        last_sync_details: mapFieldValue(rawTrade, 'last_sync_details', null),
        last_update_source: mapFieldValue(rawTrade, 'last_update_source', 'UNKNOWN'),
        last_broker_fingerprint: mapFieldValue(rawTrade, 'last_broker_fingerprint', null),
        retry_count: parseInt(mapFieldValue(rawTrade, 'retry_count', 0)),
        cooldown_until: mapFieldValue(rawTrade, 'cooldown_until', null),
        session_system_fills_ledger: mapFieldValue(rawTrade, 'session_system_fills_ledger', null),
        ocr_confidence: parseFloat(mapFieldValue(rawTrade, 'ocr_confidence', 0)),
        master_trader_snapshot: mapFieldValue(rawTrade, 'master_trader_snapshot', null),

        // === RISK METRICS ===
        luld_high: parseFloat(mapFieldValue(rawTrade, 'luld_high', 0)),
        luld_low: parseFloat(mapFieldValue(rawTrade, 'luld_low', 0)),

        // === COLLECTIONS ===
        local_orders: mapFieldValue(rawTrade, 'local_orders', []),
        fills: mapFieldValue(rawTrade, 'fills', []),
        child_trades: mapFieldValue(rawTrade, 'child_trades', []),

        // === MARKET DATA AVAILABILITY ===
        market_data_available: Boolean(mapFieldValue(rawTrade, 'market_data_available', false)) ||
                              Boolean(rawTrade.bid_price || rawTrade.ask_price || rawTrade.last_price),
        hierarchy_data_available: Boolean(mapFieldValue(rawTrade, 'hierarchy_data_available', false)) ||
                                 Boolean(rawTrade.parent_trade_id || rawTrade.is_parent || rawTrade.child_trades?.length)
    };

    // === CALCULATED FIELDS ===
    // Calculate market value if not provided
    if (!normalized.market_value && normalized.quantity && normalized.last_price) {
        normalized.market_value = normalized.quantity * normalized.last_price;
    } else {
        normalized.market_value = parseFloat(mapFieldValue(rawTrade, 'market_value', 0));
    }

    // Calculate P&L per share if not provided
    if (!normalized.pnl_per_share && normalized.quantity && normalized.unrealized_pnl) {
        normalized.pnl_per_share = normalized.unrealized_pnl / Math.abs(normalized.quantity);
    }

    // Calculate spread
    normalized.spread = (normalized.ask_price && normalized.bid_price) ?
                       Math.abs(normalized.ask_price - normalized.bid_price) : 0;

    // Calculate day change
    normalized.day_change = normalized.last_price - normalized.average_price;
    normalized.day_change_percent = normalized.average_price > 0 ?
                                   (normalized.day_change / normalized.average_price) * 100 : 0;

    // === VALIDATION & WARNINGS ===
    const warnings = [];
    if (normalized.symbol === 'UNKNOWN') warnings.push('Missing symbol');
    if (normalized.quantity === 0) warnings.push('Zero quantity');
    if (normalized.average_price === 0 && normalized.last_price === 0) warnings.push('No price data');

    if (warnings.length > 0 && APP_CONFIG.debugMode) {
        console.warn(`[normalizeTestradeDataComplete] Warnings for ${normalized.symbol}:`, warnings);
    }

    return normalized;
}

// Debug function to show field mapping coverage
function debugFieldCoverage(rawTrade) {
    if (!rawTrade) {
        console.log('No trade data provided');
        return;
    }

    console.log('=== FIELD COVERAGE ANALYSIS ===');

    const rawFields = Object.keys(rawTrade);
    const mappedFields = [];
    const unmappedFields = [];

    // Check each raw field
    rawFields.forEach(fieldName => {
        let isMapped = false;

        // Check if this field is used in any mapping group
        Object.entries(TESTRADE_FIELD_MAPPINGS).forEach(([group, options]) => {
            if (options.includes(fieldName)) {
                mappedFields.push(fieldName);
                isMapped = true;
            }
        });

        if (!isMapped) {
            unmappedFields.push(fieldName);
        }
    });

    console.log(`Total fields in raw data: ${rawFields.length}`);
    console.log(`Mapped fields: ${mappedFields.length}`, mappedFields);
    console.log(`Unmapped fields: ${unmappedFields.length}`, unmappedFields);
    console.log(`Coverage: ${((mappedFields.length / rawFields.length) * 100).toFixed(1)}%`);

    // Test normalization
    const normalized = normalizeTestradeDataComplete(rawTrade);
    const normalizedFields = Object.keys(normalized);

    console.log(`Normalized fields: ${normalizedFields.length}`);
    console.log('Fields with zero/null values:');

    normalizedFields.forEach(field => {
        const value = normalized[field];
        if (value === 0 || value === null || value === 'UNKNOWN' || value === '') {
            console.log(`  ${field}: ${value}`);
        }
    });

    console.log('===============================');

    return {
        rawFieldCount: rawFields.length,
        mappedFields: mappedFields,
        unmappedFields: unmappedFields,
        coverage: ((mappedFields.length / rawFields.length) * 100).toFixed(1) + '%',
        normalized: normalized
    };
}

// Update the main normalization function
window.normalizeTestradeData = normalizeTestradeDataComplete;
window.debugFieldCoverage = debugFieldCoverage;

// Add field mapping info to global debug
window.TESTRADE_FIELD_MAPPINGS = TESTRADE_FIELD_MAPPINGS;



// Fix the toggleHierarchy function calls
function toggleHierarchy(hierarchyId, event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    const content = document.getElementById(hierarchyId);
    if (!content) {
        console.warn(`Hierarchy content not found: ${hierarchyId}`);
        return;
    }

    const isCollapsed = content.classList.contains('collapsed');
    content.classList.toggle('collapsed');

    // Update button
    const section = content.closest('.trade-hierarchy-section');
    const button = section ? section.querySelector('.hierarchy-toggle') : null;
    if (button) {
        button.textContent = isCollapsed ? '▲' : '▼';
        button.setAttribute('aria-expanded', !isCollapsed);
    }

    // Update state
    if (isCollapsed) {
        hierarchyState.expandedSections.add(hierarchyId);
    } else {
        hierarchyState.expandedSections.delete(hierarchyId);
    }
}

function handleToggle(content, button, hierarchyId) {
    const wasCollapsed = content.classList.contains('collapsed');

    console.log(`[handleToggle] Current state - collapsed: ${wasCollapsed}`);

    // Toggle the collapsed class
    content.classList.toggle('collapsed');

    // Update button text and state
    const isNowCollapsed = content.classList.contains('collapsed');
    if (button) {
        button.textContent = isNowCollapsed ? '▼' : '▲';
        button.setAttribute('aria-expanded', !isNowCollapsed);
        console.log(`[handleToggle] Button updated - now showing: ${button.textContent}`);
    }

    // Add visual feedback
    const action = isNowCollapsed ? 'Collapsed' : 'Expanded';
    console.log(`[handleToggle] ${action} ${hierarchyId}`);

    // Update hierarchy state management
    if (isNowCollapsed) {
        hierarchyState.expandedSections.delete(hierarchyId);
    } else {
        hierarchyState.expandedSections.add(hierarchyId);
    }
    hierarchyState.lastUpdateTime = Date.now();

    // Store state for debugging (legacy)
    if (!window.hierarchyStates) window.hierarchyStates = {};
    window.hierarchyStates[hierarchyId] = !isNowCollapsed;
}
// Debug function to check all hierarchy elements
function debugAllHierarchies() {
    console.log('=== ALL HIERARCHY ELEMENTS DEBUG ===');

    const hierarchySections = document.querySelectorAll('.trade-hierarchy-section');
    console.log(`Found ${hierarchySections.length} hierarchy sections`);

    hierarchySections.forEach((section, index) => {
        const header = section.querySelector('.hierarchy-header');
        const content = section.querySelector('.hierarchy-content');
        const button = section.querySelector('.hierarchy-toggle');

        console.log(`Section ${index}:`, {
            hasHeader: !!header,
            hasContent: !!content,
            hasButton: !!button,
            contentId: content ? content.id : 'no-id',
            isCollapsed: content ? content.classList.contains('collapsed') : 'no-content',
            buttonText: button ? button.textContent : 'no-button',
            onclickHandler: header ? header.getAttribute('onclick') : 'no-onclick'
        });

        // Check if the onclick handler is correct
        if (header) {
            const onclick = header.getAttribute('onclick');
            if (onclick && content) {
                console.log(`  -> onclick: ${onclick}`);
                console.log(`  -> content.id: ${content.id}`);
            }
        }
    });

    console.log('Stored hierarchy states:', window.hierarchyStates || 'none');
    console.log('========================================');

    return `Found ${hierarchySections.length} hierarchy sections`;
}

// Add to window for console access
window.debugAllHierarchies = debugAllHierarchies;

// =============================================================================
// FIX 7: HIERARCHY STATE MANAGEMENT
// =============================================================================
// Ensure hierarchyState is properly initialized
if (typeof hierarchyState === 'undefined') {
    window.hierarchyState = {
        expandedSections: new Set(),
        lastUpdateTime: Date.now()
    };
} else {
    // hierarchyState already exists, ensure it has the right structure
    const hierarchyState = {
        expandedSections: new Set(),
        lastUpdateTime: Date.now()
    };
}

// Function to restore hierarchy states after DOM update
function restoreHierarchyStates() {
    hierarchyState.expandedSections.forEach(hierarchyId => {
        const content = document.getElementById(hierarchyId);
        if (content) {
            const hierarchySection = content.closest('.trade-hierarchy-section');
            const button = hierarchySection?.querySelector('.hierarchy-toggle');

            if (button) {
                content.classList.remove('collapsed');
                button.textContent = '▲';
                button.setAttribute('aria-expanded', 'true');
            }
        }
    });

    hierarchyState.lastUpdateTime = Date.now();
}





// 4. ADD this debug function to test the independent system:
function testIndependentPills() {
    console.log('=== TESTING INDEPENDENT PILLS ===');

    // Test each pill independently
    console.log('Testing core pill...');
    statusPills.core.update('healthy', 'Test healthy status', true);

    setTimeout(() => {
        console.log('Testing core pill error...');
        statusPills.core.update('error', 'Test error status', true);
    }, 2000);

    setTimeout(() => {
        console.log('Testing API pill...');
        statusPills.api.update('warning', 'Test warning status', true);
    }, 4000);

    setTimeout(() => {
        console.log('Testing rapid updates (should be debounced)...');
        for (let i = 0; i < 10; i++) {
            statusPills.redis.update('healthy', `Rapid update ${i}`, false);
        }
    }, 6000);

    console.log('Independent pill test started - watch for updates');
    return 'Test sequence started';
}

// 5. ADD this function to force stop any remaining old intervals:
function cleanupOldSystem() {
    console.log('=== CLEANING UP OLD SYSTEM ===');

    // Stop any remaining intervals
    if (state.healthPollingInterval) {
        clearInterval(state.healthPollingInterval);
        console.log('Stopped health polling interval');
    }

    // Remove old debounce object if it exists
    if (window.statusUpdateDebounce) {
        Object.keys(window.statusUpdateDebounce).forEach(key => {
            if (window.statusUpdateDebounce[key].timer) {
                clearTimeout(window.statusUpdateDebounce[key].timer);
            }
        });
        delete window.statusUpdateDebounce;
        console.log('Removed old debounce system');
    }

    // Restart health polling with independent system
    startSystemHealthPolling();

    console.log('Cleanup completed - system should be stable now');
    return 'Old system cleaned up';
}

// Add to window for console access
window.testIndependentPills = testIndependentPills;
window.cleanupOldSystem = cleanupOldSystem;

// Debug function to check hierarchy state
function debugHierarchyState() {
    console.log('=== HIERARCHY STATE DEBUG ===');
    console.log('Expanded sections:', Array.from(hierarchyState.expandedSections));
    console.log('Last update time:', new Date(hierarchyState.lastUpdateTime).toISOString());
    console.log('Time since last update:', Date.now() - hierarchyState.lastUpdateTime, 'ms');

    // Check actual DOM state
    const hierarchyContents = document.querySelectorAll('.hierarchy-content');
    console.log(`Found ${hierarchyContents.length} hierarchy sections in DOM:`);

    hierarchyContents.forEach((content, index) => {
        const id = content.id;
        const isCollapsed = content.classList.contains('collapsed');
        const isInExpandedSet = hierarchyState.expandedSections.has(id);
        const button = content.closest('.trade-hierarchy-section')?.querySelector('.hierarchy-toggle');

        console.log(`  ${index}: ID=${id}, collapsed=${isCollapsed}, inSet=${isInExpandedSet}, button=${button?.textContent}`);
    });

    console.log('================================');
    return `Found ${hierarchyContents.length} hierarchy sections`;
}

// Add to window for console debugging
window.debugHierarchyState = debugHierarchyState;
window.hierarchyState = hierarchyState;

// SOLUTION 2: Smart market data indicator that only shows messages when state changes
function updateMarketDataIndicatorIfChanged(marketDataAvailable, hierarchyAvailable) {
    const currentState = `${marketDataAvailable}-${hierarchyAvailable}`;

    // Only update and show messages if the state actually changed
    if (updateTracker.lastMarketDataState !== currentState) {
        updateTracker.lastMarketDataState = currentState;

        const marketDataIndicator = document.getElementById('guiMarketDataIndicator');
        if (marketDataIndicator) {
            const liveDot = marketDataIndicator.querySelector('.live-dot');
            const liveText = marketDataIndicator.querySelector('span');

            let statusText = "Market Data: ";
            let dotColor = 'var(--accent-danger)';

            if (marketDataAvailable === true && hierarchyAvailable === true) {
                statusText += "Live & Complete";
                dotColor = 'var(--accent-primary)';
            } else if (marketDataAvailable === true) {
                statusText += "Live (Hierarchy Missing)";
                dotColor = 'var(--accent-warning)';
            } else if (hierarchyAvailable === true) {
                statusText += "Stale (Hierarchy OK)";
                dotColor = 'var(--accent-warning)';
            } else {
                statusText += "Unavailable";
            }

            if (liveDot) liveDot.style.backgroundColor = dotColor;
            if (liveText) liveText.textContent = statusText;

            // **CRITICAL**: Only show system messages when state changes
            if (marketDataAvailable === false) {
                addSystemMessage('📉 Position data missing market quotes (bid/ask/last)', 'warning');
            }
            if (hierarchyAvailable === false) {
                addSystemMessage('📉 Position data missing parent/child trade hierarchy', 'warning');
            }
        }
    }
}

// Debug function to check what's causing the updates
function debugUpdateCauses() {
    console.log('=== UPDATE CAUSES DEBUG ===');
    console.log('Last data hash:', updateTracker.lastDataHash);
    console.log('Last market state:', updateTracker.lastMarketDataState);
    console.log('Update in progress:', updateTracker.updateInProgress);
    console.log('Pending update:', !!updateTracker.pendingUpdate);
    console.log('Active trades count:', state.active_trades?.length || 0);
    console.log('===========================');

    return `Hash: ${updateTracker.lastDataHash?.substring(0, 20)}..., State: ${updateTracker.lastMarketDataState}`;
}

// Add to window for debugging
window.debugUpdateCauses = debugUpdateCauses;
window.updateTracker = updateTracker;

function updateHistoricalTradesDisplay() {
    console.log(">>> updateHistoricalTradesDisplay called, active tab:", uiState.activeHistoryTab);
    const container = document.getElementById('historicalTradesContainer');
    const noHistoryMessage = document.getElementById('noHistoricalTradesMessage');
    if (!container) {
        console.error("! Critical Error: Historical trades container 'historicalTradesContainer' not found!");
        return;
    }

    let tradesToDisplay = [];
    const allTrades = tradeState.historicalTrades || [];

    switch (uiState.activeHistoryTab) {
        case 'today':
            const todayDateString = new Date().toDateString();
            console.log(`[HIST_FILTER] Today's date string: ${todayDateString}`);
            tradesToDisplay = allTrades.filter(t => {
                // t.timestamp should now be in MS from the mapping step
                const tradeDate = new Date(t.timestamp);
                const tradeDateString = tradeDate.toDateString();
                const isToday = tradeDateString === todayDateString;
                console.log(`[HIST_FILTER] Trade ts: ${t.timestamp}, Trade Date: ${tradeDate.toUTCString()}, TradeDateString: ${tradeDateString}, IsToday: ${isToday}`);
                return isToday;
            });
            break;
        case 'winners':
            tradesToDisplay = allTrades.filter(t => (t.pnl || t.realized_pnl || 0) > 0);
            break;
        case 'losers':
            tradesToDisplay = allTrades.filter(t => (t.pnl || t.realized_pnl || 0) < 0);
            break;
        case 'all_time':
            tradesToDisplay = allTrades;
            break;
        default:
            tradesToDisplay = allTrades;
    }

    console.log(">>> Historical trades to display:", tradesToDisplay.length, JSON.parse(JSON.stringify(tradesToDisplay.slice(0,5))));


    if (!tradesToDisplay || tradesToDisplay.length === 0) {
        container.innerHTML = '';
        if (noHistoryMessage) noHistoryMessage.style.display = 'block';
        updateHistoryStats({ total: 0, winners: 0, losers: 0, totalPnL: 0, totalWinnerPnL: 0, totalLoserPnL: 0 });
        const guiHistCountEl = document.getElementById('guiHistoryTradesTodayCount');
        if(guiHistCountEl) guiHistCountEl.textContent = '0';
        const realizedEl = document.getElementById('guiHistoryRealizedPnlTotal');
        if(realizedEl){
            realizedEl.innerHTML = `Realized: <strong>${formatCurrency(0)}</strong>`;
            realizedEl.className = getPnLClass(0);
        }
        return;
    }


    if (noHistoryMessage) noHistoryMessage.style.display = 'none';
    container.innerHTML = '';

    const groupedHistoricalTrades = {};
    tradesToDisplay.forEach(trade => {
        const groupId = trade.is_parent ? trade.trade_id : (trade.parent_trade_id || trade.trade_id || `${trade.symbol}-${getJsTimestamp(trade.timestamp)/1000}`);
        if (!groupId) {
            console.warn("Historical trade missing groupId:", trade);
            return;
        }
        if (!groupedHistoricalTrades[groupId]) {
            groupedHistoricalTrades[groupId] = {
                parent_details: null,
                trades: [],
                group_pnl: 0,
                group_shares: 0,
                group_commissions: 0,
                first_timestamp: getJsTimestamp(trade.timestamp || 0) / 1000,
                main_symbol: trade.symbol,
                has_rejections: false,
                has_fills: false,
                total_rejected: 0,
                total_filled: 0,
                avg_fill_price: 0,
                total_fill_qty: 0
            };
        }
        if (trade.is_parent || !groupedHistoricalTrades[groupId].parent_details) {
            groupedHistoricalTrades[groupId].parent_details = trade;
            groupedHistoricalTrades[groupId].main_symbol = trade.symbol;
        }
        groupedHistoricalTrades[groupId].trades.push(trade);
        groupedHistoricalTrades[groupId].group_pnl += (trade.pnl || trade.realized_pnl || 0);
        groupedHistoricalTrades[groupId].group_shares += Math.abs(trade.quantity || 0);
        groupedHistoricalTrades[groupId].group_commissions += (trade.commission || 0);
        
        // Track rejections and fills
        if (trade.is_rejection || trade.status === 'REJECTED') {
            groupedHistoricalTrades[groupId].has_rejections = true;
            groupedHistoricalTrades[groupId].total_rejected += Math.abs(trade.quantity || 0);
        }
        if (trade.is_fill || trade.status === 'FILLED') {
            groupedHistoricalTrades[groupId].has_fills = true;
            groupedHistoricalTrades[groupId].total_filled += Math.abs(trade.quantity || 0);
            // Calculate weighted average fill price
            const fillValue = Math.abs(trade.quantity || 0) * (trade.price || 0);
            groupedHistoricalTrades[groupId].total_fill_qty += Math.abs(trade.quantity || 0);
            groupedHistoricalTrades[groupId].avg_fill_price = 
                (groupedHistoricalTrades[groupId].avg_fill_price * (groupedHistoricalTrades[groupId].total_fill_qty - Math.abs(trade.quantity || 0)) + fillValue) / 
                groupedHistoricalTrades[groupId].total_fill_qty;
        }
        
        const currentTradeTimestamp = getJsTimestamp(trade.timestamp || 0) / 1000;
        if (currentTradeTimestamp < groupedHistoricalTrades[groupId].first_timestamp) {
            groupedHistoricalTrades[groupId].first_timestamp = currentTradeTimestamp;
        }
    });

    console.log(">>> Grouped historical trades:", groupedHistoricalTrades);

    const sortedGroups = Object.values(groupedHistoricalTrades).sort((a,b) => (b.first_timestamp || 0) - (a.first_timestamp || 0));

    let totalRealizedPnlForTab = 0;

    sortedGroups.forEach(group => {
        const groupData = group.parent_details || group.trades[0];
        
        // Merge group data with calculated fields
        const enhancedGroupData = {
            ...groupData,
            // Aggregate P&L data
            realized_pnl: group.group_pnl,
            unrealized_pnl: 0, // Historical trades have no unrealized P&L
            pnl: group.group_pnl,
            commission: group.group_commissions,
            
            // Aggregate quantity data
            quantity: group.group_shares,
            total_fills_count: group.trades.length,
            total_traded: group.group_shares,
            
            // Market data (use latest from trades)
            bid_price: group.trades[0]?.bid_price || 0,
            ask_price: group.trades[0]?.ask_price || 0,
            last_price: group.trades[0]?.last_price || group.trades[0]?.price || 0,
            current_price: group.trades[0]?.last_price || group.trades[0]?.price || 0,
            
            // Average price calculation
            average_price: group.total_fill_qty > 0 ? group.avg_fill_price : (groupData.average_price || groupData.price || 0),
            
            // Status
            status: group.trades.every(t => t.status === 'CLOSED' || t.status === 'FILLED') ? 'CLOSED' : 
                    group.trades.some(t => t.status === 'REJECTED') ? 'REJECTED' : 'PARTIAL',
            
            // Rejection info (if any)
            is_rejection: group.has_rejections,
            rejection_reason: group.trades.find(t => t.is_rejection)?.rejection_reason,
            source_component: group.trades.find(t => t.is_rejection)?.source_component,
            
            // Timing info
            timestamp: group.first_timestamp * 1000, // Convert back to ms
            close_timestamp: Math.max(...group.trades.map(t => getJsTimestamp(t.timestamp) || 0))
        };
        
        // Create enhanced position card
        const cardHTML = createEnhancedPositionCard(enhancedGroupData, 'HISTORICAL');
        container.insertAdjacentHTML('beforeend', cardHTML);
        
        // Add child trades to the hierarchy section
        const hierarchyId = `historical-${enhancedGroupData.symbol.toLowerCase()}-${enhancedGroupData.trade_id || Date.now()}-hierarchy`;
        const hierarchyContent = document.getElementById(hierarchyId);
        if (hierarchyContent) {
            const childTradesHTML = group.trades
                .sort((a,b) => (getJsTimestamp(b.timestamp)/1000) - (getJsTimestamp(a.timestamp)/1000))
                .map(trade => createTradeHierarchyEntryHTML(trade, 
                    trade.is_parent ? 'PARENT' : 
                    trade.is_child ? 'CHILD' : 
                    trade.is_rejection ? 'REJECTION' : 
                    trade.is_fill ? 'FILL' : 'TRADE'))
                .join('');
            hierarchyContent.innerHTML = childTradesHTML;
        }
        
        totalRealizedPnlForTab += group.group_pnl;
    });


    const stats = {
        total: tradesToDisplay.length,
        winners: tradesToDisplay.filter(t => (t.pnl || t.realized_pnl || 0) > 0).length,
        losers: tradesToDisplay.filter(t => (t.pnl || t.realized_pnl || 0) < 0).length,
        totalPnL: totalRealizedPnlForTab,
        totalWinnerPnL: tradesToDisplay.filter(t => (t.pnl || t.realized_pnl || 0) > 0).reduce((sum, t) => sum + (t.pnl || t.realized_pnl || 0), 0),
        totalLoserPnL: tradesToDisplay.filter(t => (t.pnl || t.realized_pnl || 0) < 0).reduce((sum, t) => sum + (t.pnl || t.realized_pnl || 0), 0),
    };
    updateHistoryStats(stats);

    const guiHistCountEl = document.getElementById('guiHistoryTradesTodayCount');
    if(guiHistCountEl) guiHistCountEl.textContent = stats.total; // Show count for current tab

    const realizedEl = document.getElementById('guiHistoryRealizedPnlTotal');
    if(realizedEl) {
        realizedEl.innerHTML = `Realized: <strong>${formatCurrency(stats.totalPnL)}</strong>`;
        realizedEl.className = getPnLClass(stats.totalPnL);
    }
    console.log(">>> updateHistoricalTradesDisplay finished rendering.");
}

// Add this function to handle adding new historical trades
// This function should be called by your WebSocket handler for 'trade_closed', 'order_fill_update' (if fills are historical), etc.
function addHistoricalTrade(trade) {
    let timestamp = trade.timestamp || 0;
    if (timestamp > **********000) timestamp /= 1000; // ms to s
    trade.timestamp = timestamp;
    trade.pnl = trade.pnl || trade.realized_pnl || 0;

    tradeState.historicalTrades.unshift(trade);
    updateHistoricalTradesDisplay();
}

function updateHistoryStats(stats) {
    const elTotal = document.getElementById('histStatTotal');
    const elWinners = document.getElementById('histStatWinners');
    const elLosers = document.getElementById('histStatLosers');
    const elWinRate = document.getElementById('histStatWinRate');
    const elAvgWin = document.getElementById('histStatAvgWin');
    const elAvgLoss = document.getElementById('histStatAvgLoss');

    if (elTotal) elTotal.textContent = stats.total;
    if (elWinners) {
        elWinners.textContent = stats.winners;
        elWinners.className = 'stat-value ' + getPnLClass(stats.winners > 0 ? 1 : (stats.winners === 0 ? 0 : -1) );
    }
    if (elLosers) {
        elLosers.textContent = stats.losers;
        elLosers.className = 'stat-value ' + getPnLClass(stats.losers > 0 ? -1 : 0);
    }

    const winLossTrades = stats.winners + stats.losers;
    const winRate = winLossTrades > 0 ? (stats.winners / winLossTrades * 100) : 0;
     if (elWinRate) {
        elWinRate.textContent = (isNaN(winRate) ? 0 : winRate).toFixed(1) + '%';
        elWinRate.className = 'stat-value ' + getPnLClass(winRate >= 50 ? 1 : (winRate === 0 && winLossTrades === 0 ? 0 : -1));
    }

    const avgWinner = stats.winners > 0 ? stats.totalWinnerPnL / stats.winners : 0;
    if (elAvgWin) {
        elAvgWin.textContent = formatCurrency(avgWinner);
        elAvgWin.className = 'stat-value ' + getPnLClass(avgWinner);
    }

    const avgLoser = stats.losers > 0 ? stats.totalLoserPnL / stats.losers : 0;
     if (elAvgLoss) {
        elAvgLoss.textContent = formatCurrency(Math.abs(avgLoser));
        elAvgLoss.className = 'stat-value ' + getPnLClass(avgLoser);
    }
}

// --- END UI Manager ---






// --- API Client ---
// Handles all HTTP communication with the FastAPI backend.
async function sendCommand(command, parameters = {}) {
    if (!state.connected && command !== 'health_check') {
        addSystemMessage(`Cannot send command: ${command}. Not connected.`, 'error');
        return { success: false, error: 'Not connected' };
    }
    try {
        const response = await fetch(`${state.apiUrl}/control/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                command: command,
                parameters: parameters
            })
        });

        const responseData = await response.json();
        if (response.ok) {
            // Don't show message for update_trading_params auto-save
            if (command !== 'update_trading_params') {
                addSystemMessage(`Command sent: ${command} (${responseData.status || 'OK'})`, 'success');
            }
            return { success: true, data: responseData };
        } else {
            addSystemMessage(`Command failed: ${command} - ${responseData.detail || responseData.message || 'Unknown error'}`, 'error');
            return { success: false, data: responseData };
        }
    } catch (error) {
        addSystemMessage(`API Error sending ${command}: ${error.message}`, 'error');
        return { success: false, error: error.message };
    }
}

async function updateConfig(key, value) {
    try {
        console.log(`Updating config: ${key} = ${value} (type: ${typeof value})`);

        const response = await fetch(`${state.apiUrl}/config`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({[key]: value})
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error(`Failed to update config ${key}:`, errorData);
            addSystemMessage(`❌ Failed to update ${key}: ${errorData.detail || 'Unknown error'}`, 'error');
        } else {
            console.log(`Successfully updated config ${key} to ${value}`);
            addSystemMessage(`✅ Updated ${key}`, 'success');
        }
    } catch (error) {
        console.error(`Error updating config ${key}:`, error);
        addSystemMessage(`❌ Error updating ${key}: ${error.message}`, 'error');
    }
}

async function loadInitialROI() {
    try {
        const response = await fetch(`${state.apiUrl}/config/roi`);
        const data = await response.json();

        if (response.ok && data.status === 'success' && data.roi) {
            state.currentROI = data.roi;
            updateROIDisplay();
            addSystemMessage(`✅ ROI loaded from ${data.source}: [${data.roi.join(', ')}]`, 'success');
        } else {
            addSystemMessage(`⚠️ Failed to load ROI: ${data.message || 'Unknown error'}`, 'warning');
            // Set fallback ROI
            state.currentROI = [64, 159, 681, 296];
            updateROIDisplay();
            addSystemMessage('Using default ROI coordinates', 'info');
        }
    } catch (error) {
        addSystemMessage(`❌ Error loading initial ROI: ${error.message}`, 'error');
        // Set fallback ROI
        state.currentROI = [64, 159, 681, 296];
        updateROIDisplay();
        addSystemMessage('Using default ROI coordinates due to error', 'warning');
    }
}

// 🚨 CHUNK 19.2: Enhanced debugging for health status fetching
async function fetchHealthStatus() {
    try {
        const response = await fetch(`${state.apiUrl}/health`);
        if (response.ok) {
            const healthData = await response.json();
            console.log('[fetchHealthStatus] Received health data:', healthData);
            updateSystemHealthStatus(healthData); // This updates state.lastHealthUpdate if core is healthy
            return healthData;
        } else {
            console.warn(`[fetchHealthStatus] API Error: ${response.status} ${response.statusText}`);
        }
    } catch (error) {
        console.warn('[fetchHealthStatus] Network or processing error:', error);
        return null;
    }
}

async function executeManualTrade(action, shares, symbol = null) {
    try {
        const response = await fetch(`${state.apiUrl}/control/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                command: 'manual_trade',
                parameters: {
                    action: action,
                    shares: shares,
                    symbol: symbol
                }
            })
        });

        const responseData = await response.json();
        return { success: response.ok, data: responseData };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function executeForceCloseAll(reason = 'Manual GUI Force Close Button') {
    try {
        const response = await fetch(`${state.apiUrl}/control/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                command: 'force_close_all',
                parameters: {
                    reason: reason
                }
            })
        });

        const responseData = await response.json();
        return { success: response.ok, data: responseData };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function executeEmergencyStop(reason = 'Manual GUI Emergency Stop Button') {
    try {
        const response = await fetch(`${state.apiUrl}/control/command`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                command: 'emergency_stop',
                parameters: {
                    reason: reason
                }
            })
        });

        const responseData = await response.json();
        return { success: response.ok, data: responseData };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function loadConfiguration() {
    try {
        // Load configuration from backend
        const response = await fetch(`${state.apiUrl}/config/load`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (response.ok) {
            const config = await response.json();

            // Update GUI fields with loaded values
            if (config.initial_share_size !== undefined) {
                document.getElementById('initialShares').value = config.initial_share_size;
            }
            if (config.add_type !== undefined) {
                document.getElementById('addType').value = config.add_type;
            }
            if (config.reduce_percentage !== undefined) {
                document.getElementById('reducePercent').value = config.reduce_percentage;
            }
            if (config.manual_shares !== undefined) {
                document.getElementById('manualShares').value = config.manual_shares;
            }

            // Load development settings if available
            if (config.development_mode !== undefined) {
                document.getElementById('developmentMode').checked = config.development_mode;
            }
            if (config.enable_image_recording !== undefined) {
                document.getElementById('enableImageRecording').checked = config.enable_image_recording;
            }
            if (config.enable_raw_ocr_recording !== undefined) {
                document.getElementById('enableRawOcrRecording').checked = config.enable_raw_ocr_recording;
            }
            if (config.enable_intellisense_logging !== undefined) {
                document.getElementById('enableIntelliSenseLogging').checked = config.enable_intellisense_logging;
            }
            if (config.enable_observability_logging !== undefined) {
                document.getElementById('enableObservabilityLogging').checked = config.enable_observability_logging;
            }
            if (config.log_level !== undefined) {
                document.getElementById('logLevel').value = config.log_level;
            }
            if (config.enable_ocr_debug_logging !== undefined) {
                document.getElementById('enableOcrDebugLogging').checked = config.enable_ocr_debug_logging;
            }

            // Load OCR parameters if available
            if (config.ocr_upscale_factor !== undefined) {
                document.getElementById('upscaleFactor').value = config.ocr_upscale_factor;
                document.getElementById('upscaleValue').textContent = config.ocr_upscale_factor;
            }
            if (config.ocr_unsharp_strength !== undefined) {
                document.getElementById('unsharpStrength').value = config.ocr_unsharp_strength;
                document.getElementById('unsharpValue').textContent = config.ocr_unsharp_strength;
            }
            if (config.ocr_threshold_c !== undefined) {
                document.getElementById('thresholdC').value = config.ocr_threshold_c;
                document.getElementById('thresholdCValue').textContent = config.ocr_threshold_c;
            }
            if (config.ocr_red_boost !== undefined) {
                document.getElementById('redBoost').value = config.ocr_red_boost;
                document.getElementById('redBoostValue').textContent = config.ocr_red_boost;
            }
            if (config.ocr_green_boost !== undefined) {
                document.getElementById('greenBoost').value = config.ocr_green_boost;
                document.getElementById('greenBoostValue').textContent = config.ocr_green_boost;
            }
            if (config.ocr_text_mask_min_contour_area !== undefined) {
                document.getElementById('textMaskMinContourArea').value = config.ocr_text_mask_min_contour_area;
                document.getElementById('contourAreaValue').textContent = config.ocr_text_mask_min_contour_area;
            }
            if (config.ocr_text_mask_min_width !== undefined) {
                document.getElementById('textMaskMinWidth').value = config.ocr_text_mask_min_width;
                document.getElementById('maskWidthValue').textContent = config.ocr_text_mask_min_width;
            }
            if (config.ocr_text_mask_min_height !== undefined) {
                document.getElementById('textMaskMinHeight').value = config.ocr_text_mask_min_height;
                document.getElementById('maskHeightValue').textContent = config.ocr_text_mask_min_height;
            }
            if (config.ocr_symbol_max_height !== undefined) {
                document.getElementById('symbolMaxHeight').value = config.ocr_symbol_max_height;
                document.getElementById('symbolMaxHeightValue').textContent = config.ocr_symbol_max_height;
            }
            if (config.ocr_period_comma_ratio_min !== undefined) {
                document.getElementById('periodCommaRatioMin').value = config.ocr_period_comma_ratio_min;
                document.getElementById('periodRatioMinValue').textContent = config.ocr_period_comma_ratio_min;
            }
            if (config.ocr_period_comma_ratio_max !== undefined) {
                document.getElementById('periodCommaRatioMax').value = config.ocr_period_comma_ratio_max;
                document.getElementById('periodRatioMaxValue').textContent = config.ocr_period_comma_ratio_max;
            }
            if (config.ocr_period_comma_radius !== undefined) {
                document.getElementById('periodCommaRadius').value = config.ocr_period_comma_radius;
                document.getElementById('periodRadiusValue').textContent = config.ocr_period_comma_radius;
            }
            if (config.ocr_hyphen_min_ratio !== undefined) {
                document.getElementById('hyphenMinRatio').value = config.ocr_hyphen_min_ratio;
                document.getElementById('hyphenRatioValue').textContent = config.ocr_hyphen_min_ratio;
            }
            if (config.ocr_hyphen_min_height !== undefined) {
                document.getElementById('hyphenMinHeight').value = config.ocr_hyphen_min_height;
                document.getElementById('hyphenHeightValue').textContent = config.ocr_hyphen_min_height;
            }

            // Load OCR checkboxes
            if (config.ocr_force_black_text_on_white !== undefined) {
                document.getElementById('forceBlackText').checked = config.ocr_force_black_text_on_white;
            }
            if (config.ocr_apply_text_mask_cleaning !== undefined) {
                document.getElementById('textMaskCleaning').checked = config.ocr_apply_text_mask_cleaning;
            }
            if (config.ocr_enhance_small_symbols !== undefined) {
                document.getElementById('enhanceSmallSymbols').checked = config.ocr_enhance_small_symbols;
            }

            // Update status displays to reflect loaded settings
            updateDevelopmentStatusDisplays();
            updateRecordingStatusDisplays();
            updateLoggingStatusDisplays();

            addSystemMessage('✅ Configuration loaded from control.json', 'success');
            return { success: true, config: config };
        } else {
            addSystemMessage('❌ Failed to load configuration', 'error');
            return { success: false, error: 'Failed to load configuration' };
        }
    } catch (error) {
        addSystemMessage(`❌ Error loading configuration: ${error.message}`, 'error');
        return { success: false, error: error.message };
    }
}

async function saveConfiguration() {
    const originalText = 'Save Config';
    const saveBtn = document.querySelector('button[onclick="saveConfiguration()"]');
    
    try {
        // Set button state to loading
        if (saveBtn) {
            setButtonState(saveBtn, 'loading', originalText);
        }

        // Use the command system instead of direct config endpoint
        const response = await sendCommand('save_configuration', {});

        if (response && response.success && response.data) {
            // Use the message from the command result
            if (response.data.message) {
                addSystemMessage(`✅ ${response.data.message}`, 'success');
            } else {
                addSystemMessage('✅ Configuration saved', 'success');
            }
            
            if (saveBtn) {
                setButtonState(saveBtn, 'success', originalText);
            }
            
            // If there were changes, show them - data is nested in response.data.data
            if (response.data.data && response.data.data.changed_count > 0) {
                addSystemMessage(`📝 ${response.data.data.changed_count} parameters changed: ${response.data.data.changed_params.join(', ')}`, 'info');
            }
        } else {
            const errorMsg = response?.data?.message || response?.data?.detail || response?.error || 'Unknown error';
            addSystemMessage(`❌ Failed to save configuration: ${errorMsg}`, 'error');
            if (saveBtn) {
                setButtonState(saveBtn, 'error', originalText);
            }
        }
    } catch (error) {
        addSystemMessage(`❌ Error saving configuration: ${error.message}`, 'error');
        console.error('Save configuration error:', error);
        if (saveBtn) {
            setButtonState(saveBtn, 'error', originalText);
        }
    }
}

// --- END API Client ---







// --- WebSocket Manager ---
// Manages WebSocket connection, message reception, and initial dispatch.
// The 'state.ws' object is the core of this manager.

function connectWebSocket() {
    const wsUrl = state.apiUrl.replace('http', 'ws') + '/ws';
    if (state.ws && (state.ws.readyState === WebSocket.OPEN || state.ws.readyState === WebSocket.CONNECTING)) {
        addSystemMessage('WebSocket connection attempt already in progress or connected.', 'info');
        return;
    }
    try {
        state.ws = new WebSocket(wsUrl);
        addSystemMessage('Attempting WebSocket connection...', 'info');

        state.ws.onopen = function() {
            apiStatusManager.setConnected(true);
            addSystemMessage('WebSocket connected', 'success');

            // Start system health monitoring
            startSystemHealthPolling();

            // --- BOOTSTRAP PATTERN: Request initial state via single command ---
            addSystemMessage('Requesting initial application state from backend via REQUEST_INITIAL_STATE...', 'info');

            // REMOVED OLD INDIVIDUAL GET_* COMMANDS:
            // sendCommand('get_all_positions');    // Now handled by bootstrap
            // sendCommand('get_trade_history', { date: new Date().toISOString().split('T')[0] }); // Keep this separate for now
            // sendCommand('get_account_summary');  // Now handled by bootstrap
            // sendCommand('get_position_summary'); // Now handled by bootstrap

            // ADD SINGLE BOOTSTRAP COMMAND:
            sendCommand('request_initial_state') // This triggers all bootstrap data publishing
                .then(response => {
                    if (response && response.success) {
                        // The command_id here is for the REQUEST_INITIAL_STATE command itself.
                        // The actual data will come via separate WebSocket messages with this ID as bootstrap_correlation_id.
                        const cmdIdShort = response.data?.command_id?.substring(0,8) || 'N/A';
                        addSystemMessage(`✅ Initial state request successfully sent (CmdID: ${cmdIdShort}). Waiting for stream updates.`, 'success');
                    } else {
                        const errorMsg = response?.data?.message || response?.error || 'Unknown error sending initial state request';
                        addSystemMessage(`⚠️ Failed to send initial state request: ${errorMsg}`, 'error');
                        console.error("Failed to send 'request_initial_state':", response);
                    }
                })
                .catch(err => {
                    addSystemMessage(`❌ Network error sending initial state request: ${err.message}`, 'error');
                    console.error("Network error in sendCommand('request_initial_state'):", err);
                });

            // REMOVED: get_trade_history command - now handled by REQUEST_INITIAL_STATE bootstrap
            // Trade history data comes via testrade:trade-history Redis stream
        };

        state.ws.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            } catch (error) {
                console.error('WebSocket message parse error:', error, event.data);
                addSystemMessage('Error parsing WebSocket message.', 'error');
            }
        };

        state.ws.onclose = function() {
            apiStatusManager.setConnected(false);
            addSystemMessage('WebSocket disconnected. Attempting to reconnect...', 'warning');
            state.ws = null;

            // Prevent multiple simultaneous reconnection attempts
            if (!state.reconnecting) {
                state.reconnecting = true;
                setTimeout(() => {
                    state.reconnecting = false;
                    connectWebSocket();
                }, APP_CONFIG.reconnectDelayMs);
            }
        };

        state.ws.onerror = function(error) {
            addSystemMessage('WebSocket error. Check console.', 'error');
            console.error('WebSocket error:', error);
        };

    } catch (error) {
        addSystemMessage(`WebSocket connection failed: ${error.message}`, 'error');

        // Prevent multiple simultaneous reconnection attempts
        if (!state.reconnecting) {
            state.reconnecting = true;
            setTimeout(() => {
                state.reconnecting = false;
                connectWebSocket();
            }, APP_CONFIG.reconnectionTimeoutMs);
        }
    }
}

function handleWebSocketMessage(data) {
    // Track WebSocket message arrival for latency calculation
    const messageArrivalTime = performance.now();
    
    // Reduced logging - only log in debug mode or for important messages
    if (APP_CONFIG.debugMode || ['command_response_v2', 'system_message'].includes(data.type)) {
        console.log('%c[WS MSG RECEIVED]', 'color: dodgerblue; font-weight: bold;', 'Type:', data.type);

        // Only log full data for debug mode
        if (APP_CONFIG.debugMode) {
            console.log('Full Data:', JSON.parse(JSON.stringify(data)));
        }
    }

    // Remove excessive content structure logging unless in debug mode
    if (APP_CONFIG.debugMode && data.content) {
        try {
            const parsed = JSON.parse(data.content);
            console.log(`${data.type} content structure:`, Object.keys(parsed));
            if (parsed.payload) {
                console.log(`${data.type} payload structure:`, Object.keys(parsed.payload));
            }
        } catch (e) {
            console.log(`${data.type} content (not JSON):`, data.content.substring(0, 100));
        }
    }

    // Update last update time for any message
    const lastUpdateEl = domManager.get('lastUpdate');
    if (lastUpdateEl) lastUpdateEl.textContent = formatTime();
    
    // Track message for latency calculation
    wsLatencyTracker.trackMessage();

    // The 'is_bootstrap' flag helps UI functions know if this is a full refresh or an incremental update.
    const isBootstrapData = data.is_bootstrap === true || (data.payload && data.payload.is_bootstrap === true) ||
                           (data.account && data.account.is_bootstrap === true) || (data.summary && data.summary.is_bootstrap === true);
    if (isBootstrapData) {
        const corrId = data.bootstrap_correlation_id || (data.payload && data.payload.bootstrap_correlation_id) ||
                      (data.account && data.account.bootstrap_correlation_id) || (data.summary && data.summary.bootstrap_correlation_id);
        addSystemMessage(`🔄 Received bootstrap data for type: ${data.type} (CorrID: ${corrId ? corrId.substring(0,8) : 'N/A'})`, 'info');
    }

    // 🚀 NEW: Try MessageRouter first for organized handling
    if (messageRouter && messageRouter.route(data)) {
        // Message was handled by MessageRouter, performance tracking for new architecture
        if (APP_CONFIG.debugMode) {
            console.log(`✅ Message ${data.type} handled by MessageRouter`);
        }
        return;
    }

    // Fall back to existing switch statement for backward compatibility
    switch(data.type) {
        case 'image_grab':
            // Track OCR update rate for performance analysis
            if (!window.ocrUpdateStats) {
                window.ocrUpdateStats = { count: 0, lastTime: performance.now(), rates: [] };
            }
            window.ocrUpdateStats.count++;
            const now = performance.now();
            const timeSinceLastUpdate = now - window.ocrUpdateStats.lastTime;
            window.ocrUpdateStats.lastTime = now;

            // Log update rate every 10 updates
            if (window.ocrUpdateStats.count % 10 === 0) {
                const avgInterval = window.ocrUpdateStats.rates.length > 0 ?
                    window.ocrUpdateStats.rates.reduce((a, b) => a + b) / window.ocrUpdateStats.rates.length : 0;
                console.log(`[OCR_UPDATE_RATE] ${window.ocrUpdateStats.count} updates, avg interval: ${avgInterval.toFixed(1)}ms (~${(1000/avgInterval).toFixed(1)} FPS)`);
            }
            window.ocrUpdateStats.rates.push(timeSinceLastUpdate);
            if (window.ocrUpdateStats.rates.length > 20) window.ocrUpdateStats.rates.shift(); // Keep last 20

            // 🚨 CHUNK 19.1: Measure frontend latency for OCR performance analysis
            if (data.timestamp_backend_sent_ns) {
                const arrivalTimeMs = performance.now();
                const backendSentTimeMs = data.timestamp_backend_sent_ns / 1_000_000; // Convert ns to ms
                const transitLatency = arrivalTimeMs - backendSentTimeMs;
                if (transitLatency > 0 && transitLatency < 10000) { // Only update for reasonable latencies
                    console.log(`[OCR_IMG_LATENCY] WS Transit + Frontend Parse for ${data.type}: ${transitLatency.toFixed(2)} ms`);
                    updateLatencyDisplay(transitLatency);
                }
            }

            console.log(`[OCR_IMG_WS] Received ${data.type}. Image data present: ${!!data.image_data}. Length: ${data.image_data ? data.image_data.length : 0}`);
            console.log('Image grab received:', !!data.image_data, data.image_data ? data.image_data.substring(0, 50) : 'no data');
            if (data.image_data) {
                updatePreviewImage(data.image_data, 'raw');
            } else if (data.content) {
                // Try to extract from content
                try {
                    const grabData = JSON.parse(data.content);
                    if (grabData.payload && grabData.payload.frame_base64) {
                        console.log('Found raw image in grab content');
                        updatePreviewImage(grabData.payload.frame_base64, 'raw');
                    }
                } catch (e) {
                    console.log('Could not parse image grab content');
                }
            }
            break;
        case 'preview_frame':
            // 🚨 CHUNK 19.1: Measure frontend latency for OCR performance analysis
            if (data.timestamp_backend_sent_ns) {
                const arrivalTimeMs = performance.now();
                const backendSentTimeMs = data.timestamp_backend_sent_ns / 1_000_000; // Convert ns to ms
                const transitLatency = arrivalTimeMs - backendSentTimeMs;
                if (transitLatency > 0 && transitLatency < 10000) { // Only update for reasonable latencies
                    console.log(`[OCR_IMG_LATENCY] WS Transit + Frontend Parse for ${data.type}: ${transitLatency.toFixed(2)} ms`);
                    updateLatencyDisplay(transitLatency);
                }
            }

            console.log('Preview frame received:', !!data.image_data, !!data.content);
            if (data.image_data) {
                updatePreviewImage(data.image_data, 'processed');
            }
            // Handle OCR content from preview frame - show as raw text
            if (data.content) {
                const rawOcrStream = document.getElementById('rawOcrStream');
                if (rawOcrStream) {
                    rawOcrStream.textContent = data.content;
                }

                // Extract image from OCR content if no direct image_data
                if (!data.image_data) {
                    try {
                        const ocrData = JSON.parse(data.content);
                        if (ocrData.payload && ocrData.payload.processed_frame_base64) {
                            console.log('Found image in preview frame OCR content');
                            updatePreviewImage(ocrData.payload.processed_frame_base64, 'processed');
                        }
                    } catch (e) {
                        // Not JSON, ignore
                    }
                }
            }
            if (data.confidence) updateConfidence(data.confidence);

            // Check for latency data in OCR messages
            if (data.metrics) {
                const latency = data.metrics.processing_time_ms ||
                               data.metrics.ocr_latency_ms ||
                               data.metrics.response_time_ms;
                if (latency) {
                    updateLatencyDisplay(latency);
                }
            }
            break;
        case 'raw_ocr':
            // Raw OCR comes as text content - show as free-flowing text
            if (data.content) {
                const rawOcrStream = document.getElementById('rawOcrStream');
                if (rawOcrStream) {
                    rawOcrStream.textContent = data.content;
                }

                // Extract images from OCR data if present
                try {
                    const ocrData = JSON.parse(data.content);
                    if (ocrData.payload) {
                        // Check for processed image
                        if (ocrData.payload.processed_frame_base64) {
                            console.log('Found processed image in OCR data');
                            updatePreviewImage(ocrData.payload.processed_frame_base64, 'processed');
                        }
                        // Check for raw image (multiple possible field names)
                        if (ocrData.payload.raw_frame_base64) {
                            console.log('Found raw image in OCR data (raw_frame_base64)');
                            updatePreviewImage(ocrData.payload.raw_frame_base64, 'raw');
                        } else if (ocrData.payload.frame_base64) {
                            console.log('Found raw image in OCR data (frame_base64)');
                            updatePreviewImage(ocrData.payload.frame_base64, 'raw');
                        }
                    }
                } catch (e) {
                    // Not JSON, ignore
                }
            }
            if (data.confidence) updateConfidence(data.confidence);
            break;
        case 'processed_ocr':
            // Check if this is a "no trade data" message to clear display
            if (data.no_trade_data) {
                console.log('No trade data detected - clearing processed OCR display');
                updateProcessedOcrTable({}, data.timestamp); // Clear the table
                break;
            }

            // Handle processed OCR data - check both direct snapshots and nested content
            if (data.snapshots) {
                updateProcessedOcrTable(data.snapshots, data.timestamp);
            } else if (data.content) {
                try {
                    const processedData = JSON.parse(data.content);
                    if (processedData.payload && processedData.payload.snapshots) {
                        const timestamp = processedData.metadata?.timestamp || processedData.payload?.timestamp || data.timestamp;
                        updateProcessedOcrTable(processedData.payload.snapshots, timestamp);
                    }
                } catch (e) {
                    console.warn('Could not parse processed OCR content:', e);
                }
            }

            // Check for latency data in processed OCR messages
            if (data.metrics) {
                const latency = data.metrics.processing_time_ms ||
                               data.metrics.ocr_latency_ms ||
                               data.metrics.response_time_ms;
                if (latency) {
                    updateLatencyDisplay(latency);
                }
            }
            break;
        case 'raw_ocr_snapshots':
            // Handle raw OCR snapshots for table display
            if (data.snapshots) {
                updateRawOcrTable(data.snapshots);
            } else if (data.content) {
                try {
                    const rawData = JSON.parse(data.content);
                    if (rawData.payload && rawData.payload.snapshots) {
                        updateRawOcrTable(rawData.payload.snapshots);
                    }
                } catch (e) {
                    console.warn('Could not parse raw OCR snapshots:', e);
                }
            }
            break;
        case 'core_status_update':
            // 🚨 CHUNK 19.2: Enhanced debugging for core status updates
            console.log('[WS_CORE_STATUS_UPDATE]', data);

            // Handle core status and OCR state updates using independent manager
            if (data.status_event && (data.status_event.includes("RUNNING") || data.status_event.includes("STARTED") || data.status_event === "OCR_ACTIVE")) {
                coreStatusManager.updateHealth();
                console.log(`[WS_CORE_STATUS_UPDATE] Core is healthy. Updated via CoreStatusManager`);
            } else {
                statusPills.core.update('error', `Core: ${data.status_event || 'Unknown status'} (WS)`, true);
            }

            // Handle OCR status updates via Redis signals
            if (data.status_event) {
                if (data.status_event.includes("OCR_STARTED") || data.status_event === "OCR_ACTIVE") {
                    updateOCRButtonState(true);
                    addSystemMessage('OCR started successfully', 'success');
                } else if (data.status_event.includes("OCR_STOPPED") || data.status_event === "OCR_INACTIVE") {
                    updateOCRButtonState(false);
                    addSystemMessage('OCR stopped successfully', 'success');
                }
            }

            // Only log significant status changes, not routine heartbeats
            if (data.status_event && !data.status_event.includes("HEARTBEAT")) {
                addSystemMessage(`Core Status: ${data.status_event}`, 'info');
            }
            break;
        case 'health_update':
            // 🚨 CHUNK 19.2: Enhanced debugging for health updates
            console.log('[WS_HEALTH_UPDATE]', data);

            // Handle health status updates using centralized function
            if (data.component === 'ApplicationCore' || data.component === 'Core') {
                let status = 'error';
                if (data.status === 'healthy' || data.status === 'RUNNING') {
                    status = 'healthy';
                    // FIXED: Update lastHealthUpdate for healthy status from WebSocket
                    state.lastHealthUpdate = Date.now();
                    console.log(`[WS_HEALTH_UPDATE] Core is ${status}. Updated state.lastHealthUpdate to: ${new Date(state.lastHealthUpdate).toISOString()}`);
                } else if (data.status === 'degraded') {
                    status = 'warning';
                }

                if (status === 'healthy') {
                    coreStatusManager.updateHealth();
                } else {
                    statusPills.core.update(status, `Core Health: ${data.status} (WS)`, true);
                }
            }

            // FIXED: Update latency display from multiple possible sources
            if (data.metrics) {
                const latency = data.metrics.gui_latency_ms ||
                               data.metrics.response_time_ms ||
                               data.metrics.latency_ms ||
                               data.metrics.message_latency;

                if (latency) {
                    updateLatencyDisplay(latency);
                    console.log(`[WS_HEALTH_UPDATE] Latency updated from metrics: ${latency}ms`);
                }
            }

            // Also try to parse content if it exists (for backward compatibility)
            if (data.content) {
                try {
                    const healthData = JSON.parse(data.content);
                    updateSystemHealthStatus(healthData);
                } catch (e) {
                    console.warn('Could not parse health update content:', e);
                }
            }
            break;
        case 'command_response':
            const msgLevel = data.status === 'success' ? 'success' : 'error';
            addSystemMessage(`Cmd Resp (${data.command_type}): ${data.status} - ${data.message || ''}`, msgLevel);

            // Handle ROI update from SET_ROI_ABSOLUTE success
            if (data.command_type === 'SET_ROI_ABSOLUTE' && data.status === 'success') {
                // Expect backend to send new ROI in data field or a separate roi_update message
                if (data.data) {
                    if (Array.isArray(data.data.roi) && data.data.roi.length === 4) {
                        state.currentROI = data.data.roi;
                        updateROIDisplay();
                    } else if (data.data.x1 !== undefined && data.data.y1 !== undefined && data.data.x2 !== undefined && data.data.y2 !== undefined) {
                        state.currentROI = [data.data.x1, data.data.y1, data.data.x2, data.data.y2];
                        updateROIDisplay();
                    } else {
                        // If not in command_response, wait for a dedicated 'roi_update' message
                        addSystemMessage('SET_ROI_ABSOLUTE success, waiting for roi_update message for confirmation.', 'info');
                    }
                }
            }
            break;
        case 'roi_update':
            if (data.roi && Array.isArray(data.roi) && data.roi.length === 4) {
                state.currentROI = data.roi;
                updateROIDisplay();
                addSystemMessage('ROI updated from backend via roi_update message.', 'info');
            } else if (data.x1 !== undefined && data.y1 !== undefined && data.x2 !== undefined && data.y2 !== undefined){
                 state.currentROI = [data.x1, data.y1, data.x2, data.y2];
                 updateROIDisplay();
                 addSystemMessage('ROI updated from backend via roi_update message (object format).', 'info');
            }
            break;
        case 'system_message':
            addSystemMessage(data.message, data.level || 'info');
            break;

        case 'position_summary_update':
            console.log('%c[WS POSITION_SUMMARY_UPDATE]', 'color: teal; font-weight: bold;', 'Data:', data);
            if (data.summary) {
                // Handle bootstrap flag for initial data load
                const isBootstrap = data.summary.is_bootstrap || false;
                const correlationId = data.summary.bootstrap_correlation_id || 'unknown';

                if (isBootstrap) {
                    console.log(`[WS POSITION_SUMMARY_UPDATE] Bootstrap data received (correlation: ${correlationId})`);
                    addSystemMessage('🚀 Bootstrap: Position summary loaded', 'success');
                } else {
                    addSystemMessage('📊 Position summary updated', 'info');
                }

                // Update position summary UI elements
                updatePositionSummaryUI(data.summary);
            } else if (data.market_data_missing) {
                addSystemMessage('Position summary missing market data. Requesting update...', 'warning');
            }
            break;

        case 'account_summary_update':
            console.log('%c[WS ACCOUNT_SUMMARY_UPDATE]', 'color: purple; font-weight: bold;', 'Data:', data);
            if (data.account) {
                // Handle bootstrap flag for initial data load
                const isBootstrap = data.account.is_bootstrap || false;
                const correlationId = data.account.bootstrap_correlation_id || 'unknown';

                if (isBootstrap) {
                    console.log(`[WS ACCOUNT_SUMMARY_UPDATE] Bootstrap data received (correlation: ${correlationId})`);
                    addSystemMessage('🚀 Bootstrap: Account summary loaded', 'success');
                } else {
                    addSystemMessage('💰 Account summary updated', 'info');
                }

                // Update account summary UI elements
                updateAccountSummaryUI(data.account);
            }
            break;

        // =============================================================================
        // FIX 5: WEBSOCKET MESSAGE HANDLING
        // =============================================================================
        // Simplified WebSocket handler for trades_update:
        case 'trades_update':
            if (data.payload && data.payload.trades && Array.isArray(data.payload.trades)) {
                const isBootstrap = data.payload.is_bootstrap || false;

                if (isBootstrap) {
                    console.log(`[WS TRADES_UPDATE] Bootstrap data: ${data.payload.trades.length} positions`);
                    addSystemMessage(`🚀 Bootstrap: Loaded ${data.payload.trades.length} positions`, 'success');
                }

                updateActiveTrades(data.payload.trades);

            } else if (data.trades && Array.isArray(data.trades)) {
                console.log('[WS TRADES_UPDATE] Fallback: using data.trades');
                updateActiveTrades(data.trades);
            } else {
                console.error('[WS TRADES_UPDATE] Invalid data structure:', data);
                addSystemMessage('❌ Invalid trades data received', 'error');
            }
            break;

        case 'market_data_update':
            // Lightweight price-only update - no DOM rebuild
            if (data.symbol) {
                updatePositionPrices(data);
            }
            break;
            
        case 'position_update':
            // Single position update without market data
            if (data.symbol && data.position) {
                // Update internal state
                const existingIndex = state.activeTrades.findIndex(t => t.symbol === data.symbol);
                if (data.is_open) {
                    if (existingIndex >= 0) {
                        // Preserve market data from existing position
                        const existing = state.activeTrades[existingIndex];
                        data.position.bid_price = existing.bid_price;
                        data.position.ask_price = existing.ask_price;
                        data.position.last_price = existing.last_price;
                        state.activeTrades[existingIndex] = data.position;
                    } else {
                        state.activeTrades.push(data.position);
                    }
                } else {
                    // Remove closed position
                    if (existingIndex >= 0) {
                        state.activeTrades.splice(existingIndex, 1);
                    }
                }
                // Rebuild display
                updateActiveTrades(state.activeTrades);
            }
            break;

        case 'orders_update':
            // The payload structure from gui_backend.py's handle_order_batch_update_message:
            // data = {
            //   "type": "orders_update",
            //   "orders": [list of formatted order objects],
            //   "total_orders": ...,
            //   "is_bootstrap": bool,
            //   "bootstrap_correlation_id": string
            // }
            console.log('%c[WS ORDERS_UPDATE]', 'color: orange; font-weight: bold;', 'Data:', data);

            if (data.orders && Array.isArray(data.orders)) {
                const isBootstrapOrders = data.is_bootstrap === true;
                const correlationId = data.bootstrap_correlation_id || 'unknown';

                console.log(`[WS ORDERS_UPDATE] Processing ${data.orders.length} orders. Bootstrap: ${isBootstrapOrders}`);

                if (isBootstrapOrders) {
                    console.log(`[WS ORDERS_UPDATE] Bootstrap data received (correlation: ${correlationId})`);
                    // For bootstrap, replace the entire open orders state
                    if (!tradeState.openOrders) {
                        tradeState.openOrders = []; // Initialize if not exists
                    }
                    tradeState.openOrders = data.orders; // Replace with bootstrap data
                    addSystemMessage(`📋 Bootstrap: Loaded ${data.orders.length} open orders`, 'success');
                } else {
                    console.log(`[WS ORDERS_UPDATE] Live update received: ${data.orders.length} orders`);
                    // For live updates, merge or replace as appropriate
                    if (!tradeState.openOrders) {
                        tradeState.openOrders = [];
                    }
                    tradeState.openOrders = data.orders; // For now, replace entirely
                    addSystemMessage(`📋 Orders updated: ${data.orders.length} open orders`, 'info');
                }

                // Update orders UI - for now, log the structure and add to system messages
                if (data.orders.length > 0) {
                    console.log('[WS ORDERS_UPDATE] First order structure:', JSON.parse(JSON.stringify(data.orders[0])));
                    // Add orders to historical trades display temporarily (until dedicated orders UI is implemented)
                    data.orders.forEach(order => {
                        // Convert order format to trade format for display
                        const tradeForDisplay = {
                            trade_id: order.order_id || order.local_order_id,
                            symbol: order.symbol,
                            action: order.side || 'ORDER',
                            status: order.status || 'PENDING',
                            quantity: order.quantity || 0,
                            price: order.price || 0,
                            timestamp: order.timestamp || Date.now(),
                            pnl: 0, // Orders don't have P&L
                            is_open_order: true, // Flag to distinguish from trades
                            order_type: order.order_type,
                            filled_quantity: order.filled_quantity || 0,
                            remaining_quantity: order.remaining_quantity || order.quantity
                        };
                        // Note: This is a temporary solution until dedicated orders UI is implemented
                        console.log('[WS ORDERS_UPDATE] Order converted for display:', tradeForDisplay);
                    });
                }

                // TODO: Implement dedicated updateOpenOrdersUI function
                // updateOpenOrdersUI(data.orders);
                console.log(`[WS ORDERS_UPDATE] Open orders state updated. Total: ${tradeState.openOrders.length}`);

            } else {
                console.error('[WS ORDERS_UPDATE] Invalid orders data structure!', 'Data received:', data);
                addSystemMessage('❌ Error: Received invalid orders_update data structure from backend.', 'error');
            }
            break;

        // Handle individual order updates if they come through a different type
        case 'order_status_update': // Singular, for individual live updates
            console.log('%c[WS ORDER_STATUS_UPDATE]', 'color: darkorange; font-weight: bold;', 'Data:', data);

            if (data.payload) { // Assuming payload contains a single order object
                console.log("[WS ORDER_STATUS_UPDATE] Processing single order update.", data.payload);

                // ENHANCED: Check if this is a rejected/failed order
                const orderHandled = handleRejectedTrade(data.payload);

                if (!orderHandled) {
                    // Update or add this single order in the open orders state
                    if (!tradeState.openOrders) {
                        tradeState.openOrders = [];
                    }

                    // Find existing order by ID and update, or add if new
                    const orderId = data.payload.order_id || data.payload.local_order_id;
                    const classification = classifyTradeStatus(data.payload);

                    if (classification.shouldShowInOpen) {
                        const existingIndex = tradeState.openOrders.findIndex(order =>
                            (order.order_id === orderId) || (order.local_order_id === orderId)
                        );

                        if (existingIndex >= 0) {
                            // Update existing order
                            tradeState.openOrders[existingIndex] = { ...tradeState.openOrders[existingIndex], ...data.payload };
                            console.log(`[WS ORDER_STATUS_UPDATE] Updated existing order: ${orderId}`);
                        } else {
                            // Add new order
                            tradeState.openOrders.push(data.payload);
                            console.log(`[WS ORDER_STATUS_UPDATE] Added new order: ${orderId}`);
                        }
                    } else {
                        // Remove closed order from open orders
                        tradeState.openOrders = tradeState.openOrders.filter(order =>
                            (order.order_id !== orderId) && (order.local_order_id !== orderId)
                        );
                        console.log(`[WS ORDER_STATUS_UPDATE] Removed closed order: ${orderId}`);
                    }

                    const statusColor = classification.category === 'closed' ? 'warning' : 'info';
                    addSystemMessage(`📋 Order status update: ${data.payload.symbol} ${classification.displayStatus}`, statusColor);
                }

                // 🚨 CHUNK 16.3: Re-render active trades to update hierarchies if an order linked to a position changed
                updateActiveTrades(state.active_trades);

                // Also update dedicated orders UI if available
                if (typeof updateOpenOrdersUI === 'function') {
                    updateOpenOrdersUI(tradeState.openOrders);
                }

            } else {
                console.error('[WS ORDER_STATUS_UPDATE] Invalid payload structure.', data);
                addSystemMessage('❌ Error: Received invalid order status update data.', 'error');
            }
            break;

        case 'trade_history_update':
            console.log("🎯 Inside trade_history_update case", data.trades);
            console.log("🔍 data.trades exists:", !!data.trades);
            console.log("🔍 data.trades is array:", Array.isArray(data.trades));
            console.log("🔍 data.trades content:", data.trades);

            if (data.trades) {
                // Backend sends flat list. The new addHistoricalTrade and updateHistoricalTradesDisplay
                // will handle grouping and rendering.
                // Check if this is an incremental update (1-2 trades) or bulk load
                const isIncrementalUpdate = data.trades.length <= 2 && !data.isBootstrap;
                
                if (isIncrementalUpdate) {
                    // For incremental updates (like rejections), add to existing trades
                    console.log("📈 Incremental trade history update, adding to existing trades");
                    data.trades.forEach(t => {
                        let rawTimestamp = t.timestamp || 0;
                        let jsTimestamp = rawTimestamp;
                        
                        addHistoricalTrade({
                            ...t,
                            timestamp: jsTimestamp,
                            pnl: t.pnl || t.realized_pnl || 0
                        });
                    });
                } else {
                    // For bulk updates, replace the entire array
                    console.log("📊 Bulk trade history update, replacing all trades");
                    tradeState.historicalTrades = data.trades.map(t => {
                    let rawTimestamp = t.timestamp || 0; // Original timestamp from backend
                    let jsTimestamp = rawTimestamp;
                    // Your backend for trade_history_update already converts to MS:
                    // "timestamp": trade.get("timestamp", 0) * 1000
                    // So, jsTimestamp should already be in MS here.

                    // LOGGING:
                    console.log(`[HIST_MAP] Original ts: ${rawTimestamp}, JS ts for Date(): ${jsTimestamp}, Date obj: ${new Date(jsTimestamp).toUTCString()}, DateString: ${new Date(jsTimestamp).toDateString()}`);

                    return {
                        ...t,
                        timestamp: jsTimestamp, // Store the JS-compatible millisecond timestamp
                        pnl: t.pnl || t.realized_pnl || 0 // Normalize P&L
                    };
                });
                    console.log("📞 Calling updateHistoricalTradesDisplay, tradeState.historicalTrades is now:", tradeState.historicalTrades);
                    updateHistoricalTradesDisplay();
                    addSystemMessage(`Trade history loaded: ${data.trades.length} trades for ${data.date || 'current view'}`, 'success');
                }
            } else {
                console.log("❌ No trades data found in trade_history_update message");
            }
            break;

        case 'trade_opened':
            if (data.trade) {
                let timestamp = data.trade.open_timestamp || data.trade.timestamp || (Date.now()/1000);
                if (timestamp > **********000) timestamp /= 1000;
                addHistoricalTrade({
                    // Map all available fields from data.trade
                    trade_id: data.trade.trade_id || `open-${Date.now()}`,
                    symbol: data.trade.symbol,
                    action: data.trade.side ? data.trade.side.toUpperCase() : 'OPEN',
                    status: 'OPEN', // Or 'PARTIALLY_FILLED' if applicable
                    quantity: data.trade.quantity,
                    price: data.trade.open_price || data.trade.price || 0,
                    timestamp: timestamp,
                    is_parent: data.trade.is_parent !== undefined ? data.trade.is_parent : true, // Assume opened is parent unless specified
                    is_child: data.trade.is_child || false,
                    parent_trade_id: data.trade.parent_trade_id,
                    strategy: data.trade.strategy || 'N/A',
                    pnl: 0, // Usually 0 for opened trade
                    // Additional enriched fields
                    order_id: data.trade.order_id,
                    local_order_id: data.trade.local_order_id,
                    broker_order_id: data.trade.broker_order_id,
                    commission: data.trade.commission || 0,
                    order_type: data.trade.order_type || 'MARKET',
                    time_in_force: data.trade.time_in_force || 'DAY',
                    exchange: data.trade.exchange,
                    account: data.trade.account,
                    ocr_confidence: data.trade.ocr_confidence,
                    signal_to_fill_latency_ms: data.trade.signal_to_fill_latency_ms,
                    order_to_fill_latency_ms: data.trade.order_to_fill_latency_ms,
                    fill_duration_ms: data.trade.fill_duration_ms,
                    correlation_id: data.trade.correlation_id,
                    source_component: data.trade.source_component || 'TradingEngine'
                });
                addSystemMessage(`Trade opened: ${data.trade.symbol} ${data.trade.side} ${data.trade.quantity}`, 'success');
                // Request updates if needed
                // setTimeout(() => requestPositionSummary(), 1000);
                // setTimeout(() => requestPositionUpdate(), 1200);
            }
            break;

        case 'trade_closed':
            if (data.trade) {
                let timestamp = data.trade.close_timestamp || data.trade.timestamp || (Date.now()/1000);
                if (timestamp > **********000) timestamp /= 1000;
                addHistoricalTrade({
                    trade_id: data.trade.trade_id || `close-${Date.now()}`,
                    symbol: data.trade.symbol,
                    action: 'CLOSE',
                    status: 'CLOSED', // Or 'FILLED' if it represents the closing fill
                    quantity: data.trade.quantity, // Usually the closed quantity
                    price: data.trade.close_price || data.trade.price || 0,
                    timestamp: timestamp,
                    pnl: data.trade.realized_pnl || 0,
                    is_parent: data.trade.is_parent,
                    is_child: data.trade.is_child,
                    parent_trade_id: data.trade.parent_trade_id, // Important for grouping
                    strategy: data.trade.strategy || 'N/A',
                    // Additional enriched fields
                    realized_pnl: data.trade.realized_pnl || 0,
                    unrealized_pnl: data.trade.unrealized_pnl || 0,
                    order_id: data.trade.order_id,
                    local_order_id: data.trade.local_order_id,
                    broker_order_id: data.trade.broker_order_id,
                    commission: data.trade.commission || data.trade.total_commission || 0,
                    order_type: data.trade.order_type || 'MARKET',
                    time_in_force: data.trade.time_in_force || 'DAY',
                    exchange: data.trade.exchange,
                    account: data.trade.account,
                    average_price: data.trade.average_price || data.trade.close_price || data.trade.price || 0,
                    total_fills_count: data.trade.total_fills_count || 1,
                    close_reason: data.trade.close_reason || 'Manual',
                    signal_to_fill_latency_ms: data.trade.signal_to_fill_latency_ms,
                    order_to_fill_latency_ms: data.trade.order_to_fill_latency_ms,
                    fill_duration_ms: data.trade.fill_duration_ms,
                    trade_duration_ms: data.trade.trade_duration_ms,
                    correlation_id: data.trade.correlation_id,
                    source_component: data.trade.source_component || 'TradingEngine'
                });
                const pnlText = formatCurrency(data.trade.realized_pnl || 0);
                addSystemMessage(`Trade closed: ${data.trade.symbol} P&L ${pnlText}`, (data.trade.realized_pnl || 0) >= 0 ? 'success' : 'warning');
                // Request updates
                // setTimeout(() => {
                //     requestPositionSummary();
                //     requestAccountSummary();
                //     requestPositionUpdate();
                // }, 1000);
            }
            break;

        case 'order_fill_update':
            if (data.fill || data.order) {
                const fillData = data.fill || data.order;
                let timestamp = fillData.fill_timestamp || fillData.timestamp || (Date.now()/1000);
                if (timestamp > **********000) timestamp /= 1000;

                // 🚨 CHUNK 16.3: Update the status of the corresponding order in tradeState.openOrders
                const orderIdToUpdate = fillData.order_id || fillData.local_order_id;
                let affectedOrderFound = false;
                if (tradeState.openOrders && orderIdToUpdate) {
                    tradeState.openOrders = tradeState.openOrders.map(order => {
                        if (order.order_id === orderIdToUpdate || order.local_order_id === orderIdToUpdate) {
                            affectedOrderFound = true;
                            const newFilledQty = (order.filled_quantity || 0) + (fillData.filled_quantity || fillData.quantity || 0);
                            const newRemainingQty = Math.max(0, (order.quantity || 0) - newFilledQty);

                            return {
                                ...order,
                                filled_quantity: newFilledQty,
                                avg_fill_price: fillData.fill_price || fillData.price || order.avg_fill_price,
                                status: fillData.status || (newRemainingQty === 0 ? 'FILLED' : 'PARTIALLY_FILLED'),
                                remaining_quantity: newRemainingQty,
                                last_update_timestamp: timestamp
                            };
                        }
                        return order;
                    });

                    // Remove fully filled orders from open orders if they should be closed
                    if (affectedOrderFound) {
                        const updatedOrder = tradeState.openOrders.find(order =>
                            order.order_id === orderIdToUpdate || order.local_order_id === orderIdToUpdate
                        );
                        if (updatedOrder && updatedOrder.status === 'FILLED') {
                            tradeState.openOrders = tradeState.openOrders.filter(order =>
                                order.order_id !== orderIdToUpdate && order.local_order_id !== orderIdToUpdate
                            );
                            console.log(`[WS ORDER_FILL_UPDATE] Removed fully filled order: ${orderIdToUpdate}`);
                        }
                    }
                }

                addHistoricalTrade({
                    trade_id: fillData.order_id || fillData.trade_id || `fill-${Date.now()}`,
                    symbol: fillData.symbol,
                    action: fillData.side ? fillData.side.toUpperCase() : 'FILL',
                    status: fillData.status || 'FILLED',
                    quantity: fillData.filled_quantity || fillData.quantity || 0,
                    price: fillData.fill_price || fillData.price || 0,
                    timestamp: timestamp,
                    pnl: fillData.realized_pnl || 0,
                    is_parent: fillData.is_parent || false, // Usually fills are children
                    is_child: fillData.is_child !== undefined ? fillData.is_child : true,
                    parent_trade_id: fillData.parent_trade_id || fillData.parent_order_id,
                    strategy: fillData.strategy,
                    commission: fillData.commission || 0,
                    filled_quantity: fillData.filled_quantity,
                    fill_price: fillData.fill_price,
                    signal_to_fill_latency_ms: fillData.signal_to_fill_latency_ms,
                    order_to_fill_latency_ms: fillData.order_to_fill_latency_ms,
                    fill_duration_ms: fillData.fill_duration_ms
                });

                const fillQty = fillData.filled_quantity || fillData.quantity || 0;
                const fillPrice = fillData.fill_price || fillData.price || 0;
                addSystemMessage(`💰 Order fill: ${fillData.symbol} ${fillQty} @ ${formatCurrency(fillPrice)}`, 'info');

                // 🚨 CHUNK 16.3: Re-render active trades if an open order was affected by the fill
                if (affectedOrderFound) {
                    updateActiveTrades(state.active_trades);
                    if (typeof updateOpenOrdersUI === 'function') {
                        updateOpenOrdersUI(tradeState.openOrders);
                    }
                }
            }
            break;

        case 'core_health_message':
            handle_core_health_message(data);
            break;

        case 'core_status_message':
            handle_core_status_message(data);
            break;

        default:
            console.log('Unknown WebSocket message type:', data.type);
            break;
    }

    // FALLBACK: Check for latency data in any message type
    if (data.latency_ms && data.latency_ms > 0) {
        updateLatencyDisplay(data.latency_ms);
    } else if (data.timestamp && data.received_timestamp) {
        // Calculate latency from timestamps if available
        const calculatedLatency = data.received_timestamp - data.timestamp;
        if (calculatedLatency > 0 && calculatedLatency < 10000) { // Reasonable latency range
            updateLatencyDisplay(calculatedLatency);
        }
    }
}

// UPDATED: Enhanced core health message handler
async function handle_core_health_message(data) {
    if (!data.payload) return;

    const payload = data.payload;
    const component = payload.component || "ApplicationCore";
    const status = payload.status || "unknown";
    const timestamp = payload.timestamp || Date.now() / 1000;

    // Filter out old messages
    const current_time = Date.now();
    const message_age = current_time - (timestamp * 1000); // Convert to ms
    if (message_age > 30000) { // 30 seconds
        console.debug(`Ignoring old core health message (age: ${message_age / 1000}s)`);
        return;
    }

    // Use high-priority source for Redis health messages
    updateStatusPill('core', status, `Core: ${status}`, 'redis_stream', 0);

    // FIXED: Update lastHealthUpdate for healthy status
    if (status === 'healthy') {
        state.lastHealthUpdate = Date.now();
    }
}

// UPDATED: Enhanced core status message handler
async function handle_core_status_message(data) {
    if (!data.payload) return;

    const payload = data.payload;
    const status_event = payload.event || "UNKNOWN_EVENT";

    // Map status events to health status
    let health_status = 'error';
    if (status_event.includes("RUNNING") || status_event.includes("STARTED") || status_event === "OCR_ACTIVE") {
        health_status = 'healthy';
    } else if (status_event.includes("STOPPING") || status_event.includes("DEGRADED")) {
        health_status = 'warning';
    }

    // Use independent manager for status events
    if (health_status === 'healthy') {
        coreStatusManager.updateHealth();
    } else {
        statusPills.core.update(health_status, `Core: ${status_event}`, true);
    }
}

function startSystemHealthPolling() {
    // DISABLED: This was causing race conditions with the independent pill managers
    // Each status pill now manages its own state independently
    // CoreStatusManager handles core health checks
    // Other pills are updated via WebSocket messages
    return;
    
    /* Original code commented out to prevent race conditions
    // Poll system health every 10 seconds
    if (state.healthPollingInterval) {
        clearInterval(state.healthPollingInterval);
    }

    state.healthPollingInterval = setInterval(async () => {
        if (!state.connected) return;
        try {
            await fetchHealthStatus();
        } catch (error) {
            console.warn('Health polling error:', error);
            // Don't spam error messages for health polling failures
        }
    }, APP_CONFIG.healthPollingIntervalMs);

    // Initial health check
    setTimeout(async () => {
        if (state.connected) {
            try {
                await fetchHealthStatus();
            } catch (error) {
                console.warn('Initial health check error:', error);
            }
        }
    }, 1000);
    */
}

// Note: The actual ws.onopen, onmessage, onclose, onerror handlers inside connectWebSocket
// are part of this conceptual module. We will refactor them to be cleaner methods
// of an object later if needed.
// --- END WebSocket Manager ---



// --- Event Handlers & Initialization ---
// Functions triggered by user interactions and page load setup.

function toggleOCR() {
    const btn = document.getElementById('ocrToggle');

    // Prevent rapid clicking - check if button is already disabled
    if (btn.disabled) {
        addSystemMessage('OCR command already in progress, please wait...', 'warning');
        return;
    }

    const command = state.ocrActive ? 'stop_ocr' : 'start_ocr';

    // Set button to loading state
    btn.disabled = true;
    btn.textContent = state.ocrActive ? 'Stopping...' : 'Starting...';
    btn.className = 'btn btn-warning';

    sendCommand(command).then(() => {
        addSystemMessage(`OCR ${state.ocrActive ? 'stop' : 'start'} command sent`, 'info');
        // Button state will be updated by Redis status signals
    }).catch(err => {
        console.error("Failed to send toggle OCR command", err);
        addSystemMessage(`Failed to ${state.ocrActive ? 'stop' : 'start'} OCR`, 'error');
        // Reset button on error
        updateOCRButtonState(state.ocrActive);
    });
}



async function manualAdd() {
    const originalText = 'Manual Add';
    setButtonState('manualAddBtn', 'loading', originalText);

    try {
        const sharesEl = document.getElementById('manualShares');
        if (!sharesEl) {
            addSystemMessage('Manual shares field not found', 'error');
            setButtonState('manualAddBtn', 'error', originalText);
            return;
        }

        const shares = sharesEl.value.trim();

        // Validate input is not empty
        if (!shares) {
            addSystemMessage('Share quantity is required', 'error');
            setButtonState('manualAddBtn', 'error', originalText);
            return;
        }

        // Validate input is numeric
        if (!/^\d+$/.test(shares)) {
            addSystemMessage('Share quantity must be a positive integer', 'error');
            setButtonState('manualAddBtn', 'error', originalText);
            return;
        }

        const sharesInt = parseInt(shares, 10);

        if (sharesInt <= 0) {
            addSystemMessage('Invalid share quantity - must be greater than 0', 'error');
            setButtonState('manualAddBtn', 'error', originalText);
            return;
        }

        if (sharesInt > APP_CONFIG.maxSharesLimit) {
            addSystemMessage(`Share quantity too large - maximum ${APP_CONFIG.maxSharesLimit.toLocaleString()} shares`, 'error');
            setButtonState('manualAddBtn', 'error', originalText);
            return;
        }

        // Check if connected
        if (!state.connected) {
            addSystemMessage('Cannot execute manual trade - not connected to backend', 'error');
            setButtonState('manualAddBtn', 'error', originalText);
            return;
        }

        // Save manual shares value to config (async, but don't wait)
        updateConfig('manual_shares', sharesInt);

        addSystemMessage(`Submitting manual ADD trade: ${sharesInt} shares...`, 'info');

        // Execute the manual trade using API client
        const result = await executeManualTrade('add', sharesInt, null);

        if (result.success) {
            addSystemMessage(`✅ Manual ADD trade sent (ID: ${result.data.command_id?.substring(0, 8) || 'N/A'})`, 'success');
            addSystemMessage(`📊 ${sharesInt} shares → ${result.data.target_service || 'CORE'}`, 'info');
            setButtonState('manualAddBtn', 'success', originalText);
        } else {
            addSystemMessage(`❌ Manual trade failed: ${result.data?.detail || result.data?.message || result.error || 'Unknown error'}`, 'error');
            setButtonState('manualAddBtn', 'error', originalText);
        }

    } catch (error) {
        addSystemMessage(`Manual trade error: ${error.message}`, 'error');
        console.error('Manual trade error:', error);
        setButtonState('manualAddBtn', 'error', originalText);
    }
}

async function forceClose() {
    const originalText = 'Force Close';

    try {
        // Check if connected first
        if (!state.connected) {
            addSystemMessage('Cannot execute force close - not connected to backend', 'error');
            return;
        }

        setButtonState('forceCloseBtn', 'loading', originalText);
        addSystemMessage('Initiating FORCE CLOSE ALL...', 'warning');

        // Execute force close using API client
        const result = await executeForceCloseAll('Manual GUI Force Close Button');

        if (result.success) {
            addSystemMessage(`FORCE CLOSE ALL command sent successfully (ID: ${result.data.command_id?.substring(0, 8) || 'N/A'})`, 'success');
            addSystemMessage(`System shutdown initiated - all positions will be closed`, 'warning');
            addSystemMessage(`Command routed to ${result.data.target_service || 'CORE'} for execution`, 'info');
            setButtonState('forceCloseBtn', 'success', originalText);
        } else {
            addSystemMessage(`Force close failed: ${result.data?.detail || result.data?.message || result.error || 'Unknown error'}`, 'error');
            setButtonState('forceCloseBtn', 'error', originalText);
        }

    } catch (error) {
        addSystemMessage(`Force close error: ${error.message}`, 'error');
        console.error('Force close error:', error);
        setButtonState('forceCloseBtn', 'error', originalText);
    }
}

async function emergencyStop() {
    const originalText = 'Emergency';

    try {
        // Emergency stop should work even if not connected (try anyway)
        const isConnected = state.connected;

        setButtonState('emergencyBtn', 'loading', originalText);

        if (!isConnected) {
            addSystemMessage('Not connected to backend - attempting emergency stop anyway...', 'warning');
        }

        addSystemMessage('EMERGENCY STOP INITIATED - SHUTTING DOWN SYSTEM!', 'error');

        // Execute emergency stop using API client
        const result = await executeEmergencyStop('Manual GUI Emergency Stop Button');

        if (result.success) {
            addSystemMessage(`EMERGENCY STOP command sent (ID: ${result.data.command_id?.substring(0, 8) || 'N/A'})`, 'error');
            addSystemMessage(`SYSTEM SHUTDOWN IN PROGRESS - ALL TRADING HALTED`, 'error');
            addSystemMessage(`Emergency command routed to ${result.data.target_service || 'CORE'}`, 'warning');
            setButtonState('emergencyBtn', 'success', originalText);
        } else {
            addSystemMessage(`Emergency stop command failed: ${result.data?.detail || result.data?.message || result.error || 'Unknown error'}`, 'error');
            addSystemMessage(`MANUAL INTERVENTION MAY BE REQUIRED!`, 'error');
            setButtonState('emergencyBtn', 'error', originalText);
        }

    } catch (error) {
        addSystemMessage(`Emergency stop error: ${error.message}`, 'error');
        addSystemMessage(`CRITICAL: Manual intervention required - check system status!`, 'error');
        console.error('Emergency stop error:', error);
        setButtonState('emergencyBtn', 'error', originalText);
    }
}

async function startCore() {
    addSystemMessage('Attempting to start TESTRADE Core...', 'info');
    try {
        const result = await sendCommand('start_testrade_core');
        if (result.success) {
            addSystemMessage('Core start command sent successfully', 'success');
        } else {
            addSystemMessage(`Core start failed: ${result.error || 'Unknown error'}`, 'error');
        }
    } catch (error) {
        addSystemMessage(`Core start error: ${error.message}`, 'error');
    }
}

function toggleRecording() {
    sendCommand('toggle_recording_mode');
    state.recordingActive = !state.recordingActive;
}

function toggleAllSymbols() {
    state.allSymbols = !state.allSymbols;
    sendCommand('toggle_all_symbols_mode', { enabled: state.allSymbols });
}

function toggleConfidenceMode() {
    state.confidenceMode = !state.confidenceMode;
    sendCommand('toggle_confidence_mode', {enabled: state.confidenceMode});
}

function toggleVideoMode() {
    state.videoMode = !state.videoMode;
    sendCommand('toggle_video_mode', {enabled: state.videoMode});
}



/**
 * Switches between raw and processed image modes
 * Supports both dropdown selectors and toggle buttons
 * @param {string} [targetMode] - Optional specific mode to switch to ('raw' or 'processed')
 */
function switchImageMode(targetMode = null) {
    let newMode;

    // Check if there's a dropdown selector first
    const imageModeDropdown = document.getElementById('imageMode');
    if (imageModeDropdown && !targetMode) {
        // Use dropdown value if it exists and no target mode specified
        newMode = imageModeDropdown.value;
    } else if (targetMode) {
        // Use specified target mode
        newMode = targetMode;
    } else {
        // Toggle between raw and processed if no dropdown and no target mode
        newMode = state.imageMode === 'raw' ? 'processed' : 'raw';
    }

    // Update state
    state.imageMode = newMode;

    // Display the appropriate image
    if (newMode === 'raw') {
        if (state.lastRawImage) {
            displayImage(state.lastRawImage);
            addSystemMessage('Switched to raw image mode', 'info');
        } else {
            addSystemMessage('Switched to raw image mode (no raw image available)', 'warning');
        }
    } else {
        if (state.lastProcessedImage) {
            displayImage(state.lastProcessedImage);
            addSystemMessage('Switched to processed image mode', 'info');
        } else {
            addSystemMessage('Switched to processed image mode (no processed image available)', 'warning');
        }
    }

    // Update button text if there's a toggle button
    const toggleButton = domManager.get('imageModeToggle') ||
                        document.getElementById('imageToggle') ||
                        document.getElementById('toggleImageMode') ||
                        document.getElementById('previewToggle');
    if (toggleButton) {
        // Button shows current mode, not the mode it will switch to
        toggleButton.textContent = newMode === 'raw' ? 'Raw' : 'Processed';
        toggleButton.title = `Currently showing ${newMode} image. Click to switch to ${newMode === 'raw' ? 'processed' : 'raw'}.`;
    }

    // Update dropdown if it exists
    if (imageModeDropdown) {
        imageModeDropdown.value = newMode;
    }
}

// Alias for backward compatibility and clearer intent
function toggleImageMode() {
    switchImageMode(); // This will toggle since no target mode is specified
}

// Debug function to check image state (only available in debug mode)
function debugImageState() {
    if (!APP_CONFIG.debugMode) {
        addSystemMessage('Debug mode disabled. Set APP_CONFIG.debugMode = true to enable.', 'warning');
        return;
    }

    console.log('=== IMAGE STATE DEBUG ===');
    console.log(`Current mode: ${state.imageMode}`);
    console.log(`Raw image available: ${!!state.lastRawImage} (${state.lastRawImage ? state.lastRawImage.length : 0} chars)`);
    console.log(`Processed image available: ${!!state.lastProcessedImage} (${state.lastProcessedImage ? state.lastProcessedImage.length : 0} chars)`);
    console.log(`Raw history: ${state.rawImageHistory.length} images`);
    console.log(`Processed history: ${state.processedImageHistory.length} images`);
    console.log('========================');

    addSystemMessage(`🔍 Debug - Mode: ${state.imageMode}, Raw: ${!!state.lastRawImage}, Processed: ${!!state.lastProcessedImage}`, 'info');
}

// Helper function for conditional debug logging
function debugLog(...args) {
    if (APP_CONFIG.debugMode) {
        console.log('[DEBUG]', ...args);
    }
}

// Memory usage monitoring
function checkMemoryUsage() {
    // CRITICAL FIX: Add history trimming to prevent memory leaks
    const MAX_HISTORY_ITEMS = APP_CONFIG.maxImageHistoryItems;
    if (state.rawImageHistory.length > MAX_HISTORY_ITEMS) {
        state.rawImageHistory = state.rawImageHistory.slice(-MAX_HISTORY_ITEMS);
        console.log(`🧹 Trimmed rawImageHistory to ${MAX_HISTORY_ITEMS} items`);
    }
    if (state.processedImageHistory.length > MAX_HISTORY_ITEMS) {
        state.processedImageHistory = state.processedImageHistory.slice(-MAX_HISTORY_ITEMS);
        console.log(`🧹 Trimmed processedImageHistory to ${MAX_HISTORY_ITEMS} items`);
    }

    // Existing memory calculation remains
    const rawMemory = state.rawImageHistory.reduce((total, img) => total + img.size, 0);
    const processedMemory = state.processedImageHistory.reduce((total, img) => total + img.size, 0);
    const totalMemoryMB = (rawMemory + processedMemory) / 1024 / 1024;

    debugLog(`Memory usage: ${totalMemoryMB.toFixed(1)}MB (Raw: ${(rawMemory / 1024 / 1024).toFixed(1)}MB, Processed: ${(processedMemory / 1024 / 1024).toFixed(1)}MB)`);

    if (totalMemoryMB > APP_CONFIG.memoryWarningThresholdMB) {
        console.warn(`High memory usage detected: ${totalMemoryMB.toFixed(1)}MB`);
        addSystemMessage(`⚠️ High memory usage: ${totalMemoryMB.toFixed(1)}MB (consider refreshing page)`, 'warning');
    }

    return { rawMemoryMB: rawMemory / 1024 / 1024, processedMemoryMB: processedMemory / 1024 / 1024, totalMemoryMB };
}

// Proactive memory cleanup function
function cleanupImageHistory() {
    const MAX_HISTORY_ITEMS = APP_CONFIG.maxImageHistoryItems;
    let cleaned = false;

    if (state.rawImageHistory.length > MAX_HISTORY_ITEMS) {
        const before = state.rawImageHistory.length;
        state.rawImageHistory = state.rawImageHistory.slice(-MAX_HISTORY_ITEMS);
        console.log(`🧹 Proactive cleanup: Trimmed rawImageHistory from ${before} to ${state.rawImageHistory.length} items`);
        cleaned = true;
    }

    if (state.processedImageHistory.length > MAX_HISTORY_ITEMS) {
        const before = state.processedImageHistory.length;
        state.processedImageHistory = state.processedImageHistory.slice(-MAX_HISTORY_ITEMS);
        console.log(`🧹 Proactive cleanup: Trimmed processedImageHistory from ${before} to ${state.processedImageHistory.length} items`);
        cleaned = true;
    }

    if (cleaned) {
        debugLog('🧹 Proactive image history cleanup completed');
    }
}

// Enhanced debug function with memory info
function debugMemoryState() {
    if (!APP_CONFIG.debugMode) {
        addSystemMessage('Debug mode disabled. Set APP_CONFIG.debugMode = true to enable.', 'warning');
        return;
    }

    const memoryInfo = checkMemoryUsage();
    console.log('=== MEMORY STATE DEBUG ===');
    console.log(`Total memory usage: ${memoryInfo.totalMemoryMB.toFixed(2)}MB`);
    console.log(`Raw images: ${state.rawImageHistory.length} (${memoryInfo.rawMemoryMB.toFixed(2)}MB)`);
    console.log(`Processed images: ${state.processedImageHistory.length} (${memoryInfo.processedMemoryMB.toFixed(2)}MB)`);
    console.log(`Memory threshold: ${APP_CONFIG.memoryWarningThresholdMB}MB`);
    console.log('==========================');

    addSystemMessage(`🧠 Memory - Total: ${memoryInfo.totalMemoryMB.toFixed(1)}MB, Raw: ${state.rawImageHistory.length}, Processed: ${state.processedImageHistory.length}`, 'info');
}

function expandROI() {
    const newRoiCoords = [50, 20, 450, 100]; // Example expanded ROI
    // Send command with parameters {x1, y1, x2, y2}
    sendCommand('set_roi_absolute', {
        x1: newRoiCoords[0],
        y1: newRoiCoords[1],
        x2: newRoiCoords[2],
        y2: newRoiCoords[3]
    });
    // state.currentROI = newRoiCoords; // Optimistic update (optional)
    // updateROIDisplay(); // Optimistic update (optional)
    addSystemMessage('Attempting to expand ROI via set_roi_absolute.', 'info');
}

function adjustROI(axis, direction, event = null) {
    const shiftHeld = event ? event.shiftKey : false;

    // Map axis+direction to edge and direction for individual edge control
    let edge = axis;  // x1, x2, y1, y2
    let roiDirection = '';

    if (direction === '-') {
        if (axis === 'x1' || axis === 'x2') roiDirection = 'left';
        else if (axis === 'y1' || axis === 'y2') roiDirection = 'up';
    } else if (direction === '+') {
        if (axis === 'x1' || axis === 'x2') roiDirection = 'right';
        else if (axis === 'y1' || axis === 'y2') roiDirection = 'down';
    }

    // Phase 5: Send ROI_ADJUST command with individual edge control
    sendCommand('roi_adjust', {
        edge: edge,           // Which edge to move (x1, x2, y1, y2)
        direction: roiDirection,  // Which direction to move it
        step_size: shiftHeld ? 5 : 1,  // Large or small step
        shift: shiftHeld
    });

    addSystemMessage(`ROI edge ${edge} adjusted ${roiDirection} (${shiftHeld ? 'large step' : 'small step'})`, 'info');
}



function resetOCRSettings() {
    // Reset all OCR sliders to default values
    document.getElementById('upscaleFactor').value = 3.0;
    document.getElementById('upscaleValue').textContent = '3.0';
    document.getElementById('unsharpStrength').value = 1.7;
    document.getElementById('unsharpValue').textContent = '1.7';
    document.getElementById('thresholdC').value = -6;
    document.getElementById('thresholdCValue').textContent = '-6';
    document.getElementById('redBoost').value = 1.0;
    document.getElementById('redBoostValue').textContent = '1.0';
    document.getElementById('greenBoost').value = 1.0;
    document.getElementById('greenBoostValue').textContent = '1.0';

    // Reset checkboxes
    document.getElementById('forceBlackText').checked = true;
    document.getElementById('textMaskCleaning').checked = true;
    document.getElementById('enhanceSmallSymbols').checked = true;

    addSystemMessage('OCR settings reset to defaults', 'info');

    // Apply the reset settings
    applyOCRSettings();
}

function togglePerfTracking() {
    addSystemMessage('Performance tracking toggle - implementation pending', 'info');
}

function exportPerfStats() {
    addSystemMessage('Performance stats export - implementation pending', 'info');
}

function resetTimeSync() {
    addSystemMessage('Time sync reset - implementation pending', 'info');
}

function testFunction() {
    addSystemMessage('Test function executed', 'info');
}

function createSession() {
    addSystemMessage('Session creation - implementation pending', 'info');
}

function loadSessionData() {
    addSystemMessage('Session data loading - implementation pending', 'info');
}

function startReplay() {
    addSystemMessage('Replay start - implementation pending', 'info');
}

function stopReplay() {
    addSystemMessage('Replay stop - implementation pending', 'info');
}

function exportTradeHistory() {
    addSystemMessage('Trade history export - implementation pending', 'info');
}

function clearTradeHistory() {
    if (confirm('Are you sure you want to clear trade history?')) {
        addSystemMessage('Trade history cleared - implementation pending', 'info');
    }
}

// --- END Trading Control Functions ---

// --- Configuration and Initialization Functions ---



function checkCurrentOCRStatus() {
    // Send a GET_OCR_STATUS command to check current state
    sendCommand('get_ocr_status', {}).catch(() => {
        // Fallback: assume OCR is inactive
        updateOCRButtonState(false);
    });
}

function updateDevelopmentMode() {
    const developmentMode = document.getElementById('developmentMode').checked;

    // Send development mode configuration to backend
    sendCommand('update_development_mode', {
        development_mode: developmentMode
    });

    // Auto-save to control.json via config endpoint
    updateConfig('development_mode', developmentMode);
    addSystemMessage('Saving development mode to control.json...', 'info');

    // Update status text
    const statusText = document.getElementById('developmentStatusText');
    if (developmentMode) {
        statusText.textContent = 'Active: 90% less storage, all tools still work';
        statusText.style.color = 'var(--accent-primary)';
        addSystemMessage('Development mode: Reduced storage volume', 'success');
    } else {
        statusText.textContent = 'Disabled: Full logging/storage (Large files!)';
        statusText.style.color = 'var(--accent-warning)';
        addSystemMessage('Full storage mode: Large files possible!', 'warning');
    }
}

function updateRecordingSettings() {
    const imageRecording = document.getElementById('enableImageRecording').checked;
    const rawOcrRecording = document.getElementById('enableRawOcrRecording').checked;
    const intelliSenseLogging = document.getElementById('enableIntelliSenseLogging').checked;
    const observabilityLogging = document.getElementById('enableObservabilityLogging').checked;

    // Send recording configuration to backend
    sendCommand('update_recording_settings', {
        enable_image_recording: imageRecording,
        enable_raw_ocr_recording: rawOcrRecording,
        enable_intellisense_logging: intelliSenseLogging,
        enable_observability_logging: observabilityLogging
    });

    // Auto-save each setting to control.json via config endpoint
    updateConfig('enable_image_recording', imageRecording);
    updateConfig('enable_raw_ocr_recording', rawOcrRecording);
    updateConfig('enable_intellisense_logging', intelliSenseLogging);
    updateConfig('enable_observability_logging', observabilityLogging);
    addSystemMessage('Saving recording settings to control.json...', 'info');

    // Update status text
    const statusText = document.getElementById('recordingStatusText');
    const activeSettings = [];
    if (imageRecording) activeSettings.push('Images');
    if (rawOcrRecording) activeSettings.push('Raw OCR');
    if (intelliSenseLogging) activeSettings.push('IntelliSense');
    if (observabilityLogging) activeSettings.push('Observability');

    if (activeSettings.length === 0) {
        statusText.textContent = 'Development Mode: No Recording';
        statusText.style.color = 'var(--accent-primary)';
    } else {
        statusText.textContent = `Recording: ${activeSettings.join(', ')}`;
        // Show red warning if observability is enabled (massive files)
        statusText.style.color = observabilityLogging ? 'var(--accent-danger)' :
                                (imageRecording ? 'var(--accent-warning)' : 'var(--text-muted)');
    }

    // Show warnings
    if (observabilityLogging) {
        addSystemMessage('⚠️ Observability logging enabled - GIGABYTE files possible!', 'error');
    } else if (imageRecording) {
        addSystemMessage('⚠️ Image recording enabled - High memory usage!', 'warning');
    }
}

function updateLoggingSettings() {
    const logLevel = document.getElementById('logLevel').value;
    const ocrDebugLogging = document.getElementById('enableOcrDebugLogging').checked;

    // Send logging configuration to backend
    sendCommand('update_logging_settings', {
        log_level: logLevel,
        enable_ocr_debug_logging: ocrDebugLogging
    });

    // Auto-save each setting to control.json via config endpoint
    updateConfig('log_level', logLevel);
    updateConfig('enable_ocr_debug_logging', ocrDebugLogging);
    addSystemMessage('⚠️ Saving logging settings to control.json...', 'info');

    // Update status text
    const statusText = document.getElementById('loggingStatusText');
    statusText.textContent = `Current: ${logLevel} level, OCR Debug ${ocrDebugLogging ? 'ON' : 'OFF'}`;

    // Color coding based on verbosity
    if (logLevel === 'DEBUG' || ocrDebugLogging) {
        statusText.style.color = 'var(--accent-warning)';
        addSystemMessage('⚠️ Verbose logging enabled - Large log files!', 'warning');
    } else if (logLevel === 'ERROR') {
        statusText.style.color = 'var(--accent-primary)';
        addSystemMessage('✅ Minimal logging enabled - Small log files', 'success');
    } else {
        statusText.style.color = 'var(--text-muted)';
    }
}

function toggleOCRSettings() {
    const collapsible = document.querySelector('.collapsible');
    const content = document.getElementById('ocrSettingsContent');
    state.ocrSettingsExpanded = !state.ocrSettingsExpanded;
    collapsible.classList.toggle('collapsed', !state.ocrSettingsExpanded);
    content.classList.toggle('expanded', state.ocrSettingsExpanded);
}

function toggleDevelopmentSettings() {
    const collapsibles = document.querySelectorAll('.collapsible');
    const developmentCollapsible = collapsibles[1]; // Second collapsible (Development Settings)
    const content = document.getElementById('developmentSettingsContent');
    state.developmentSettingsExpanded = !state.developmentSettingsExpanded;
    developmentCollapsible.classList.toggle('collapsed', !state.developmentSettingsExpanded);
    content.classList.toggle('expanded', state.developmentSettingsExpanded);
}

function setupConfigAutoSave() {
    // Trading configuration - these use the /config endpoint (ONLY for basic trading config)
    document.getElementById('initialShares').addEventListener('change', () => onConfigChange('initialShares', 'initial_share_size'));
    document.getElementById('addType').addEventListener('change', () => onConfigChange('addType', 'add_type'));
    document.getElementById('reducePercent').addEventListener('change', () => onConfigChange('reducePercent', 'reduce_percentage'));
    document.getElementById('manualShares').addEventListener('change', () => onConfigChange('manualShares', 'manual_shares'));

    // OCR configuration - MUST use the command system, NOT direct config!
    // These go through: GUI → Backend → Redis → Babysitter → ApplicationCore → OCR Service
    document.getElementById('upscaleFactor').addEventListener('change', () => applyOCRSettings());
    document.getElementById('unsharpStrength').addEventListener('change', () => applyOCRSettings());
    document.getElementById('thresholdC').addEventListener('change', () => applyOCRSettings());
    document.getElementById('redBoost').addEventListener('change', () => applyOCRSettings());
    document.getElementById('greenBoost').addEventListener('change', () => applyOCRSettings());
    document.getElementById('textMaskMinContourArea').addEventListener('change', () => applyOCRSettings());
    document.getElementById('textMaskMinWidth').addEventListener('change', () => applyOCRSettings());
    document.getElementById('textMaskMinHeight').addEventListener('change', () => applyOCRSettings());
    document.getElementById('symbolMaxHeight').addEventListener('change', () => applyOCRSettings());
    document.getElementById('periodCommaRatioMin').addEventListener('change', () => applyOCRSettings());
    document.getElementById('periodCommaRatioMax').addEventListener('change', () => applyOCRSettings());
    document.getElementById('periodCommaRadius').addEventListener('change', () => applyOCRSettings());
    document.getElementById('hyphenMinRatio').addEventListener('change', () => applyOCRSettings());
    document.getElementById('hyphenMinHeight').addEventListener('change', () => applyOCRSettings());

    // OCR checkboxes - MUST use the command system, NOT direct config!
    document.getElementById('forceBlackText').addEventListener('change', () => applyOCRSettings());
    document.getElementById('textMaskCleaning').addEventListener('change', () => applyOCRSettings());
    document.getElementById('enhanceSmallSymbols').addEventListener('change', () => applyOCRSettings());
}

function onConfigChange(elementId, configKey) {
    const element = domManager.get(elementId);
    if (element) {
        let value;

        // Handle different input types properly
        if (element.type === 'checkbox') {
            value = element.checked;
        } else if (element.type === 'number' || element.type === 'range') {
            // Handle numeric inputs and range sliders
            if (configKey.includes('_c') || configKey.includes('_area') || configKey.includes('_width') ||
                configKey.includes('_height') || configKey.includes('_radius') || configKey.includes('_size') ||
                configKey.includes('block_size') || configKey.includes('manual_shares') || configKey.includes('initial_share_size')) {
                value = parseInt(element.value);
            } else {
                value = parseFloat(element.value);
            }
        } else {
            value = element.value;
        }

        // Use command system for trading parameters
        if (['initial_share_size', 'add_type', 'reduce_percentage', 'manual_shares'].includes(configKey)) {
            // Use the command system for proper config updates
            const params = {};
            params[configKey] = value;
            
            sendCommand('update_trading_params', params).then(response => {
                if (response && response.success) {
                    // Success - show simple confirmation
                    addSystemMessage(`✅ ${configKey} saved`, 'success');
                } else {
                    // Failure already logged by sendCommand
                }
            });
        } else {
            // For other config keys, still use the old endpoint (this should be migrated too)
            updateConfig(configKey, value);
            addSystemMessage(`✅ ${configKey} updated to ${value}`, 'info');
        }
    }
}



function setupOCRSliderListeners() {
    // Basic preprocessing sliders - only handle display updates, saving handled by setupConfigAutoSave
    document.getElementById('upscaleFactor').addEventListener('input', function() {
        document.getElementById('upscaleValue').textContent = this.value;
    });
    document.getElementById('unsharpStrength').addEventListener('input', function() {
        document.getElementById('unsharpValue').textContent = this.value;
    });
    document.getElementById('thresholdC').addEventListener('input', function() {
        document.getElementById('thresholdCValue').textContent = this.value;
    });

    // Color boost sliders
    document.getElementById('redBoost').addEventListener('input', function() {
        document.getElementById('redBoostValue').textContent = this.value;
    });
    document.getElementById('greenBoost').addEventListener('input', function() {
        document.getElementById('greenBoostValue').textContent = this.value;
    });

    // Text mask cleaning sliders
    document.getElementById('textMaskMinContourArea').addEventListener('input', function() {
        document.getElementById('contourAreaValue').textContent = this.value;
    });
    document.getElementById('textMaskMinWidth').addEventListener('input', function() {
        document.getElementById('maskWidthValue').textContent = this.value;
    });
    document.getElementById('textMaskMinHeight').addEventListener('input', function() {
        document.getElementById('maskHeightValue').textContent = this.value;
    });

    // Symbol enhancement sliders
    document.getElementById('symbolMaxHeight').addEventListener('input', function() {
        document.getElementById('symbolMaxHeightValue').textContent = this.value;
    });
    document.getElementById('periodCommaRatioMin').addEventListener('input', function() {
        document.getElementById('periodRatioMinValue').textContent = this.value;
    });
    document.getElementById('periodCommaRatioMax').addEventListener('input', function() {
        document.getElementById('periodRatioMaxValue').textContent = this.value;
    });
    document.getElementById('periodCommaRadius').addEventListener('input', function() {
        document.getElementById('periodRadiusValue').textContent = this.value;
    });
    document.getElementById('hyphenMinRatio').addEventListener('input', function() {
        document.getElementById('hyphenRatioValue').textContent = this.value;
    });
    document.getElementById('hyphenMinHeight').addEventListener('input', function() {
        document.getElementById('hyphenHeightValue').textContent = this.value;
    });
}

function setCorrectDefaults() {
    // Set development settings to correct defaults
    const developmentMode = document.getElementById('developmentMode');
    const enableRawOcrRecording = document.getElementById('enableRawOcrRecording');
    const logLevel = document.getElementById('logLevel');

    if (developmentMode) developmentMode.checked = true;  // Development mode ON
    if (enableRawOcrRecording) enableRawOcrRecording.checked = false;  // Raw OCR OFF
    if (logLevel) logLevel.value = 'ERROR';  // Log level ERROR

    // Update status displays
    updateDevelopmentStatusDisplays();
    updateRecordingStatusDisplays();
    updateLoggingStatusDisplays();

    addSystemMessage('✅ Applied correct default settings', 'info');
}

function applyOCRSettings() {
    console.log('🔧 applyOCRSettings() called!');
    addSystemMessage('🔧 applyOCRSettings() function called', 'info');

    // Collect all OCR settings from the GUI
    const settings = {
        // Basic preprocessing parameters
        ocr_upscale_factor: parseFloat(document.getElementById('upscaleFactor').value),
        ocr_unsharp_strength: parseFloat(document.getElementById('unsharpStrength').value),
        ocr_threshold_c: parseInt(document.getElementById('thresholdC').value),
        ocr_red_boost: parseFloat(document.getElementById('redBoost').value),
        ocr_green_boost: parseFloat(document.getElementById('greenBoost').value),
        ocr_text_mask_min_contour_area: parseInt(document.getElementById('textMaskMinContourArea').value),
        ocr_text_mask_min_width: parseInt(document.getElementById('textMaskMinWidth').value),
        ocr_text_mask_min_height: parseInt(document.getElementById('textMaskMinHeight').value),
        ocr_symbol_max_height: parseInt(document.getElementById('symbolMaxHeight').value),
        ocr_period_comma_ratio_min: parseFloat(document.getElementById('periodCommaRatioMin').value),
        ocr_period_comma_ratio_max: parseFloat(document.getElementById('periodCommaRatioMax').value),
        ocr_period_comma_radius: parseInt(document.getElementById('periodCommaRadius').value),
        ocr_hyphen_min_ratio: parseFloat(document.getElementById('hyphenMinRatio').value),
        ocr_hyphen_min_height: parseInt(document.getElementById('hyphenMinHeight').value),

        // Checkbox settings
        ocr_force_black_text_on_white: document.getElementById('forceBlackText').checked,
        ocr_apply_text_mask_cleaning: document.getElementById('textMaskCleaning').checked,
        ocr_enhance_small_symbols: document.getElementById('enhanceSmallSymbols').checked
    };

    console.log('🔧 OCR settings collected:', settings);
    addSystemMessage(`🔧 Applying OCR settings via command system... (${Object.keys(settings).length} parameters)`, 'info');

    // Use the proper command system: GUI → Backend → Redis → Babysitter → ApplicationCore → OCR Service
    sendCommand('update_ocr_preprocessing_full', settings).then((result) => {
        console.log('✅ OCR settings command result:', result);
        addSystemMessage('✅ OCR settings command sent successfully', 'success');
    }).catch(err => {
        console.error("❌ Failed to send OCR settings command", err);
        addSystemMessage('❌ Failed to apply OCR settings', 'error');
    });
}

// --- END Configuration and Initialization Functions ---

// 5. INITIALIZE IMPROVED SYSTEMS
function initializeImprovedSystems() {
    try {
        // Initialize global error handling
        window.addEventListener('error', (event) => {
            errorHandler.handleError(event.error, 'Global Error', 'critical');
        });

        window.addEventListener('unhandledrejection', (event) => {
            errorHandler.handleError(event.reason, 'Unhandled Promise Rejection', 'critical');
        });

        // Initialize performance monitoring
        if (APP_CONFIG.debugMode) {
            setInterval(() => {
                performanceMonitor.logPerformanceReport();
            }, 30000); // Every 30 seconds
        }

        console.log('✅ Improved systems initialized successfully');

    } catch (error) {
        errorHandler.handleError(error, 'System Initialization', 'critical');
    }
}

// --- NEW: GUI Initialization with Bootstrap API Support ---
async function initializeGUI() {
    addSystemMessage('🚀 Initializing TESTRADE GUI...', 'info');
    performanceMonitor.startTiming('initializeGUI');

    // Display initial placeholder for preview image
    initializePreview();
    // Load initial ROI from backend config
    await loadInitialROI();
    // Load other configurations from backend
    await loadConfiguration();

    // Set initial button states or UI elements
    updateOCRButtonState(false); // Assume OCR is off until a status message confirms

    // Check for feature flag - use global variable if available, otherwise default to true
    const useBootstrapAPI = (typeof global_app_config !== 'undefined' &&
                            global_app_config.FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API !== undefined)
                            ? global_app_config.FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API
                            : true; // Default to new mode for development

    if (useBootstrapAPI) {
        addSystemMessage('ℹ️ Bootstrap + XREAD mode enabled. Fetching initial state via APIs...', 'info');
        try {
            // --- Step 1: Bootstrap API Calls ---
            const bootstrapStartTime = performance.now();

            addSystemMessage('📡 Fetching current positions...', 'info');
            const positionsPromise = fetch(`${state.apiUrl}/api/v1/positions/current`)
                .then(response => {
                    if (!response.ok) throw new Error(`Positions API Error: ${response.status} ${response.statusText}`);
                    return response.json();
                })
                .then(data => {
                    addSystemMessage('✅ Positions loaded.', 'success');
                    console.log("Bootstrap Positions Data:", data);
                    // Handle different possible response structures
                    if (data && data.positions) {
                        updateActiveTrades(data.positions);
                        updateMarketDataIndicator(data.market_data_available, data.hierarchy_data_available !== false);
                    } else if (data && Array.isArray(data)) {
                        updateActiveTrades(data);
                    } else {
                        console.warn("Unexpected positions data structure from bootstrap API:", data);
                        updateActiveTrades([]);
                    }
                })
                .catch(error => {
                    errorHandler.handleError(error, 'Bootstrap Positions API', 'error');
                    updateActiveTrades([]);
                });

            addSystemMessage('📡 Fetching account summary...', 'info');
            const accountPromise = fetch(`${state.apiUrl}/api/v1/account/summary`)
                .then(response => {
                    if (!response.ok) throw new Error(`Account API Error: ${response.status} ${response.statusText}`);
                    return response.json();
                })
                .then(data => {
                    addSystemMessage('✅ Account summary loaded.', 'success');
                    console.log("Bootstrap Account Data:", data);
                    if (data) {
                        updateAccountSummaryUI(data);
                    }
                })
                .catch(error => errorHandler.handleError(error, 'Bootstrap Account API', 'error'));

            addSystemMessage('📡 Fetching today\'s orders...', 'info');
            const ordersPromise = fetch(`${state.apiUrl}/api/v1/orders/today`)
                .then(response => {
                    if (!response.ok) throw new Error(`Orders API Error: ${response.status} ${response.statusText}`);
                    return response.json();
                })
                .then(data => {
                    addSystemMessage('✅ Today\'s orders loaded.', 'success');
                    console.log("Bootstrap Orders Data:", data);
                    if (data && data.orders && Array.isArray(data.orders)) {
                        // 🚨 ENHANCED: Normalize order data and populate tradeState.openOrders
                        tradeState.openOrders = data.orders.map(order => normalizeOrderData(order));
                        console.log(`🚨 ORDERS: Bootstrap loaded ${tradeState.openOrders.length} orders into tradeState.openOrders`);

                        // 🚨 DEBUG: Log child orders for hierarchy debugging
                        const childOrders = tradeState.openOrders.filter(order => order.is_child);
                        const parentOrders = tradeState.openOrders.filter(order => !order.is_child);
                        console.log(`🚨 ORDERS: Found ${childOrders.length} child orders and ${parentOrders.length} parent/standalone orders`);

                        // 🚨 DEBUG: Log YIBO orders specifically
                        const yiboOrders = tradeState.openOrders.filter(order => order.symbol === 'YIBO');
                        if (yiboOrders.length > 0) {
                            console.log(`🚨 YIBO ORDERS: Found ${yiboOrders.length} YIBO orders:`, yiboOrders);
                        }

                        // Call function to update orders UI if it exists
                        if (typeof updateOpenOrdersUI === 'function') {
                            updateOpenOrdersUI(tradeState.openOrders);
                        }
                    } else if (data && Array.isArray(data)) {
                        tradeState.openOrders = data.map(order => normalizeOrderData(order));
                        console.log("Bootstrap Open Orders (direct array):", tradeState.openOrders);
                    } else {
                        tradeState.openOrders = [];
                        console.warn("No orders data received from Bootstrap API");
                    }
                })
                .catch(error => errorHandler.handleError(error, 'Bootstrap Orders API', 'error'));

            addSystemMessage('📡 Fetching market status...', 'info');
            const marketStatusPromise = fetch(`${state.apiUrl}/api/v1/market/status`)
                .then(response => {
                    if (!response.ok) throw new Error(`Market Status API Error: ${response.status} ${response.statusText}`);
                    return response.json();
                })
                .then(data => {
                    addSystemMessage('✅ Market status loaded.', 'success');
                    console.log("Bootstrap Market Status Data:", data);
                    // Update relevant UI elements for market status if needed
                    if (data && data.market_open !== undefined) {
                        // Could update a market status indicator here
                        console.log("Market open:", data.market_open);
                    }
                })
                .catch(error => errorHandler.handleError(error, 'Bootstrap Market Status API', 'error'));
            
            addSystemMessage('📡 Fetching trade rejections...', 'info');
            const rejectionsPromise = fetch(`${state.apiUrl}/api/v1/trades/rejections`)
                .then(response => {
                    if (!response.ok) throw new Error(`Trade Rejections API Error: ${response.status} ${response.statusText}`);
                    return response.json();
                })
                .then(data => {
                    addSystemMessage('✅ Trade rejections loaded.', 'success');
                    console.log("Bootstrap Trade Rejections Data:", data);
                    if (data && data.trades) {
                        // Set the historical trades with the rejections
                        tradeState.historicalTrades = data.trades;
                        updateHistoricalTradesDisplay();
                        console.log(`Loaded ${data.trades.length} historical rejections`);
                    }
                })
                .catch(error => errorHandler.handleError(error, 'Bootstrap Trade Rejections API', 'error'));

            // Wait for all bootstrap calls to complete
            await Promise.all([positionsPromise, accountPromise, ordersPromise, marketStatusPromise, rejectionsPromise]);
            const bootstrapEndTime = performance.now();
            addSystemMessage(`🏁 All bootstrap data fetched in ${(bootstrapEndTime - bootstrapStartTime).toFixed(0)} ms.`, 'success');

        } catch (error) {
            errorHandler.handleError(error, 'GUI Bootstrap Sequence', 'critical');
            addSystemMessage('❌ Critical error during GUI bootstrap. Some data may be missing.', 'error');
        }
    } else {
        addSystemMessage('ℹ️ Legacy Consumer Group mode enabled. Initial state will populate from streams.', 'info');
        // In legacy mode, initial data will come from WebSocket messages as they arrive
        // The old sendCommand calls for initial data are now in the WebSocket onopen handler
    }

    // --- Step 2: Start Live Updates via WebSocket ---
    addSystemMessage('🔗 Connecting to WebSocket for live updates...', 'info');
    connectWebSocket();

    // Other initializations
    updateTime(); // Initial time update
    setInterval(updateTime, 1000);
    // REMOVED: setInterval(checkCoreStatus, APP_CONFIG.coreStatusCheckIntervalMs);
    // Core status checking is now handled by CoreStatusManager automatically
    
    // Ensure historical trades are displayed after bootstrap
    if (tradeState.historicalTrades && tradeState.historicalTrades.length > 0) {
        updateHistoricalTradesDisplay();
        console.log(`Loaded ${tradeState.historicalTrades.length} historical trades after bootstrap`);
    }

    performanceMonitor.endTiming('initializeGUI');
    addSystemMessage('✅ GUI Initialized and ready.', 'success');
}

// Ensure global_app_config is available (mock it if not running in a context where backend injects it)
var global_app_config = global_app_config || {
    FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API: true // Default for dev if not injected
};

// =============================================================================
// FIX 6: INITIALIZATION ORDER
// =============================================================================
// Replace your DOMContentLoaded handler with this fixed version:

document.addEventListener('DOMContentLoaded', async function() {
    try {
        console.log('🚀 Starting TESTRADE GUI initialization...');

        // Initialize error handling first
        window.addEventListener('error', (event) => {
            if (errorHandler) {
                errorHandler.handleError(event.error, 'Global Error', 'critical');
            }
        });

        // Initialize systems in correct order
        if (typeof initializeImprovedSystems === 'function') {
            initializeImprovedSystems();
        }
        
        // Refresh DOM cache after DOM is ready
        if (domManager && typeof domManager.refreshCache === 'function') {
            domManager.refreshCache();
            console.log('DOM cache refreshed after page load');
        }

        if (typeof initializeIndependentStatusPills === 'function') {
            initializeIndependentStatusPills();
        }

        // Initialize state
        state.active_trades = state.active_trades || [];
        tradeState.openOrders = tradeState.openOrders || [];
        tradeState.historicalTrades = tradeState.historicalTrades || [];

        // Set up UI
        if (typeof setCorrectDefaults === 'function') {
            setCorrectDefaults();
        }

        if (typeof setupConfigAutoSave === 'function') {
            setupConfigAutoSave();
        }

        if (typeof setupOCRSliderListeners === 'function') {
            setupOCRSliderListeners();
        }

        // Initialize GUI
        if (typeof initializeGUI === 'function') {
            await initializeGUI();
        } else {
            // Fallback initialization
            console.log('🔗 Connecting WebSocket...');
            connectWebSocket();
        }

        // Set up image preview
        const previewImage = document.getElementById('previewImage');
        if (previewImage) {
            previewImage.addEventListener('load', () => {
                if (typeof updateROIDisplay === 'function') {
                    updateROIDisplay();
                }
            });
        }

        console.log('✅ TESTRADE GUI initialization complete');
        addSystemMessage('✅ TESTRADE Platform Ready', 'success');

    } catch (error) {
        console.error('❌ Critical initialization error:', error);
        if (errorHandler) {
            errorHandler.handleError(error, 'DOMContentLoaded', 'critical');
        }
        addSystemMessage('❌ Critical initialization error - check console', 'error');
    }
});

// --- Keyboard Shortcuts ---
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key.toLowerCase()) {
            case 'm': e.preventDefault(); manualAdd(); break;
            case 'f': e.preventDefault(); forceClose(); break;
            case 'o': e.preventDefault(); toggleOCR(); break;
            case 'e': e.preventDefault(); emergencyStop(); break;
            case 'd':
                if (e.shiftKey) { // Ctrl+Shift+D for debug
                    e.preventDefault();
                    debugImageState();
                } else { // Ctrl+D for memory debug
                    e.preventDefault();
                    debugMemoryState();
                }
                break;
            case 'i': // Ctrl+I for image mode toggle
                e.preventDefault();
                switchImageMode();
                break;
        }
    }
});

// --- Cleanup and Memory Leak Prevention ---
function cleanup() {
    // Clear intervals
    if (state.healthPollingInterval) {
        clearInterval(state.healthPollingInterval);
        state.healthPollingInterval = null;
    }

    // Clear timeouts
    if (state.imageUpdateTimeout) {
        clearTimeout(state.imageUpdateTimeout);
        state.imageUpdateTimeout = null;
    }

    // Close WebSocket connection
    if (state.ws) {
        state.ws.close();
        state.ws = null;
    }

    // Clear image history to free memory using improved ImageMemoryManager
    imageManager.forceCleanup();
    state.lastRawImage = null;
    state.lastProcessedImage = null;

    // CRITICAL FIX: Clear image history arrays to prevent memory leaks
    state.rawImageHistory = [];
    state.processedImageHistory = [];
    console.log('🧹 Cleared image history arrays during cleanup');

    // Reset connection state
    state.connected = false;
    state.reconnecting = false;

    addSystemMessage('✅ Application cleanup completed', 'info');
}

// =============================================================================
// FIX 8: MEMORY MANAGEMENT
// =============================================================================
// Add this cleanup to prevent memory leaks:

// Clean up function references that might cause circular dependencies
function cleanupCircularReferences() {
    // Clear any circular references in the update tracker
    if (window.updateTracker) {
        window.updateTracker.pendingUpdate = null;
    }

    if (window.updateStrategy) {
        window.updateStrategy.pendingDataUpdate = null;
    }
}

// Call cleanup periodically
setInterval(cleanupCircularReferences, 60000); // Every minute

// Register cleanup on page unload
window.addEventListener('beforeunload', cleanup);

// Also cleanup on visibility change (when tab becomes hidden)
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // Page is hidden, reduce activity
        console.log('Page hidden - reducing background activity');
    } else {
        // Page is visible again, resume normal activity
        console.log('Page visible - resuming normal activity');
        if (!state.connected && !state.reconnecting) {
            // Try to reconnect if we're not connected
            setTimeout(connectWebSocket, 1000);
        }
    }
});

// --- Helper Functions ---

// 🚨 NEW: Normalize order data from Bootstrap API
function normalizeOrderData(order) {
    // Ensure all required fields are present with defaults
    return {
        local_id: order.local_id || 0,
        ls_order_id: order.ls_order_id || null,
        parent_trade_id: order.parent_trade_id || null,
        symbol: order.symbol || 'UNKNOWN',
        side: order.side || 'UNKNOWN',
        event_type: order.event_type || 'UNKNOWN',
        requested_shares: order.requested_shares || 0,
        requested_lmt_price: order.requested_lmt_price || null,
        order_type: order.order_type || 'LMT',
        timestamp_requested: order.timestamp_requested || Date.now() / 1000,
        ls_status: order.ls_status || 'UNKNOWN',
        filled_quantity: order.filled_quantity || 0,
        leftover_shares: order.leftover_shares || 0,
        avg_fill_price: order.avg_fill_price || null,
        master_correlation_id: order.master_correlation_id || null,
        comment: order.comment || null,
        rejection_reason: order.rejection_reason || null,
        cancel_reason: order.cancel_reason || null,
        is_child: order.is_child || false,
        fills_count: order.fills_count || 0,
        version: order.version || 0,
        timestamp_broker: order.timestamp_broker || null,
        timestamp_sent: order.timestamp_sent || null,
        // Add computed fields for UI
        timestamp: order.timestamp_requested || Date.now() / 1000,
        price: order.avg_fill_price || order.requested_lmt_price || 0,
        quantity: order.requested_shares || 0,
        status: order.ls_status || 'UNKNOWN'
    };
}

// Add placeholder functions for missing UI updates
function updatePositionSummaryUI(summary) {
    console.log('[updatePositionSummaryUI] Called with:', summary);
    
    if (!summary) return;
    
    // Update position count
    const positionCountEl = document.getElementById('guiOpenPositionsCount');
    if (positionCountEl && summary.total_positions !== undefined) {
        positionCountEl.textContent = summary.total_positions;
    }
    
    // Update total shares
    const totalSharesEl = document.getElementById('guiTotalShares');
    if (totalSharesEl && summary.total_shares !== undefined) {
        totalSharesEl.textContent = formatNumber(Math.abs(summary.total_shares));
    }
    
    // Update unrealized P&L
    const unrealizedPnLEl = document.getElementById('guiUnrealizedPnL');
    if (unrealizedPnLEl && summary.unrealized_pnl !== undefined) {
        unrealizedPnLEl.innerHTML = formatPnLWithClass(summary.unrealized_pnl);
    }
    
    // Update realized P&L
    const realizedPnLEl = document.getElementById('guiRealizedPnL');
    if (realizedPnLEl && summary.realized_pnl !== undefined) {
        realizedPnLEl.innerHTML = formatPnLWithClass(summary.realized_pnl);
    }
    
    // Update total P&L
    const totalPnLEl = document.getElementById('guiTotalPnL');
    if (totalPnLEl && summary.total_pnl !== undefined) {
        totalPnLEl.innerHTML = formatPnLWithClass(summary.total_pnl);
    }
    
    // Update long/short counts
    const longCountEl = document.getElementById('guiLongPositionsCount');
    if (longCountEl && summary.long_positions !== undefined) {
        longCountEl.textContent = summary.long_positions;
    }
    
    const shortCountEl = document.getElementById('guiShortPositionsCount');
    if (shortCountEl && summary.short_positions !== undefined) {
        shortCountEl.textContent = summary.short_positions;
    }
    
    // Update exposure
    const totalExposureEl = document.getElementById('guiTotalExposure');
    if (totalExposureEl && summary.total_exposure !== undefined) {
        totalExposureEl.textContent = formatCurrency(Math.abs(summary.total_exposure));
    }
    
    // Update any other summary fields
    if (summary.avg_position_size !== undefined) {
        const avgSizeEl = document.getElementById('guiAvgPositionSize');
        if (avgSizeEl) avgSizeEl.textContent = formatNumber(summary.avg_position_size);
    }
    
    console.log('[updatePositionSummaryUI] Updated position summary:', {
        positions: summary.total_positions,
        unrealizedPnL: summary.unrealized_pnl,
        realizedPnL: summary.realized_pnl,
        totalPnL: summary.total_pnl
    });
}

function updateAccountSummaryUI(account) {
    console.log('[updateAccountSummaryUI] Called with:', account);

    // Update tradeState with account data
    if (account) {
        if (account.account_value !== undefined) tradeState.accountValue = account.account_value;
        if (account.buying_power !== undefined) tradeState.buyingPower = account.buying_power;
        if (account.day_pnl !== undefined) tradeState.dayPnL = account.day_pnl;
        if (account.total_pnl !== undefined) tradeState.totalPnL = account.total_pnl;

        // Update UI elements if they exist
        const accountValueEl = document.getElementById('accountValue');
        if (accountValueEl && account.account_value !== undefined) {
            accountValueEl.textContent = formatCurrency(account.account_value);
        }

        const buyingPowerEl = document.getElementById('buyingPower');
        if (buyingPowerEl && account.buying_power !== undefined) {
            buyingPowerEl.textContent = formatCurrency(account.buying_power);
        }

        const dayPnLEl = document.getElementById('dayTotalPnL');
        if (dayPnLEl && account.day_pnl !== undefined) {
            dayPnLEl.innerHTML = formatPnLWithClass(account.day_pnl);
        }

        console.log('[updateAccountSummaryUI] Updated account data:', {
            accountValue: tradeState.accountValue,
            buyingPower: tradeState.buyingPower,
            dayPnL: tradeState.dayPnL
        });
        
        // Calculate and update BP Available
        updateBuyingPowerUsage();
    }
}

// Calculate used buying power from open positions
function calculateUsedBuyingPower() {
    let usedBP = 0;
    
    // Calculate from active trades
    if (state.activeTrades && Array.isArray(state.activeTrades)) {
        console.log('[calculateUsedBuyingPower] Active trades:', state.activeTrades.length);
        state.activeTrades.forEach(trade => {
            if (trade.is_open && trade.quantity && trade.average_price) {
                // Used BP = quantity * average price
                const tradeValue = Math.abs(trade.quantity) * trade.average_price;
                usedBP += tradeValue;
                console.log(`[calculateUsedBuyingPower] ${trade.symbol}: ${trade.quantity} @ ${trade.average_price} = ${tradeValue}`);
            }
        });
    } else {
        console.log('[calculateUsedBuyingPower] No active trades found');
    }
    
    return usedBP;
}

// Update buying power usage display
function updateBuyingPowerUsage() {
    const totalBP = tradeState.buyingPower || 0;
    const usedBP = calculateUsedBuyingPower();
    const availableBP = totalBP - usedBP;
    const usagePercent = totalBP > 0 ? (usedBP / totalBP) * 100 : 0;
    
    // Update BP Available display
    const bpAvailableEl = document.getElementById('buyingPowerLeft');
    if (bpAvailableEl) {
        bpAvailableEl.textContent = formatCurrency(Math.max(0, availableBP));
    }
    
    // Update the meter bar
    const bpUsageBar = document.getElementById('buyingPowerUsageBar');
    if (bpUsageBar) {
        bpUsageBar.style.width = Math.min(100, usagePercent) + '%';
        
        // Change color based on usage
        if (usagePercent >= 90) {
            bpUsageBar.style.background = 'linear-gradient(90deg, var(--accent-danger), var(--accent-danger-dark))';
        } else if (usagePercent >= 75) {
            bpUsageBar.style.background = 'linear-gradient(90deg, var(--accent-warning), var(--accent-danger))';
        } else {
            bpUsageBar.style.background = 'linear-gradient(90deg, var(--accent-primary), var(--accent-warning))';
        }
    }
    
    // Update usage text
    const usageTextEl = bpUsageBar?.parentElement?.nextElementSibling;
    if (usageTextEl) {
        usageTextEl.textContent = `${formatCurrency(usedBP)} used of ${formatCurrency(totalBP)} (${usagePercent.toFixed(1)}%)`;
    }
    
    console.log('[updateBuyingPowerUsage] BP Usage:', {
        total: totalBP,
        used: usedBP,
        available: availableBP,
        percent: usagePercent
    });
}

// Add this helper function
function updateMarketDataIndicator(marketDataAvailable, hierarchyAvailable) {
    const marketDataIndicator = document.getElementById('guiMarketDataIndicator');
    if (marketDataIndicator) {
        const liveDot = marketDataIndicator.querySelector('.live-dot');
        const liveText = marketDataIndicator.querySelector('span'); // Assuming there's a span for text

        let statusText = "Market Data: ";
        let dotColor = 'var(--accent-danger)'; // Default to error/stale

        if (marketDataAvailable === true && hierarchyAvailable === true) {
            statusText += "Live & Complete";
            dotColor = 'var(--accent-primary)';
        } else if (marketDataAvailable === true) {
            statusText += "Live (Hierarchy Missing)";
            dotColor = 'var(--accent-warning)';
        } else if (hierarchyAvailable === true) {
            statusText += "Stale (Hierarchy OK)";
            dotColor = 'var(--accent-warning)';
        } else {
            statusText += "Unavailable";
        }

        if (liveDot) liveDot.style.backgroundColor = dotColor;
        if (liveText) liveText.textContent = statusText;

        if (marketDataAvailable === false) {
            addSystemMessage('📉 Position data missing market quotes (bid/ask/last)', 'warning');
        }
        if (hierarchyAvailable === false) {
            addSystemMessage('📉 Position data missing parent/child trade hierarchy', 'warning');
        }
    }
}

// --- END Event Handlers & Initialization ---

// --- WebSocket Connection Management ---
let ws = null;
let reconnectInterval = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 10;
const reconnectDelay = 2000;

function connectWebSocket() {
    const wsUrl = 'ws://localhost:8001/ws';
    console.log('Attempting WebSocket connection to:', wsUrl);
    
    try {
        ws = new WebSocket(wsUrl);
        
        ws.onopen = function() {
            console.log('WebSocket connected successfully');
            state.connected = true;
            reconnectAttempts = 0;
            
            // Update status indicators
            const apiStatusDot = document.getElementById('apiStatusDot');
            if (apiStatusDot) {
                apiStatusDot.style.background = 'var(--accent-success)';
            }
            
            addSystemMessage('Connected to TESTRADE backend', 'success');
        };
        
        ws.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                
                // Route message through MessageRouter
                if (messageRouter) {
                    messageRouter.route(data);
                } else {
                    // Fallback to old message handler if it exists
                    if (typeof handleWebSocketMessage === 'function') {
                        handleWebSocketMessage(data);
                    } else {
                        console.warn('No message handler available for:', data.type);
                    }
                }
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };
        
        ws.onerror = function(error) {
            console.error('WebSocket error:', error);
            addSystemMessage('WebSocket connection error', 'error');
        };
        
        ws.onclose = function() {
            console.log('WebSocket disconnected');
            state.connected = false;
            ws = null;
            
            // Update status indicators
            const apiStatusDot = document.getElementById('apiStatusDot');
            if (apiStatusDot) {
                apiStatusDot.style.background = 'var(--accent-danger)';
            }
            
            // Attempt reconnection
            if (reconnectAttempts < maxReconnectAttempts) {
                reconnectAttempts++;
                addSystemMessage(`Connection lost. Reconnecting... (attempt ${reconnectAttempts}/${maxReconnectAttempts})`, 'warning');
                setTimeout(connectWebSocket, reconnectDelay);
            } else {
                addSystemMessage('Failed to connect after maximum attempts', 'error');
            }
        };
        
    } catch (error) {
        console.error('Failed to create WebSocket:', error);
        addSystemMessage('Failed to create WebSocket connection', 'error');
    }
}

// Initialize on page load
window.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing TESTRADE GUI...');
    
    // Initialize DOM manager cache
    if (domManager && domManager.refreshCache) {
        domManager.refreshCache();
    }
    
    // Connect WebSocket
    connectWebSocket();
    
    // Check current OCR status
    if (typeof checkCurrentOCRStatus === 'function') {
        setTimeout(checkCurrentOCRStatus, 1000);
    }
    
    // Start time updates
    setInterval(updateTime, 1000);
    
    console.log('TESTRADE GUI initialization complete');
});
