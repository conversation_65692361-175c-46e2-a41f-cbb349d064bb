#pragma once
#if !defined(LS_L_OBSERVER_H)
#define LS_L_OBSERVER_H

// Copyright (c) 2001-2018 Lightspeed Financial, Inc. All rights reserved.

#include "L_Thread.h"
#include "L_Memory.h"
#include "L_Messages.h"
#include "L_TimeUtil.h"

namespace LightspeedTrader
{

class L_Message;
class L_Observer;
class L_ObserverDelete;
class L_ObserverImpl;
class L_ObservableImpl;


void L_CreateObserver(L_Observer *o);
L_ObserverImpl *L_CreateObserverImplOnStrand(L_Observer *o, L_Strand *s, L_ObserverDelete *deleter);
void L_DestroyObserver(L_Observer *o);
void L_DestroyObserverImpl(L_ObserverImpl *observer_l_impl);
void L_QueueDestroy(L_Observer *o);
L_ObservableImpl *L_CreateObservableImpl(L_Strand *strand);
void L_DestroyObservableImpl(L_ObservableImpl *impl);


class L_ObserverImpl
{
public:
	virtual void L_QueueMessage(L_Message const *message) = 0;
	virtual void L_ScheduleMessage(L_Message const *message, long long hn_when) = 0;
	virtual L_Strand *L_ImplGetStrand() const = 0;
	L_ObserverDelete *l_Deleter;
};

class L_ObservableImpl
{
public:
	virtual void L_ImplAttach(L_Observer *observer) const = 0;
	virtual void L_ImplDetach(L_Observer *observer) const = 0;
	virtual void L_ImplNotify(L_Message const *message) = 0;
	virtual void L_ImplScheduleNotification(L_Message const *message, long long hn_when) = 0;
	virtual L_Strand *L_ImplGetStrand() const = 0;
};



class L_Observer
{
public:
	virtual void HandleMessage(L_Message const *message) = 0;

#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

	template<typename pointerToBuffer>
	void C_QueueMessage(pointerToBuffer x)
	{
		observer_l_impl->L_QueueMessage(get_pointer(x));
	}

	template<typename pointerToBuffer>
	void C_ScheduleMessage(pointerToBuffer x, long long hn_when)
	{
		observer_l_impl->L_ScheduleMessage(get_pointer(x), hn_when);
	}

	template<typename pointerToBuffer>
	void C_QueueMessageDelayed(pointerToBuffer x, long millisfromnow)
	{
		observer_l_impl->L_ScheduleMessage(get_pointer(x), L_GetHNTime() + static_cast<long long>(millisfromnow) * l_hn_per_millisecond);
	}

	L_Observer()
		: observer_l_impl(0)
	{
		observer_l_impl = L_CreateObserverImplOnStrand(this, L_GetCurrentStrand(), 0);
	}

	L_Observer(L_Strand *strandInit)
		: observer_l_impl(0)
	{
		observer_l_impl = L_CreateObserverImplOnStrand(this, strandInit, 0);
	}

	L_Observer(L_Observer const &rhs)
		: observer_l_impl(0)
	{
		observer_l_impl = L_CreateObserverImplOnStrand(this, rhs.L_GetHomeStrand(), 0);
	}

	L_Observer &operator=(L_Observer const &)
	{
		return *this;
	}

	~L_Observer()
	{
		L_DestroyObserverImpl(observer_l_impl);
	}

	L_Strand *L_GetHomeStrand() const
	{
		return observer_l_impl->L_ImplGetStrand();
	}

#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

	L_ObserverImpl *observer_l_impl;
};

class L_Observable
{
public:
#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

	L_Strand *L_GetStrand() const { return observable_l_impl->L_ImplGetStrand(); }

	L_Observable() : observable_l_impl(0)
	{
		observable_l_impl = L_CreateObservableImpl(L_GetCurrentStrand());
	}

	L_Observable(L_Strand *strandInit) : observable_l_impl(0)
	{
		observable_l_impl = L_CreateObservableImpl(strandInit);
	}

	L_Observable(L_Observable const &rhs) : observable_l_impl(0)
	{
		observable_l_impl = L_CreateObservableImpl(rhs.L_GetStrand());
	}

	L_Observable &operator=(L_Observable const &)
	{
		return *this;
	}

	~L_Observable()
	{
		L_DestroyObservableImpl(observable_l_impl);
	}
#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

	virtual void L_Attach(L_Observer *observer) const
#if defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
		= 0;
#else
	{
		observable_l_impl->L_ImplAttach(observer);
	}
#endif // defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
	virtual void L_Detach(L_Observer *observer) const
#if defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
		= 0;
#else
	{
		observable_l_impl->L_ImplDetach(observer);
	}
#endif // defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

protected:
#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
	template<typename pointerToBuffer>
	void C_Notify(pointerToBuffer x)
	{
		observable_l_impl->L_ImplNotify(get_pointer(x));
	}

	template<typename pointerToBuffer>
	void C_ScheduleNotification(pointerToBuffer x, long long hn_when)
	{
		observable_l_impl->L_ImplScheduleNotification(get_pointer(x), hn_when);
	}

	template<typename pointerToBuffer>
	void C_NotifyDelayed(pointerToBuffer x, long millisfromnow)
	{
		observable_l_impl->L_ImplScheduleNotification(get_pointer(x), L_GetHNTime() + static_cast<long long>(millisfromnow) * l_hn_per_millisecond);
	}
#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

	L_ObservableImpl *observable_l_impl;
};

class L_ObserverDelete
{
public:
	virtual void DoDelete(L_Observer *o) = 0;
};

#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

inline void L_CreateObserver(L_Observer *o)
{
	if (o->observer_l_impl == 0)
	{
		o->observer_l_impl = L_GetApplication()->L_CreateObserverImpl(o);
	}
}
inline L_ObserverImpl *L_CreateObserverImplOnStrand(L_Observer *o, L_Strand *s, L_ObserverDelete *deleter)
{
	return L_GetApplication()->L_CreateObserverImplOnStrand(o, s, deleter);
}
inline void L_DestroyObserver(L_Observer *o)
{
	if (o->observer_l_impl != 0)
	{
		L_GetHost()->L_DestroyObserverImpl(o->observer_l_impl);
		o->observer_l_impl = 0;
	}
}
inline void L_DestroyObserverImpl(L_ObserverImpl *observer_l_impl)
{
	L_GetHost()->L_DestroyObserverImpl(observer_l_impl);
}
inline void L_QueueDestroy(L_Observer *o)
{
	if (o->observer_l_impl != 0)
	{
		L_GetApplication()->L_QueueDelete(o->observer_l_impl);
	}
}
inline L_ObservableImpl *L_CreateObservableImpl(L_Strand *strand)
{
	return L_GetApplication()->L_CreateObservableImpl(strand);
}
inline void L_DestroyObservableImpl(L_ObservableImpl *impl)
{
	L_GetApplication()->L_DestroyObservableImpl(impl);
}

template<typename T>
static void C_DefaultObserverDelete(L_Observer *o)
{
	delete static_cast<T *>(o);
}

template<typename T>
class C_StatelessDefaultDeleter : public L_ObserverDelete
{
public:
	virtual void DoDelete(L_Observer *o)
	{
		C_DefaultObserverDelete<T>(o);
	}
};

template<typename D>
class C_ObserverDeleter : public L_ObserverDelete
{
public:
	enum : long { id = L_BufferType::ObserverImpl };
	typedef L_ObserverDelete alloc_interface_type;

	C_ObserverDeleter(D deleterInit)
		: deleter(deleterInit)
	{
	}
	C_ObserverDeleter(C_ObserverDeleter const &rhs)
		: deleter(rhs.deleter)
	{
	}
	C_ObserverDeleter &operator=(C_ObserverDeleter const &rhs)
	{
		deleter = rhs.deleter;
		return *this;
	}
	virtual void DoDelete(L_Observer *o)
	{
		deleter(o);
		l_Buffer->L_DecRef();
	}
	D deleter;
	L_Buffer *l_Buffer;
};


template<typename T, typename D>
class C_QueueDeleter
{
public:
	C_QueueDeleter(D deleteFuncInit)
		: deleteFunc(C_Alloc<C_ObserverDeleter<D> >(deleteFuncInit))
	{
	}
	C_QueueDeleter(C_QueueDeleter const &rhs)
		: deleteFunc(rhs.deleteFunc)
	{
	}
	C_QueueDeleter &operator=(C_QueueDeleter const &rhs)
	{
		deleteFunc = rhs.deleteFunc;
		return *this;
	}
	void operator()(T *ob)
	{
		deleteFunc->l_Buffer->L_IncRef();
		ob->observer_l_impl->l_Deleter = deleteFunc.get();
		L_QueueDestroy(ob);
	}
	C_Ptr<C_ObserverDeleter<D> > deleteFunc;
};
template<typename T, typename D>
C_QueueDeleter<T, D> C_QueueCustDeleter(D deleter) { return C_QueueDeleter<T, D>(deleter); }

template<typename T>
void C_QueueDelete(T *ob)
{
	static C_StatelessDefaultDeleter<T> del;
	ob->observer_l_impl->l_Deleter = &del;
	L_QueueDestroy(ob);
}

#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

} // namespace LightspeedTrader


#endif // !defined(LS_L_OBSERVER_H)



