# utils/flags.py
emergency_exit_active = False
debug_mode_active = False  # Set to True for debugging, False for production

def set_kill_switch(value: bool):
    global emergency_exit_active
    emergency_exit_active = value

def get_kill_switch() -> bool:
    return emergency_exit_active

def set_debug_mode(value: bool):
    global debug_mode_active
    debug_mode_active = value

def get_debug_mode() -> bool:
    return debug_mode_active
