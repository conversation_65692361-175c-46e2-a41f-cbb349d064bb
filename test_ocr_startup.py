#!/usr/bin/env python3
"""Test OCR service startup and diagnose issues"""

import sys
import logging
from core.dependency_injection import DIContainer
from core.di_registration import register_all_services
from interfaces.ocr.services import IOCRDataConditioningService, IOCRScalpingSignalOrchestratorService
from interfaces.ocr.services import IOCRProcessManager

# Configure logging to see all messages
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_ocr_services():
    """Test OCR service resolution and startup"""
    print("=" * 80)
    print("Testing OCR Service Startup")
    print("=" * 80)
    
    # Create DI container
    container = DIContainer()
    
    # Register all services
    print("\n1. Registering all services...")
    try:
        register_all_services(container)
        print("✅ Service registration successful")
    except Exception as e:
        print(f"❌ Service registration failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Test OCR Process Manager
    print("\n2. Testing IOCRProcessManager resolution...")
    try:
        ocr_process_manager = container.resolve(IOCRProcessManager)
        print(f"✅ IOCRProcessManager resolved: {ocr_process_manager}")
        print(f"   Type: {type(ocr_process_manager)}")
    except Exception as e:
        print(f"❌ IOCRProcessManager resolution failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Test OCR Data Conditioning Service
    print("\n3. Testing IOCRDataConditioningService resolution...")
    try:
        ocr_conditioning = container.resolve(IOCRDataConditioningService)
        print(f"✅ IOCRDataConditioningService resolved: {ocr_conditioning}")
        print(f"   Type: {type(ocr_conditioning)}")
    except Exception as e:
        print(f"❌ IOCRDataConditioningService resolution failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Test OCR Orchestrator Service
    print("\n4. Testing IOCRScalpingSignalOrchestratorService resolution...")
    try:
        ocr_orchestrator = container.resolve(IOCRScalpingSignalOrchestratorService)
        print(f"✅ IOCRScalpingSignalOrchestratorService resolved: {ocr_orchestrator}")
        print(f"   Type: {type(ocr_orchestrator)}")
    except Exception as e:
        print(f"❌ IOCRScalpingSignalOrchestratorService resolution failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Test dependencies that OCR services need
    print("\n5. Testing OCR service dependencies...")
    dependencies = [
        'IConfigService',
        'ITelemetryService',
        'DI_IEventBus',
        'DI_IPriceProvider',
        'IPositionManager',
        'IOrderRepository',
        'IBrokerService',
        'ITradeLifecycleManager'
    ]
    
    for dep_name in dependencies:
        try:
            # Import the interface
            if dep_name == 'IConfigService':
                from interfaces.core.services import IConfigService
                dep_interface = IConfigService
            elif dep_name == 'ITelemetryService':
                from interfaces.core.telemetry_interfaces import ITelemetryService
                dep_interface = ITelemetryService
            elif dep_name == 'DI_IEventBus':
                from interfaces.core.services import IEventBus as DI_IEventBus
                dep_interface = DI_IEventBus
            elif dep_name == 'DI_IPriceProvider':
                from interfaces.data.services import IPriceProvider as DI_IPriceProvider
                dep_interface = DI_IPriceProvider
            elif dep_name == 'IPositionManager':
                from interfaces.trading.services import IPositionManager
                dep_interface = IPositionManager
            elif dep_name == 'IOrderRepository':
                from interfaces.order_management.services import IOrderRepository
                dep_interface = IOrderRepository
            elif dep_name == 'IBrokerService':
                from interfaces.broker.services import IBrokerService
                dep_interface = IBrokerService
            elif dep_name == 'ITradeLifecycleManager':
                from interfaces.trading.services import ITradeLifecycleManager
                dep_interface = ITradeLifecycleManager
            else:
                print(f"   ⚠️  Unknown dependency: {dep_name}")
                continue
                
            dep = container.resolve(dep_interface)
            print(f"   ✅ {dep_name} resolved")
        except Exception as e:
            print(f"   ❌ {dep_name} resolution failed: {e}")
    
    print("\n" + "=" * 80)
    print("Test complete!")
    print("=" * 80)

if __name__ == "__main__":
    test_ocr_services()