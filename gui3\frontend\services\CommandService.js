/**
 * Command Service
 * Handles sending commands to the backend following Horseshoe Architecture
 */

import { ActionConfig, validateActionData } from '../config/actions.js';

export class CommandService {
    constructor() {
        this.baseUrl = 'http://localhost:8001';
        this.wsUrl = 'ws://localhost:8001/ws';
        this.ws = null;
        this.connected = false;
        this.commandCallbacks = new Map();
        
        // Initialize WebSocket connection
        this.connect();
    }

    static getInstance() {
        if (!CommandService.instance) {
            CommandService.instance = new CommandService();
        }
        return CommandService.instance;
    }

    connect() {
        this.ws = new WebSocket(this.wsUrl);
        
        this.ws.onopen = () => {
            this.connected = true;
            console.log('Connected to GUI backend');
            this.emit('connected');
        };
        
        this.ws.onclose = () => {
            this.connected = false;
            console.log('Disconnected from GUI backend');
            this.emit('disconnected');
            
            // Reconnect after delay
            setTimeout(() => this.connect(), 5000);
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.emit('error', error);
        };
        
        this.ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.handleMessage(message);
            } catch (error) {
                console.error('Failed to parse message:', error);
            }
        };
    }

    handleMessage(message) {
        const { type, command_id } = message;
        
        // Handle command acknowledgments
        if (type === 'command_ack' && command_id) {
            const callback = this.commandCallbacks.get(command_id);
            if (callback) {
                callback(message);
                this.commandCallbacks.delete(command_id);
            }
        }
        
        // Emit message for other handlers
        this.emit('message', message);
    }

    async sendCommand(action, data) {
        const config = ActionConfig[action];
        if (!config) {
            throw new Error(`Unknown action: ${action}`);
        }
        
        // Validate data
        const validation = validateActionData(action, data);
        if (!validation.valid) {
            throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
        }
        
        // Prepare request data
        const requestData = {
            action,
            data
        };
        
        // Handle special case for orders
        if (action === 'buy' || action === 'sell') {
            requestData.data.side = action.toUpperCase();
        }
        
        // Send via REST API or WebSocket
        if (this.connected && config.useWebSocket !== false) {
            return this.sendViaWebSocket(action, requestData);
        } else {
            return this.sendViaREST(config, requestData);
        }
    }

    async sendViaWebSocket(action, requestData) {
        return new Promise((resolve, reject) => {
            const commandId = `cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            
            // Set up callback for response
            const timeout = setTimeout(() => {
                this.commandCallbacks.delete(commandId);
                reject(new Error('Command timeout'));
            }, 30000);
            
            this.commandCallbacks.set(commandId, (response) => {
                clearTimeout(timeout);
                if (response.status === 'sent') {
                    resolve(response);
                } else {
                    reject(new Error(response.message || 'Command failed'));
                }
            });
            
            // Send command
            this.ws.send(JSON.stringify({
                type: 'command',
                command: action,
                data: requestData.data,
                command_id: commandId
            }));
        });
    }

    async sendViaREST(config, requestData) {
        const response = await fetch(`${this.baseUrl}${config.endpoint}`, {
            method: config.method || 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData.data)
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || `HTTP ${response.status}`);
        }
        
        return response.json();
    }

    // Event emitter functionality
    listeners = new Map();

    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    off(event, callback) {
        const callbacks = this.listeners.get(event);
        if (callbacks) {
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    emit(event, ...args) {
        const callbacks = this.listeners.get(event);
        if (callbacks) {
            callbacks.forEach(callback => callback(...args));
        }
    }
}