#!/usr/bin/env python3
"""
Check for specific DLL dependencies that might be missing.
"""

import os
import sys
import ctypes
from ctypes import wintypes
import platform

def check_dll_in_directory(dll_name, directory):
    """Check if a specific DLL can be loaded from a directory."""
    dll_path = os.path.join(directory, dll_name)
    if os.path.exists(dll_path):
        try:
            # Try to load the DLL
            handle = ctypes.windll.kernel32.LoadLibraryW(dll_path)
            if handle:
                ctypes.windll.kernel32.FreeLibrary(handle)
                return True, "Loads successfully"
            else:
                error = ctypes.windll.kernel32.GetLastError()
                return False, f"Load failed with error {error}"
        except Exception as e:
            return False, f"Exception: {e}"
    else:
        return False, "File not found"

def check_critical_dlls():
    """Check critical DLLs in the build directory."""
    print("=== Critical DLL Check ===")
    
    build_dir = os.path.join("ocr_accelerator", "x64", "Release")
    
    if not os.path.exists(build_dir):
        print(f"❌ Build directory not found: {build_dir}")
        return False
    
    critical_dlls = [
        "tesseract55.dll",
        "leptonica-1.85.0.dll", 
        "libpng16.dll",
        "zlib1.dll",
        "jpeg62.dll",
        "tiff.dll"
    ]
    
    all_good = True
    for dll in critical_dlls:
        can_load, message = check_dll_in_directory(dll, build_dir)
        status = "✅" if can_load else "❌"
        print(f"  {status} {dll}: {message}")
        if not can_load:
            all_good = False
    
    return all_good

def check_pyd_dependencies():
    """Try to identify what's preventing the .pyd from loading."""
    print("\n=== PYD Module Analysis ===")
    
    build_dir = os.path.abspath(os.path.join("ocr_accelerator", "x64", "Release"))
    pyd_path = os.path.join(build_dir, "ocr_accelerator.pyd")
    
    if not os.path.exists(pyd_path):
        print(f"❌ PYD file not found: {pyd_path}")
        return False
    
    print(f"✅ PYD file exists: {pyd_path}")
    print(f"   Size: {os.path.getsize(pyd_path):,} bytes")
    
    # Try to load the .pyd as a DLL to see if it has dependency issues
    try:
        # First, add the build directory to DLL search path
        if hasattr(os, 'add_dll_directory'):
            os.add_dll_directory(build_dir)
            print("✅ Added build directory to DLL search path")
        
        # Try to load the .pyd as a library
        handle = ctypes.windll.kernel32.LoadLibraryW(pyd_path)
        if handle:
            print("✅ PYD loads as DLL successfully")
            ctypes.windll.kernel32.FreeLibrary(handle)
            
            # Now try the Python import
            if build_dir not in sys.path:
                sys.path.insert(0, build_dir)
            
            print("🔄 Attempting Python import...")
            import ocr_accelerator
            print("✅ Python import successful!")
            return True
        else:
            error = ctypes.windll.kernel32.GetLastError()
            print(f"❌ PYD failed to load as DLL, error: {error}")
            
            # Error code meanings
            error_meanings = {
                126: "The specified module could not be found (missing dependency)",
                127: "The specified procedure could not be found",
                193: "Not a valid Win32 application (architecture mismatch)",
                1114: "A dynamic link library (DLL) initialization routine failed"
            }
            
            if error in error_meanings:
                print(f"   Meaning: {error_meanings[error]}")
            
            return False
            
    except Exception as e:
        print(f"❌ Exception during PYD analysis: {e}")
        return False

def suggest_solutions():
    """Suggest potential solutions."""
    print("\n=== Suggested Solutions ===")
    print("1. 🔄 Rebuild the C++ module with proper dependencies")
    print("2. 📁 Copy missing DLLs to the build directory")
    print("3. 🛠️  Check vcpkg installation and library linking")
    print("4. 🔍 Use Dependency Walker or similar tool for detailed analysis")
    print("5. ⚙️  Verify all Tesseract and image libraries are properly linked")

if __name__ == "__main__":
    if platform.system() != "Windows":
        print("This script is Windows-only")
        sys.exit(1)
    
    print("TESTRADE C++ Module Dependency Checker")
    print("=" * 50)
    
    # Check critical DLLs
    dlls_ok = check_critical_dlls()
    
    # Check PYD dependencies
    pyd_ok = check_pyd_dependencies()
    
    if dlls_ok and pyd_ok:
        print("\n🎉 All checks passed! Module should work.")
    else:
        print("\n❌ Issues found with module dependencies.")
        suggest_solutions()