# Sample Correlation Log Entries Analysis

## Overview
This document provides actual JSON samples of correlation log entries produced by Correlation<PERSON>ogger from various TESTRADE components, showing the exact structure and field locations for correlation ID propagation.

## Complete Pipeline Flow Sample

### 1. MillisecondOptimizationCapture - INJECTION_CONTROL
**Stage:** T0_CmdInject  
**Source Sense:** INJECTION_CONTROL  
**Purpose:** Logs the initial trade command injection

```json
{
  "global_sequence_id": 1001,
  "epoch_timestamp_s": 1748917126.4014971,
  "perf_timestamp_ns": 28251294925200,
  "source_sense": "INJECTION_CONTROL",
  "event_payload": {
    "injection_type": "trade_command",
    "stimulus_details": {
      "symbol": "AAPL",
      "side": "BUY",
      "quantity": 100.0,
      "order_type": "MKT",
      "correlation_id": "corr_abc123def456"
    },
    "derived_order_request_id": "inj_req_789xyz_1748917126401",
    "correlation_id": "corr_abc123def456",
    "injection_perf_timestamp_ns": 28251294925200,
    "injection_epoch_timestamp_s": 1748917126.4014971
  },
  "source_component_name": "MillisecondOptimizationCapture"
}
```

**Key Fields:**
- `correlation_id`: Primary correlation identifier
- `derived_order_request_id`: Generated request ID for pipeline tracking
- `injection_perf_timestamp_ns`: Precise injection timing for latency calculation

### 2. RiskManagementService - T1_OrderRequest
**Stage:** T1_OrderRequest  
**Source Sense:** CAPTURE  
**Purpose:** Logs risk assessment of order request

```json
{
  "global_sequence_id": 1002,
  "epoch_timestamp_s": 1748917126.4064972,
  "perf_timestamp_ns": 28251299925200,
  "source_sense": "CAPTURE",
  "event_payload": {
    "event_name": "OrderRequestEvent",
    "correlation_id": "corr_abc123def456",
    "request_id": "inj_req_789xyz_1748917126401",
    "symbol": "AAPL",
    "side": "BUY",
    "quantity": 100.0,
    "order_type": "MKT",
    "risk_assessment_status": "VALIDATED",
    "rejection_reason": null,
    "validation_pipeline_id": "val_corr_abc123def456",
    "component_processing_latency_ns": 2000000
  },
  "source_component_name": "RiskManagementService"
}
```

**Key Fields:**
- `event_name`: "OrderRequestEvent" for EventConditionChecker matching
- `risk_assessment_status`: "VALIDATED" or "REJECTED"
- `component_processing_latency_ns`: Internal processing time

### 3. TradeManagerService - T2_ValidatedOrderProcessed
**Stage:** T2_ValidatedOrderProcessed  
**Source Sense:** CAPTURE  
**Purpose:** Logs validated order processing and forwarding

```json
{
  "global_sequence_id": 1003,
  "epoch_timestamp_s": 1748917126.4134972,
  "perf_timestamp_ns": 28251306925200,
  "source_sense": "CAPTURE",
  "event_payload": {
    "event_name_in_payload": "ValidatedOrderProcessedByExecutor",
    "correlation_id": "corr_abc123def456",
    "request_id": "inj_req_789xyz_1748917126401",
    "symbol": "AAPL",
    "action_type": "OPEN_LONG",
    "order_params_side": "BUY",
    "order_params_quantity": 100.0,
    "order_params_type": "MKT",
    "status": "FORWARDED_TO_EXECUTOR",
    "validation_pipeline_id": "val_corr_abc123def456",
    "component_processing_latency_ns": 3000000
  },
  "source_component_name": "TradeManagerService"
}
```

**Key Fields:**
- `event_name_in_payload`: "ValidatedOrderProcessedByExecutor" for EventConditionChecker
- `status`: "FORWARDED_TO_EXECUTOR" or "SKIPPED_TRADING_DISABLED"
- `action_type`: Trade action classification

### 4. EnhancedLightspeedBroker - Broker ACK
**Stage:** T3_BrokerAck  
**Source Sense:** CAPTURE  
**Purpose:** Logs broker order acknowledgment

```json
{
  "global_sequence_id": 1004,
  "epoch_timestamp_s": 1748917126.4264972,
  "perf_timestamp_ns": 28251319925200,
  "source_sense": "CAPTURE",
  "event_payload": {
    "event_type": "broker_order_ack",
    "correlation_id": "corr_abc123def456",
    "original_request_id": "inj_req_789xyz_1748917126401",
    "broker_order_id": "LS_ORD_98765",
    "symbol": "AAPL",
    "broker_response_type": "ORDER_ACCEPTED",
    "broker_data": {
      "order_status": "PENDING_NEW",
      "side": "BUY",
      "quantity": 100.0,
      "order_type": "MKT",
      "timestamp": 1748917126.4264972
    },
    "component_processing_latency_ns": 1000000
  },
  "source_component_name": "EnhancedLightspeedBroker"
}
```

**Key Fields:**
- `broker_response_type`: "ORDER_ACCEPTED", "ORDER_REJECTED", "FILL"
- `broker_order_id`: Broker-assigned order identifier
- `original_request_id`: Links back to original request

## Enhanced Component Samples

### 5. EnhancedOCRDataConditioningService
**Purpose:** Logs OCR data processing with correlation tracking

```json
{
  "global_sequence_id": 2001,
  "epoch_timestamp_s": 1748917126.4024968,
  "perf_timestamp_ns": 28251296246400,
  "source_sense": "CAPTURE",
  "event_payload": {
    "event_type": "ocr_data_processed",
    "correlation_id": "corr_ocr789price456",
    "frame_number": 12345,
    "symbol": "MSFT",
    "ocr_processing_result": {
      "symbols_detected": ["MSFT"],
      "position_data": {
        "shares": 200,
        "avg_price": 285.5,
        "unrealized_pnl": 1250.0
      },
      "confidence_score": 0.95
    },
    "processing_stage": "data_conditioning",
    "component_processing_latency_ns": 8000000
  },
  "source_component_name": "EnhancedOCRDataConditioningService"
}
```

### 6. EnhancedPriceRepository
**Purpose:** Logs price updates with correlation tracking

```json
{
  "global_sequence_id": 2002,
  "epoch_timestamp_s": 1748917126.404497,
  "perf_timestamp_ns": 28251298246400,
  "source_sense": "CAPTURE",
  "event_payload": {
    "event_type": "price_update_processed",
    "correlation_id": "corr_ocr789price456",
    "symbol": "MSFT",
    "price_data": {
      "bid": 285.45,
      "ask": 285.55,
      "last": 285.5,
      "volume": 1500000,
      "timestamp": 1748917126.404497
    },
    "update_source": "websocket_feed",
    "component_processing_latency_ns": 500000
  },
  "source_component_name": "EnhancedPriceRepository"
}
```

## Key Field Location Summary

### Correlation ID Locations
- **Primary:** `event_payload.correlation_id` (all components)
- **Injection:** `event_payload.stimulus_details.correlation_id` (MillisecondOptimizationCapture)

### Request ID Locations
- **RMS/TMS:** `event_payload.request_id`
- **Injection:** `event_payload.derived_order_request_id`
- **Broker:** `event_payload.original_request_id`

### Event Type Identification
- **RMS:** `event_payload.event_name` = "OrderRequestEvent"
- **TMS:** `event_payload.event_name_in_payload` = "ValidatedOrderProcessedByExecutor"
- **Broker:** `event_payload.broker_response_type` = "ORDER_ACCEPTED"/"FILL"/"ORDER_REJECTED"
- **Enhanced:** `event_payload.event_type` = component-specific type

### Timing Fields
- **Log Level:** `perf_timestamp_ns` (nanosecond precision)
- **Injection:** `event_payload.injection_perf_timestamp_ns` (pipeline start time)
- **Component:** `event_payload.component_processing_latency_ns` (internal processing time)

## Pipeline Stage Mapping

| Stage | Source Sense | Event Type Identifier | Key Matching Fields |
|-------|--------------|----------------------|-------------------|
| T0_CmdInject | INJECTION_CONTROL | injection_type: "trade_command" | correlation_id, derived_order_request_id |
| T1_OrderRequest | CAPTURE | event_name: "OrderRequestEvent" | correlation_id, request_id |
| T2_ValidatedOrderProcessed | CAPTURE | event_name_in_payload: "ValidatedOrderProcessedByExecutor" | correlation_id, request_id |
| T3_BrokerAck | CAPTURE | broker_response_type: "ORDER_ACCEPTED" | correlation_id, original_request_id |

## EventConditionChecker Compatibility

Each log entry is structured to work with the corresponding EventConditionChecker functions:
- `check_injected_trade_command_logged` - matches INJECTION_CONTROL events
- `check_order_request_event_logged_by_rms` - matches RMS events with event_name
- `check_validated_order_event_logged_by_executor` - matches TMS events with event_name_in_payload
- `check_broker_ack_logged` - matches broker events with broker_response_type

This structure ensures reliable pipeline completion detection and correlation ID propagation throughout the entire TESTRADE system.
