import sys
import os
import importlib.util
import time
import logging
import gc
import psutil

# Configure logging
logging.basicConfig(level=logging.ERROR, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# First, import and execute patch_imports.py to prevent cv2 and pytesseract imports
logger.error("NATIVE_ISOLATE19: Loading patch_imports.py...")
patch_imports_path = os.path.join(os.path.dirname(__file__), 'patch_imports.py')
spec = importlib.util.spec_from_file_location("patch_imports", patch_imports_path)
patch_imports = importlib.util.module_from_spec(spec)
spec.loader.exec_module(patch_imports)

# Next, import and execute patch_main.py to set up tracemalloc
logger.error("NATIVE_ISOLATE19: Loading patch_main.py...")
patch_main_path = os.path.join(os.path.dirname(__file__), 'patch_main.py')
spec = importlib.util.spec_from_file_location("patch_main", patch_main_path)
patch_main = importlib.util.module_from_spec(spec)
spec.loader.exec_module(patch_main)

# Create test configuration
logger.error("NATIVE_ISOLATE19: Creating test configuration...")
test_config_path = os.path.join(os.path.dirname(__file__), 'test_config.py')
spec = importlib.util.spec_from_file_location("test_config", test_config_path)
test_config = importlib.util.module_from_spec(spec)
spec.loader.exec_module(test_config)
test_config.create_test_config()

def run_test():
    logger.error("NATIVE_ISOLATE19: Starting memory leak test for 15 minutes...")

    # Take baseline memory measurement before importing any modules
    process = psutil.Process()
    baseline_rss = process.memory_info().rss
    logger.error(f"NATIVE_ISOLATE19: Baseline RSS before imports: {baseline_rss / (1024 * 1024):.2f} MB")

    # Force garbage collection to ensure clean state
    gc.collect()

    # Import key modules that might cause memory leaks
    try:
        # Measure memory before config import
        pre_config_rss = process.memory_info().rss
        logger.error(f"NATIVE_ISOLATE19: Pre-config import RSS: {pre_config_rss / (1024 * 1024):.2f} MB")

        logger.error("NATIVE_ISOLATE19: Importing config.py...")
        import utils.config

        # Measure memory after config import
        post_config_rss = process.memory_info().rss
        logger.error(f"NATIVE_ISOLATE19: Post-config import RSS: {post_config_rss / (1024 * 1024):.2f} MB")
        logger.error(f"NATIVE_ISOLATE19: Config import memory impact: {(post_config_rss - pre_config_rss) / (1024 * 1024):.2f} MB")

        # Force garbage collection
        gc.collect()

        # Measure memory before global_config import
        pre_global_config_rss = process.memory_info().rss
        logger.error(f"NATIVE_ISOLATE19: Pre-global_config import RSS: {pre_global_config_rss / (1024 * 1024):.2f} MB")

        logger.error("NATIVE_ISOLATE19: Importing global_config.py...")
        import utils.global_config

        # Measure memory after global_config import
        post_global_config_rss = process.memory_info().rss
        logger.error(f"NATIVE_ISOLATE19: Post-global_config import RSS: {post_global_config_rss / (1024 * 1024):.2f} MB")
        logger.error(f"NATIVE_ISOLATE19: Global_config import memory impact: {(post_global_config_rss - pre_global_config_rss) / (1024 * 1024):.2f} MB")

        # Force garbage collection
        gc.collect()

        # Measure memory before load_global_config
        pre_load_config_rss = process.memory_info().rss
        logger.error(f"NATIVE_ISOLATE19: Pre-load_global_config RSS: {pre_load_config_rss / (1024 * 1024):.2f} MB")

        logger.error("NATIVE_ISOLATE19: Loading global configuration...")
        # Use default control.json path
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'utils', 'control.json')
        logger.error(f"NATIVE_ISOLATE19: Using config path: {config_path}")
        utils.global_config.load_global_config(config_path)

        # Measure memory after load_global_config
        post_load_config_rss = process.memory_info().rss
        logger.error(f"NATIVE_ISOLATE19: Post-load_global_config RSS: {post_load_config_rss / (1024 * 1024):.2f} MB")
        logger.error(f"NATIVE_ISOLATE19: Load_global_config memory impact: {(post_load_config_rss - pre_load_config_rss) / (1024 * 1024):.2f} MB")

        logger.error("NATIVE_ISOLATE19: Imports successful.")
    except Exception as e:
        logger.error(f"NATIVE_ISOLATE19: Error during imports: {e}", exc_info=True)

    # Log initial memory usage
    process = psutil.Process()
    initial_rss = process.memory_info().rss
    logger.error(f"NATIVE_ISOLATE19: Initial RSS: {initial_rss / (1024 * 1024):.2f} MB")

    # Create directory for memory leak logs if it doesn't exist
    os.makedirs('mem leak', exist_ok=True)

    # Write test start to file
    with open(os.path.join('mem leak', 'native_isolate19_progress.log'), 'w') as f:
        f.write(f"NATIVE_ISOLATE19 Test Started: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Initial RSS: {initial_rss / (1024 * 1024):.2f} MB\n\n")

    # Log memory usage every minute
    last_log_time = time.time()
    log_interval = 60  # seconds
    end_time = time.time() + 15 * 60  # 15 minutes

    # Run a loop that periodically forces garbage collection and logs memory usage
    while time.time() < end_time:
        try:
            # Force garbage collection to ensure we're measuring real leaks
            gc.collect()

            # Log memory usage every minute
            current_time = time.time()
            if current_time - last_log_time >= log_interval:
                current_rss = process.memory_info().rss
                elapsed_minutes = (current_time - end_time + 15 * 60) / 60
                growth_mb = (current_rss - initial_rss) / (1024 * 1024)

                # Log to console
                logger.error(f"NATIVE_ISOLATE19: [{elapsed_minutes:.1f} min] RSS: {current_rss / (1024 * 1024):.2f} MB, Growth: {growth_mb:.2f} MB")

                # Write intermediate results to file
                with open(os.path.join('mem leak', 'native_isolate19_progress.log'), 'a') as f:
                    f.write(f"[{elapsed_minutes:.1f} min] RSS: {current_rss / (1024 * 1024):.2f} MB, Growth: {growth_mb:.2f} MB\n")

                last_log_time = current_time

            time.sleep(1.0)  # Sleep to reduce CPU usage
        except KeyboardInterrupt:
            logger.error("NATIVE_ISOLATE19: Test interrupted by user.")
            break
        except Exception as e:
            logger.error(f"NATIVE_ISOLATE19: Error during sleep: {e}", exc_info=True)
            # Continue the test even if there are errors

    # Log final memory usage
    final_rss = process.memory_info().rss
    growth_mb = (final_rss - initial_rss) / (1024 * 1024)
    logger.error(f"NATIVE_ISOLATE19: Final RSS: {final_rss / (1024 * 1024):.2f} MB, Total Growth: {growth_mb:.2f} MB")

    # Write final results to file
    with open(os.path.join('mem leak', 'native_isolate19_progress.log'), 'a') as f:
        f.write(f"\nTest Completed: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Final RSS: {final_rss / (1024 * 1024):.2f} MB\n")
        f.write(f"Total Growth: {growth_mb:.2f} MB\n")

    logger.error("NATIVE_ISOLATE19: Test completed. Results saved to 'mem leak/native_isolate19_progress.log'")

    # Clean up
    logger.error("NATIVE_ISOLATE19: Performing final cleanup...")

    # Force garbage collection one more time
    gc.collect()

    # Clean up imported modules
    try:
        # Remove references to imported modules
        if 'utils.config' in sys.modules:
            del sys.modules['utils.config']
        if 'utils.global_config' in sys.modules:
            del sys.modules['utils.global_config']
        logger.error("NATIVE_ISOLATE19: Module cleanup completed.")
    except Exception as e_cleanup:
        logger.error(f"NATIVE_ISOLATE19: Error during cleanup: {e_cleanup}", exc_info=True)

# Run the test
if __name__ == "__main__":
    logger.error("NATIVE_ISOLATE19: Starting test...")
    try:
        run_test()
        logger.error("NATIVE_ISOLATE19: Test completed successfully.")
    except Exception as e:
        logger.error(f"NATIVE_ISOLATE19: Test failed with error: {e}", exc_info=True)
    finally:
        logger.error("NATIVE_ISOLATE19: Test execution finished.")
