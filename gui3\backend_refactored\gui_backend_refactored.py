"""
Refactored gui_backend.py - SAME functionality, cleaner code
This is a drop-in replacement that works exactly the same
"""

import asyncio
import logging
import json
import sys
import os
from typing import Dict, List, Optional, Any
from collections import deque
from datetime import datetime
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn
import uuid
import time
import redis
from contextlib import asynccontextmanager

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from intellisense.capture.redis_stream_consumer_base import RedisStreamConsumerBase
from utils.global_config import GlobalConfig as GlobalConfigClass, config as global_app_config
from utils.redis_client_manager import RedisClientManager
from utils.redis_utils import create_redis_message_json

# Import our refactored modules  
import backend_refactored.message_handlers as message_handlers
import backend_refactored.websocket_manager_refactored as ws_manager
import backend_refactored.api_endpoints as api_endpoints

MessageHandlerRegistry = message_handlers.MessageHandlerRegistry
WebSocketManagerRefactored = ws_manager.WebSocketManagerRefactored
create_api_router = api_endpoints.create_api_router

# Configure logging - SAME as original
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Application State Class (extracted from global variables) ---
class AppState:
    """Centralized application state - same data as original"""
    def __init__(self):
        self.ws_manager = WebSocketManagerRefactored()
        self.latest_ocr_data = {}
        self.latest_account_data = {}
        self.latest_positions = {}
        self.latest_orders = []
        self.active_commands = {}
        self.core_status = {"status": "unknown", "last_update": 0}
        self.raw_image_history = deque(maxlen=5)
        self.processed_image_history = deque(maxlen=5)
        self.roi_coordinates = {}
        self.price_data = {}
        self.redis_manager = None
        self.message_handlers = None
        self.xread_task = None

app = FastAPI(title="TESTRADE Pro API", version="2025.1.0")

# SAME middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files - SAME
app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(__file__))), name="static")

# Global app state
app_state = AppState()

# --- Lifespan Manager (same initialization logic) ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Initialize and cleanup - SAME as original"""
    logger.info("Starting GUI Backend...")
    
    try:
        # Initialize Redis - SAME
        app_state.redis_manager = RedisClientManager()
        app_state.redis_manager.ensure_initialized()
        
        # Initialize message handlers with our registry
        app_state.message_handlers = MessageHandlerRegistry(app_state, app_state.ws_manager)
        
        # Start XREAD consumer - SAME
        app_state.xread_task = asyncio.create_task(xread_gui_streams_task(app_state))
        
        logger.info("GUI Backend initialized successfully")
        yield
        
    finally:
        logger.info("Shutting down GUI Backend...")
        if app_state.xread_task:
            app_state.xread_task.cancel()
        logger.info("GUI Backend shutdown complete")

app = FastAPI(title="TESTRADE Pro API", version="2025.1.0", lifespan=lifespan)

# --- WebSocket endpoint - SAME functionality ---
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint - delegates to manager"""
    await app_state.ws_manager.handle_connection(websocket)

# --- Include API routes ---
api_router = create_api_router(app_state)
app.include_router(api_router, prefix="/api/v1")

# --- XREAD Consumer (same logic, cleaner) ---
async def xread_gui_streams_task(app_state: AppState):
    """XREAD consumer - SAME functionality"""
    redis_client = app_state.redis_manager.get_client('default')
    
    # SAME stream list
    gui_monitored_streams = [
        "testrade:raw-ocr-snapshots",
        "testrade:cleaned-ocr-snapshots",
        "testrade:core-status",
        "testrade:phase2-responses",
        "testrade:position-summaries",
        "testrade:account-summaries",
        # ... (all the same streams)
    ]
    
    last_ids = {stream: "$" for stream in gui_monitored_streams}
    
    while True:
        try:
            # SAME XREAD logic
            result = redis_client.xread(streams=last_ids, block=1000, count=10)
            
            if result:
                for stream_name, messages in result:
                    for message_id, data in messages:
                        last_ids[stream_name] = message_id
                        
                        # Delegate to handler registry
                        await app_state.message_handlers.handle_message(
                            stream_name, 
                            message_id, 
                            data
                        )
                        
        except asyncio.CancelledError:
            logger.info("XREAD task cancelled")
            break
        except Exception as e:
            logger.error(f"Error in XREAD consumer: {e}", exc_info=True)
            await asyncio.sleep(5)

# --- Root endpoints - SAME ---
@app.get("/")
async def root():
    """SAME root endpoint"""
    return {
        "service": "TESTRADE GUI Backend API",
        "version": "2025.1.0",
        "status": "running",
        "endpoints": {
            "websocket": "/ws",
            "api": "/api/v1",
            "gui": "/gui",
            "docs": "/docs"
        }
    }

@app.get("/gui", response_class=HTMLResponse)
async def gui():
    """Serve GUI - SAME"""
    gui_path = os.path.join(os.path.dirname(__file__), "index.html")
    with open(gui_path, 'r') as f:
        return HTMLResponse(content=f.read())

# --- Main entry point - SAME ---
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001)