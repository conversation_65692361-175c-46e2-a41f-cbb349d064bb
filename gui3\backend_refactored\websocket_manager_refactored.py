"""
Refactored WebSocket Manager - extracted from gui_backend.py
Maintains exact same functionality
"""

import asyncio
import logging
import json
import time
from typing import List, Dict, Any
from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger(__name__)


class WebSocketManagerRefactored:
    """Extracted WebSocket management - same functionality as original"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.logger = logging.getLogger(__name__)
        self._last_connection_status_log = 0
        self._connection_status_interval = 300  # Same 5 minute interval
    
    async def connect(self, websocket: WebSocket):
        """Accept new connection - same as original"""
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"🔌 GUI client connected: {websocket.client.host} (Total: {len(self.active_connections)})")
    
    def disconnect(self, websocket: WebSocket):
        """Remove connection - same as original"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"🔌 GUI client disconnected: {websocket.client.host} (Remaining: {len(self.active_connections)})")
    
    async def _send_to_connection(self, connection: WebSocket, message_str: str):
        """Send to single connection - same error handling"""
        try:
            await connection.send_text(message_str)
        except WebSocketDisconnect:
            self.logger.warning(f"WebSocket client {connection.client.host} disconnected during send.")
            self.disconnect(connection)
        except Exception as e:
            self.logger.error(f"Error sending to WebSocket client {connection.client.host}: {e}", exc_info=True)
            self.disconnect(connection)
    
    async def broadcast(self, message: dict, high_priority: bool = False):
        """Broadcast to all connections - same logic as original"""
        if not self.active_connections:
            # Same periodic logging
            current_time = time.time()
            if current_time - self._last_connection_status_log > self._connection_status_interval:
                self.logger.info(f"📊 GUI Backend Status: No GUI clients connected, but receiving data streams (OCR, images, etc.)")
                self.logger.info(f"💡 To connect GUI: Open browser to http://localhost:8001/gui or ws://localhost:8001/ws")
                self._last_connection_status_log = current_time
            
            # Same priority-based logging
            message_type = message.get('type', 'unknown')
            if high_priority or message_type in ['system_message', 'error', 'core_status_update', 'command_response']:
                self.logger.info(f"🔌 No GUI clients connected for broadcast: {message_type}")
            else:
                self.logger.debug(f"🔌 No GUI clients connected for broadcast: {message_type}")
            return
        
        # Same broadcast implementation
        message_type = message.get('type', 'unknown')
        self.logger.info(f"📡 Broadcasting {message_type} to {len(self.active_connections)} connections")
        
        if high_priority:
            self.logger.info(f"🚨 HIGH PRIORITY: Broadcasting {message_type}")
        
        # Convert once
        message_str = json.dumps(message)
        
        # Send to all with same error handling
        disconnected_clients = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message_str)
            except Exception as e:
                self.logger.debug(f"Failed to send to client {connection.client.host}: {e}")
                disconnected_clients.append(connection)
        
        # Cleanup disconnected
        for client in disconnected_clients:
            self.disconnect(client)
    
    async def handle_connection(self, websocket: WebSocket):
        """Handle a WebSocket connection - main entry point"""
        await self.connect(websocket)
        
        try:
            while True:
                # Wait for messages from client
                data = await websocket.receive_text()
                
                # Parse and handle - same as original
                try:
                    message = json.loads(data)
                    logger.debug(f"Received from client: {message.get('type', 'unknown')}")
                    
                    # Handle different message types
                    if message.get('type') == 'ping':
                        await websocket.send_json({'type': 'pong', 'timestamp': time.time()})
                    
                    # Other message handling can be added here
                    
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON from client: {data[:100]}")
                    
        except WebSocketDisconnect:
            logger.info("Client disconnected")
        except Exception as e:
            logger.error(f"WebSocket error: {e}", exc_info=True)
        finally:
            self.disconnect(websocket)