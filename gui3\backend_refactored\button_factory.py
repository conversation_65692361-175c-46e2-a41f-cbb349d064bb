"""
Button Factory to simplify button creation in app.js
Makes it easy to add buttons without writing hundreds of lines
"""

class ButtonFactory {
    constructor() {
        this.buttonConfigs = {};
    }

    /**
     * Register a button configuration
     * This makes adding a new button a one-liner instead of 50+ lines
     */
    registerButton(id, config) {
        this.buttonConfigs[id] = {
            id: id,
            label: config.label || id,
            action: config.action || (() => console.log(`${id} clicked`)),
            confirmMessage: config.confirmMessage || null,
            successMessage: config.successMessage || `${config.label || id} successful`,
            errorMessage: config.errorMessage || `${config.label || id} failed`,
            className: config.className || 'btn btn-primary',
            icon: config.icon || null,
            requiresData: config.requiresData || [],
            ...config
        };
    }

    /**
     * Create a button element with all handlers attached
     * Replaces repetitive button creation code
     */
    createButton(id) {
        const config = this.buttonConfigs[id];
        if (!config) {
            console.error(`No configuration found for button: ${id}`);
            return null;
        }

        const button = document.createElement('button');
        button.id = config.id;
        button.className = config.className;
        button.innerHTML = config.icon ? 
            `<i class="${config.icon}"></i> ${config.label}` : 
            config.label;

        // Attach click handler with all the standard logic
        button.addEventListener('click', async () => {
            try {
                // Disable button
                button.disabled = true;
                button.classList.add('loading');

                // Show confirmation if needed
                if (config.confirmMessage) {
                    if (!confirm(config.confirmMessage)) {
                        button.disabled = false;
                        button.classList.remove('loading');
                        return;
                    }
                }

                // Gather required data
                const data = {};
                for (const field of config.requiresData) {
                    const value = document.getElementById(field)?.value;
                    if (!value) {
                        throw new Error(`${field} is required`);
                    }
                    data[field] = value;
                }

                // Execute action
                const result = await config.action(data);

                // Show success
                this.showSuccess(button, config.successMessage);
                
                // Call success callback if provided
                if (config.onSuccess) {
                    config.onSuccess(result);
                }

            } catch (error) {
                // Show error
                this.showError(button, config.errorMessage || error.message);
                
                // Call error callback if provided
                if (config.onError) {
                    config.onError(error);
                }
            } finally {
                // Re-enable button after delay
                setTimeout(() => {
                    button.disabled = false;
                    button.classList.remove('loading', 'success', 'error');
                }, 2000);
            }
        });

        return button;
    }

    showSuccess(button, message) {
        button.classList.remove('loading', 'error');
        button.classList.add('success');
        const originalText = button.innerHTML;
        button.innerHTML = `<i class="fas fa-check"></i> ${message}`;
        setTimeout(() => {
            button.innerHTML = originalText;
        }, 2000);
    }

    showError(button, message) {
        button.classList.remove('loading', 'success');
        button.classList.add('error');
        const originalText = button.innerHTML;
        button.innerHTML = `<i class="fas fa-times"></i> ${message}`;
        setTimeout(() => {
            button.innerHTML = originalText;
        }, 2000);
    }
}

// Export for use in app.js
window.ButtonFactory = ButtonFactory;


/* 
USAGE EXAMPLE - This is how simple it becomes to add buttons:

const buttonFactory = new ButtonFactory();

// Register all your buttons with their configs
buttonFactory.registerButton('buyButton', {
    label: 'Buy',
    icon: 'fas fa-shopping-cart',
    className: 'btn btn-success',
    confirmMessage: 'Confirm buy order?',
    requiresData: ['symbolInput', 'quantityInput'],
    action: async (data) => {
        return await sendCommand('buy_order', {
            symbol: data.symbolInput,
            quantity: data.quantityInput
        });
    },
    successMessage: 'Buy order placed!',
    errorMessage: 'Failed to place buy order'
});

// Create and add to DOM
const buyBtn = buttonFactory.createButton('buyButton');
document.getElementById('tradingButtons').appendChild(buyBtn);

// That's it! No more 50+ lines of repetitive code per button
*/