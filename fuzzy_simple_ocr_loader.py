#!/usr/bin/env python3
"""
FUZZY'S SIMPLE OCR ACCELERATOR LOADER
No complex DLL management - just works!
"""

import os
import sys
import platform

def load_ocr_accelerator_simple():
    """Simple, reliable OCR accelerator loader"""
    
    if platform.system() != "Windows":
        raise ImportError("OCR accelerator requires Windows")
    
    # Build directory
    build_dir = os.path.abspath("ocr_accelerator/x64/Release")
    
    if not os.path.exists(build_dir):
        raise ImportError(f"Build directory not found: {build_dir}")
    
    # Check critical files
    pyd_file = os.path.join(build_dir, "ocr_accelerator.pyd")
    if not os.path.exists(pyd_file):
        raise ImportError(f"ocr_accelerator.pyd not found in {build_dir}")
    
    # Add to DLL search path
    if hasattr(os, 'add_dll_directory'):
        os.add_dll_directory(build_dir)
    
    # Add to PATH
    current_path = os.environ.get('PATH', '')
    if build_dir not in current_path:
        os.environ['PATH'] = build_dir + os.pathsep + current_path
    
    # Add to sys.path
    if build_dir not in sys.path:
        sys.path.insert(0, build_dir)
    
    # Change to build directory and import
    original_cwd = os.getcwd()
    try:
        os.chdir(build_dir)
        import ocr_accelerator
        return ocr_accelerator
    finally:
        os.chdir(original_cwd)

def test_simple_loader():
    """Test the simple loader"""
    print("🎯 FUZZY'S SIMPLE OCR LOADER TEST")
    print("=" * 40)
    
    try:
        ocr_module = load_ocr_accelerator_simple()
        print("✅ Module loaded successfully")
        
        # Check functions
        attrs = [attr for attr in dir(ocr_module) if not attr.startswith('_')]
        print(f"Available functions: {attrs}")
        
        # Test functions
        if hasattr(ocr_module, 'test_function'):
            result = ocr_module.test_function()
            print(f"✅ test_function(): {result}")
        else:
            print("❌ test_function not found")
        
        if hasattr(ocr_module, 'process_image_and_ocr'):
            print("✅ process_image_and_ocr function found")
        else:
            print("❌ process_image_and_ocr not found")
        
        return ocr_module
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_simple_loader()
