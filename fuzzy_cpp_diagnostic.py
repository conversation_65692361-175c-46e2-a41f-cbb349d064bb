#!/usr/bin/env python3
"""
FUZZY'S COMPREHENSIVE C++ MODULE DIAGNOSTIC
Diagnose why the rebuilt module still has no functions.
"""
import os
import sys
from datetime import datetime

def fuzzy_diagnose():
    """FUZZY'S deep diagnostic of the C++ module issue."""
    print("🔍 === FUZZY'S C++ MODULE DIAGNOSTIC === 🔍")
    
    # 1. Check project file fix
    print("\n🔧 1. CHECKING PROJECT FILE FIX...")
    vcxproj_path = "ocr_accelerator/ocr_accelerator.vcxproj"
    if os.path.exists(vcxproj_path):
        with open(vcxproj_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "COMMENTED OUT TO FIX PYBIND11 EXPORTS" in content:
            print("✅ FUZZY'S fix is in place")
        elif "<!-- <ModuleDefinitionFile>exports.def</ModuleDefinitionFile>" in content:
            print("✅ ModuleDefinitionFile is commented out")
        elif "<ModuleDefinitionFile>exports.def</ModuleDefinitionFile>" in content:
            print("❌ CRITICAL: ModuleDefinitionFile is still ACTIVE!")
            print("💡 Visual Studio may have reverted the change")
            return False
        else:
            print("⚠️  No ModuleDefinitionFile found")
    else:
        print("❌ Project file not found")
        return False
    
    # 2. Check .pyd file
    print("\n📅 2. CHECKING BUILD ARTIFACTS...")
    pyd_path = "ocr_accelerator/x64/Release/ocr_accelerator.pyd"
    if os.path.exists(pyd_path):
        stat = os.stat(pyd_path)
        mod_time = datetime.fromtimestamp(stat.st_mtime)
        size = stat.st_size
        age_minutes = (datetime.now() - mod_time).total_seconds() / 60
        
        print(f"✅ .pyd file exists: {size} bytes")
        print(f"📅 Last modified: {mod_time} ({age_minutes:.1f} min ago)")
        
        if age_minutes > 30:
            print("⚠️  File is old - may not be recently rebuilt")
    else:
        print("❌ .pyd file not found")
        return False
    
    # 3. Check C++ source
    print("\n🔍 3. CHECKING C++ SOURCE...")
    cpp_path = "ocr_accelerator/ocr_accelerator.cpp"
    if os.path.exists(cpp_path):
        with open(cpp_path, 'r', encoding='utf-8') as f:
            cpp_content = f.read()
        
        # Check for pybind11 module definition
        if "PYBIND11_MODULE(ocr_accelerator, m)" in cpp_content:
            print("✅ PYBIND11_MODULE definition found")
        else:
            print("❌ CRITICAL: PYBIND11_MODULE missing!")
            return False
            
        # Check function definitions
        if 'm.def("test_function"' in cpp_content:
            print("✅ test_function export found")
        else:
            print("❌ test_function export missing!")
            
        if 'm.def("process_image_and_ocr"' in cpp_content:
            print("✅ process_image_and_ocr export found")
        else:
            print("❌ process_image_and_ocr export missing!")
    else:
        print("❌ C++ source not found")
        return False
    
    # 4. Test module import
    print("\n🧪 4. TESTING MODULE IMPORT...")
    try:
        # Clear any cached imports
        if 'ocr_accelerator' in sys.modules:
            del sys.modules['ocr_accelerator']
        
        # Add path and import
        sys.path.insert(0, "ocr_accelerator/x64/Release")
        import ocr_accelerator
        
        print("✅ Module imports successfully")
        print(f"📍 Module file: {ocr_accelerator.__file__}")
        
        # Check available functions
        attrs = [attr for attr in dir(ocr_accelerator) if not attr.startswith('_')]
        print(f"📋 Available functions: {attrs}")
        
        if len(attrs) == 0:
            print("❌ CRITICAL: NO FUNCTIONS EXPORTED!")
            print("💡 This confirms the export issue")
            return False
        
        # Test specific functions
        if hasattr(ocr_accelerator, 'test_function'):
            print("✅ test_function available")
            try:
                result = ocr_accelerator.test_function()
                print(f"✅ test_function works: {result}")
            except Exception as e:
                print(f"❌ test_function error: {e}")
        else:
            print("❌ test_function not available")
            
        if hasattr(ocr_accelerator, 'process_image_and_ocr'):
            print("✅ process_image_and_ocr available")
        else:
            print("❌ process_image_and_ocr not available")
            
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Module test failed: {e}")
        return False
    
    return True

def fuzzy_suggest_fixes():
    """FUZZY'S suggested fixes for the build issue."""
    print("\n🔧 === FUZZY'S SUGGESTED FIXES === 🔧")
    print("1. 🧹 CLEAN SOLUTION: Build → Clean Solution in Visual Studio")
    print("2. 🗑️  DELETE BUILD FOLDER: Manually delete ocr_accelerator/x64 folder")
    print("3. 🔄 REBUILD ALL: Build → Rebuild Solution (not just Build)")
    print("4. 📝 VERIFY CONFIG: Ensure Release x64 configuration is selected")
    print("5. 🔍 CHECK PROJECT: Verify ModuleDefinitionFile line is commented out")
    print("6. 🚀 NUCLEAR OPTION: Close VS, delete all build artifacts, reopen VS, rebuild")
    print("\n💡 FUZZY'S THEORY: Visual Studio may be using cached build settings")
    print("   that still reference the exports.def file even though it's commented out.")

def check_vs_cache():
    """Check for Visual Studio cache files that might be causing issues."""
    print("\n🗂️  5. CHECKING VS CACHE FILES...")
    
    cache_paths = [
        "ocr_accelerator/.vs",
        "ocr_accelerator/x64/Release/ocr_accelerator.tlog",
        "ocr_accelerator/x64/Release/ocr_accelerator.log"
    ]
    
    for path in cache_paths:
        if os.path.exists(path):
            print(f"⚠️  Found cache: {path}")
            if path.endswith('.log'):
                # Check if log mentions exports.def
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        log_content = f.read()
                    if 'exports.def' in log_content:
                        print(f"❌ Build log still references exports.def!")
                        print("💡 This confirms VS is using cached settings")
                except:
                    pass
        else:
            print(f"✅ No cache: {path}")

if __name__ == "__main__":
    print("🚀 FUZZY'S C++ MODULE DIAGNOSTIC STARTING...")
    
    success = fuzzy_diagnose()
    check_vs_cache()
    
    if not success:
        fuzzy_suggest_fixes()
        print("\n❌ FUZZY FOUND CRITICAL ISSUES - FOLLOW SUGGESTED FIXES")
    else:
        print("\n✅ FUZZY FOUND NO OBVIOUS ISSUES - MODULE SHOULD BE WORKING")
    
    print("\n🎯 FUZZY'S FINAL RECOMMENDATION:")
    print("   Try the NUCLEAR OPTION: Close VS, delete x64 folder, rebuild from scratch")
