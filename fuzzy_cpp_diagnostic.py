#!/usr/bin/env python3
"""
FUZZY'S C++ EXTENSION DIAGNOSTIC TOOL
"""

import os
import sys
import platform

def fuzzy_diagnose_cpp_extension():
    """<PERSON><PERSON>'s comprehensive C++ extension diagnostic"""
    print("🔍 FUZZY'S C++ EXTENSION DIAGNOSTIC")
    print("=" * 50)
    
    # 1. Check platform
    print(f"Platform: {platform.system()} {platform.architecture()}")
    print(f"Python: {sys.version}")
    
    # 2. Check build directory
    build_dir = "ocr_accelerator/x64/Release"
    abs_build_dir = os.path.abspath(build_dir)
    
    print(f"\n📁 BUILD DIRECTORY CHECK:")
    print(f"Path: {abs_build_dir}")
    print(f"Exists: {os.path.exists(build_dir)}")
    
    if not os.path.exists(build_dir):
        print("❌ BUILD DIRECTORY NOT FOUND!")
        return False
    
    # 3. Check critical files
    critical_files = [
        "ocr_accelerator.pyd",
        "tesseract55.dll", 
        "leptonica-1.85.0.dll",
        "opencv_core4.dll",
        "opencv_imgproc4.dll"
    ]
    
    print(f"\n📋 CRITICAL FILES CHECK:")
    missing_files = []
    for file in critical_files:
        file_path = os.path.join(build_dir, file)
        exists = os.path.exists(file_path)
        print(f"  {file}: {'✅' if exists else '❌'}")
        if not exists:
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ MISSING FILES: {missing_files}")
        return False
    
    # 4. Check current working directory
    print(f"\n📍 CURRENT WORKING DIRECTORY:")
    print(f"CWD: {os.getcwd()}")
    
    # 5. Check sys.path
    print(f"\n🛤️ PYTHON PATH CHECK:")
    if abs_build_dir in sys.path:
        print(f"✅ Build dir in sys.path")
    else:
        print(f"❌ Build dir NOT in sys.path")
        print(f"Adding: {abs_build_dir}")
        sys.path.insert(0, abs_build_dir)
    
    # 6. Check DLL search path (Windows)
    if platform.system() == "Windows":
        print(f"\n🔍 WINDOWS DLL SEARCH PATH:")
        current_path = os.environ.get('PATH', '')
        if abs_build_dir in current_path:
            print(f"✅ Build dir in PATH")
        else:
            print(f"❌ Build dir NOT in PATH")
            print(f"Adding to PATH...")
            os.environ['PATH'] = abs_build_dir + os.pathsep + current_path
        
        # Try add_dll_directory if available
        if hasattr(os, 'add_dll_directory'):
            try:
                os.add_dll_directory(abs_build_dir)
                print(f"✅ Added DLL directory: {abs_build_dir}")
            except Exception as e:
                print(f"❌ Failed to add DLL directory: {e}")
    
    # 7. Test import from build directory
    print(f"\n🧪 IMPORT TEST:")
    original_cwd = os.getcwd()
    
    try:
        print(f"Changing to: {abs_build_dir}")
        os.chdir(abs_build_dir)
        
        print("Attempting import...")
        import ocr_accelerator
        
        print("✅ IMPORT SUCCESSFUL!")
        
        # Check available functions
        attrs = [attr for attr in dir(ocr_accelerator) if not attr.startswith('_')]
        print(f"Available functions: {attrs}")
        
        # Test basic function if available
        if hasattr(ocr_accelerator, 'test_function'):
            try:
                result = ocr_accelerator.test_function()
                print(f"✅ test_function() result: {result}")
            except Exception as e:
                print(f"❌ test_function() failed: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ IMPORT FAILED: {e}")
        
        # Additional diagnostics for ImportError
        print(f"\n🔬 IMPORT ERROR ANALYSIS:")
        error_str = str(e).lower()
        
        if "dll load failed" in error_str:
            print("  → DLL dependency issue")
            print("  → Check if all required DLLs are present")
            print("  → Verify DLL architecture (x64 vs x86)")
        elif "no module named" in error_str:
            print("  → Module not found in current directory")
            print("  → Check if .pyd file exists")
        elif "bad magic number" in error_str:
            print("  → Python version mismatch")
            print("  → Rebuild extension for current Python version")
        
        return False
        
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        os.chdir(original_cwd)
        print(f"Restored CWD: {os.getcwd()}")

def fuzzy_test_ocr_dll_manager():
    """Test the OCR DLL manager approach"""
    print(f"\n🧪 TESTING OCR DLL MANAGER:")
    
    try:
        from ocr_dll_manager import test_ocr_accelerator
        result = test_ocr_accelerator()
        print(f"OCR DLL Manager test: {'✅ PASSED' if result else '❌ FAILED'}")
        return result
    except Exception as e:
        print(f"❌ OCR DLL Manager test failed: {e}")
        return False

if __name__ == "__main__":
    print("🎯 FUZZY'S C++ EXTENSION DIAGNOSTIC SUITE")
    print("=" * 60)
    
    # Run main diagnostic
    main_result = fuzzy_diagnose_cpp_extension()
    
    # Test DLL manager if main test fails
    if not main_result:
        dll_manager_result = fuzzy_test_ocr_dll_manager()
        
        if dll_manager_result:
            print(f"\n✅ SOLUTION: Use OCR DLL Manager approach")
        else:
            print(f"\n❌ BOTH APPROACHES FAILED")
            print(f"\n🔧 FUZZY'S RECOMMENDATIONS:")
            print(f"1. Verify C++ extension was built for correct Python version")
            print(f"2. Check all DLL dependencies are present")
            print(f"3. Ensure x64 architecture consistency")
            print(f"4. Try rebuilding the extension")
    else:
        print(f"\n✅ C++ EXTENSION LOADING WORKS!")
