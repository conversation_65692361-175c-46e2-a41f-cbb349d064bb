#!/usr/bin/env python3
"""
Position Pipeline Diagnostic Tool

Helps diagnose the 10-minute delay in position data flowing from broker → GUI.
Uses your existing pipeline validator and performance benchmarker tools.
"""

import sys
import os
import time
import json
import redis
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.performance_benchmarker import PerformanceBenchmarker
from utils.pipeline_validator import PipelineValidator

# Redis connection settings
REDIS_HOST = "**************"
REDIS_PORT = 6379
REDIS_DB = 0

def connect_to_redis():
    """Connect to Redis with error handling."""
    try:
        client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB, decode_responses=True)
        client.ping()
        print(f"✅ Connected to Redis at {REDIS_HOST}:{REDIS_PORT}")
        return client
    except Exception as e:
        print(f"❌ Failed to connect to Redis: {e}")
        return None

def check_position_stream_health(redis_client):
    """Check the health of position-related Redis streams."""
    print("\n🔍 POSITION STREAM HEALTH CHECK")
    print("=" * 50)
    
    position_streams = [
        'testrade:position-updates',
        'testrade:order-fills',
        'testrade:order-status',
        'testrade:trades_update'  # GUI data source
    ]
    
    for stream in position_streams:
        try:
            length = redis_client.xlen(stream)
            print(f"{stream}: {length:,} messages")
            
            if length > 0:
                # Check consumer groups
                groups = redis_client.xinfo_groups(stream)
                if groups:
                    for group in groups:
                        group_name = group.get('name', 'unknown')
                        pending = group.get('pending', 0)
                        consumers = group.get('consumers', 0)
                        
                        if pending > 10:  # Potential backlog
                            print(f"  ⚠️ Group '{group_name}': {pending} pending messages, {consumers} consumers")
                        else:
                            print(f"  ✅ Group '{group_name}': {pending} pending, {consumers} consumers")
                else:
                    print(f"  ❌ No consumer groups - stream not being consumed!")
                    
        except Exception as e:
            print(f"{stream}: ERROR - {e}")

def measure_redis_latency(redis_client, iterations=10):
    """Measure Redis operation latency."""
    print(f"\n⏱️ REDIS LATENCY TEST ({iterations} iterations)")
    print("=" * 50)
    
    latencies = []
    
    for i in range(iterations):
        start_time = time.time()
        redis_client.ping()
        latency = (time.time() - start_time) * 1000  # Convert to ms
        latencies.append(latency)
    
    avg_latency = sum(latencies) / len(latencies)
    max_latency = max(latencies)
    min_latency = min(latencies)
    
    print(f"Average latency: {avg_latency:.2f}ms")
    print(f"Min latency: {min_latency:.2f}ms")
    print(f"Max latency: {max_latency:.2f}ms")
    
    if avg_latency > 10:
        print("⚠️ High Redis latency detected!")
    else:
        print("✅ Redis latency looks good")
    
    return avg_latency

def check_recent_position_updates(redis_client):
    """Check for recent position updates in the stream."""
    print("\n📊 RECENT POSITION UPDATES")
    print("=" * 50)
    
    stream = 'testrade:position-updates'
    
    try:
        # Get last 5 messages
        messages = redis_client.xrevrange(stream, count=5)
        
        if not messages:
            print("❌ No position updates found in stream")
            return
        
        print(f"Last {len(messages)} position updates:")
        
        for msg_id, fields in messages:
            # Parse timestamp from message ID (format: timestamp-sequence)
            timestamp_ms = int(msg_id.split('-')[0])
            msg_time = datetime.fromtimestamp(timestamp_ms / 1000)
            time_ago = datetime.now() - msg_time
            
            # Parse message content
            json_payload = fields.get('json_payload', '{}')
            try:
                data = json.loads(json_payload)
                symbol = data.get('payload', {}).get('symbol', 'Unknown')
                shares = data.get('payload', {}).get('total_shares', 'Unknown')
                
                print(f"  {msg_time.strftime('%H:%M:%S')} ({time_ago.total_seconds():.0f}s ago): {symbol} = {shares} shares")
                
                if time_ago.total_seconds() > 600:  # 10 minutes
                    print(f"    ⚠️ This update is {time_ago.total_seconds()/60:.1f} minutes old!")
                    
            except json.JSONDecodeError:
                print(f"  {msg_time.strftime('%H:%M:%S')}: Invalid JSON payload")
                
    except Exception as e:
        print(f"❌ Error checking position updates: {e}")

def start_pipeline_monitoring():
    """Start monitoring the position pipeline in real-time."""
    print("\n🔄 STARTING REAL-TIME PIPELINE MONITORING")
    print("=" * 50)
    print("This will monitor position data flow for 60 seconds...")
    print("Press Ctrl+C to stop early")
    
    redis_client = connect_to_redis()
    if not redis_client:
        return
    
    # Initialize benchmarker
    benchmarker = PerformanceBenchmarker(redis_client=redis_client)
    
    stream = 'testrade:position-updates'
    start_time = time.time()
    message_count = 0
    
    try:
        # Monitor for 60 seconds
        while time.time() - start_time < 60:
            try:
                # Check for new messages (last 1 second)
                end_time = int(time.time() * 1000)
                start_check = end_time - 1000  # 1 second ago
                
                messages = redis_client.xrange(stream, min=start_check, max=end_time)
                
                for msg_id, fields in messages:
                    message_count += 1
                    
                    # Calculate message age
                    msg_timestamp_ms = int(msg_id.split('-')[0])
                    current_time_ms = int(time.time() * 1000)
                    age_ms = current_time_ms - msg_timestamp_ms
                    
                    # Record latency metric
                    benchmarker.capture_metric('position_update_age_ms', age_ms)
                    
                    print(f"📨 Position update received (age: {age_ms}ms)")
                    
                    if age_ms > 5000:  # 5+ seconds old
                        print(f"  ⚠️ Stale data detected! Message is {age_ms/1000:.1f} seconds old")
                
                time.sleep(1)  # Check every second
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Error during monitoring: {e}")
                time.sleep(1)
    
    except KeyboardInterrupt:
        pass
    
    print(f"\n📈 MONITORING RESULTS")
    print(f"Messages detected: {message_count}")
    
    if message_count > 0:
        stats = benchmarker.get_stats('position_update_age_ms')
        print(f"Average message age: {stats['mean']:.0f}ms")
        print(f"Max message age: {stats['max']:.0f}ms")
        
        if stats['max'] > 10000:  # 10+ seconds
            print("⚠️ Significant delays detected in position pipeline!")
        else:
            print("✅ Position pipeline latency looks acceptable")
    else:
        print("❌ No position updates detected during monitoring period")

def main():
    """Main diagnostic function."""
    print("TESTRADE Position Pipeline Diagnostic Tool")
    print("=" * 60)
    
    # Connect to Redis
    redis_client = connect_to_redis()
    if not redis_client:
        print("Cannot proceed without Redis connection")
        return
    
    # Run diagnostics
    check_position_stream_health(redis_client)
    measure_redis_latency(redis_client)
    check_recent_position_updates(redis_client)
    
    # Ask user if they want real-time monitoring
    print("\n" + "=" * 60)
    response = input("Start real-time pipeline monitoring? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        start_pipeline_monitoring()
    
    print("\n✅ Diagnostic complete!")
    print("\nNext steps:")
    print("1. If Redis latency is high → Check network/memory")
    print("2. If streams have no consumers → Check GUI backend")
    print("3. If messages are old → Check EventBus publishing")
    print("4. If no recent updates → Check PositionManager")

if __name__ == "__main__":
    main()
