"""
Example: Chunk 4 OCR Replay Implementation

This example demonstrates how to use the complete OCR replay implementation
with correlation log parsing and timed replay functionality.
"""

import json
import tempfile
import os
import time
from typing import List

# Import IntelliSense components
from intellisense.core.types import (
    Position,
    OCRTimelineEvent,
    TestSessionConfig,
    OCRTestConfig
)
from intellisense.engines.datasources import IntelliSenseOCRReplaySource


def create_sample_correlation_log(file_path: str) -> None:
    """Create a sample correlation log file for demonstration."""
    
    # Sample correlation log entries
    log_entries = [
        {
            'perf_timestamp_ns': 12345678900000000,  # Base time
            'epoch_timestamp_s': 1234567890.0,
            'event_payload': {
                'frame_number': 1,
                'ocr_confidence': 0.95,
                'expected_position': {
                    'symbol': 'AAPL',
                    'shares': 100,
                    'avg_price': 150.0,
                    'timestamp': 1234567890.0,
                    'realized_pnl': 0.0,
                    'market_value': 15000.0,
                    'unrealized_pnl': 0.0,
                    'cost_basis': 15000.0
                },
                'validation_id': 'val_001'
            },
            'correlation_id': 'corr_001'
        },
        {
            'perf_timestamp_ns': 12345678900500000,  # 0.5 seconds later
            'epoch_timestamp_s': 1234567890.5,
            'event_payload': {
                'frame_number': 2,
                'ocr_confidence': 0.92,
                'expected_position': {
                    'symbol': 'AAPL',
                    'shares': 150,  # Position changed
                    'avg_price': 150.0,
                    'timestamp': 1234567890.5,
                    'realized_pnl': 0.0,
                    'market_value': 22500.0,
                    'unrealized_pnl': 0.0,
                    'cost_basis': 22500.0
                },
                'validation_id': 'val_002'
            },
            'correlation_id': 'corr_002'
        },
        {
            'perf_timestamp_ns': 12345678901000000,  # 1.0 seconds from start
            'epoch_timestamp_s': 1234567891.0,
            'event_payload': {
                'frame_number': 3,
                'ocr_confidence': 0.88,
                'expected_position': {
                    'symbol': 'AAPL',
                    'shares': 200,  # Position changed again
                    'avg_price': 150.0,
                    'timestamp': 1234567891.0,
                    'realized_pnl': 0.0,
                    'market_value': 30000.0,
                    'unrealized_pnl': 0.0,
                    'cost_basis': 30000.0
                },
                'validation_id': 'val_003'
            },
            'correlation_id': 'corr_003'
        }
    ]
    
    # Write to JSON Lines format
    with open(file_path, 'w') as f:
        for entry in log_entries:
            f.write(json.dumps(entry) + '\n')
    
    print(f"✅ Created sample correlation log with {len(log_entries)} entries: {file_path}")


def demonstrate_position_from_dict():
    """Demonstrate Position.from_dict functionality."""
    print("\n🔧 Demonstrating Position.from_dict:")
    
    # Example position data from correlation log
    position_data = {
        'symbol': 'AAPL',
        'shares': 100,
        'avg_price': 150.0,
        'timestamp': 1234567890.0,
        'realized_pnl': 25.50,
        'market_value': 15000.0,
        'unrealized_pnl': 100.0,
        'cost_basis': 15000.0
    }
    
    position = Position.from_dict(position_data)
    
    print(f"  📊 Position: {position.symbol}")
    print(f"     Shares: {position.shares}")
    print(f"     Avg Price: ${position.avg_price:.2f}")
    print(f"     Market Value: ${position.market_value:.2f}")
    print(f"     Realized PnL: ${position.realized_pnl:.2f}")
    print(f"     Unrealized PnL: ${position.unrealized_pnl:.2f}")


def demonstrate_ocr_event_from_log():
    """Demonstrate OCRTimelineEvent.from_correlation_log_entry functionality."""
    print("\n🔧 Demonstrating OCRTimelineEvent.from_correlation_log_entry:")
    
    # Example correlation log entry
    log_entry = {
        'perf_timestamp_ns': 12345678900000000,
        'epoch_timestamp_s': 1234567890.0,
        'event_payload': {
            'frame_number': 1,
            'ocr_confidence': 0.95,
            'expected_position': {
                'symbol': 'AAPL',
                'shares': 100,
                'avg_price': 150.0,
                'timestamp': 1234567890.0,
                'realized_pnl': 0.0
            },
            'validation_id': 'val_001'
        },
        'correlation_id': 'corr_001'
    }
    
    ocr_event = OCRTimelineEvent.from_correlation_log_entry(log_entry)
    
    print(f"  📹 OCR Event:")
    print(f"     Frame: {ocr_event.frame_number}")
    print(f"     Confidence: {ocr_event.ocr_confidence:.2f}")
    print(f"     Timestamp: {ocr_event.timestamp}")
    print(f"     Perf Counter: {ocr_event.perf_counter_timestamp:.6f}")
    print(f"     Position: {ocr_event.expected_position.symbol} {ocr_event.expected_position.shares} shares")
    print(f"     Validation ID: {ocr_event.validation_id}")


def demonstrate_ocr_replay_source():
    """Demonstrate complete OCR replay source functionality."""
    print("\n🔧 Demonstrating IntelliSenseOCRReplaySource:")
    
    # Create temporary correlation log file
    temp_dir = tempfile.mkdtemp()
    log_file = os.path.join(temp_dir, "demo_correlation.jsonl")
    
    try:
        # Create sample log file
        create_sample_correlation_log(log_file)
        
        # Create configuration
        ocr_config = OCRTestConfig(
            frame_processing_fps=30,
            accuracy_validation_enabled=True,
            confidence_threshold=0.80
        )
        
        session_config = TestSessionConfig(
            session_name="demo_session",
            description="Demonstration of OCR replay",
            test_date="2025-01-27",
            controlled_trade_log_path=log_file,
            historical_price_data_path_sync="/demo/prices.csv",
            broker_response_log_path="/demo/broker.log",
            speed_factor=5.0,  # 5x speed for demo
            ocr_config=ocr_config
        )
        
        # Create OCR replay source
        replay_source = IntelliSenseOCRReplaySource(
            replay_file_path=log_file,
            ocr_config=ocr_config,
            session_config=session_config
        )
        
        print(f"  📁 Replay Source: {replay_source.replay_file_path}")
        print(f"  ⚡ Speed Factor: {session_config.speed_factor}x")
        
        # Load timeline data
        print("\n  📥 Loading timeline data...")
        success = replay_source.load_timeline_data()
        print(f"     Load Success: {success}")
        print(f"     Events Loaded: {len(replay_source.timeline_data)}")
        
        # Start replay
        print("\n  ▶️  Starting replay...")
        replay_source.start()
        
        # Stream events with timing
        print("     Streaming events:")
        start_time = time.perf_counter()
        
        for i, event in enumerate(replay_source.get_data_stream()):
            elapsed = time.perf_counter() - start_time
            progress = replay_source.get_replay_progress()
            
            print(f"     [{elapsed:.2f}s] Frame {event.frame_number}: "
                  f"{event.expected_position.symbol} {event.expected_position.shares} shares "
                  f"(confidence: {event.ocr_confidence:.2f}, progress: {progress:.1%})")
        
        total_time = time.perf_counter() - start_time
        print(f"\n  ✅ Replay completed in {total_time:.2f} seconds")
        print(f"     Final progress: {replay_source.get_replay_progress():.1%}")
        
    finally:
        # Clean up
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)


def demonstrate_error_handling():
    """Demonstrate error handling capabilities."""
    print("\n🔧 Demonstrating Error Handling:")
    
    # Test with missing file
    ocr_config = OCRTestConfig()
    session_config = TestSessionConfig(
        session_name="error_test",
        description="Error handling test",
        test_date="2025-01-27",
        controlled_trade_log_path="/nonexistent/file.jsonl",
        speed_factor=1.0,
        ocr_config=ocr_config
    )
    
    replay_source = IntelliSenseOCRReplaySource(
        replay_file_path="/nonexistent/file.jsonl",
        ocr_config=ocr_config,
        session_config=session_config
    )
    
    print("  🚫 Testing with missing file...")
    success = replay_source.load_timeline_data()
    print(f"     Load Success: {success} (expected: False)")
    print(f"     Events Loaded: {len(replay_source.timeline_data)} (expected: 0)")


def main():
    """Run all demonstrations."""
    print("🎬 Chunk 4 OCR Replay Implementation Demo")
    print("=" * 50)
    
    # Demonstrate individual components
    demonstrate_position_from_dict()
    demonstrate_ocr_event_from_log()
    
    # Demonstrate complete replay functionality
    demonstrate_ocr_replay_source()
    
    # Demonstrate error handling
    demonstrate_error_handling()
    
    print("\n🎉 Demo completed successfully!")
    print("\nKey Features Demonstrated:")
    print("  ✅ Position.from_dict() for correlation log parsing")
    print("  ✅ OCRTimelineEvent.from_correlation_log_entry() for event creation")
    print("  ✅ Complete correlation log file parsing with error handling")
    print("  ✅ Precise timed replay using perf_counter_timestamp")
    print("  ✅ Speed factor control for replay acceleration")
    print("  ✅ Progress tracking during replay")
    print("  ✅ Robust error handling for missing files and malformed data")


if __name__ == "__main__":
    main()
