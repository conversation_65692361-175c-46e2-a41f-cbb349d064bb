#!/usr/bin/env python3
"""
Test script for ServiceLifecycleManager Tank mode awareness
"""

import os
import sys
import logging
from unittest.mock import Mock, MagicMock

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_service_lifecycle_tank_modes():
    """Test ServiceLifecycleManager in different Tank modes"""
    
    # Test each mode
    modes = ["TANK_SEALED", "TANK_BUFFERED", "LIVE"]
    
    for mode in modes:
        print(f"\n{'='*60}")
        print(f"Testing {mode} mode")
        print(f"{'='*60}")
        
        # Set the environment variable
        os.environ['TESTRADE_MODE'] = mode
        
        # Import after setting env var to ensure mode is detected
        from utils.testrade_modes import get_current_mode, requires_ipc_services, requires_telemetry_service
        from core.services.service_lifecycle_manager import ServiceLifecycleManager
        
        # Verify mode detection
        current_mode = get_current_mode()
        print(f"Current mode detected: {current_mode.value}")
        print(f"Requires IPC services: {requires_ipc_services()}")
        print(f"Requires telemetry service: {requires_telemetry_service()}")
        
        # Create mock dependencies
        mock_di_container = Mock()
        mock_config = Mock()
        mock_event_bus = Mock()
        
        # Mock the service resolutions
        mock_telemetry = Mock()
        mock_telemetry.start = Mock()
        mock_telemetry.is_ready = True
        
        mock_ipc_manager = Mock()
        mock_ipc_manager.start = Mock()
        mock_ipc_manager.is_ready = True
        
        mock_market_publisher = Mock()
        mock_market_publisher.start = Mock()
        mock_market_publisher.is_ready = True
        
        mock_order_repo = Mock()
        mock_order_repo.start = Mock()
        mock_order_repo.is_ready = True
        
        mock_broker = Mock()
        mock_broker.start = Mock()
        mock_broker.is_ready = True
        
        # Setup DI container resolve behavior
        def mock_resolve(service_type):
            if service_type.__name__ == 'ITelemetryService':
                return mock_telemetry
            elif service_type.__name__ == 'IIPCManager':
                return mock_ipc_manager
            elif service_type.__name__ == 'IMarketDataPublisher':
                return mock_market_publisher
            elif service_type.__name__ == 'IOrderRepository':
                return mock_order_repo
            elif service_type.__name__ == 'IBrokerService':
                return mock_broker
            else:
                # Return None for other services to simulate optional resolution
                return None
                
        mock_di_container.resolve = Mock(side_effect=mock_resolve)
        
        # Create ServiceLifecycleManager
        try:
            slm = ServiceLifecycleManager(
                di_container=mock_di_container,
                config_service=mock_config,
                event_bus=mock_event_bus
            )
            
            # Check what services were attempted to be resolved
            print(f"\nService resolution attempts:")
            for call in mock_di_container.resolve.call_args_list:
                service_name = call[0][0].__name__ if hasattr(call[0][0], '__name__') else str(call[0][0])
                print(f"  - {service_name}")
            
            # Check which services were actually set
            print(f"\nServices status after resolution:")
            print(f"  - telemetry_service: {'Set' if slm.telemetry_service is not None else 'None (skipped)'}")
            print(f"  - ipc_manager: {'Set' if slm.ipc_manager is not None else 'None (skipped)'}")
            print(f"  - market_data_publisher: {'Set' if slm.market_data_publisher is not None else 'None (skipped)'}")
            print(f"  - order_repository: {'Set' if slm.order_repository is not None else 'None'}")
            print(f"  - broker_service: {'Set' if slm.broker_service is not None else 'None'}")
            
            # Check tier lists
            print(f"\nService tiers configuration:")
            print(f"  - TIER_1_SERVICES: {slm.TIER_1_SERVICES}")
            print(f"  - TIER_3_SERVICES contains IIPCManager: {'IIPCManager' in slm.TIER_3_SERVICES}")
            
        except Exception as e:
            print(f"Error creating ServiceLifecycleManager: {e}")
            import traceback
            traceback.print_exc()

def test_mode_utilities():
    """Test the Tank mode utility functions"""
    print("\n" + "="*60)
    print("Testing Tank mode utilities")
    print("="*60)
    
    from utils.testrade_modes import (
        TestradeMode, get_current_mode, is_tank_sealed, 
        is_tank_buffered, is_live_mode, requires_telemetry_service,
        requires_ipc_services, requires_external_publishing
    )
    
    # Test each mode
    for mode_value in ["TANK_SEALED", "TANK_BUFFERED", "LIVE"]:
        os.environ['TESTRADE_MODE'] = mode_value
        mode = get_current_mode()
        
        print(f"\nMode: {mode_value}")
        print(f"  - get_current_mode(): {mode.value}")
        print(f"  - is_tank_sealed(): {is_tank_sealed()}")
        print(f"  - is_tank_buffered(): {is_tank_buffered()}")
        print(f"  - is_live_mode(): {is_live_mode()}")
        print(f"  - requires_telemetry_service(): {requires_telemetry_service()}")
        print(f"  - requires_ipc_services(): {requires_ipc_services()}")
        print(f"  - requires_external_publishing(): {requires_external_publishing()}")

if __name__ == "__main__":
    # First test the utilities
    test_mode_utilities()
    
    # Then test the service lifecycle manager
    test_service_lifecycle_tank_modes()
    
    print("\n" + "="*60)
    print("Tank mode testing complete!")
    print("="*60)