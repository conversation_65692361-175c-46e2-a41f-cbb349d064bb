#!/usr/bin/env python3
"""
Windows-compatible test script for C++ module.
"""

import os
import sys
import platform

print(f"Platform: {platform.system()}")
print(f"Working directory: {os.getcwd()}")
print(f"Python: {sys.version}")

# Check if build directory exists
build_dir = "ocr_accelerator/x64/Release"
print(f"Build directory exists: {os.path.exists(build_dir)}")

# Check if .pyd exists
pyd_path = os.path.join(build_dir, "ocr_accelerator.pyd")
print(f"PYD file exists: {os.path.exists(pyd_path)}")

if os.path.exists(pyd_path):
    print(f"PYD size: {os.path.getsize(pyd_path):,} bytes")

# Check critical DLLs
critical_dlls = ["tesseract55.dll", "leptonica-1.85.0.dll"]
for dll in critical_dlls:
    # Check in build directory
    build_dll = os.path.join(build_dir, dll)
    print(f"{dll} in build: {os.path.exists(build_dll)}")

print("\n=== Cross-Platform Import Test ===")
try:
    # Add to sys.path
    abs_build = os.path.abspath(build_dir)
    if abs_build not in sys.path:
        sys.path.insert(0, abs_build)
        print(f"Added to sys.path: {abs_build}")
    
    # Windows-specific DLL handling
    if platform.system() == "Windows":
        try:
            os.add_dll_directory(abs_build)
            print(f"Added DLL directory: {abs_build}")
        except AttributeError:
            print("os.add_dll_directory not available")
        
        # Add to PATH
        current_path = os.environ.get('PATH', '')
        if abs_build not in current_path:
            os.environ['PATH'] = abs_build + os.pathsep + current_path
            print(f"Added to PATH: {abs_build}")
    
    # Change to build directory for import (critical for DLL loading)
    original_cwd = os.getcwd()
    os.chdir(abs_build)
    print(f"Changed directory to: {abs_build}")
    
    # Try import
    import ocr_accelerator
    print("✅ SUCCESS!")
    
    # Restore directory
    os.chdir(original_cwd)
    print(f"Restored directory to: {original_cwd}")
    
    # Test function
    if hasattr(ocr_accelerator, 'test_function'):
        result = ocr_accelerator.test_function()
        print(f"test_function(): {result}")
    else:
        print("❌ No test_function found")
        
    # List functions
    funcs = [f for f in dir(ocr_accelerator) if not f.startswith('_')]
    print(f"Available functions: {funcs}")
    
    # Test main function
    if hasattr(ocr_accelerator, 'process_image_and_ocr'):
        print("✅ process_image_and_ocr function found")
    else:
        print("❌ process_image_and_ocr function not found")
    
except Exception as e:
    print(f"❌ FAILED: {e}")
    print(f"Error type: {type(e).__name__}")
    
    # Show detailed error
    import traceback
    traceback.print_exc()
    
    # Restore directory if needed
    if 'original_cwd' in locals():
        os.chdir(original_cwd)

print("\nDone.")