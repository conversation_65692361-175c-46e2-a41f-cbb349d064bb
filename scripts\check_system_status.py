#!/usr/bin/env python3
"""
Comprehensive TESTRADE system status checker
"""

import redis
import requests
import json
import time
from datetime import datetime

def check_redis_connection():
    """Check Redis connectivity and streams"""
    print("🔍 Checking Redis Connection...")
    try:
        r = redis.Redis(host='**************', port=6379, db=0, decode_responses=True)
        r.ping()
        
        # Get stream count
        streams = r.keys('testrade:*')
        stream_count = len([s for s in streams if r.type(s) == 'stream'])
        
        print(f"✅ Redis: Connected (v{r.info()['redis_version']}, {stream_count} streams)")
        return True, r
    except Exception as e:
        print(f"❌ Redis: Connection failed - {e}")
        return False, None

def check_gui_backend():
    """Check GUI backend health"""
    print("\n🔍 Checking GUI Backend...")
    try:
        response = requests.get('http://localhost:8001/health', timeout=5)
        health = response.json()
        
        status = health.get('status', 'unknown')
        ws_connections = health.get('websocket_connections', 0)
        components = health.get('components', {})
        
        print(f"✅ GUI Backend: {status.upper()}")
        print(f"   📱 WebSocket connections: {ws_connections}")
        print(f"   🔄 Redis consumers: {components.get('redis_consumers', {}).get('active_count', 0)}")
        print(f"   🔗 Babysitter IPC: {components.get('babysitter', {}).get('status', 'unknown')}")
        
        return status == 'healthy'
    except Exception as e:
        print(f"❌ GUI Backend: Not responding - {e}")
        return False

def check_core_health(redis_client):
    """Check ApplicationCore health from Redis streams"""
    print("\n🔍 Checking ApplicationCore Health...")
    try:
        # Get latest health message
        messages = redis_client.xrevrange('testrade:health:core', count=1)
        if not messages:
            print("⚠️  ApplicationCore: No health messages found")
            return False
        
        msg_id, fields = messages[0]
        payload_str = fields.get('json_payload', '{}')
        payload = json.loads(payload_str)
        
        core_data = payload.get('payload', {})
        status = core_data.get('status', 'unknown')
        metrics = core_data.get('metrics', {})
        timestamp = core_data.get('timestamp', 0)
        
        # Check if health message is recent (within last 30 seconds)
        age_seconds = time.time() - timestamp
        is_recent = age_seconds < 30
        
        print(f"✅ ApplicationCore: {status.upper()} ({'recent' if is_recent else f'{age_seconds:.0f}s ago'})")
        print(f"   🔗 Babysitter IPC: {'✅' if metrics.get('babysitter_ipc_healthy') else '❌'}")
        print(f"   📡 ZMQ Trading: {'✅' if metrics.get('zmq_trading_connected') else '❌'}")
        print(f"   📡 ZMQ System: {'✅' if metrics.get('zmq_system_connected') else '❌'}")
        print(f"   📡 ZMQ Bulk: {'✅' if metrics.get('zmq_bulk_connected') else '❌'}")
        print(f"   👁️  OCR Process: {'✅' if metrics.get('ocr_process_alive') else '❌'} (PID: {metrics.get('ocr_process_pid', 'N/A')})")
        print(f"   💼 Broker: {'✅' if metrics.get('broker_connected') else '❌'}")
        
        return status == 'healthy' and is_recent
    except Exception as e:
        print(f"❌ ApplicationCore: Health check failed - {e}")
        return False

def check_websocket_connectivity():
    """Check WebSocket connectivity"""
    print("\n🔍 Checking WebSocket Connectivity...")
    try:
        import websocket
        
        def on_message(ws, message):
            print(f"✅ WebSocket: Received message - {message[:100]}...")
            ws.close()
        
        def on_error(ws, error):
            print(f"❌ WebSocket: Error - {error}")
        
        def on_open(ws):
            print("✅ WebSocket: Connected successfully")
            ws.close()
        
        ws = websocket.WebSocketApp("ws://localhost:8001/ws",
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_open=on_open)
        
        # Quick connection test
        ws.run_forever(timeout=3)
        return True
    except ImportError:
        print("⚠️  WebSocket: websocket-client not installed (pip install websocket-client)")
        return None
    except Exception as e:
        print(f"❌ WebSocket: Connection failed - {e}")
        return False

def main():
    print("🧪 TESTRADE System Status Check")
    print("=" * 50)
    print(f"📅 Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check Redis
    redis_ok, redis_client = check_redis_connection()
    
    # Check GUI Backend
    gui_ok = check_gui_backend()
    
    # Check ApplicationCore
    core_ok = False
    if redis_client:
        core_ok = check_core_health(redis_client)
    
    # Check WebSocket
    ws_ok = check_websocket_connectivity()
    
    print("\n" + "=" * 50)
    print("📊 SYSTEM STATUS SUMMARY")
    print("=" * 50)
    print(f"🔴 Redis Server:      {'✅ HEALTHY' if redis_ok else '❌ FAILED'}")
    print(f"🔴 GUI Backend:       {'✅ HEALTHY' if gui_ok else '❌ FAILED'}")
    print(f"🔴 ApplicationCore:   {'✅ HEALTHY' if core_ok else '❌ FAILED'}")
    print(f"🔴 WebSocket:         {'✅ HEALTHY' if ws_ok else '❌ FAILED' if ws_ok is False else '⚠️  UNTESTED'}")
    
    overall_status = all([redis_ok, gui_ok, core_ok])
    print(f"\n🎯 OVERALL STATUS: {'🟢 ALL SYSTEMS OPERATIONAL' if overall_status else '🔴 ISSUES DETECTED'}")
    
    if not overall_status:
        print("\n💡 TROUBLESHOOTING TIPS:")
        if not redis_ok:
            print("   - Start Redis server: redis-server")
            print("   - Check Redis configuration and network connectivity")
        if not gui_ok:
            print("   - Restart GUI backend: python gui/gui_backend.py")
            print("   - Check for port conflicts on 8001")
        if not core_ok:
            print("   - Check ApplicationCore logs for errors")
            print("   - Verify core startup and health monitoring")
        if ws_ok is False:
            print("   - Check WebSocket endpoint availability")
            print("   - Verify no firewall blocking connections")

if __name__ == "__main__":
    main()
