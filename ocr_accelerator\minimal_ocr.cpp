#include <pybind11/pybind11.h>
#include <pybind11/numpy.h>
#include <pybind11/stl.h>

#include <tesseract/baseapi.h>
#include <memory>
#include <stdexcept>
#include <chrono>

namespace py = pybind11;

// Custom deleter for Tesseract's memory to prevent leaks
struct TessTextDeleter {
    void operator()(char* text) const { if (text) { delete[] text; } }
};
using TessTextPtr = std::unique_ptr<char[], TessTextDeleter>;

// Minimal OCR function - just Tesseract, no OpenCV preprocessing
py::dict tesseract_ocr_only(py::array_t<unsigned char> preprocessed_image) {
    auto t0_entry = std::chrono::high_resolution_clock::now();
    
    // Convert NumPy array to raw data for Tesseract
    py::buffer_info buf = preprocessed_image.request();
    if (buf.ndim != 2) {
        throw std::runtime_error("Input must be a 2D grayscale/binary image");
    }
    
    int height = buf.shape[0];
    int width = buf.shape[1];
    unsigned char* data = static_cast<unsigned char*>(buf.ptr);
    
    auto t1_tess_start = std::chrono::high_resolution_clock::now();
    
    // Initialize Tesseract
    auto tess = std::make_unique<tesseract::TessBaseAPI>();
    if (tess->Init(nullptr, "eng")) {
        throw std::runtime_error("Could not initialize Tesseract");
    }
    tess->SetPageSegMode(tesseract::PSM_SINGLE_BLOCK);
    
    // Set image data (assumes 1 byte per pixel, 1 channel)
    tess->SetImage(data, width, height, 1, width);
    
    // Get text
    TessTextPtr out_text(tess->GetUTF8Text());
    std::string result_text = std::string(out_text.get());
    
    // Get confidence
    float total_confidence = 0.0f;
    int word_count = 0;
    tesseract::ResultIterator* ri = tess->GetIterator();
    if (ri != nullptr) {
        do {
            const char* word = ri->GetUTF8Text(tesseract::RIL_WORD);
            if (word == nullptr) continue;
            
            float conf = ri->Confidence(tesseract::RIL_WORD);
            if (std::string(word).length() > 0) {
                total_confidence += conf;
                word_count++;
            }
            delete[] word;
        } while (ri->Next(tesseract::RIL_WORD));
        delete ri;
    }
    
    float average_confidence = (word_count > 0) ? (total_confidence / word_count) : 0.0f;
    auto t2_tess_done = std::chrono::high_resolution_clock::now();
    
    // Return results
    py::dict result;
    result["text"] = result_text;
    result["confidence"] = average_confidence;
    result["t0_entry_ns"] = std::chrono::duration_cast<std::chrono::nanoseconds>(t0_entry.time_since_epoch()).count();
    result["t1_tess_start_ns"] = std::chrono::duration_cast<std::chrono::nanoseconds>(t1_tess_start.time_since_epoch()).count();
    result["t2_tess_done_ns"] = std::chrono::duration_cast<std::chrono::nanoseconds>(t2_tess_done.time_since_epoch()).count();
    
    return result;
}

// Test function
py::dict test_function() {
    py::dict result;
    result["status"] = "success";
    result["message"] = "Minimal Tesseract-only module loaded successfully";
    return result;
}

PYBIND11_MODULE(ocr_accelerator, m) {
    m.doc() = "Minimal Tesseract-only OCR accelerator";
    m.def("test_function", &test_function, "Test function");
    m.def("tesseract_ocr_only", &tesseract_ocr_only, "Tesseract OCR on preprocessed image",
          py::arg("preprocessed_image"));
}