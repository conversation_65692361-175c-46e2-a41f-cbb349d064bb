#!/usr/bin/env python3
"""
Redis Memory Audit Script
Analyzes Redis memory usage and identifies memory-consuming streams.
"""

import redis
import json
import time
from typing import Dict, List, Tuple

def connect_redis():
    """Connect to Redis with TESTRADE settings."""
    try:
        client = redis.Redis(
            host='**************',  # TESTRADE Redis host
            port=6379,
            db=0,
            decode_responses=True
        )
        client.ping()
        print("✅ Connected to Redis")
        return client
    except Exception as e:
        print(f"❌ Failed to connect to Redis: {e}")
        return None

def analyze_stream_memory(client: redis.Redis) -> List[Dict]:
    """Analyze memory usage of all TESTRADE streams."""
    streams = []
    
    # Get all TESTRADE streams
    for key in client.scan_iter(match="testrade:*", _type="stream"):
        try:
            # Get stream info
            info = client.xinfo_stream(key)
            length = info.get('length', 0)
            
            # Get consumer groups
            try:
                groups = client.xinfo_groups(key)
                group_count = len(groups)
                consumer_count = sum(len(client.xinfo_consumers(key, g['name'])) for g in groups)
            except:
                group_count = 0
                consumer_count = 0
            
            # Estimate memory usage (rough calculation)
            # Each message ~1-200KB depending on content
            estimated_memory_mb = length * 0.05  # Conservative 50KB per message
            
            streams.append({
                'stream': key,
                'length': length,
                'groups': group_count,
                'consumers': consumer_count,
                'estimated_mb': estimated_memory_mb
            })
            
        except Exception as e:
            print(f"⚠️ Error analyzing stream {key}: {e}")
    
    return sorted(streams, key=lambda x: x['estimated_mb'], reverse=True)

def analyze_consumer_groups(client: redis.Redis) -> Dict:
    """Analyze consumer group accumulation."""
    group_analysis = {}
    
    for key in client.scan_iter(match="testrade:*", _type="stream"):
        try:
            groups = client.xinfo_groups(key)
            group_analysis[key] = []
            
            for group in groups:
                consumers = client.xinfo_consumers(key, group['name'])
                group_analysis[key].append({
                    'name': group['name'],
                    'consumers': len(consumers),
                    'pending': group.get('pending', 0),
                    'last_delivered_id': group.get('last-delivered-id', 'N/A')
                })
                
        except Exception as e:
            print(f"⚠️ Error analyzing groups for {key}: {e}")
    
    return group_analysis

def get_redis_memory_info(client: redis.Redis) -> Dict:
    """Get Redis memory information."""
    info = client.info('memory')
    return {
        'used_memory_mb': info.get('used_memory', 0) / (1024 * 1024),
        'used_memory_peak_mb': info.get('used_memory_peak', 0) / (1024 * 1024),
        'used_memory_rss_mb': info.get('used_memory_rss', 0) / (1024 * 1024),
        'mem_fragmentation_ratio': info.get('mem_fragmentation_ratio', 0)
    }

def main():
    """Main audit function."""
    print("🔍 TESTRADE Redis Memory Audit")
    print("=" * 50)
    
    client = connect_redis()
    if not client:
        return
    
    # Get overall memory info
    print("\n📊 Redis Memory Overview:")
    memory_info = get_redis_memory_info(client)
    for key, value in memory_info.items():
        if 'mb' in key:
            print(f"   {key}: {value:.2f} MB")
        else:
            print(f"   {key}: {value}")
    
    # Analyze streams
    print("\n📈 Stream Memory Analysis:")
    streams = analyze_stream_memory(client)
    total_estimated_mb = sum(s['estimated_mb'] for s in streams)
    
    print(f"   Total estimated stream memory: {total_estimated_mb:.2f} MB")
    print(f"   Number of streams: {len(streams)}")
    print("\n   Top memory-consuming streams:")
    
    for stream in streams[:10]:  # Top 10
        print(f"   📊 {stream['stream']}")
        print(f"      Length: {stream['length']:,} messages")
        print(f"      Groups: {stream['groups']}, Consumers: {stream['consumers']}")
        print(f"      Estimated: {stream['estimated_mb']:.2f} MB")
        print()
    
    # Analyze consumer groups
    print("\n👥 Consumer Group Analysis:")
    groups = analyze_consumer_groups(client)
    total_groups = sum(len(group_list) for group_list in groups.values())
    total_consumers = sum(
        sum(g['consumers'] for g in group_list) 
        for group_list in groups.values()
    )
    
    print(f"   Total consumer groups: {total_groups}")
    print(f"   Total consumers: {total_consumers}")
    
    # Show problematic streams
    print("\n⚠️ Potential Issues:")
    for stream_name, group_list in groups.items():
        if len(group_list) > 3:  # More than 3 groups per stream
            print(f"   🚨 {stream_name}: {len(group_list)} consumer groups (potential accumulation)")
        
        for group in group_list:
            if group['pending'] > 100:  # High pending messages
                print(f"   ⚠️ {stream_name}:{group['name']}: {group['pending']} pending messages")
    
    print("\n🎯 Recommendations:")
    if total_estimated_mb > 500:
        print("   🔥 High memory usage detected!")
        print("   💡 Consider adding TTL to streams")
        print("   💡 Clean up old consumer groups")
        print("   💡 Reduce image recording frequency")
    
    if total_groups > 20:
        print("   🧹 Too many consumer groups detected!")
        print("   💡 Clean up old IntelliSense test groups")
    
    print(f"\n✅ Audit complete. Check streams with >100MB estimated usage.")

if __name__ == "__main__":
    main()
