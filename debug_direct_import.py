#!/usr/bin/env python3
"""
Direct import test to bypass directory structure issues.
"""

import os
import sys
import importlib.util

def test_direct_pyd_import():
    """Test importing the .pyd file directly."""
    print("=== Direct .pyd Import Test ===")
    
    # Direct path to the .pyd file
    pyd_path = "ocr_accelerator/x64/Release/ocr_accelerator.pyd"
    abs_pyd_path = os.path.abspath(pyd_path)
    
    print(f"PYD file path: {abs_pyd_path}")
    print(f"PYD file exists: {os.path.exists(abs_pyd_path)}")
    
    if os.path.exists(abs_pyd_path):
        pyd_size = os.path.getsize(abs_pyd_path)
        print(f"PYD file size: {pyd_size} bytes")
        
        # Try loading the module directly using importlib
        try:
            spec = importlib.util.spec_from_file_location("ocr_accelerator_direct", abs_pyd_path)
            if spec is None:
                print("❌ Could not create module spec from .pyd file")
                return False
                
            print(f"✅ Module spec created: {spec}")
            
            module = importlib.util.module_from_spec(spec)
            if module is None:
                print("❌ Could not create module from spec")
                return False
                
            print(f"✅ Module object created: {module}")
            
            # Set up DLL path before execution
            dll_dir = os.path.dirname(abs_pyd_path)
            if hasattr(os, 'add_dll_directory'):
                try:
                    os.add_dll_directory(dll_dir)
                    print(f"✅ Added DLL directory: {dll_dir}")
                except Exception as e:
                    print(f"⚠️ Could not add DLL directory: {e}")
            
            # Execute the module
            spec.loader.exec_module(module)
            print(f"✅ Module executed successfully")
            
            # Check module contents
            print(f"\nModule attributes: {dir(module)}")
            
            # Look for our functions
            functions = []
            for attr_name in dir(module):
                if not attr_name.startswith('_'):
                    attr = getattr(module, attr_name)
                    if callable(attr):
                        functions.append(attr_name)
                        print(f"Function found: {attr_name} - {type(attr)}")
            
            if functions:
                print(f"✅ Found {len(functions)} functions: {functions}")
                
                # Test the test_function if available
                if 'test_function' in functions:
                    try:
                        result = module.test_function()
                        print(f"✅ test_function() result: {result}")
                    except Exception as e:
                        print(f"❌ test_function() failed: {e}")
                
                return True
            else:
                print("❌ No functions found in module")
                return False
                
        except Exception as e:
            print(f"❌ Error during direct import: {e}")
            import traceback
            traceback.print_exc()
            return False
    else:
        print("❌ PYD file not found")
        return False

if __name__ == "__main__":
    test_direct_pyd_import()