"""
<PERSON><PERSON><PERSON> to verify PriceFetchingService instantiation and start.
This script runs the application for a short time and then exits,
capturing all logs related to PriceFetchingService.
"""

import os
import sys
import time
import logging
import datetime

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"pfs_verify_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

# Configure a file handler for the logs
file_handler = logging.FileHandler(log_file, mode="w", encoding="utf-8")
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter("[%(asctime)s] [%(levelname)-8s] [%(name)s] %(message)s")
file_handler.setFormatter(file_formatter)

# Configure a console handler for the logs
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter("[%(levelname)-8s] %(message)s")
console_handler.setFormatter(console_formatter)

# Get the root logger and add the handlers
root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG)
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

# Create a logger for this script
logger = logging.getLogger("pfs_verify")

def main():
    """Main function to run the application and capture logs."""
    logger.info("Starting pfs_verify.py")
    logger.info(f"Logs will be saved to: {log_file}")
    
    # Import the necessary modules
    try:
        from main import setup_main_logging, load_global_config, load_symbols_from_csv
        from utils.global_config import GlobalConfig
        from modules.price_fetching.price_fetching_service import PriceFetchingService
        from modules.price_fetching.price_repository import PriceRepository
        import alpaca_trade_api as tradeapi
        
        logger.info("Successfully imported all required modules")
    except ImportError as e:
        logger.error(f"Failed to import required modules: {e}")
        return
    
    # Set up logging
    setup_main_logging()
    logger.info("Set up main logging")
    
    # Load global config
    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "utils", "control.json")
    config = load_global_config(config_path)
    logger.info("Loaded global config")
    
    # Load symbols
    symbols_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "tradeable_symbols.csv")
    load_symbols_from_csv(symbols_path, fix_bom=True, force_reload=True)
    logger.info("Loaded symbols")
    
    # Initialize Alpaca REST client
    alpaca_config = {
        'key_id': config.alpaca_api_key,
        'secret_key': config.alpaca_api_secret,
        'base_url': config.alpaca_api_url
    }
    rest_client = tradeapi.REST(**alpaca_config)
    logger.info("Initialized Alpaca REST client")
    
    # Initialize PriceRepository
    price_repository = PriceRepository(
        risk_service=None,
        rest_client=rest_client,
        config_service=config
    )
    logger.info("Initialized PriceRepository")
    
    # Initialize PriceFetchingService
    price_service = PriceFetchingService(
        config={
            'key_id': config.alpaca_api_key,
            'secret_key': config.alpaca_api_secret,
            'sip_url': 'wss://stream.data.alpaca.markets/v2/sip'
        },
        receiver=price_repository,
        initial_symbols=set(),
        rest_client=rest_client,
        tkinter_root_for_callbacks=None
    )
    logger.info("Initialized PriceFetchingService")
    
    # Check if PriceFetchingService is disabled in the configuration
    if hasattr(config, 'disable_price_fetching_service') and config.disable_price_fetching_service:
        logger.error("A1_TEST: Price Fetching Service is DISABLED in configuration for memory leak testing")
        logger.error("APP_INIT_PFS_CHECK: PriceFetchingService.start() WAS INTENTIONALLY SKIPPED for memory leak testing")
    else:
        # Start the service only if not disabled
        price_service.start()
        logger.info("Started PriceFetchingService")
    
    # Wait for a short time
    logger.info("Waiting for 5 seconds...")
    time.sleep(5)
    
    # Stop the service if it was started
    if not hasattr(config, 'disable_price_fetching_service') or not config.disable_price_fetching_service:
        price_service.stop()
        logger.info("Stopped PriceFetchingService")
    
    logger.info("Done.")
    logger.info(f"Logs saved to: {log_file}")

if __name__ == "__main__":
    main()
