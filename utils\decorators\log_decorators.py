"""
Real Decorators Module

This module provides the real implementation of decorators used in the application.
These implementations are used when decorators are enabled.
"""

import logging
import time
from functools import wraps
from typing import Dict, Callable, Any

# Global flag to enable/disable decorators
DECORATORS_ENABLED = True

# Global flag dictionary for module-specific logging
LOGGING_ENABLED_FLAGS: Dict[str, bool] = {
    'trade_manager': False,
    'copy_manager': False,
    'app_flow': True,  # Keep this TRUE for now to see app init logs from decorator
    'broker_bridge': False,
    'ocr_handler': False,
    'ocr_service': False,
    'risk_manager': False,
    'price_fetcher': False,
}

# Set up logging
logger = logging.getLogger("real_decorators")
logger.info("Real decorators module loaded")

def enable_decorators(enabled=True):
    """
    Enable or disable decorators globally.
    
    Args:
        enabled: True to enable decorators, False to disable them
        
    Returns:
        The new state of decorators
    """
    global DECORATORS_ENABLED
    old_state = DECORATORS_ENABLED
    DECORATORS_ENABLED = enabled
    
    # Log the change
    logger.info(f"Decorators {'enabled' if enabled else 'disabled'}. Previous state: {old_state}")
    
    return DECORATORS_ENABLED

def is_decorators_enabled():
    """
    Check if decorators are enabled.
    
    Returns:
        True if decorators are enabled, False otherwise
    """
    return DECORATORS_ENABLED

def set_module_logging(module_key: str, enabled: bool) -> None:
    """
    Safely enables or disables logging for a specific module key in the
    LOGGING_ENABLED_FLAGS dictionary.
    
    Args:
        module_key: The module key to set logging for
        enabled: Whether to enable logging for the module
    """
    if not DECORATORS_ENABLED:
        return
        
    if module_key in LOGGING_ENABLED_FLAGS:
        LOGGING_ENABLED_FLAGS[module_key] = bool(enabled)
        # Use a more general logger for this utility function itself
        logging.getLogger("decorator_control").info(f"Logging for '{module_key}' set to: {LOGGING_ENABLED_FLAGS[module_key]}")
    else:
        logging.getLogger("decorator_control").warning(f"Unknown logging key '{module_key}' provided for set_module_logging.")

def log_function_call(module_key: str) -> Callable:
    """
    Decorator factory that creates a decorator to log function details.
    Handles class methods (like __init__) and regular functions.
    
    Args:
        module_key: The module key to log under
        
    Returns:
        A decorator function
    """
    logger = logging.getLogger(module_key)  # Get logger once per decorator instance

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Skip logging if decorators are disabled globally
            if not DECORATORS_ENABLED:
                return func(*args, **kwargs)
                
            # Skip logging if this specific module's logging is disabled
            if not LOGGING_ENABLED_FLAGS.get(str(module_key), False):
                return func(*args, **kwargs)

            func_qualname = func.__qualname__  # e.g., App.__init__, my_function
            
            # Simplified argument representation for robustness
            try:
                # For methods, args[0] is self. Don't log its full repr.
                logged_args_repr = []
                if args:
                    if hasattr(args[0], '__class__') and func_qualname.startswith(args[0].__class__.__name__):
                        # Likely a method call
                        logged_args_repr.append(f"self=<{args[0].__class__.__name__} instance>")
                        if len(args) > 1:
                            logged_args_repr.extend(repr(a) for a in args[1:])
                    else:  # Regular function or static/class method where first arg isn't instance
                        logged_args_repr.extend(repr(a) for a in args)
                
                logged_kwargs_repr = [f"{k}={v!r}" for k, v in kwargs.items()]
                
                arg_str_parts = logged_args_repr + logged_kwargs_repr
                arg_str = ", ".join(arg_str_parts)
                if len(arg_str) > 200:  # Truncate very long arg strings
                    arg_str = arg_str[:200] + "..."
            except Exception:
                arg_str = "..."  # Fallback if arg logging fails

            logger.debug(f"ENTERING: {func_qualname}({arg_str})")
            start_time = time.perf_counter()
            
            try:
                # THIS IS THE CRITICAL CALL. Python handles 'self' correctly.
                result = func(*args, **kwargs)
                
                end_time = time.perf_counter()
                duration_ms = (end_time - start_time) * 1000

                try:
                    result_repr = repr(result)
                    if len(result_repr) > 150:
                        result_repr = result_repr[:150] + "..."
                except Exception:
                    result_repr = "<error representing result>"
                
                logger.debug(f"EXITING : {func_qualname} (Duration: {duration_ms:.3f} ms) -> Return: {result_repr}")
                return result
            except Exception as e:
                end_time = time.perf_counter()
                duration_ms = (end_time - start_time) * 1000
                logger.error(f"EXCEPTION in {func_qualname} (Duration: {duration_ms:.3f} ms): {type(e).__name__}: {e}", exc_info=True)
                raise
        return wrapper
    return decorator
