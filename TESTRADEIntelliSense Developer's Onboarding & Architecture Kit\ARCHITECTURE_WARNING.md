# ⚠️ ARCHITECTURE WARNING - READ BEFORE CODING ⚠️

## STOP! This System Has Clean Architecture

Before adding ANY new code, understand these critical architectural patterns:

## 1. Central Interfaces Directory (`/interfaces/`)

**ALL interfaces are in `/interfaces/`** organized by domain:
- `/interfaces/core/` - Infrastructure (EventBus, Config, Lifecycle)
- `/interfaces/trading/` - Trading domain (Orders, Positions, Broker)
- `/interfaces/market_data/` - Market data (Prices, Publishing)
- `/interfaces/ocr/` - OCR services
- `/interfaces/risk/` - Risk management

**✅ ALWAYS import from interfaces:**
```python
from interfaces import IEventBus, IPositionManager
```

**❌ NEVER import from implementations:**
```python
from core.event_bus import EventBus  # WRONG!
```

## 2. ServiceLifecycleManager Handles ALL Service Startup

**DO NOT create custom service managers!** The system already has:
- Automatic service discovery via DI metadata
- Phased startup (Infrastructure → Listeners → External → Data → UI)
- Proper shutdown in reverse order
- Health monitoring

**To add a service:**
```python
# In core/di_registration.py
container.register_factory_with_metadata(
    IYourService,
    your_service_factory,
    phase=1,  # Choose appropriate phase
    name="YourService"
)
```

## 3. EventBus WeakSet Handler Pattern

**CRITICAL:** EventBus uses WeakSet for handlers. You MUST store handler references:

```python
def start(self):
    # ❌ WRONG - Handler will be garbage collected!
    self.event_bus.subscribe(MyEvent, lambda e: self.handle(e))
    
    # ✅ CORRECT - Store reference
    self._my_handler = lambda e: self.handle(e)
    self.event_bus.subscribe(MyEvent, self._my_handler)
```

## 4. Position-Based Market Data Filtering

**The system uses position-based filtering, NOT ActiveSymbolsService:**
- PositionManager publishes PositionUpdateEvent
- FilteredMarketDataPublisher maintains allow list
- PriceFetchingService subscribes/unsubscribes dynamically

**Flow:**
Position Change → Event → Update Allow List → Filter Market Data

## 5. Breaking Circular Dependencies

**Use these patterns:**
1. **LazyProxy** - For simple circular dependencies
2. **Setter Injection** - For complex dependencies
3. **Interface Segregation** - Extract specific interfaces (e.g., IMessageFormatter)

## 6. All Services MUST Implement ILifecycleService

```python
from interfaces import ILifecycleService

class YourService(IYourInterface, ILifecycleService):
    def start(self):
        # Subscribe to events, start threads
        
    def stop(self):
        # Unsubscribe, stop threads, cleanup
        
    @property
    def is_ready(self) -> bool:
        return self._initialized
```

## Common Mistakes That Break The System

1. **Creating duplicate service managers** → Use ServiceLifecycleManager
2. **Manual service instantiation** → Use DI container
3. **Forgetting WeakSet pattern** → Store handler references
4. **Importing implementations** → Always import interfaces
5. **Creating ActiveSymbolsService** → Use position-based filtering

## Quick Reference

- **Architecture Guide**: `/PROJECT_MEMORY/clean_architecture.md`
- **Session Memory**: `/PROJECT_MEMORY/session_handoff.md`
- **DI Registration**: `/core/di_registration.py`
- **Interfaces**: `/interfaces/`

## If You're About To:

- **Add a service**: Register with DI container + metadata
- **Handle events**: Store handler reference as instance variable
- **Import a class**: Import interface from `/interfaces/`
- **Track symbols**: Use PositionManager events
- **Start services**: Let ServiceLifecycleManager handle it

## Remember

This is a sophisticated event-driven trading system with clean architecture. The patterns are there for resilience, testability, and maintainability. Follow them!