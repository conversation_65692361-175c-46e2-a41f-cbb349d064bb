"""
Performance Tracker Interface

This module provides a unified interface for performance tracking functions.
It conditionally imports either the real performance tracker or a dummy implementation
based on a global configuration setting.

Usage:
    from utils.performance_tracker.interface import create_timestamp_dict, add_timestamp
    
    # These functions will be either real implementations or no-op implementations
    # depending on the global configuration
    timestamps = create_timestamp_dict()
    add_timestamp(timestamps, 'operation_start')
    # ... perform operation ...
    add_timestamp(timestamps, 'operation_end')
"""

import os
import logging
import importlib.util
from typing import Dict, List, Optional, Any, Union
from contextlib import contextmanager

# Set up logging
logger = logging.getLogger(__name__)

# Try to import global config to check if performance tracking should be enabled
try:
    from utils.global_config import config as global_app_config
    # Check if the enable_full_performance_tracking_module setting exists
    _use_real_performance_tracker = getattr(global_app_config, 'enable_full_performance_tracking_module', False)
except Exception as e:
    logger.error(f"Error importing global_config or accessing config: {e}")
    _use_real_performance_tracker = False

# Check if we're in diagnostic mode (PERFORMANCE_TRACKING_FORCED_OFF is set in main.py)
try:
    import sys
    main_module = sys.modules.get('__main__')
    if main_module and hasattr(main_module, 'PERFORMANCE_TRACKING_FORCED_OFF'):
        logger.error("DIAGNOSTIC MODE DETECTED: Performance tracking FORCED OFF")
        _use_real_performance_tracker = False
except Exception as e:
    logger.error(f"Error checking for diagnostic mode: {e}")

# Log the decision
if _use_real_performance_tracker:
    logger.info("Using REAL performance tracker module")
else:
    logger.info("Using DUMMY performance tracker module")

# CORRECTED LOGIC: Import from only ONE tracker based on configuration
# First, attempt to check real tracker's PERFORMANCE_TRACKING_ENABLED state
_real_tracker_enabled_flag = False
_pt_interface_logger = logger

try:
    from . import real as _real_module
    _real_tracker_enabled_flag = _real_module.PERFORMANCE_TRACKING_ENABLED
    _pt_interface_logger = _real_module._pt_logger  # Use logger from real.py if available
    _pt_interface_logger.info(f"PerformanceTracker Interface: real.py loaded, PERFORMANCE_TRACKING_ENABLED = {_real_tracker_enabled_flag}")
except ImportError:
    _pt_interface_logger.warning("PerformanceTracker Interface: real.py not found during initial flag check.")

# Import from appropriate module based on configuration AND real tracker state
if _use_real_performance_tracker:
    try:
        _pt_interface_logger.info("PerformanceTracker Interface: Using REAL tracker implementations.")
        # Import the real performance tracker
        from .real import (
            enable_performance_tracking,
            is_performance_tracking_enabled,
            create_timestamp_dict,
            add_timestamp,
            calculate_durations,
            add_to_stats,
            get_performance_stats,
            reset_performance_stats,
            reset_all_trackers,
            log_performance_durations,
            export_performance_stats_to_csv,
            get_tracker,
            track_performance,
            PerformanceTracker,
            DummyTracker,
            _stats_collector,
            # Enhanced benchmarker integration functions
            initialize_global_benchmarker,
            get_global_benchmarker,
            capture_metric_with_benchmarker,
            add_timestamp_with_capture,
            finalize_performance_scope,
            capture_queue_size,
            capture_throughput_metric,
            capture_cpu_usage
        )

        # Explicitly enable the real performance tracker
        enable_performance_tracking(True)
        _pt_interface_logger.info("Real performance_tracker module loaded and ENABLED")

    except ImportError as e:
        _pt_interface_logger.error(f"Error importing real performance tracker: {e}")
        # Fall back to dummy implementation
        _use_real_performance_tracker = False

# ONLY load dummy if real tracker is NOT being used
if not _use_real_performance_tracker:
    try:
        _pt_interface_logger.warning("PerformanceTracker Interface: Using DUMMY tracker implementations as real tracker is disabled.")
        # Import the dummy performance tracker
        from .dummy import (
            enable_performance_tracking,
            is_performance_tracking_enabled,
            create_timestamp_dict,
            add_timestamp,
            calculate_durations,
            add_to_stats,
            get_performance_stats,
            reset_performance_stats,
            reset_all_trackers,
            log_performance_durations,
            export_performance_stats_to_csv,
            get_tracker,
            track_performance,
            DummyTracker as PerformanceTracker,  # Use DummyTracker as PerformanceTracker
            DummyTracker,
            _stats_collector,
            # Enhanced benchmarker integration dummy functions
            initialize_global_benchmarker,
            get_global_benchmarker,
            capture_metric_with_benchmarker,
            add_timestamp_with_capture,
            finalize_performance_scope,
            capture_queue_size,
            capture_throughput_metric,
            capture_cpu_usage
        )

        # Explicitly disable performance tracking (though the dummy's enable is a no-op)
        enable_performance_tracking(False)
        _pt_interface_logger.info("DUMMY performance_tracker module loaded. Real performance tracking is OFF")
    except ImportError as e:
        _pt_interface_logger.error(f"Error importing dummy performance tracker: {e}")
        # Define minimal fallback implementations
        def enable_performance_tracking(enabled=True): return False
        def is_performance_tracking_enabled(): return False
        def create_timestamp_dict(): return None
        def add_timestamp(*args, **kwargs): return args[0] if args else None
        def calculate_durations(*args, **kwargs): return {}
        def add_to_stats(*args, **kwargs): pass
        def get_performance_stats(): return {}
        def reset_performance_stats(): pass
        def reset_all_trackers(): pass
        def log_performance_durations(*args, **kwargs): pass
        def export_performance_stats_to_csv(*args, **kwargs): return False
        def get_tracker(*args, **kwargs): return DummyTracker()
        
        @contextmanager
        def track_performance(*args, **kwargs):
            yield
        
        class DummyTracker:
            def __init__(self, *args, **kwargs): pass
            def mark(self, *args, **kwargs): return 0.0
            def duration(self, *args, **kwargs): return None
            def record_duration(self, *args, **kwargs): pass
            def get_stats(self, *args, **kwargs): return {}
            def reset(self): pass
            def reset_timestamps(self): pass
            def to_dict(self): return {"name": "dummy", "timestamps": {}, "durations": {}}
            
            @contextmanager
            def track(self, *args, **kwargs):
                yield
        
        PerformanceTracker = DummyTracker
        
        class _DummyStatsCollector:
            def add_measurement(self, *args, **kwargs): pass
            def get_stats(self): return {}
            def reset(self): pass
        
        _stats_collector = _DummyStatsCollector()
        
        logger.error("FALLBACK dummy performance tracker functions defined")

# Add a global function to update the GlobalConfig with the performance tracking setting
def update_global_config_with_performance_tracking(enabled=True):
    """
    Update the global config with the performance tracking setting.
    
    Args:
        enabled: True to enable performance tracking, False to disable it
    """
    try:
        from utils.global_config import config, save_global_config
        
        # Create a new attribute if it doesn't exist
        if not hasattr(config, 'enable_full_performance_tracking_module'):
            config.enable_full_performance_tracking_module = enabled
        else:
            config.enable_full_performance_tracking_module = enabled
        
        # Save the updated config
        save_global_config(config)
        logger.info(f"Updated global config with enable_full_performance_tracking_module={enabled}")
        
        return True
    except Exception as e:
        logger.error(f"Error updating global config with performance tracking setting: {e}")
        return False
