<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TESTRADE - Enhanced Trade Display</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700;800&display=swap');
        
        :root {
            --bg-primary: #0a0a0f;
            --bg-secondary: #1a1a2e;
            --bg-tertiary: #16213e;
            --bg-glass: rgba(255, 255, 255, 0.03);
            --accent-primary: #00ff88;
            --accent-secondary: #0088ff;
            --accent-danger: #ff4466;
            --accent-warning: #ffaa00;
            --accent-purple: #8b5cf6;
            --text-primary: #ffffff;
            --text-secondary: #b8bcc8;
            --text-muted: #6b7280;
            --border-subtle: rgba(0, 255, 136, 0.2);
            --border-bright: rgba(0, 255, 136, 0.5);
            --glow-primary: 0 0 20px rgba(0, 255, 136, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'JetBrains Mono', 'Monaco', 'Consolas', monospace;
            background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Animated matrix background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 1px 1px, rgba(0, 255, 136, 0.15) 1px, transparent 0);
            background-size: 40px 40px;
            animation: matrixRain 30s linear infinite;
            z-index: -1;
        }

        @keyframes matrixRain {
            0% { transform: translateY(-40px); }
            100% { transform: translateY(100vh); }
        }

        .main-container {
            display: grid;
            grid-template-areas:
                "header"
                "content"
                "footer";
            grid-template-rows: 50px 1fr 30px;
            min-height: 100vh;
            gap: 4px;
            padding: 4px;
        }

        /* Header */
        .header {
            grid-area: header;
            background: var(--bg-glass);
            backdrop-filter: blur(25px);
            border: 1px solid var(--border-subtle);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            box-shadow: var(--glow-primary);
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            font-size: 24px;
            font-weight: 800;
            color: var(--accent-primary);
            text-transform: uppercase;
            letter-spacing: 3px;
            text-shadow: 0 0 20px rgba(0, 255, 136, 0.7);
        }

        .intellisense-badge {
            background: linear-gradient(45deg, var(--accent-purple), var(--accent-secondary));
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
        }

        .status-indicators {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .status-pill {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: var(--bg-glass);
            border: 1px solid var(--border-subtle);
            border-radius: 20px;
            font-size: 10px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        /* Content Area */
        .content-area {
            grid-area: content;
            display: grid;
            grid-template-columns: calc(100% - 224px) 220px;
            gap: 4px;
            overflow: hidden;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
            overflow-y: auto;
        }

        /* Panel Base */
        .panel {
            background: var(--bg-glass);
            backdrop-filter: blur(25px);
            border: 1px solid var(--border-subtle);
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
            opacity: 0.8;
        }

        .panel-header {
            padding: 12px 16px 8px 16px;
            border-bottom: 1px solid var(--border-subtle);
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(0, 0, 0, 0.3);
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .panel-header:hover {
            background: rgba(0, 255, 136, 0.05);
        }

        .panel-title {
            font-size: 12px;
            font-weight: 800;
            color: var(--accent-primary);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .panel-content {
            flex: 1;
            padding: 0;
            overflow: hidden;
        }

        /* Live indicator standardization */
        .live-indicator {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            font-size: 9px;
            color: var(--accent-primary);
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: var(--accent-primary);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }

        /* Enhanced Toggle Button */
        .toggle-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-subtle);
            border-radius: 6px;
            color: var(--accent-primary);
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-left: 8px;
        }

        .toggle-btn:hover {
            background: var(--accent-primary);
            color: var(--bg-primary);
            transform: scale(1.1);
            box-shadow: var(--glow-primary);
        }

        .toggle-btn.expanded {
            transform: rotate(45deg);
            background: var(--accent-primary);
            color: var(--bg-primary);
        }

        /* Compact Position Summary Cards */
        .position-summary-card {
            background: var(--bg-glass);
            backdrop-filter: blur(25px);
            border: 1px solid var(--border-subtle);
            border-radius: 6px;
            margin: 4px;
            margin-bottom: 4px;
            overflow: hidden;
        }

        .position-summary-header {
            background: rgba(0, 0, 0, 0.3);
            padding: 12px 24px;
            border-bottom: 1px solid var(--border-subtle);
            display: grid;
            grid-template-rows: auto auto;
            grid-template-columns: 160px 120px 120px 140px 120px 120px 120px 120px;
            gap: 18px 26px;
            font-family: 'JetBrains Mono', monospace;
            align-items: start;
            min-height: 64px;
            width: 100%;
        }

        /* Top row - Primary trading info */
        .top-row {
            display: contents;
        }

        /* Bottom row - Secondary details */
        .bottom-row {
            display: contents;
        }

        .summary-section {
            display: flex;
            flex-direction: column;
            gap: 2px;
            white-space: nowrap;
            overflow: hidden;
        }

        .summary-section-label {
            color: var(--text-muted);
            text-transform: uppercase;
            font-size: 7px;
            font-weight: 600;
            letter-spacing: 0.3px;
            line-height: 1;
        }

        .summary-section-value {
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1.1;
        }

        .summary-section-subtitle {
            font-size: 7px;
            color: var(--text-secondary);
            font-family: 'JetBrains Mono', monospace;
            line-height: 1;
        }

        /* Top row - Primary info (bigger fonts) */
        .primary-section .summary-section-value {
            font-size: 12px;
            font-weight: 800;
        }

        .primary-section .summary-section-label {
            font-size: 8px;
        }

        /* Bottom row - Secondary info (smaller fonts) */
        .secondary-section .summary-section-value {
            font-size: 9px;
            font-weight: 600;
        }

        .secondary-section .summary-section-label {
            font-size: 6px;
        }

        /* Symbol styling - white and prominent */
        .symbol-section .summary-section-value {
            color: var(--text-primary);
            font-size: 16px;
            font-weight: 800;
            text-transform: uppercase;
            letter-spacing: 0.8px;
        }

        .symbol-section .summary-section-subtitle {
            color: var(--accent-primary);
            font-weight: 700;
            text-transform: uppercase;
            font-size: 8px;
        }

        /* P&L styling with larger font */
        .pnl-section .summary-section-value {
            font-size: 14px;
            font-weight: 800;
        }

        .market-data-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            font-size: 10px;
            line-height: 1.4;
        }

        .market-data-item {
            display: flex;
            justify-content: space-between;
        }

        /* Enhanced Positions Table */
        .positions-table-container {
            padding: 0;
        }

        .positions-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 9px;
        }

        .positions-table th {
            background: var(--bg-primary);
            color: var(--accent-primary);
            font-weight: 800;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 8px;
            padding: 8px 4px;
            text-align: left;
            border-bottom: 1px solid var(--border-subtle);
        }

        .positions-table td {
            padding: 6px 4px;
            text-align: left;
            border-bottom: 1px solid var(--border-subtle);
            vertical-align: middle;
        }
            gap: 16px;
            font-size: 11px;
        }

        .summary-section {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .summary-section-label {
            color: var(--text-muted);
            text-transform: uppercase;
            font-size: 9px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .summary-section-value {
            font-weight: bold;
            font-size: 13px;
        }

        .summary-section-subtitle {
            font-size: 10px;
            color: var(--text-secondary);
        }

        .market-data-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            font-size: 10px;
            line-height: 1.4;
        }

        .market-data-item {
            display: flex;
            justify-content: space-between;
        }

        /* Enhanced Positions Table */
        .positions-table-container {
            padding: 0;
        }

        .positions-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 9px;
        }

        .positions-table th {
            background: var(--bg-primary);
            color: var(--accent-primary);
            font-weight: 800;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 8px;
            padding: 8px 4px;
            text-align: left;
            border-bottom: 1px solid var(--border-subtle);
        }

        .positions-table td {
            padding: 6px 4px;
            text-align: left;
            border-bottom: 1px solid var(--border-subtle);
            vertical-align: middle;
        }

        /* Position Row Styling */
        .position-row {
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .position-row:hover {
            background: rgba(0, 255, 136, 0.08);
        }

        .position-row.flipped {
            background: rgba(0, 255, 136, 0.1);
        }

        .position-details {
            display: none;
            background: var(--bg-tertiary);
            border-left: 3px solid var(--accent-secondary);
            margin: 4px 0;
        }

        .position-details.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 6px;
            padding: 8px;
            font-size: 8px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .detail-label {
            color: var(--text-muted);
            text-transform: uppercase;
            font-size: 7px;
        }

        .detail-value {
            color: var(--text-primary);
            font-weight: 600;
        }

        /* Enhanced Parent-Child Hierarchy */
        .parent-row {
            background: rgba(0, 255, 136, 0.05);
            border-left: 3px solid var(--accent-primary);
        }

        .child-row {
            background: rgba(0, 136, 255, 0.03);
            border-left: 3px solid var(--accent-secondary);
        }

        .child-symbol {
            position: relative;
            color: var(--accent-secondary);
            font-style: italic;
            padding-left: 24px;
        }

        .child-symbol::before {
            content: '└─';
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--accent-secondary);
            font-weight: bold;
            font-size: 10px;
        }

        .child-row .child-symbol {
            font-weight: normal;
        }

        /* Enhanced child row quantity styling */
        .child-quantity {
            position: relative;
            padding-left: 16px;
            color: var(--accent-secondary);
        }

        .child-quantity::before {
            content: '↳';
            position: absolute;
            left: 2px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--accent-secondary);
            font-weight: bold;
        }

        /* Compact Market Data */
        .market-data {
            font-family: 'JetBrains Mono', monospace;
            font-size: 7px;
            line-height: 1.1;
        }

        .market-data div {
            margin-bottom: 1px;
        }

        /* P&L Styling */
        .pnl-positive { color: var(--accent-primary); font-weight: 700; }
        .pnl-negative { color: var(--accent-danger); font-weight: 700; }

        /* Enhanced Historical Trades with Tabs */
        .history-panel {
            position: relative;
        }

        .history-tabs {
            display: flex;
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-subtle);
        }

        .history-tab {
            padding: 8px 16px;
            font-size: 9px;
            font-weight: 600;
            text-transform: uppercase;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
            flex: 1;
            text-align: center;
        }

        .history-tab:hover {
            background: rgba(0, 255, 136, 0.1);
        }

        .history-tab.active {
            background: rgba(0, 255, 136, 0.1);
            border-bottom-color: var(--accent-primary);
            color: var(--accent-primary);
        }

        .history-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s ease;
        }

        .history-content.expanded {
            max-height: 600px;
            overflow-y: auto;
        }

        .history-table-container {
            padding: 12px;
            overflow-y: auto;
            max-height: 300px;
        }

        .history-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 8px;
        }

        .history-table th {
            background: var(--bg-primary);
            color: var(--accent-primary);
            font-weight: 800;
            text-transform: uppercase;
            font-size: 7px;
            padding: 6px 3px;
            text-align: left;
            border-bottom: 1px solid var(--border-subtle);
            position: sticky;
            top: 0;
        }

        .history-table td {
            padding: 4px 3px;
            border-bottom: 1px solid var(--border-subtle);
        }

        .history-table tr:hover {
            background: rgba(0, 255, 136, 0.05);
        }

        /* Trade Type Indicators */
        .trade-type {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            border-radius: 3px;
            font-size: 6px;
            font-weight: 800;
            color: var(--bg-primary);
        }

        .trade-type.parent {
            background: var(--accent-primary);
        }

        .trade-type.child {
            background: var(--accent-secondary);
        }

        .trade-type.close {
            background: var(--accent-warning);
        }

        /* Compact Stats Row */
        .stats-row {
            padding: 8px 16px;
            background: var(--bg-primary);
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
            font-size: 8px;
            border-top: 1px solid var(--border-subtle);
        }

        .stat-item {
            text-align: center;
        }

        .stat-label {
            color: var(--text-muted);
            font-size: 7px;
            text-transform: uppercase;
        }

        .stat-value {
            font-weight: 700;
            margin-top: 2px;
        }

        /* Side Panel */
        .side-panel {
            display: flex;
            flex-direction: column;
            gap: 8px;
            overflow-y: auto;
        }

        .control-section {
            background: var(--bg-glass);
            backdrop-filter: blur(25px);
            border: 1px solid var(--border-subtle);
            border-radius: 10px;
            padding: 12px;
        }

        .control-section-title {
            font-size: 10px;
            font-weight: 800;
            color: var(--accent-primary);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
            padding-bottom: 4px;
            border-bottom: 1px solid var(--border-subtle);
        }

        .btn-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6px;
        }

        .btn {
            padding: 8px 10px;
            border: 1px solid var(--border-subtle);
            border-radius: 6px;
            background: var(--bg-glass);
            color: var(--text-primary);
            font-size: 9px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            backdrop-filter: blur(10px);
        }

        .btn:hover {
            background: var(--accent-primary);
            color: var(--bg-primary);
            border-color: var(--accent-primary);
            transform: translateY(-1px);
            box-shadow: var(--glow-primary);
        }

        .btn-primary {
            background: var(--accent-primary);
            color: var(--bg-primary);
            border-color: var(--accent-primary);
        }

        .btn-danger {
            background: var(--accent-danger);
            color: var(--text-primary);
            border-color: var(--accent-danger);
        }

        /* Footer */
        .footer {
            grid-area: footer;
            background: var(--bg-glass);
            backdrop-filter: blur(25px);
            border: 1px solid var(--border-subtle);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            font-size: 9px;
            color: var(--text-secondary);
        }

        /* Scrollbar Styles */
        .main-content::-webkit-scrollbar,
        .side-panel::-webkit-scrollbar,
        .history-table-container::-webkit-scrollbar {
            width: 4px;
        }

        .main-content::-webkit-scrollbar-track,
        .side-panel::-webkit-scrollbar-track,
        .history-table-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
        }

        .main-content::-webkit-scrollbar-thumb,
        .side-panel::-webkit-scrollbar-thumb,
        .history-table-container::-webkit-scrollbar-thumb {
            background: var(--accent-primary);
            border-radius: 2px;
            opacity: 0.7;
        }

        /* Animation for smooth transitions */
        .fade-in {
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Responsive adjustments for different screen sizes */
        @media (max-width: 1600px) {
            .position-summary-header {
                grid-template-columns: 120px 90px 80px 120px 80px 90px 70px 70px;
                font-size: 7px;
            }
        }

        @media (max-width: 1400px) {
            .position-summary-header {
                grid-template-columns: 100px 80px 70px 100px 70px 80px 60px 60px;
                font-size: 7px;
            }
        }

        @media (max-width: 1200px) {
            .position-summary-header {
                grid-template-columns: 90px 70px 60px 90px 60px 70px 50px 50px;
                font-size: 6px;
            }

            .details-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Ensure position cards don't overflow */
        .position-summary-card {
            overflow-x: hidden;
        }

        .position-summary-header {
            min-width: fit-content;
            overflow: hidden;
        }

        /* Trade Hierarchy Styling - Match Raw OCR Output */
        .trade-hierarchy-section {
            margin-top: 8px;
            background: var(--bg-primary);
            border: 1px solid var(--border-subtle);
            border-radius: 4px;
            overflow: hidden;
        }

        .hierarchy-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(0, 0, 0, 0.4);
            border-bottom: 1px solid var(--border-subtle);
            cursor: pointer;
        }

        .hierarchy-header:hover {
            background: rgba(0, 0, 0, 0.5);
        }

        .hierarchy-title {
            font-size: 9px;
            font-weight: 600;
            color: var(--text-primary);
            font-family: 'JetBrains Mono', monospace;
        }

        .hierarchy-toggle {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 12px;
            cursor: pointer;
            padding: 2px 6px;
            border-radius: 2px;
            transition: all 0.2s ease;
        }

        .hierarchy-toggle:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .hierarchy-content {
            max-height: 300px;
            overflow-y: auto;
            padding: 8px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 9px;
            background: var(--bg-primary);
        }

        .hierarchy-content.collapsed {
            display: none;
        }

        .trade-entry {
            margin: 6px 0;
            border-radius: 4px;
            overflow: hidden;
            border-left: 3px solid transparent;
            background: rgba(0, 0, 0, 0.3);
        }

        .parent-trade {
            border-left-color: var(--accent-primary);
        }

        .child-trade {
            border-left-color: var(--accent-secondary);
            margin-left: 20px;
        }

        .trade-header {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            background: rgba(0, 0, 0, 0.5);
            font-size: 9px;
            font-weight: 600;
            font-family: 'JetBrains Mono', monospace;
            color: var(--text-primary);
        }

        .trade-type {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: 700;
            min-width: 50px;
            text-align: center;
            font-family: 'JetBrains Mono', monospace;
        }

        .parent-trade .trade-type {
            background: rgba(0, 100, 200, 0.8);
            color: white;
            border: 1px solid var(--accent-primary);
        }

        .child-trade .trade-type {
            background: rgba(0, 0, 0, 0.8);
            color: var(--accent-secondary);
            border: 1px solid var(--accent-secondary);
        }

        .trade-id {
            font-family: 'JetBrains Mono', monospace;
            font-size: 9px;
            color: var(--text-secondary);
            min-width: 90px;
        }

        .trade-symbol {
            font-weight: 700;
            color: var(--text-primary);
            min-width: 50px;
            font-family: 'JetBrains Mono', monospace;
        }

        .trade-action {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: 700;
            min-width: 70px;
            text-align: center;
            background: rgba(100, 150, 255, 0.3);
            color: var(--accent-info);
            font-family: 'JetBrains Mono', monospace;
        }

        .trade-status {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: 700;
            min-width: 70px;
            text-align: center;
            font-family: 'JetBrains Mono', monospace;
        }

        .status-filled {
            background: rgba(0, 200, 100, 0.3);
            color: var(--accent-success);
        }

        .status-pending {
            background: rgba(255, 200, 0, 0.3);
            color: var(--accent-warning);
        }

        .status-cancelled {
            background: rgba(200, 0, 0, 0.3);
            color: var(--accent-danger);
        }

        .status-partial {
            background: rgba(100, 150, 255, 0.3);
            color: var(--accent-info);
        }

        .trade-details {
            padding: 12px;
            background: rgba(0, 0, 0, 0.2);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .detail-row {
            display: flex;
            margin: 4px 0;
            font-size: 9px;
            font-family: 'JetBrains Mono', monospace;
        }

        .detail-label {
            color: var(--text-secondary);
            font-weight: 600;
            min-width: 100px;
            margin-right: 12px;
            font-size: 10px;
        }

        .detail-value {
            color: var(--text-primary);
            font-family: 'JetBrains Mono', monospace;
            flex: 1;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header">
            <div class="logo-section">
                <div class="logo">TESTRADE</div>
                <div class="intellisense-badge">IntelliSense AI</div>
            </div>
            <div class="status-indicators">
                <div class="status-pill">
                    <span>Day P&L:</span>
                    <span class="pnl-positive">+$2,847</span>
                </div>
                <div class="status-pill">
                    <span>Open:</span>
                    <span>3 positions</span>
                </div>
                <div class="status-pill">
                    <span>BP:</span>
                    <span style="color: var(--accent-warning);">85%</span>
                </div>
            </div>
        </div>

        <div class="content-area">
            <!-- Main Content -->
            <div class="main-content">
                
                <!-- OCR Panels -->
                <div class="panel" style="min-height: 120px;">
                    <div class="panel-header">
                        <div class="panel-title">OCR Preview</div>
                        <div class="live-indicator">
                            <div class="live-dot"></div>
                            <span>Live | 87%</span>
                        </div>
                    </div>
                    <div class="panel-content" style="padding: 12px;">
                        <div style="text-align: center; padding: 20px; color: var(--text-muted); border: 1px dashed var(--border-subtle); border-radius: 6px;">
                            [OCR Preview Image Here]
                        </div>
                    </div>
                </div>

                <div class="panel" style="min-height: 100px;">
                    <div class="panel-header">
                        <div class="panel-title">Raw OCR Output</div>
                        <div class="live-indicator">
                            <div class="live-dot"></div>
                            <span>Live</span>
                        </div>
                    </div>
                    <div class="panel-content" style="padding: 12px;">
                        <div style="background: var(--bg-primary); border: 1px solid var(--border-subtle); border-radius: 4px; padding: 8px; font-family: monospace; font-size: 9px; max-height: 80px; overflow-y: auto;">
                            RNAZ 500 7.37 -185.00 -2.50 -187.50 -0.37 1,500<br>
                            YIBO 14,100 1.31 -72.00 0.00 -72.00 -0.01 16,100
                        </div>
                    </div>
                </div>

                <div class="panel" style="min-height: 80px;">
                    <div class="panel-header">
                        <div class="panel-title">Processed OCR</div>
                        <div class="live-indicator">
                            <div class="live-dot"></div>
                            <span>Parsed</span>
                        </div>
                    </div>
                    <div class="panel-content" style="padding: 12px;">
                        <div style="font-size: 9px; color: var(--text-secondary);">
                            Symbols detected: RNAZ, YIBO | Confidence: 87%
                        </div>
                    </div>
                </div>

                <!-- Enhanced Open Positions Panel -->
                <div class="panel">
                    <div class="panel-header">
                        <div class="panel-title">Open Positions - Live Data</div>
                        <div style="display: flex; gap: 12px; font-size: 11px;">
                            <span>Open: <strong id="guiOpenPositionsCount">3</strong></span>
                            <span class="pnl-positive" id="guiUnrealizedPnlTotal">Unrealized: <strong>+$1,247</strong></span>
                            <div class="live-indicator">
                                <div class="live-dot"></div>
                                <span>Market Data</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="panel-content" style="padding: 0;" id="openPositionsContainer">
                        <!-- RNAZ Position Group -->
                        <div class="position-summary-card">
                            <div class="position-summary-header">
                                <!-- TOP ROW - Primary Trading Info (8 elements) -->
                                <div class="summary-section symbol-section primary-section">
                                    <div class="summary-section-label">SYMBOL</div>
                                    <div class="summary-section-value">RNAZ</div>
                                    <div class="summary-section-subtitle">MOMENTUM</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">QUANTITY</div>
                                    <div class="summary-section-value">600</div>
                                    <div class="summary-section-subtitle">3 fills</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">AVG COST</div>
                                    <div class="summary-section-value">$7.37</div>
                                    <div class="summary-section-subtitle">per share</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">MARKET</div>
                                    <div class="summary-section-value">$9.22×$9.26</div>
                                    <div class="summary-section-subtitle">bid × ask</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">LAST PRICE</div>
                                    <div class="summary-section-value">$9.24</div>
                                    <div class="summary-section-subtitle">current</div>
                                </div>
                                <div class="summary-section pnl-section primary-section">
                                    <div class="summary-section-label">REALIZED P&L</div>
                                    <div class="summary-section-value pnl-positive">+$1,122</div>
                                    <div class="summary-section-subtitle">session</div>
                                </div>
                                <div class="summary-section pnl-section primary-section">
                                    <div class="summary-section-label">UNREALIZED P&L</div>
                                    <div class="summary-section-value pnl-positive">+$1,122</div>
                                    <div class="summary-section-subtitle">current</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">P&L PER SHARE</div>
                                    <div class="summary-section-value pnl-positive">+$1.87</div>
                                    <div class="summary-section-subtitle">$9.24-$7.37</div>
                                </div>

                                <!-- BOTTOM ROW - Secondary Details (8 elements) -->
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">TOTAL TRADED</div>
                                    <div class="summary-section-value">1,000</div>
                                    <div class="summary-section-subtitle">shares</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">COMMISSIONS</div>
                                    <div class="summary-section-value">$3.00</div>
                                    <div class="summary-section-subtitle">estimated</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">SPREAD</div>
                                    <div class="summary-section-value">$0.04</div>
                                    <div class="summary-section-subtitle">bid-ask</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">VOLUME</div>
                                    <div class="summary-section-value">1.5M</div>
                                    <div class="summary-section-subtitle">market</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">SIGNAL LAT</div>
                                    <div class="summary-section-value">150ms</div>
                                    <div class="summary-section-subtitle">to fill</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">ORDER LAT</div>
                                    <div class="summary-section-value">75ms</div>
                                    <div class="summary-section-subtitle">to fill</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">MARKET VALUE</div>
                                    <div class="summary-section-value">$5,544</div>
                                    <div class="summary-section-subtitle">current</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">OPENED</div>
                                    <div class="summary-section-value">09:32:15</div>
                                    <div class="summary-section-subtitle">time</div>
                                </div>
                            </div>
                            
                            <!-- RNAZ Trade Hierarchy Details -->
                            <div class="trade-hierarchy-section">
                                <div class="hierarchy-header">
                                    <span class="hierarchy-title">Trade Hierarchy (Parent: TRD-12301)</span>
                                    <button class="hierarchy-toggle" onclick="toggleHierarchy('rnaz-hierarchy')">▼</button>
                                </div>

                                <div id="rnaz-hierarchy" class="hierarchy-content">
                                    <!-- Child Trade 4 - CANCELLED (Most Recent) -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12301-4</span>
                                            <span class="trade-symbol">RNAZ</span>
                                            <span class="trade-action">ADD</span>
                                            <span class="trade-status status-cancelled">CANCELLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">10:35:12 | 200 shares @ $7.50 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Cancelled:</span>
                                                <span class="detail-value">10:35:45 | Reason: Market moved away | User cancelled</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 138ms | Order: 65ms | Duration: 33s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">No commission | No fill | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">RNAZ 600 7.37 -185.00 -2.50 -187.50 -0.37 1,500</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">97.9% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Child Trade 3 - REDUCE (Pending) -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12301-3</span>
                                            <span class="trade-symbol">RNAZ</span>
                                            <span class="trade-action">REDUCE</span>
                                            <span class="trade-status status-pending">PENDING</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">10:28:45 | 150 shares @ $9.20 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Status:</span>
                                                <span class="detail-value">Submitted to broker | Order ID: LS-789456 | Awaiting fill</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 142ms | Order: 71ms | Fill: Pending</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Estimated:</span>
                                                <span class="detail-value">Commission: $1.00 | Net: $1,379.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">RNAZ 600 7.37 -185.00 -2.50 -187.50 -0.37 1,500</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">98.1% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Child Trade 2 - ADD -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12301-2</span>
                                            <span class="trade-symbol">RNAZ</span>
                                            <span class="trade-action">ADD</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">10:12:08 | 100 shares @ $7.35 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">10:12:11 | 100 shares @ $7.36 | Cost: $736.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 155ms | Order: 82ms | Fill: 2.9s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.00 | Net: $737.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">RNAZ 500 7.37 -185.00 -2.50 -187.50 -0.37 1,500</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">96.8% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Child Trade 1 - ADD -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12301-1</span>
                                            <span class="trade-symbol">RNAZ</span>
                                            <span class="trade-action">ADD</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">09:45:22 | 300 shares @ $7.40 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">09:45:25 | 300 shares @ $7.38 | Cost: $2,214.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 145ms | Order: 68ms | Fill: 2.8s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.00 | Net: $2,215.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">RNAZ 200 7.37 -185.00 -2.50 -187.50 -0.37 1,500</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">97.2% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Parent Trade (Original) -->
                                    <div class="trade-entry parent-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">PARENT</span>
                                            <span class="trade-id">TRD-12301</span>
                                            <span class="trade-symbol">RNAZ</span>
                                            <span class="trade-action">OPEN_LONG</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">09:32:15 | 200 shares @ $7.35 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">09:32:18 | 200 shares @ $7.37 | Cost: $1,474.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 150ms | Order: 75ms | Fill: 3.2s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.00 | Net: $1,475.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">RNAZ 0 0.00 0.00 0.00 0.00 0.00 0</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">98.5% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- YIBO Position Group - LOSING POSITION (hopefully never in real life!) -->
                        <div class="position-summary-card">
                            <div class="position-summary-header">
                                <!-- TOP ROW - Primary Trading Info (8 elements) -->
                                <div class="summary-section symbol-section primary-section">
                                    <div class="summary-section-label">SYMBOL</div>
                                    <div class="summary-section-value">YIBO</div>
                                    <div class="summary-section-subtitle">SCALP</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">QUANTITY</div>
                                    <div class="summary-section-value">14.1K</div>
                                    <div class="summary-section-subtitle">2 fills</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">AVG COST</div>
                                    <div class="summary-section-value">$1.45</div>
                                    <div class="summary-section-subtitle">per share</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">MARKET</div>
                                    <div class="summary-section-value">$1.32×$1.34</div>
                                    <div class="summary-section-subtitle">bid × ask</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">LAST PRICE</div>
                                    <div class="summary-section-value">$1.34</div>
                                    <div class="summary-section-subtitle">current</div>
                                </div>
                                <div class="summary-section pnl-section primary-section">
                                    <div class="summary-section-label">REALIZED P&L</div>
                                    <div class="summary-section-value pnl-negative">-$1,551</div>
                                    <div class="summary-section-subtitle">session</div>
                                </div>
                                <div class="summary-section pnl-section primary-section">
                                    <div class="summary-section-label">UNREALIZED P&L</div>
                                    <div class="summary-section-value pnl-negative">-$1,551</div>
                                    <div class="summary-section-subtitle">current</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">P&L PER SHARE</div>
                                    <div class="summary-section-value pnl-negative">-$0.11</div>
                                    <div class="summary-section-subtitle">$1.34-$1.45</div>
                                </div>

                                <!-- BOTTOM ROW - Secondary Details (8 elements) -->
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">TOTAL TRADED</div>
                                    <div class="summary-section-value">14.1K</div>
                                    <div class="summary-section-subtitle">shares</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">COMMISSIONS</div>
                                    <div class="summary-section-value">$2.00</div>
                                    <div class="summary-section-subtitle">estimated</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">SPREAD</div>
                                    <div class="summary-section-value">$0.02</div>
                                    <div class="summary-section-subtitle">bid-ask</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">VOLUME</div>
                                    <div class="summary-section-value">850K</div>
                                    <div class="summary-section-subtitle">market</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">SIGNAL LAT</div>
                                    <div class="summary-section-value">89ms</div>
                                    <div class="summary-section-subtitle">to fill</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">ORDER LAT</div>
                                    <div class="summary-section-value">42ms</div>
                                    <div class="summary-section-subtitle">to fill</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">MARKET VALUE</div>
                                    <div class="summary-section-value">$18,894</div>
                                    <div class="summary-section-subtitle">current</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">OPENED</div>
                                    <div class="summary-section-value">10:15:42</div>
                                    <div class="summary-section-subtitle">time</div>
                                </div>
                            </div>
                            
                            <!-- YIBO Trade Hierarchy Details -->
                            <div class="trade-hierarchy-section">
                                <div class="hierarchy-header" onclick="toggleHierarchy('yibo-hierarchy')">
                                    <span class="hierarchy-title">Trade Hierarchy (Parent: TRD-12401)</span>
                                    <button class="hierarchy-toggle">▼</button>
                                </div>

                                <div id="yibo-hierarchy" class="hierarchy-content collapsed">
                                    <!-- Parent Trade (Foundation) -->
                                    <div class="trade-entry parent-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">PARENT</span>
                                            <span class="trade-id">TRD-12401</span>
                                            <span class="trade-symbol">YIBO</span>
                                            <span class="trade-action">OPEN_LONG</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">10:15:42 | 10,000 shares @ $1.46 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">10:15:45 | 10,000 shares @ $1.45 | Cost: $14,500.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 89ms | Order: 42ms | Fill: 2.8s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.00 | Net: $14,501.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">YIBO 0 0.00 0.00 0.00 0.00 0.00 0</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">96.8% | Strategy: SCALP | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Child Trade 1 - ADD -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12401-1</span>
                                            <span class="trade-symbol">YIBO</span>
                                            <span class="trade-action">ADD</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">10:22:15 | 4,100 shares @ $1.44 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">10:22:18 | 4,100 shares @ $1.44 | Cost: $5,904.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 92ms | Order: 45ms | Fill: 2.5s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.00 | Net: $5,905.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">YIBO 10,000 1.45 -1,100.00 -7.50 -1,107.50 -0.11 14,500</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">97.1% | Strategy: SCALP | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Child Trade 2 - REDUCE (Pending) -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12401-2</span>
                                            <span class="trade-symbol">YIBO</span>
                                            <span class="trade-action">REDUCE</span>
                                            <span class="trade-status status-pending">PENDING</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">10:45:30 | 5,000 shares @ $1.35 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Status:</span>
                                                <span class="detail-value">Submitted to broker | Order ID: LS-789457 | Awaiting fill</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 88ms | Order: 41ms | Fill: Pending</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Estimated:</span>
                                                <span class="detail-value">Commission: $1.00 | Net: $6,749.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">YIBO 14,100 1.45 -1,551.00 -10.50 -1,561.50 -0.11 20,400</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">98.3% | Strategy: SCALP | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- SPY Position Group -->
                        <div class="position-summary-card">
                            <div class="position-summary-header">
                                <!-- TOP ROW - Primary Trading Info (8 elements) -->
                                <div class="summary-section symbol-section primary-section">
                                    <div class="summary-section-label">SYMBOL</div>
                                    <div class="summary-section-value">SPY</div>
                                    <div class="summary-section-subtitle">HEDGE</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">QUANTITY</div>
                                    <div class="summary-section-value">100</div>
                                    <div class="summary-section-subtitle">1 fill</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">AVG COST</div>
                                    <div class="summary-section-value">$590.50</div>
                                    <div class="summary-section-subtitle">per share</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">MARKET</div>
                                    <div class="summary-section-value">$590.75×$590.85</div>
                                    <div class="summary-section-subtitle">bid × ask</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">LAST PRICE</div>
                                    <div class="summary-section-value">$590.80</div>
                                    <div class="summary-section-subtitle">current</div>
                                </div>
                                <div class="summary-section pnl-section primary-section">
                                    <div class="summary-section-label">REALIZED P&L</div>
                                    <div class="summary-section-value pnl-positive">+$30</div>
                                    <div class="summary-section-subtitle">session</div>
                                </div>
                                <div class="summary-section pnl-section primary-section">
                                    <div class="summary-section-label">UNREALIZED P&L</div>
                                    <div class="summary-section-value pnl-positive">+$30</div>
                                    <div class="summary-section-subtitle">current</div>
                                </div>
                                <div class="summary-section primary-section">
                                    <div class="summary-section-label">P&L PER SHARE</div>
                                    <div class="summary-section-value pnl-positive">+$0.30</div>
                                    <div class="summary-section-subtitle">$590.80-$590.50</div>
                                </div>

                                <!-- BOTTOM ROW - Secondary Details (8 elements) -->
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">TOTAL TRADED</div>
                                    <div class="summary-section-value">100</div>
                                    <div class="summary-section-subtitle">shares</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">COMMISSIONS</div>
                                    <div class="summary-section-value">$1.00</div>
                                    <div class="summary-section-subtitle">estimated</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">SPREAD</div>
                                    <div class="summary-section-value">$0.10</div>
                                    <div class="summary-section-subtitle">bid-ask</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">VOLUME</div>
                                    <div class="summary-section-value">2.1M</div>
                                    <div class="summary-section-subtitle">market</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">SIGNAL LAT</div>
                                    <div class="summary-section-value">125ms</div>
                                    <div class="summary-section-subtitle">to fill</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">ORDER LAT</div>
                                    <div class="summary-section-value">68ms</div>
                                    <div class="summary-section-subtitle">to fill</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">MARKET VALUE</div>
                                    <div class="summary-section-value">$59,080</div>
                                    <div class="summary-section-subtitle">current</div>
                                </div>
                                <div class="summary-section secondary-section">
                                    <div class="summary-section-label">OPENED</div>
                                    <div class="summary-section-value">11:22:08</div>
                                    <div class="summary-section-subtitle">time</div>
                                </div>
                            </div>
                            
                            <!-- SPY Trade Hierarchy Details -->
                            <div class="trade-hierarchy-section">
                                <div class="hierarchy-header" onclick="toggleHierarchy('spy-hierarchy')">
                                    <span class="hierarchy-title">Trade Hierarchy (Parent: TRD-12501)</span>
                                    <button class="hierarchy-toggle">▼</button>
                                </div>

                                <div id="spy-hierarchy" class="hierarchy-content collapsed">
                                    <!-- Parent Trade (Foundation) -->
                                    <div class="trade-entry parent-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">PARENT</span>
                                            <span class="trade-id">TRD-12501</span>
                                            <span class="trade-symbol">SPY</span>
                                            <span class="trade-action">OPEN_LONG</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">11:22:08 | 100 shares @ $590.50 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">11:22:11 | 100 shares @ $590.50 | Cost: $59,050.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 125ms | Order: 68ms | Fill: 2.9s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.00 | Net: $59,051.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">SPY 0 0.00 0.00 0.00 0.00 0.00 0</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">99.1% | Strategy: HEDGE | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Child Trade 1 - HEDGE ADJUSTMENT (Pending) -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12501-1</span>
                                            <span class="trade-symbol">SPY</span>
                                            <span class="trade-action">REDUCE</span>
                                            <span class="trade-status status-pending">PENDING</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">11:45:20 | 50 shares @ $591.00 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Status:</span>
                                                <span class="detail-value">Submitted to broker | Order ID: LS-789458 | Awaiting fill</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 118ms | Order: 62ms | Fill: Pending</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Estimated:</span>
                                                <span class="detail-value">Commission: $1.00 | Net: $29,549.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">SPY 100 590.50 +30.00 +0.05 +29.00 +0.05 59,080</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">98.8% | Strategy: HEDGE | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Historical Trades Panel with Tabs -->
                <div class="panel history-panel">
                    <div class="panel-header" onclick="toggleHistory()">
                        <div style="display: flex; align-items: center;">
                            <div class="panel-title">Historical Trades</div>
                            <div class="toggle-btn" id="historyToggle">+</div>
                        </div>
                        <div style="font-size: 10px;">
                            <span>Today: <strong>15</strong></span>
                            <span class="pnl-positive" style="margin-left: 12px;">Realized: <strong>+$1,600</strong></span>
                        </div>
                    </div>
                    
                    <!-- Tab Navigation -->
                    <div class="history-tabs">
                        <div class="history-tab active" onclick="switchHistoryTab('today')">Today</div>
                        <div class="history-tab" onclick="switchHistoryTab('winners')">Winners</div>
                        <div class="history-tab" onclick="switchHistoryTab('losers')">Losers</div>
                        <div class="history-tab" onclick="switchHistoryTab('all')">All Time</div>
                    </div>

                    <div class="history-content" id="historyContent">
                        <!-- Historical Trades with Hierarchy Design - ALL TRADES -->
                        <div class="historical-trades-container">

                            <!-- RNAZ Trade Group (OPEN - Most Recent Parent) -->
                            <div class="trade-hierarchy-section">
                                <div class="hierarchy-header" onclick="toggleHierarchy('rnaz-history')">
                                    <span class="hierarchy-title">RNAZ Trade Group (OPEN) - P&L: +$1,122 | Shares: 1,000 | Commissions: $4.00 | Net: +$1,118</span>
                                    <button class="hierarchy-toggle">▼</button>
                                </div>

                                <div id="rnaz-history" class="hierarchy-content collapsed">
                                    <!-- Parent Trade (Foundation) -->
                                    <div class="trade-entry parent-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">PARENT</span>
                                            <span class="trade-id">TRD-12301</span>
                                            <span class="trade-symbol">RNAZ</span>
                                            <span class="trade-action">OPEN_LONG</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">09:32:15 | 200 shares @ $7.35 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">09:32:18 | 200 shares @ $7.37 | Cost: $1,474.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 150ms | Order: 75ms | Fill: 3.2s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.00 | Net: $1,475.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">RNAZ 0 0.00 0.00 0.00 0.00 0.00 0</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">98.5% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Child Trade 1 - ADD -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12301-1</span>
                                            <span class="trade-symbol">RNAZ</span>
                                            <span class="trade-action">ADD</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">09:45:22 | 300 shares @ $7.40 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">09:45:25 | 300 shares @ $7.38 | Cost: $2,214.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 145ms | Order: 68ms | Fill: 2.8s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.00 | Net: $2,215.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">RNAZ 200 7.37 -185.00 -2.50 -187.50 -0.37 1,500</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">97.2% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Child Trade 2 - ADD -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12301-2</span>
                                            <span class="trade-symbol">RNAZ</span>
                                            <span class="trade-action">ADD</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">10:12:08 | 100 shares @ $7.35 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">10:12:11 | 100 shares @ $7.36 | Cost: $736.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 155ms | Order: 82ms | Fill: 2.9s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.00 | Net: $737.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">RNAZ 500 7.37 -185.00 -2.50 -187.50 -0.37 1,500</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">96.8% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Child Trade 3 - REDUCE (Pending) -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12301-3</span>
                                            <span class="trade-symbol">RNAZ</span>
                                            <span class="trade-action">REDUCE</span>
                                            <span class="trade-status status-pending">PENDING</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">10:28:45 | 150 shares @ $9.20 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Status:</span>
                                                <span class="detail-value">Submitted to broker | Order ID: LS-789456 | Awaiting fill</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 142ms | Order: 71ms | Fill: Pending</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Estimated:</span>
                                                <span class="detail-value">Commission: $1.00 | Net: $1,379.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">RNAZ 600 7.37 -185.00 -2.50 -187.50 -0.37 1,500</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">98.1% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Child Trade 4 - CANCELLED -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12301-4</span>
                                            <span class="trade-symbol">RNAZ</span>
                                            <span class="trade-action">ADD</span>
                                            <span class="trade-status status-cancelled">CANCELLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">10:35:12 | 200 shares @ $7.50 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Cancelled:</span>
                                                <span class="detail-value">10:35:45 | Reason: Market moved away | User cancelled</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 138ms | Order: 65ms | Duration: 33s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">No commission | No fill | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">RNAZ 600 7.37 -185.00 -2.50 -187.50 -0.37 1,500</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">97.9% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- TSLA Trade Group (CLOSED) -->
                            <div class="trade-hierarchy-section">
                                <div class="hierarchy-header" onclick="toggleHierarchy('tsla-history')">
                                    <span class="hierarchy-title">TSLA Trade Group (CLOSED) - P&L: +$575 | Shares: 150 | Commissions: $2.50 | Net: +$572.50</span>
                                    <button class="hierarchy-toggle">▼</button>
                                </div>

                                <div id="tsla-history" class="hierarchy-content collapsed">
                                    <!-- Parent Trade (Foundation) -->
                                    <div class="trade-entry parent-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">PARENT</span>
                                            <span class="trade-id">TRD-12302</span>
                                            <span class="trade-symbol">TSLA</span>
                                            <span class="trade-action">OPEN_LONG</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">09:32:15 | 100 shares @ $234.50 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">09:32:18 | 100 shares @ $234.50 | Cost: $23,450.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 158ms | Order: 82ms | Fill: 3.1s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.00 | Net: $23,451.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">TSLA 0 0.00 0.00 0.00 0.00 0.00 0</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">98.2% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Child Trade 1 - ADD -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12302-1</span>
                                            <span class="trade-symbol">TSLA</span>
                                            <span class="trade-action">ADD</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">09:35:12 | 50 shares @ $235.20 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">09:35:15 | 50 shares @ $235.20 | Cost: $11,760.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 142ms | Order: 71ms | Fill: 2.8s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.00 | Net: $11,761.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">TSLA 150 234.67 +125.00 +0.85 +123.50 +0.82 23,500</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">97.4% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Child Trade 2 - CLOSE -->
                                    <div class="trade-entry child-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">CHILD</span>
                                            <span class="trade-id">TRD-12302-2</span>
                                            <span class="trade-symbol">TSLA</span>
                                            <span class="trade-action">CLOSE</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">11:47:22 | 150 shares @ $236.80 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">11:47:25 | 150 shares @ $236.85 | Proceeds: $35,527.50</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 125ms | Order: 58ms | Fill: 2.1s</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.50 | Net: $35,526.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">TSLA 0 0.00 +575.00 +1.65 +573.50 +3.82 35,500</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">98.7% | Strategy: MOMENTUM | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- AAPL Trade Group (Closed) -->
                            <div class="trade-hierarchy-section">
                                <div class="hierarchy-header" onclick="toggleHierarchy('aapl-history')">
                                    <span class="hierarchy-title">AAPL Trade Group (CLOSED) - P&L: +$320 | Shares: 200 | Commissions: $2.00 | Net: +$318.00</span>
                                    <button class="hierarchy-toggle">▼</button>
                                </div>

                                <div id="aapl-history" class="hierarchy-content collapsed">
                                    <!-- Parent Trade Only -->
                                    <div class="trade-entry parent-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">PARENT</span>
                                            <span class="trade-id">TRD-12303</span>
                                            <span class="trade-symbol">AAPL</span>
                                            <span class="trade-action">SCALP</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">10:15:30 | 200 shares @ $145.20 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">10:15:33 | 200 shares @ $145.20 | Cost: $29,040.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Closed:</span>
                                                <span class="detail-value">12:00:45 | 200 shares @ $146.80 | Proceeds: $29,360.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 135ms | Order: 65ms | Duration: 1h45m</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$2.00 | Net P&L: +$318.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">AAPL 0 0.00 +320.00 +1.10 +318.00 +1.09 29,200</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">96.8% | Strategy: BREAKOUT | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- MSFT Trade Group (Closed) -->
                            <div class="trade-hierarchy-section">
                                <div class="hierarchy-header" onclick="toggleHierarchy('msft-history')">
                                    <span class="hierarchy-title">MSFT Trade Group (CLOSED) - P&L: +$280 | Shares: 150 | Commissions: $1.50 | Net: +$278.50</span>
                                    <button class="hierarchy-toggle">▼</button>
                                </div>

                                <div id="msft-history" class="hierarchy-content collapsed">
                                    <!-- Parent Trade Only -->
                                    <div class="trade-entry parent-trade">
                                        <div class="trade-header">
                                            <span class="trade-type">PARENT</span>
                                            <span class="trade-id">TRD-12304</span>
                                            <span class="trade-symbol">MSFT</span>
                                            <span class="trade-action">MEAN_REV</span>
                                            <span class="trade-status status-filled">FILLED</span>
                                        </div>
                                        <div class="trade-details">
                                            <div class="detail-row">
                                                <span class="detail-label">Requested:</span>
                                                <span class="detail-value">11:22:15 | 150 shares @ $387.90 LMT | DAY</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Filled:</span>
                                                <span class="detail-value">11:22:18 | 150 shares @ $387.90 | Cost: $58,185.00</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Closed:</span>
                                                <span class="detail-value">12:00:30 | 150 shares @ $389.77 | Proceeds: $58,465.50</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Performance:</span>
                                                <span class="detail-value">Signal: 118ms | Order: 52ms | Duration: 38m</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Commission:</span>
                                                <span class="detail-value">$1.50 | Net P&L: +$279.00 | Broker: LSPD</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">Master Trader:</span>
                                                <span class="detail-value">MSFT 0 0.00 +280.00 +0.48 +279.00 +0.48 58,300</span>
                                            </div>
                                            <div class="detail-row">
                                                <span class="detail-label">OCR Confidence:</span>
                                                <span class="detail-value">97.9% | Strategy: MEAN_REV | Source: LIVE_TRADING</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        
                        <!-- Stats Summary Row -->
                        <div class="stats-row">
                            <div class="stat-item">
                                <div class="stat-label">Total Trades</div>
                                <div class="stat-value">15</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Winners</div>
                                <div class="stat-value pnl-positive">13</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Losers</div>
                                <div class="stat-value pnl-negative">2</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Win Rate</div>
                                <div class="stat-value pnl-positive">86.7%</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Avg Winner</div>
                                <div class="stat-value pnl-positive">+$312</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">Avg Loser</div>
                                <div class="stat-value pnl-negative">-$75</div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Side Panel -->
            <div class="side-panel">
                <div class="control-section">
                    <div class="control-section-title">System Controls</div>
                    <div class="btn-group">
                        <button class="btn btn-primary">Start OCR</button>
                        <button class="btn btn-danger">Emergency</button>
                    </div>
                </div>

                <div class="control-section">
                    <div class="control-section-title">Quick Actions</div>
                    <div class="btn-group">
                        <button class="btn">Close All</button>
                        <button class="btn">Manual Add</button>
                    </div>
                </div>

                <div class="control-section">
                    <div class="control-section-title">Trade Management</div>
                    <div class="btn-group">
                        <button class="btn">Set Stops</button>
                        <button class="btn">Scale Out</button>
                    </div>
                    <div class="btn-group" style="margin-top: 6px;">
                        <button class="btn">Risk Check</button>
                        <button class="btn">P&L Update</button>
                    </div>
                </div>

                <div class="control-section">
                    <div class="control-section-title">View Controls</div>
                    <div class="btn-group">
                        <button class="btn" onclick="expandAllPositions()">Expand All</button>
                        <button class="btn" onclick="collapseAllPositions()">Collapse All</button>
                    </div>
                    <div class="btn-group" style="margin-top: 6px;">
                        <button class="btn" onclick="refreshData()">Refresh Data</button>
                        <button class="btn" onclick="exportData()">Export CSV</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div style="display: flex; gap: 20px;">
                <span>Feed Delay: <strong style="color: var(--accent-primary);">12ms</strong></span>
                <span>OCR Confidence: <strong style="color: var(--accent-primary);">87%</strong></span>
                <span>Last Update: <strong style="color: var(--accent-primary);">14:32:15</strong></span>
            </div>
            <div style="display: flex; gap: 20px;">
                <span>Positions: <strong>3 open</strong></span>
                <span>Day Volume: <strong>47,250</strong></span>
                <span>Profit Factor: <strong style="color: var(--accent-primary);">4.2</strong></span>
            </div>
        </div>
    </div>

    <script>
        // TESTRADE Data Manager - The Electrical Brain! ⚡
        class TESTRADEDataManager {
            constructor() {
                this.websocket = null;
                this.positions = new Map();
                this.trades = new Map();
                this.marketData = new Map();
                this.isConnected = false;
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                this.reconnectDelay = 2000;

                console.log('🔌 TESTRADE Data Manager initializing...');
                this.init();
            }

            init() {
                this.connectWebSocket();
                this.setupEventHandlers();
            }

            connectWebSocket() {
                try {
                    // Connect to TESTRADE GUI Backend WebSocket
                    console.log('🔌 Connecting to TESTRADE WebSocket at ws://localhost:8000/ws');
                    this.websocket = new WebSocket('ws://localhost:8000/ws');

                    this.websocket.onopen = () => {
                        console.log('⚡ CONNECTED to TESTRADE WebSocket!');
                        this.isConnected = true;
                        this.reconnectAttempts = 0;
                        this.updateConnectionStatus(true);

                        // Request initial data bootstrap
                        this.requestBootstrapData();
                    };

                    this.websocket.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            this.handleMessage(data);
                        } catch (error) {
                            console.error('❌ Error parsing WebSocket message:', error);
                        }
                    };

                    this.websocket.onclose = () => {
                        console.log('🔌 TESTRADE WebSocket disconnected');
                        this.isConnected = false;
                        this.updateConnectionStatus(false);
                        this.attemptReconnect();
                    };

                    this.websocket.onerror = (error) => {
                        console.error('⚡ WebSocket error:', error);
                    };

                } catch (error) {
                    console.error('❌ Failed to connect to WebSocket:', error);
                    this.attemptReconnect();
                }
            }

            attemptReconnect() {
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    console.log(`🔄 Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${this.reconnectDelay}ms`);

                    setTimeout(() => {
                        this.connectWebSocket();
                    }, this.reconnectDelay);

                    this.reconnectDelay *= 1.5; // Exponential backoff
                } else {
                    console.error('❌ Max reconnection attempts reached');
                    this.showConnectionError();
                }
            }

            requestBootstrapData() {
                if (this.isConnected) {
                    console.log('📊 Requesting bootstrap data from TESTRADE...');
                    this.websocket.send(JSON.stringify({
                        type: 'request_bootstrap',
                        data: {
                            request_positions: true,
                            request_trades: true,
                            request_market_data: true,
                            request_historical_trades: true
                        }
                    }));
                }
            }

            updateConnectionStatus(connected) {
                const indicators = document.querySelectorAll('.live-indicator .live-dot');
                indicators.forEach(dot => {
                    if (connected) {
                        dot.style.backgroundColor = 'var(--accent-primary)';
                        dot.style.animation = 'pulse 2s infinite';
                    } else {
                        dot.style.backgroundColor = 'var(--accent-danger)';
                        dot.style.animation = 'none';
                    }
                });

                // Update connection status in header
                const statusText = connected ? 'LIVE' : 'DISCONNECTED';
                const statusElements = document.querySelectorAll('.live-indicator span');
                statusElements.forEach(span => {
                    span.textContent = statusText;
                });
            }

            showConnectionError() {
                // Show connection error in UI
                const errorDiv = document.createElement('div');
                errorDiv.style.cssText = `
                    position: fixed; top: 20px; right: 20px; z-index: 9999;
                    background: var(--accent-danger); color: white; padding: 12px 20px;
                    border-radius: 8px; font-family: 'JetBrains Mono', monospace;
                    font-size: 12px; font-weight: bold;
                `;
                errorDiv.textContent = '❌ TESTRADE Connection Failed - Check Backend';
                document.body.appendChild(errorDiv);

                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.parentNode.removeChild(errorDiv);
                    }
                }, 10000);
            }

            setupEventHandlers() {
                // Handle page visibility changes
                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) {
                        console.log('📱 Page hidden - maintaining connection');
                    } else {
                        console.log('📱 Page visible - checking connection');
                        if (!this.isConnected) {
                            this.connectWebSocket();
                        }
                    }
                });

                // Handle window beforeunload
                window.addEventListener('beforeunload', () => {
                    if (this.websocket) {
                        this.websocket.close();
                    }
                });
            }
        }

        // State management
        let uiState = {
            positionsExpanded: false,
            historyExpanded: false,
            activeHistoryTab: 'today',
            expandedPositions: new Set()
        };

        // Initialize TESTRADE Data Manager
        let testradeManager = null;

        // TESTRADE PAYLOAD FIELD MAPPING ⚡
        // Maps HTML elements to Redis payload fields from payloads.md

        class TESTRADEFieldMapper {
            constructor() {
                // Position Card Element Mapping (16 elements: 8 primary + 8 secondary)
                this.positionCardMapping = [
                    // PRIMARY ROW (0-7)
                    { index: 0, label: 'SYMBOL', field: 'symbol', type: 'direct' },
                    { index: 1, label: 'QUANTITY', field: 'quantity', type: 'number' },
                    { index: 2, label: 'AVG COST', field: 'average_price', type: 'currency' },
                    { index: 3, label: 'MARKET', field: 'market_display', type: 'calculated' },
                    { index: 4, label: 'LAST PRICE', field: 'last_price', type: 'currency' },
                    { index: 5, label: 'REALIZED P&L', field: 'realized_pnl_session', type: 'pnl' },
                    { index: 6, label: 'UNREALIZED P&L', field: 'unrealized_pnl', type: 'pnl' },
                    { index: 7, label: 'P&L PER SHARE', field: 'pnl_per_share', type: 'calculated_pnl' },

                    // SECONDARY ROW (8-15)
                    { index: 8, label: 'TOTAL TRADED', field: 'total_traded', type: 'calculated' },
                    { index: 9, label: 'COMMISSIONS', field: 'commissions', type: 'calculated' },
                    { index: 10, label: 'SPREAD', field: 'spread', type: 'calculated' },
                    { index: 11, label: 'VOLUME', field: 'total_volume', type: 'volume' },
                    { index: 12, label: 'SIGNAL LAT', field: 'signal_to_fill_latency_ms', type: 'latency' },
                    { index: 13, label: 'ORDER LAT', field: 'order_to_fill_latency_ms', type: 'latency' },
                    { index: 14, label: 'MARKET VALUE', field: 'market_value', type: 'calculated' },
                    { index: 15, label: 'OPENED', field: 'position_opened_timestamp', type: 'time' }
                ];

                // Subtitle mappings
                this.subtitleMapping = [
                    { index: 0, field: 'strategy', type: 'strategy' },
                    { index: 1, field: 'total_fills_count', type: 'fills' },
                    // Add more subtitle mappings as needed
                ];
            }

            // Calculate derived fields from Redis payload
            calculateDerivedFields(position) {
                const derived = {};

                // Market display: "bid×ask"
                derived.market_display = this.formatMarketDisplay(position.bid_price, position.ask_price);

                // P&L per share
                derived.pnl_per_share = (position.last_price || position.average_price || 0) - (position.average_price || 0);

                // Total traded (session buy + sell quantities)
                derived.total_traded = (position.session_buy_quantity || 0) + (position.session_sell_quantity || 0);

                // Estimated commissions (based on fills)
                derived.commissions = (position.total_fills_count || 1) * 1.00; // $1 per fill

                // Spread
                derived.spread = (position.ask_price || 0) - (position.bid_price || 0);

                // Market value
                derived.market_value = (position.quantity || 0) * (position.last_price || position.average_price || 0);

                // Unrealized P&L (if not provided)
                if (!position.unrealized_pnl && position.last_price && position.average_price && position.quantity) {
                    derived.unrealized_pnl = (position.last_price - position.average_price) * position.quantity;
                }

                return derived;
            }

            // Get field value with fallback and calculation
            getFieldValue(position, mapping) {
                const derived = this.calculateDerivedFields(position);

                // Try derived fields first, then direct fields
                let value = derived[mapping.field] !== undefined ? derived[mapping.field] : position[mapping.field];

                // Apply formatting based on type
                switch (mapping.type) {
                    case 'direct':
                        return value || 'UNKNOWN';
                    case 'number':
                        return this.formatNumber(value || 0);
                    case 'currency':
                        return this.formatCurrency(value || 0);
                    case 'pnl':
                    case 'calculated_pnl':
                        return this.formatCurrency(value || 0);
                    case 'volume':
                        return this.formatVolume(value || 0);
                    case 'latency':
                        return `${Math.round(value || 0)}ms`;
                    case 'time':
                        return this.formatTime(value);
                    case 'calculated':
                        return value || '-';
                    default:
                        return value || '-';
                }
            }

            // Formatting utilities
            formatMarketDisplay(bid, ask) {
                if (!bid || !ask) return '-';
                return `${this.formatCurrency(bid)}×${this.formatCurrency(ask)}`;
            }

            formatCurrency(value) {
                if (typeof value !== 'number') return '$0.00';
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 2
                }).format(value);
            }

            formatNumber(value) {
                if (typeof value !== 'number') return '0';
                return new Intl.NumberFormat('en-US').format(value);
            }

            formatVolume(volume) {
                if (!volume || volume === 0) return '-';
                if (volume >= 1000000) return `${(volume / 1000000).toFixed(1)}M`;
                if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
                return volume.toLocaleString();
            }

            formatTime(timestamp) {
                if (!timestamp) return new Date().toLocaleTimeString();
                const date = new Date(timestamp * 1000); // Convert from Unix timestamp
                return date.toLocaleTimeString();
            }
        }

        // TESTRADE Data Handler Extensions ⚡
        TESTRADEDataManager.prototype.handleMessage = function(data) {
            console.log('📨 Processing TESTRADE message:', data.type);

            switch (data.type) {
                case 'trades_update':
                    this.handleTradesUpdate(data.payload);
                    break;
                case 'position_update':
                    this.handlePositionUpdate(data.payload);
                    break;
                case 'order_update':
                    this.handleOrderUpdate(data.payload);
                    break;
                case 'market_data_update':
                    this.handleMarketDataUpdate(data.payload);
                    break;
                case 'ocr_update':
                    this.handleOCRUpdate(data.payload);
                    break;
                case 'bootstrap_data':
                    this.handleBootstrapData(data.payload);
                    break;
                default:
                    console.log('❓ Unknown TESTRADE message type:', data.type);
            }
        };

        TESTRADEDataManager.prototype.handleBootstrapData = function(payload) {
            console.log('🎯 Bootstrap data received from TESTRADE:', payload);

            if (payload.trades && Array.isArray(payload.trades)) {
                console.log(`📊 Loading ${payload.trades.length} positions from bootstrap`);
                payload.trades.forEach(trade => {
                    this.positions.set(trade.symbol, trade);
                });
                this.updatePositionsDisplay();
            }

            if (payload.historical_trades && Array.isArray(payload.historical_trades)) {
                console.log(`📈 Loading ${payload.historical_trades.length} historical trades from bootstrap`);
                payload.historical_trades.forEach(trade => {
                    this.trades.set(trade.trade_id || trade.symbol + '_' + Date.now(), trade);
                });
                this.updateHistoricalTradesDisplay();
            }

            console.log('✅ Bootstrap data processing complete');
        };

        TESTRADEDataManager.prototype.handleTradesUpdate = function(payload) {
            console.log('💰 Processing trades update from TESTRADE:', payload);

            if (payload.trades && Array.isArray(payload.trades)) {
                payload.trades.forEach(trade => {
                    console.log(`📊 Updating position: ${trade.symbol}`);
                    this.positions.set(trade.symbol, trade);
                });
                this.updatePositionsDisplay();
            }
        };

        TESTRADEDataManager.prototype.handlePositionUpdate = function(payload) {
            console.log('📈 Processing position update from TESTRADE:', payload);

            const symbol = payload.symbol;
            if (payload.is_open) {
                console.log(`📊 Position ${symbol} is open - updating`);
                this.positions.set(symbol, payload);
            } else {
                console.log(`📊 Position ${symbol} is closed - removing`);
                this.positions.delete(symbol);
            }
            this.updatePositionsDisplay();
        };

        TESTRADEDataManager.prototype.handleOrderUpdate = function(payload) {
            console.log('📋 Processing order update from TESTRADE:', payload);

            // Find the position this order belongs to
            const symbol = payload.symbol;
            const position = this.positions.get(symbol);

            if (position) {
                // Update order status in the position
                if (!position.orders) position.orders = [];

                const existingOrderIndex = position.orders.findIndex(o => o.order_id === payload.order_id);
                if (existingOrderIndex >= 0) {
                    position.orders[existingOrderIndex] = { ...position.orders[existingOrderIndex], ...payload };
                } else {
                    position.orders.push(payload);
                }

                this.positions.set(symbol, position);
                this.updatePositionsDisplay();
            }
        };

        TESTRADEDataManager.prototype.handleMarketDataUpdate = function(payload) {
            console.log('📊 Processing market data update from TESTRADE:', payload);

            const symbol = payload.symbol;
            this.marketData.set(symbol, payload);

            // Update position with latest market data
            const position = this.positions.get(symbol);
            if (position) {
                position.current_price = payload.last_price || payload.price;
                position.bid_price = payload.bid_price;
                position.ask_price = payload.ask_price;
                position.market_value = (position.quantity || 0) * (position.current_price || 0);

                // Calculate unrealized P&L
                if (position.average_price && position.current_price && position.quantity) {
                    position.unrealized_pnl = (position.current_price - position.average_price) * position.quantity;
                }

                this.positions.set(symbol, position);
                this.updatePositionsDisplay();
            }
        };

        TESTRADEDataManager.prototype.handleOCRUpdate = function(payload) {
            console.log('👁️ Processing OCR update from TESTRADE:', payload);

            if (payload.data && payload.data.snapshots) {
                Object.keys(payload.data.snapshots).forEach(symbol => {
                    const ocrData = payload.data.snapshots[symbol];
                    const position = this.positions.get(symbol);

                    if (position) {
                        // Update position with OCR data
                        position.ocr_data = ocrData;
                        position.ocr_confidence = payload.data.ocr_confidence;
                        position.master_trader_data = `${symbol} ${ocrData.total_shares || 0} ${ocrData.cost_basis || 0} ${ocrData.unrealized_pnl || 0}`;

                        this.positions.set(symbol, position);
                    }
                });

                this.updatePositionsDisplay();
            }
        };

        // Dynamic Card Management - CLEAR Static Cards and Build from Redis! ⚡
        TESTRADEDataManager.prototype.updatePositionsDisplay = function() {
            console.log('🔌 Building position cards dynamically from Redis data (clearing static cards)');

            const positionsContainer = document.querySelector('.positions-content');
            if (!positionsContainer) {
                console.warn('❌ Positions container not found');
                return;
            }

            // Get only OPEN positions from Redis data
            const openPositions = Array.from(this.positions.values()).filter(pos => pos.is_open !== false);
            console.log(`📊 Found ${openPositions.length} open positions from Redis`);

            // COMPLETELY CLEAR the container (remove all static HTML cards)
            console.log('🗑️ Clearing all static position cards from HTML');
            positionsContainer.innerHTML = '';

            if (openPositions.length === 0) {
                // Show empty state for day trading
                positionsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: var(--text-secondary); font-family: 'JetBrains Mono', monospace;">
                        <div style="font-size: 14px; margin-bottom: 8px;">📊 No Open Positions</div>
                        <div style="font-size: 10px;">Start trading to see position cards appear here</div>
                    </div>
                `;
                console.log('📊 Showing empty state - no open positions');
                return;
            }

            // Create cards dynamically for each open position
            openPositions.forEach((position, index) => {
                console.log(`🏗️ Creating dynamic card ${index + 1} for ${position.symbol}`);
                const newCard = this.createDynamicPositionCard(position);
                positionsContainer.appendChild(newCard);
            });

            // Update summary counts
            this.updatePositionsSummary();

            console.log(`✅ Created ${openPositions.length} dynamic position cards from Redis data`);
        };

        TESTRADEDataManager.prototype.createNewPositionCard = function() {
            console.log('🏗️ Creating new position card from template');

            // Find the first existing card to use as template
            const templateCard = document.querySelector('.position-summary-card');
            if (!templateCard) {
                console.error('❌ No template card found to clone');
                return null;
            }

            // Clone the template card
            const newCard = templateCard.cloneNode(true);

            // Reset all values to defaults
            const valueElements = newCard.querySelectorAll('.summary-section-value');
            valueElements.forEach(element => {
                element.textContent = '-';
                element.className = 'summary-section-value'; // Remove P&L classes
            });

            // Reset hierarchy to collapsed
            const hierarchyContent = newCard.querySelector('.hierarchy-content');
            if (hierarchyContent) {
                hierarchyContent.classList.add('collapsed');
            }

            console.log('✅ New position card created from template');
            return newCard;
        };

        TESTRADEDataManager.prototype.createDynamicPositionCard = function(position) {
            console.log(`🏗️ Creating dynamic position card for ${position.symbol} from Redis data`);

            // Use the field mapper to build the card
            const mapper = new TESTRADEFieldMapper();

            // Create the card HTML structure dynamically
            const cardHtml = this.buildPositionCardHTML(position, mapper);

            // Create DOM element
            const cardElement = document.createElement('div');
            cardElement.innerHTML = cardHtml;
            const card = cardElement.firstElementChild;

            // Wire the card with Redis data
            this.wirePositionCard(card, position);

            console.log(`✅ Dynamic card created for ${position.symbol}`);
            return card;
        };

        TESTRADEDataManager.prototype.buildPositionCardHTML = function(position, mapper) {
            const symbol = position.symbol || 'UNKNOWN';
            const hierarchyId = `${symbol.toLowerCase()}-hierarchy`;

            return `
                <div class="position-summary-card">
                    <div class="position-summary-header">
                        <!-- TOP ROW - Primary Trading Info (8 elements) -->
                        <div class="summary-section symbol-section primary-section">
                            <div class="summary-section-label">SYMBOL</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">-</div>
                        </div>
                        <div class="summary-section primary-section">
                            <div class="summary-section-label">QUANTITY</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">-</div>
                        </div>
                        <div class="summary-section primary-section">
                            <div class="summary-section-label">AVG COST</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">per share</div>
                        </div>
                        <div class="summary-section primary-section">
                            <div class="summary-section-label">MARKET</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">bid × ask</div>
                        </div>
                        <div class="summary-section primary-section">
                            <div class="summary-section-label">LAST PRICE</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">current</div>
                        </div>
                        <div class="summary-section pnl-section primary-section">
                            <div class="summary-section-label">REALIZED P&L</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">session</div>
                        </div>
                        <div class="summary-section pnl-section primary-section">
                            <div class="summary-section-label">UNREALIZED P&L</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">current</div>
                        </div>
                        <div class="summary-section primary-section">
                            <div class="summary-section-label">P&L PER SHARE</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">-</div>
                        </div>

                        <!-- BOTTOM ROW - Secondary Details (8 elements) -->
                        <div class="summary-section secondary-section">
                            <div class="summary-section-label">TOTAL TRADED</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">shares</div>
                        </div>
                        <div class="summary-section secondary-section">
                            <div class="summary-section-label">COMMISSIONS</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">estimated</div>
                        </div>
                        <div class="summary-section secondary-section">
                            <div class="summary-section-label">SPREAD</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">bid-ask</div>
                        </div>
                        <div class="summary-section secondary-section">
                            <div class="summary-section-label">VOLUME</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">market</div>
                        </div>
                        <div class="summary-section secondary-section">
                            <div class="summary-section-label">SIGNAL LAT</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">to fill</div>
                        </div>
                        <div class="summary-section secondary-section">
                            <div class="summary-section-label">ORDER LAT</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">to fill</div>
                        </div>
                        <div class="summary-section secondary-section">
                            <div class="summary-section-label">MARKET VALUE</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">current</div>
                        </div>
                        <div class="summary-section secondary-section">
                            <div class="summary-section-label">OPENED</div>
                            <div class="summary-section-value">-</div>
                            <div class="summary-section-subtitle">time</div>
                        </div>
                    </div>

                    <!-- Trade Hierarchy Section -->
                    <div class="trade-hierarchy-section">
                        <div class="hierarchy-header" onclick="toggleHierarchy('${hierarchyId}')">
                            <span class="hierarchy-title">Trade Hierarchy (Parent: -)</span>
                            <button class="hierarchy-toggle">▼</button>
                        </div>

                        <div id="${hierarchyId}" class="hierarchy-content collapsed">
                            <!-- Will be populated by wireTradeHierarchy -->
                        </div>
                    </div>
                </div>
            `;
        };

        TESTRADEDataManager.prototype.wirePositionCard = function(card, position) {
            console.log(`🔌 Wiring position card with ${position.symbol} Redis data using field mapper`);

            // Initialize field mapper
            const mapper = new TESTRADEFieldMapper();

            // Get all value elements in the card (16 total: 8 primary + 8 secondary)
            const valueElements = card.querySelectorAll('.summary-section-value');
            const subtitleElements = card.querySelectorAll('.summary-section-subtitle');

            if (valueElements.length < 16) {
                console.warn(`❌ Expected 16 value elements, found ${valueElements.length}`);
                return;
            }

            console.log(`📊 Mapping ${mapper.positionCardMapping.length} fields from Redis payload to UI elements`);

            // Wire all elements using field mapping
            mapper.positionCardMapping.forEach(mapping => {
                const value = mapper.getFieldValue(position, mapping);
                valueElements[mapping.index].textContent = value;

                // Apply P&L color classes for financial fields
                if (mapping.type === 'pnl' || mapping.type === 'calculated_pnl') {
                    const numericValue = parseFloat(value.replace(/[$,]/g, ''));
                    valueElements[mapping.index].className = numericValue >= 0 ?
                        'summary-section-value pnl-positive' :
                        'summary-section-value pnl-negative';
                } else {
                    valueElements[mapping.index].className = 'summary-section-value';
                }

                console.log(`  📌 ${mapping.label}: ${mapping.field} = ${value}`);
            });

            // Wire subtitles
            if (subtitleElements[0]) {
                subtitleElements[0].textContent = (position.strategy || 'UNKNOWN').toUpperCase();
            }
            if (subtitleElements[1]) {
                const fillsCount = position.total_fills_count || 1;
                subtitleElements[1].textContent = `${fillsCount} fill${fillsCount !== 1 ? 's' : ''}`;
            }

            // Wire trade hierarchy with Redis order data
            this.wireTradeHierarchy(card, position);

            const derived = mapper.calculateDerivedFields(position);
            console.log(`✅ Wired ${position.symbol} card with ${Object.keys(derived).length} calculated fields`);
        };

        TESTRADEDataManager.prototype.wireTradeHierarchy = function(card, position) {
            console.log(`🔗 Wiring trade hierarchy for ${position.symbol}`);

            // Find hierarchy header and update title
            const hierarchyHeader = card.querySelector('.hierarchy-title');
            if (hierarchyHeader) {
                const parentTradeId = position.parent_trade_id || `TRD-${Date.now()}`;
                hierarchyHeader.textContent = `Trade Hierarchy (Parent: ${parentTradeId})`;
            }

            // Find all trade entries in the hierarchy
            const tradeEntries = card.querySelectorAll('.trade-entry');
            console.log(`📋 Found ${tradeEntries.length} trade entries to wire`);

            // Wire parent trade (first entry)
            if (tradeEntries[0]) {
                this.wireTradeEntry(tradeEntries[0], {
                    type: 'PARENT',
                    trade_id: position.parent_trade_id || `TRD-${Date.now()}`,
                    symbol: position.symbol,
                    action: 'OPEN_LONG',
                    status: 'FILLED',
                    quantity: position.quantity || 0,
                    price: position.average_price || 0,
                    timestamp: position.open_time || new Date().toISOString()
                });
            }

            // Wire child trades from orders array
            const orders = position.orders || [];
            orders.forEach((order, index) => {
                const tradeEntryIndex = index + 1; // +1 because parent is at index 0
                if (tradeEntries[tradeEntryIndex]) {
                    this.wireTradeEntry(tradeEntries[tradeEntryIndex], {
                        type: 'CHILD',
                        trade_id: `${position.parent_trade_id || 'TRD'}-${index + 1}`,
                        symbol: position.symbol,
                        action: order.action || 'ADD',
                        status: order.status || 'FILLED',
                        quantity: order.quantity || 0,
                        price: order.price || 0,
                        timestamp: order.timestamp || new Date().toISOString()
                    });
                }
            });

            console.log(`✅ Wired hierarchy for ${position.symbol} with ${orders.length} child orders`);
        };

        TESTRADEDataManager.prototype.wireTradeEntry = function(tradeEntry, tradeData) {
            // Update trade header elements
            const tradeId = tradeEntry.querySelector('.trade-id');
            const tradeSymbol = tradeEntry.querySelector('.trade-symbol');
            const tradeAction = tradeEntry.querySelector('.trade-action');
            const tradeStatus = tradeEntry.querySelector('.trade-status');

            if (tradeId) tradeId.textContent = tradeData.trade_id;
            if (tradeSymbol) tradeSymbol.textContent = tradeData.symbol;
            if (tradeAction) tradeAction.textContent = tradeData.action;
            if (tradeStatus) {
                tradeStatus.textContent = tradeData.status;
                tradeStatus.className = `trade-status ${this.getStatusClass(tradeData.status)}`;
            }

            // Update detail rows with Redis data
            const detailValues = tradeEntry.querySelectorAll('.detail-value');
            const timestamp = new Date(tradeData.timestamp).toLocaleTimeString();

            if (detailValues[0]) { // Requested
                detailValues[0].textContent = `${timestamp} | ${tradeData.quantity} shares @ ${this.formatCurrency(tradeData.price)} LMT | DAY`;
            }
            if (detailValues[1]) { // Filled
                detailValues[1].textContent = `${timestamp} | ${tradeData.quantity} shares @ ${this.formatCurrency(tradeData.price)} | Cost: ${this.formatCurrency(tradeData.quantity * tradeData.price)}`;
            }
            if (detailValues[4]) { // Master Trader
                detailValues[4].textContent = `${tradeData.symbol} ${tradeData.quantity} ${tradeData.price.toFixed(2)} 0.00 0.00 0.00 0.00 ${Math.floor(tradeData.quantity * tradeData.price)}`;
            }
        };

        TESTRADEDataManager.prototype.wirePositionCard = function(card, position) {
            console.log(`🔌 Wiring position card with ${position.symbol} data`);

            // Get all value elements in order (8 primary + 8 secondary = 16 total)
            const valueElements = card.querySelectorAll('.summary-section-value');
            const subtitleElements = card.querySelectorAll('.summary-section-subtitle');

            if (valueElements.length < 16) {
                console.warn(`❌ Expected 16 value elements, found ${valueElements.length}`);
                return;
            }

            // Calculate derived values
            const symbol = position.symbol || 'UNKNOWN';
            const quantity = position.quantity || 0;
            const avgPrice = position.average_price || 0;
            const currentPrice = position.current_price || avgPrice;
            const unrealizedPnl = position.unrealized_pnl || 0;
            const realizedPnl = position.realized_pnl_session || 0;
            const strategy = (position.strategy || 'UNKNOWN').toUpperCase();
            const marketValue = quantity * currentPrice;
            const pnlPerShare = currentPrice - avgPrice;
            const bidPrice = position.bid_price || currentPrice - 0.01;
            const askPrice = position.ask_price || currentPrice + 0.01;
            const spread = askPrice - bidPrice;
            const totalTraded = position.total_traded || quantity;
            const commissions = position.commissions || 1.00;
            const signalLatency = position.signal_latency || Math.floor(Math.random() * 200) + 50;
            const orderLatency = position.order_latency || Math.floor(Math.random() * 100) + 30;
            const volume = position.volume || Math.floor(Math.random() * 5000000) + 100000;
            const openTime = position.open_time || new Date().toLocaleTimeString();

            // Wire up PRIMARY ROW (0-7)
            valueElements[0].textContent = symbol;                                    // SYMBOL
            valueElements[1].textContent = this.formatNumber(quantity);              // QUANTITY
            valueElements[2].textContent = this.formatCurrency(avgPrice);            // AVG COST
            valueElements[3].textContent = `${this.formatCurrency(bidPrice)}×${this.formatCurrency(askPrice)}`; // MARKET
            valueElements[4].textContent = this.formatCurrency(currentPrice);        // LAST PRICE
            valueElements[5].textContent = this.formatCurrency(realizedPnl);         // REALIZED P&L
            valueElements[6].textContent = this.formatCurrency(unrealizedPnl);       // UNREALIZED P&L
            valueElements[7].textContent = this.formatCurrency(pnlPerShare);         // P&L PER SHARE

            // Wire up SECONDARY ROW (8-15)
            valueElements[8].textContent = this.formatNumber(totalTraded);           // TOTAL TRADED
            valueElements[9].textContent = this.formatCurrency(commissions);         // COMMISSIONS
            valueElements[10].textContent = this.formatCurrency(spread);             // SPREAD
            valueElements[11].textContent = this.formatVolume(volume);               // VOLUME
            valueElements[12].textContent = `${signalLatency}ms`;                    // SIGNAL LAT
            valueElements[13].textContent = `${orderLatency}ms`;                     // ORDER LAT
            valueElements[14].textContent = this.formatCurrency(marketValue);        // MARKET VALUE
            valueElements[15].textContent = openTime;                                // OPENED

            // Update P&L colors
            valueElements[5].className = realizedPnl >= 0 ? 'summary-section-value pnl-positive' : 'summary-section-value pnl-negative';
            valueElements[6].className = unrealizedPnl >= 0 ? 'summary-section-value pnl-positive' : 'summary-section-value pnl-negative';
            valueElements[7].className = pnlPerShare >= 0 ? 'summary-section-value pnl-positive' : 'summary-section-value pnl-negative';

            // Update strategy subtitle
            if (subtitleElements[0]) {
                subtitleElements[0].textContent = strategy;
            }

            // Wire up trade hierarchy if it exists
            this.wireTradeHierarchy(card, position);

            // Show the card
            card.style.display = 'block';

            console.log(`✅ Wired ${symbol} position card with Redis data`);
        };

        TESTRADEDataManager.prototype.createTradeHierarchy = function(position) {
            const symbol = position.symbol || 'UNKNOWN';
            const hierarchyId = `${symbol.toLowerCase()}-hierarchy`;
            const orders = position.orders || [];
            const parentTradeId = position.parent_trade_id || `TRD-${Date.now()}`;

            let hierarchyHtml = `
                <div class="trade-hierarchy-section">
                    <div class="hierarchy-header" onclick="toggleHierarchy('${hierarchyId}')">
                        <span class="hierarchy-title">Trade Hierarchy (Parent: ${parentTradeId})</span>
                        <button class="hierarchy-toggle">▼</button>
                    </div>

                    <div id="${hierarchyId}" class="hierarchy-content collapsed">
            `;

            // Parent Trade (Foundation)
            hierarchyHtml += this.createTradeEntry({
                type: 'PARENT',
                trade_id: parentTradeId,
                symbol: symbol,
                action: 'OPEN_LONG',
                status: 'FILLED',
                quantity: position.quantity || 0,
                price: position.average_price || 0,
                timestamp: position.open_time || new Date().toISOString(),
                strategy: position.strategy || 'UNKNOWN'
            });

            // Child Trades from orders
            orders.forEach((order, index) => {
                hierarchyHtml += this.createTradeEntry({
                    type: 'CHILD',
                    trade_id: `${parentTradeId}-${index + 1}`,
                    symbol: symbol,
                    action: order.action || 'ADD',
                    status: order.status || 'FILLED',
                    quantity: order.quantity || 0,
                    price: order.price || 0,
                    timestamp: order.timestamp || new Date().toISOString(),
                    strategy: position.strategy || 'UNKNOWN'
                });
            });

            hierarchyHtml += `
                    </div>
                </div>
            `;

            return hierarchyHtml;
        };

        TESTRADEDataManager.prototype.createTradeEntry = function(trade) {
            const statusClass = this.getStatusClass(trade.status);
            const actionColor = this.getActionColor(trade.action);
            const timestamp = new Date(trade.timestamp).toLocaleTimeString();

            return `
                <div class="trade-entry ${trade.type.toLowerCase()}-trade">
                    <div class="trade-header">
                        <span class="trade-type">${trade.type}</span>
                        <span class="trade-id">${trade.trade_id}</span>
                        <span class="trade-symbol">${trade.symbol}</span>
                        <span class="trade-action" style="color: ${actionColor};">${trade.action}</span>
                        <span class="trade-status ${statusClass}">${trade.status}</span>
                    </div>
                    <div class="trade-details">
                        <div class="detail-row">
                            <span class="detail-label">Requested:</span>
                            <span class="detail-value">${timestamp} | ${trade.quantity} shares @ ${this.formatCurrency(trade.price)} LMT | DAY</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Filled:</span>
                            <span class="detail-value">${timestamp} | ${trade.quantity} shares @ ${this.formatCurrency(trade.price)} | Cost: ${this.formatCurrency(trade.quantity * trade.price)}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Performance:</span>
                            <span class="detail-value">Signal: ${Math.floor(Math.random() * 200) + 50}ms | Order: ${Math.floor(Math.random() * 100) + 30}ms | Fill: ${(Math.random() * 3 + 1).toFixed(1)}s</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Commission:</span>
                            <span class="detail-value">$1.00 | Net: ${this.formatCurrency(trade.quantity * trade.price + 1)} | Broker: LSPD</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Master Trader:</span>
                            <span class="detail-value">${trade.symbol} ${trade.quantity} ${trade.price.toFixed(2)} 0.00 0.00 0.00 0.00 ${Math.floor(trade.quantity * trade.price)}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">OCR Confidence:</span>
                            <span class="detail-value">${(Math.random() * 5 + 95).toFixed(1)}% | Strategy: ${trade.strategy} | Source: LIVE_TRADING</span>
                        </div>
                    </div>
                </div>
            `;
        };

        TESTRADEDataManager.prototype.getStatusClass = function(status) {
            switch (status) {
                case 'FILLED': return 'status-filled';
                case 'PENDING': return 'status-pending';
                case 'CANCELLED': return 'status-cancelled';
                default: return 'status-filled';
            }
        };

        TESTRADEDataManager.prototype.getActionColor = function(action) {
            switch (action) {
                case 'BUY':
                case 'OPEN_LONG':
                case 'ADD': return 'var(--accent-primary)';
                case 'SELL':
                case 'CLOSE':
                case 'REDUCE': return 'var(--accent-danger)';
                default: return 'var(--accent-warning)';
            }
        };

        TESTRADEDataManager.prototype.formatCurrency = function(value) {
            if (typeof value !== 'number') return '$0.00';
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2
            }).format(value);
        };

        TESTRADEDataManager.prototype.formatNumber = function(value) {
            if (typeof value !== 'number') return '0';
            return new Intl.NumberFormat('en-US').format(value);
        };

        TESTRADEDataManager.prototype.formatVolume = function(volume) {
            if (!volume || volume === 0) return '-';
            if (volume >= 1000000) return `${(volume / 1000000).toFixed(1)}M`;
            if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
            return volume.toLocaleString();
        };

        // Toggle individual position details
        function togglePositionDetails(row) {
            const detailsRow = row.nextElementSibling;
            const isExpanded = detailsRow.classList.contains('show');
            
            if (isExpanded) {
                detailsRow.classList.remove('show');
                row.classList.remove('flipped');
            } else {
                detailsRow.classList.add('show');
                row.classList.add('flipped');
            }
        }

        // Toggle history panel
        function toggleHistory() {
            const content = document.getElementById('historyContent');
            const btn = document.getElementById('historyToggle');

            uiState.historyExpanded = !uiState.historyExpanded;

            if (uiState.historyExpanded) {
                content.classList.add('expanded');
                btn.classList.add('expanded');
                btn.textContent = '+';
            } else {
                content.classList.remove('expanded');
                btn.classList.remove('expanded');
                btn.textContent = '+';
            }
        }

        // Toggle trade hierarchy section
        function toggleHierarchy(hierarchyId) {
            const content = document.getElementById(hierarchyId);
            const button = content.previousElementSibling.querySelector('.hierarchy-toggle');

            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                button.textContent = '▼';
            } else {
                content.classList.add('collapsed');
                button.textContent = '▶';
            }
        }

        // Switch history tabs
        function switchHistoryTab(tabName) {
            // Update active tab
            document.querySelectorAll('.history-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            uiState.activeHistoryTab = tabName;
            
            // Filter data based on tab
            filterHistoryData(tabName);
        }

        // Filter history data
        function filterHistoryData(filter) {
            const tableBody = document.getElementById('historyTableBody');
            const rows = tableBody.querySelectorAll('tr');
            
            rows.forEach(row => {
                const pnlCell = row.querySelector('.pnl-positive, .pnl-negative');
                const shouldShow = shouldShowRow(row, filter, pnlCell);
                row.style.display = shouldShow ? '' : 'none';
            });
        }

        function shouldShowRow(row, filter, pnlCell) {
            switch(filter) {
                case 'winners':
                    return pnlCell && pnlCell.classList.contains('pnl-positive');
                case 'losers':
                    return pnlCell && pnlCell.classList.contains('pnl-negative');
                case 'today':
                    return true; // All current data is today
                case 'all':
                    return true;
                default:
                    return true;
            }
        }

        // Utility functions
        function expandAllPositions() {
            document.querySelectorAll('.position-row').forEach(row => {
                const detailsRow = row.nextElementSibling;
                if (!detailsRow.classList.contains('show')) {
                    togglePositionDetails(row);
                }
            });
        }

        function collapseAllPositions() {
            document.querySelectorAll('.position-row').forEach(row => {
                const detailsRow = row.nextElementSibling;
                if (detailsRow.classList.contains('show')) {
                    togglePositionDetails(row);
                }
            });
        }

        function refreshData() {
            console.log('Refreshing trade data...');
            // Your WebSocket refresh logic here
        }

        function exportData() {
            console.log('Exporting trade data to CSV...');
            // Your CSV export logic here
        }

        // Real Data Integration with TESTRADE Payloads
        let tradeData = {
            positions: [],
            history: [],
            priceData: {},
            lastUpdate: null
        };

        // Handle WebSocket messages from your TESTRADE backend
        function handleWebSocketMessage(data) {
            console.log('WebSocket message received:', data.type, data);

            switch(data.type) {
                case 'trades_update':
                    if (data.payload && data.payload.trades) {
                        updateOpenPositionsWithRealData(data.payload.trades);
                        updateMarketDataAvailability(data.payload);
                    }
                    break;
                    
                case 'enriched_position_updates':
                    handleEnrichedPositionUpdate(data);
                    break;
                    
                case 'price_quote_update':
                    handlePriceQuoteUpdate(data);
                    break;
                    
                case 'price_trade_update':
                    handlePriceTradeUpdate(data);
                    break;
                    
                case 'order_fill_update':
                    handleOrderFillUpdate(data);
                    break;
                    
                case 'trade_history_update':
                    if (data.trades) {
                        updateHistoricalTradesWithRealData(data.trades);
                    }
                    break;
                    
                case 'position_summary_update':
                    handlePositionSummaryUpdate(data);
                    break;
            }
        }

        // Utility functions for real data
        function formatCurrency(value) {
            if (value === null || value === undefined || isNaN(value)) return '-';
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(value);
        }

        function formatVolume(volume) {
            if (!volume || volume === 0) return '-';
            if (volume >= 1000000) return `${(volume / 1000000).toFixed(1)}M`;
            if (volume >= 1000) return `${(volume / 1000).toFixed(1)}K`;
            return volume.toLocaleString();
        }

        function formatNumber(num) {
            if (!num || num === 0) return '0';
            return num.toLocaleString();
        }

        function getPnLClass(value) {
            if (!value || value === 0) return '';
            return value > 0 ? 'pnl-positive' : 'pnl-negative';
        }

        // Update positions table with real TESTRADE data
        function updateOpenPositionsWithRealData(trades) {
            tradeData.positions = trades;
            
            // Group trades by symbol to create position summaries
            const positionGroups = groupTradesBySymbol(trades);
            
            // Update summary cards
            updatePositionSummaryCards(positionGroups);
            
            // Update detailed table
            updatePositionTable(trades);
            
            console.log(`Updated ${trades.length} positions in table`);
        }

        function groupTradesBySymbol(trades) {
            const groups = {};
            
            trades.forEach(trade => {
                const symbol = trade.symbol;
                if (!groups[symbol]) {
                    groups[symbol] = {
                        symbol: symbol,
                        totalQuantity: 0,
                        totalValue: 0,
                        totalCost: 0,
                        totalRealizedPnL: 0,
                        trades: [],
                        marketData: {}
                    };
                }
                
                groups[symbol].totalQuantity += trade.quantity || 0;
                groups[symbol].totalRealizedPnL += trade.realized_pnl_session || 0;
                groups[symbol].trades.push(trade);
                
                // Use latest market data
                if (trade.bid_price) {
                    groups[symbol].marketData = {
                        bid_price: trade.bid_price,
                        ask_price: trade.ask_price,
                        last_price: trade.last_price,
                        total_volume: trade.total_volume
                    };
                }
            });
            
            return groups;
        }

        function updatePositionSummaryCards(positionGroups) {
            // This would dynamically create summary cards for each position
            // For now, the HTML has static cards, but this function could
            // generate them dynamically based on real data
            
            Object.values(positionGroups).forEach(position => {
                updateSummaryCardForSymbol(position);
            });
        }

        function updateSummaryCardForSymbol(position) {
            // Find and update the summary card for this symbol
            // This would update the DOM elements with real data
            console.log(`Updating summary for ${position.symbol}:`, position);
        }

        function updatePositionTable(trades) {
            const tableBody = document.querySelector('.positions-table tbody');
            if (!tableBody) return;

            // Sort by hierarchy (parents first, then children)
            const sortedTrades = sortTradesByHierarchy(trades);
            
            let tableHTML = '';

            sortedTrades.forEach(trade => {
                const {
                    symbol,
                    quantity,
                    average_price,
                    realized_pnl_session,
                    bid_price,
                    ask_price,
                    last_price,
                    total_volume,
                    total_fills_count,
                    position_opened_timestamp,
                    is_parent,
                    is_child,
                    strategy,
                    market_data_available
                } = trade;

                const openTime = position_opened_timestamp ? 
                    new Date(position_opened_timestamp * 1000).toLocaleTimeString('en-US', {
                        hour12: false,
                        hour: '2-digit',
                        minute: '2-digit'
                    }) : '-';

                // Market data display
                const marketDataHTML = market_data_available ? `
                    <div>B: ${formatCurrency(bid_price)}</div>
                    <div>A: ${formatCurrency(ask_price)}</div>
                    <div>L: ${formatCurrency(last_price)}</div>
                ` : 'See summary above';

                const rowClass = is_child ? 'child-row' : 'parent-row';
                const quantityDisplay = is_child ? 
                    `<span class="child-quantity">${quantity > 0 ? '+' : ''}${quantity}</span>` :
                    `${quantity > 0 ? '+' : ''}${quantity}`;
                const symbolDisplay = is_child ? 
                    `<span class="child-symbol">${symbol}</span>` : 
                    `<span style="color: var(--accent-primary);">${symbol}</span>`;

                tableHTML += `
                    <tr class="position-row ${rowClass}" onclick="togglePositionDetails(this)">
                        <td style="font-weight: ${is_child ? 'normal' : 'bold'};">${quantityDisplay}</td>
                        <td style="font-weight: ${is_child ? 'normal' : 'bold'};">${symbolDisplay}</td>
                        <td>${formatCurrency(average_price)}</td>
                        <td class="${getPnLClass(realized_pnl_session)}">${formatCurrency(realized_pnl_session)}</td>
                        <td class="market-data" style="font-size: ${market_data_available ? '7px' : '9px'}; color: ${market_data_available ? 'inherit' : 'var(--text-muted)'};">${marketDataHTML}</td>
                        <td>${formatVolume(total_volume)}</td>
                        <td>${total_fills_count || 0}</td>
                        <td style="font-size: 7px;">${openTime}</td>
                        <td>${is_parent || !is_child ? '📊' : '-'}</td>
                    </tr>
                    <tr class="position-details">
                        <td colspan="9">
                            <div class="details-grid">
                                <div class="detail-item">
                                    <div class="detail-label">Strategy</div>
                                    <div class="detail-value">${(strategy || 'UNKNOWN').toUpperCase()}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Realized P&L</div>
                                    <div class="detail-value ${getPnLClass(realized_pnl_session)}">${formatCurrency(realized_pnl_session)}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Market Value</div>
                                    <div class="detail-value">${formatCurrency((last_price || average_price) * Math.abs(quantity))}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Cost Basis</div>
                                    <div class="detail-value">${formatCurrency(average_price * Math.abs(quantity))}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Fills Count</div>
                                    <div class="detail-value">${total_fills_count || 0}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Data Status</div>
                                    <div class="detail-value" style="color: ${market_data_available ? 'var(--accent-primary)' : 'var(--accent-danger)'};">
                                        ${market_data_available ? 'LIVE' : 'STALE'}
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                `;
            });

            tableBody.innerHTML = tableHTML;
        }

        function sortTradesByHierarchy(trades) {
            const parents = trades.filter(t => t.is_parent || (!t.is_child && !t.parent_trade_id));
            const children = trades.filter(t => t.is_child || t.parent_trade_id);
            
            const sorted = [];
            parents.forEach(parent => {
                sorted.push(parent);
                const parentChildren = children.filter(child => 
                    child.parent_trade_id === parent.trade_id || 
                    child.parent_trade_id === parent.symbol
                );
                sorted.push(...parentChildren);
            });
            
            // Add orphaned children
            children.forEach(child => {
                const hasParent = parents.some(parent => 
                    parent.trade_id === child.parent_trade_id || 
                    parent.symbol === child.parent_trade_id
                );
                if (!hasParent) {
                    sorted.push(child);
                }
            });
            
            return sorted;
        }

        // Handle price updates
        function handlePriceQuoteUpdate(data) {
            const { symbol, bid, ask, bid_size, ask_size, timestamp } = data;
            
            if (!tradeData.priceData[symbol]) {
                tradeData.priceData[symbol] = {};
            }
            
            tradeData.priceData[symbol] = {
                ...tradeData.priceData[symbol],
                bid_price: bid,
                ask_price: ask,
                bid_size: bid_size,
                ask_size: ask_size,
                quote_timestamp: timestamp
            };
            
            updatePositionMarketData(symbol);
            updateSummaryMarketData(symbol);
        }

        function handlePriceTradeUpdate(data) {
            const { symbol, last, size, timestamp } = data;
            
            if (!tradeData.priceData[symbol]) {
                tradeData.priceData[symbol] = {};
            }
            
            tradeData.priceData[symbol] = {
                ...tradeData.priceData[symbol],
                last_price: last,
                last_size: size,
                trade_timestamp: timestamp
            };
            
            updatePositionMarketData(symbol);
            updateSummaryMarketData(symbol);
        }

        // Update market data in position table
        function updatePositionMarketData(symbol) {
            const priceData = tradeData.priceData[symbol];
            if (!priceData) return;

            // Find and update the specific symbol's market data in the table
            const rows = document.querySelectorAll('.position-row');
            rows.forEach(row => {
                const symbolCell = row.children[1];
                if (symbolCell && symbolCell.textContent.includes(symbol)) {
                    const marketDataCell = row.children[4];
                    if (marketDataCell && marketDataCell.classList.contains('market-data')) {
                        marketDataCell.innerHTML = `
                            <div>B: ${formatCurrency(priceData.bid_price)}</div>
                            <div>A: ${formatCurrency(priceData.ask_price)}</div>
                            <div>L: ${formatCurrency(priceData.last_price)}</div>
                        `;
                        
                        // Add flash effect for price updates
                        marketDataCell.classList.add('fade-in');
                        setTimeout(() => marketDataCell.classList.remove('fade-in'), 300);
                    }
                }
            });
        }

        // Update market data in summary cards
        function updateSummaryMarketData(symbol) {
            const priceData = tradeData.priceData[symbol];
            if (!priceData) return;

            // Find and update summary cards
            const summaryCards = document.querySelectorAll('.position-summary-card');
            summaryCards.forEach(card => {
                const symbolElement = card.querySelector('.summary-section-value');
                if (symbolElement && symbolElement.textContent === symbol) {
                    const marketDataGrid = card.querySelector('.market-data-grid');
                    if (marketDataGrid) {
                        marketDataGrid.innerHTML = `
                            <div class="market-data-item">
                                <span style="color: var(--accent-danger);">Bid:</span>
                                <strong>${formatCurrency(priceData.bid_price)}</strong>
                            </div>
                            <div class="market-data-item">
                                <span style="color: var(--accent-warning);">Ask:</span>
                                <strong>${formatCurrency(priceData.ask_price)}</strong>
                            </div>
                            <div class="market-data-item">
                                <span style="color: var(--accent-primary);">Last:</span>
                                <strong>${formatCurrency(priceData.last_price)}</strong>
                            </div>
                        `;
                        
                        // Add flash effect
                        marketDataGrid.classList.add('fade-in');
                        setTimeout(() => marketDataGrid.classList.remove('fade-in'), 300);
                    }
                }
            });
        }

        // Handle historical trades
        function updateHistoricalTradesWithRealData(trades) {
            tradeData.history = trades;
            
            const tableBody = document.getElementById('historyTableBody');
            if (!tableBody) return;

            let tableHTML = '';
            let stats = {
                total: trades.length,
                winners: 0,
                losers: 0,
                totalPnL: 0,
                totalWinnerPnL: 0,
                totalLoserPnL: 0
            };

            trades.forEach(trade => {
                const {
                    symbol,
                    side,
                    quantity,
                    price,
                    timestamp,
                    trade_id,
                    parent_trade_id,
                    order_type,
                    is_parent,
                    is_child,
                    description,
                    strategy
                } = trade;

                const time = new Date(timestamp * 1000).toLocaleTimeString('en-US', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit'
                });

                const pnl = trade.realized_pnl || 0;
                const pnlPercent = ((pnl / (price * Math.abs(quantity))) * 100).toFixed(1);
                
                // Update stats
                stats.totalPnL += pnl;
                if (pnl > 0) {
                    stats.winners++;
                    stats.totalWinnerPnL += pnl;
                } else if (pnl < 0) {
                    stats.losers++;
                    stats.totalLoserPnL += pnl;
                }

                const tradeTypeClass = is_parent ? 'parent' : is_child ? 'child' : 'close';
                const tradeTypeLabel = is_parent ? 'P' : is_child ? 'C' : 'X';
                const rowClass = is_child ? 'child-row' : '';
                const symbolClass = is_child ? 'child-symbol' : '';

                tableHTML += `
                    <tr class="${rowClass}">
                        <td style="font-size: 7px;">${time}</td>
                        <td style="font-weight: bold;" class="${symbolClass}">${symbol}</td>
                        <td><span class="trade-type ${tradeTypeClass}">${tradeTypeLabel}</span></td>
                        <td style="color: ${side === 'BUY' ? 'var(--accent-primary)' : 'var(--accent-danger)'};">${side}</td>
                        <td>${Math.abs(quantity)}</td>
                        <td>${formatCurrency(price)}</td>
                        <td class="${getPnLClass(pnl)}">${formatCurrency(pnl)}</td>
                        <td class="${getPnLClass(pnl)}">${pnl !== 0 ? (pnl > 0 ? '+' : '') + pnlPercent + '%' : '-'}</td>
                        <td style="font-size: 7px;">${(strategy || 'UNKNOWN').toUpperCase()}</td>
                        <td style="font-size: 7px;">-</td>
                        <td style="font-family: monospace; font-size: 6px;">${trade_id ? trade_id.substr(-5) : '-'}</td>
                    </tr>
                `;
            });

            tableBody.innerHTML = tableHTML;
            
            // Update stats row
            updateHistoryStats(stats);
            
            console.log(`Updated ${trades.length} historical trades`);
        }

        // Update history statistics
        function updateHistoryStats(stats) {
            const statsElements = document.querySelectorAll('.stats-row .stat-value');
            if (statsElements.length >= 6) {
                const winRate = stats.total > 0 ? (stats.winners / stats.total * 100).toFixed(1) : '0.0';
                const avgWinner = stats.winners > 0 ? stats.totalWinnerPnL / stats.winners : 0;
                const avgLoser = stats.losers > 0 ? stats.totalLoserPnL / stats.losers : 0;

                statsElements[0].textContent = stats.total;
                statsElements[1].textContent = stats.winners;
                statsElements[1].className = 'stat-value pnl-positive';
                statsElements[2].textContent = stats.losers;
                statsElements[2].className = 'stat-value pnl-negative';
                statsElements[3].textContent = winRate + '%';
                statsElements[3].className = `stat-value ${winRate >= 50 ? 'pnl-positive' : 'pnl-negative'}`;
                statsElements[4].textContent = formatCurrency(avgWinner);
                statsElements[4].className = 'stat-value pnl-positive';
                statsElements[5].textContent = formatCurrency(Math.abs(avgLoser));
                statsElements[5].className = 'stat-value pnl-negative';
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 'h':
                        e.preventDefault();
                        toggleHistory();
                        break;
                    case 'e':
                        e.preventDefault();
                        expandAllPositions();
                        break;
                    case 'c':
                        e.preventDefault();
                        collapseAllPositions();
                        break;
                    case 'r':
                        e.preventDefault();
                        refreshData();
                        break;
                }
            }
        });

        // Auto-refresh simulation (replace with your real WebSocket updates)
        setInterval(() => {
            // Simulate live P&L updates
            const pnlElements = document.querySelectorAll('.pnl-positive, .pnl-negative');
            pnlElements.forEach(element => {
                if (Math.random() > 0.95) { // 5% chance to update
                    element.classList.add('fade-in');
                    setTimeout(() => element.classList.remove('fade-in'), 300);
                }
            });
        }, 2000);

        // Initialize TESTRADE System on page load ⚡
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 TESTRADE Enhanced Trading Interface Loading...');
            console.log('⚡ Initializing real-time data connections...');

            // Initialize TESTRADE Data Manager
            console.log('🔌 Starting TESTRADE Data Manager...');
            testradeManager = new TESTRADEDataManager();

            // Set initial UI state
            const positionsContent = document.getElementById('positionsContent');
            const historyContent = document.getElementById('historyContent');

            if (positionsContent) {
                positionsContent.style.maxHeight = '0px';
            }

            if (historyContent) {
                historyContent.style.maxHeight = '0px';
            }

            // Auto-expand positions panel to show live data
            setTimeout(() => {
                togglePositions();
                console.log('✅ TESTRADE Interface Ready!');
                console.log('🎯 Keyboard shortcuts: Ctrl+H (history), Ctrl+E (expand), Ctrl+C (collapse)');
            }, 500);

            // Show connection status after a delay
            setTimeout(() => {
                showConnectionStatus();
            }, 2000);

            // DEMO: Inject sample TESTRADE data for testing (remove when real backend is connected)
            setTimeout(() => {
                if (testradeManager && !testradeManager.isConnected) {
                    console.log('🧪 Injecting sample TESTRADE data for demo...');
                    injectSampleTESTRADEData();
                }
            }, 3000);
        });

        // Connection Status Helper
        function showConnectionStatus() {
            const statusDiv = document.createElement('div');
            statusDiv.style.cssText = `
                position: fixed; bottom: 20px; right: 20px; z-index: 9999;
                background: rgba(0, 0, 0, 0.8); color: var(--accent-primary);
                padding: 12px 20px; border-radius: 8px; border: 1px solid var(--accent-primary);
                font-family: 'JetBrains Mono', monospace; font-size: 11px;
                backdrop-filter: blur(10px);
            `;

            if (testradeManager && testradeManager.isConnected) {
                statusDiv.innerHTML = '⚡ TESTRADE CONNECTED';
                statusDiv.style.color = 'var(--accent-primary)';
                statusDiv.style.borderColor = 'var(--accent-primary)';
            } else {
                statusDiv.innerHTML = '🔌 TESTRADE CONNECTING...';
                statusDiv.style.color = 'var(--accent-warning)';
                statusDiv.style.borderColor = 'var(--accent-warning)';
            }

            document.body.appendChild(statusDiv);

            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.parentNode.removeChild(statusDiv);
                }
            }, 5000);
        }

        // DEMO: Sample TESTRADE Data Injection (for testing without backend)
        function injectSampleTESTRADEData() {
            if (!testradeManager) return;

            console.log('🧪 Injecting sample TESTRADE positions...');

            // Sample position data in EXACT TESTRADE Redis payload format (from payloads.md)
            const samplePositions = [
                {
                    // Core position fields
                    symbol: 'RNAZ',
                    quantity: 600.0,
                    average_price: 7.37,
                    realized_pnl_session: 0.00,
                    is_open: true,
                    strategy: 'momentum',
                    last_update_timestamp: Date.now() / 1000,

                    // Trade hierarchy
                    parent_trade_id: 'TRD-12301',
                    is_parent: false,
                    is_child: true,
                    trade_id: 'TRD-12301-CHILD',

                    // Timing & fills
                    position_opened_timestamp: (Date.now() - 3600000) / 1000, // 1 hour ago
                    last_fill_timestamp: Date.now() / 1000,
                    total_fills_count: 3,
                    session_buy_quantity: 900.0,
                    session_sell_quantity: 300.0,
                    session_buy_value: 6633.0,
                    session_sell_value: 2775.0,

                    // Market data
                    bid_price: 9.24,
                    ask_price: 9.26,
                    last_price: 9.25,
                    total_volume: 2500000,
                    market_data_available: true,
                    hierarchy_data_available: true,

                    // Performance metrics
                    signal_to_fill_latency_ms: 142.5,
                    order_to_fill_latency_ms: 68.2,

                    // Calculated unrealized P&L
                    unrealized_pnl: (9.25 - 7.37) * 600.0, // $1,128.00

                    orders: [
                        { action: 'ADD', quantity: 300, price: 7.40, status: 'FILLED', timestamp: new Date().toISOString() },
                        { action: 'ADD', quantity: 100, price: 7.35, status: 'FILLED', timestamp: new Date().toISOString() },
                        { action: 'REDUCE', quantity: 150, price: 9.20, status: 'PENDING', timestamp: new Date().toISOString() }
                    ]
                },
                {
                    // Core position fields
                    symbol: 'YIBO',
                    quantity: 14100.0,
                    average_price: 1.31,
                    realized_pnl_session: 0.00,
                    is_open: true,
                    strategy: 'scalping',
                    last_update_timestamp: Date.now() / 1000,

                    // Trade hierarchy
                    parent_trade_id: 'TRD-12401',
                    is_parent: true,
                    is_child: false,
                    trade_id: 'TRD-12401',

                    // Timing & fills
                    position_opened_timestamp: (Date.now() - 1800000) / 1000, // 30 min ago
                    last_fill_timestamp: Date.now() / 1000,
                    total_fills_count: 2,
                    session_buy_quantity: 14100.0,
                    session_sell_quantity: 0.0,
                    session_buy_value: 18471.0,
                    session_sell_value: 0.0,

                    // Market data
                    bid_price: 1.32,
                    ask_price: 1.34,
                    last_price: 1.33,
                    total_volume: 8500000,
                    market_data_available: true,
                    hierarchy_data_available: true,

                    // Performance metrics
                    signal_to_fill_latency_ms: 89.3,
                    order_to_fill_latency_ms: 42.1,

                    // Calculated unrealized P&L
                    unrealized_pnl: (1.33 - 1.31) * 14100.0, // $282.00

                    orders: [
                        { action: 'ADD', quantity: 4100, price: 1.44, status: 'FILLED', timestamp: new Date().toISOString() },
                        { action: 'REDUCE', quantity: 5000, price: 1.35, status: 'PENDING', timestamp: new Date().toISOString() }
                    ]
                }
            ];

            // Inject the sample data
            samplePositions.forEach(position => {
                testradeManager.positions.set(position.symbol, position);
            });

            // Update the display
            testradeManager.updatePositionsDisplay();

            console.log('✅ Sample TESTRADE data injected - 3 positions created dynamically!');
        }
    </script>
</body>
</html>
                                