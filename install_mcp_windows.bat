@echo off
echo Installing MCP 1.10.1 and dependencies for Windows...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

echo Python found:
python --version
echo.

REM Install MCP 1.10.1
echo Installing MCP 1.10.1...
python -m pip install mcp==1.10.1
if errorlevel 1 (
    echo ERROR: Failed to install MCP
    pause
    exit /b 1
)

REM Install Redis
echo.
echo Installing Redis client...
python -m pip install redis>=4.0.0
if errorlevel 1 (
    echo ERROR: Failed to install Redis
    pause
    exit /b 1
)

REM Install other dependencies
echo.
echo Installing other dependencies...
python -m pip install pydantic>=2.0.0 httpx>=0.24.0 typing-extensions>=4.0.0

REM Test the installation
echo.
echo Testing MCP installation...
python -c "from mcp.server import FastMCP; print('MCP 1.10.1 imported successfully!')"
if errorlevel 1 (
    echo ERROR: MCP import test failed
    pause
    exit /b 1
)

echo.
echo Testing Redis installation...
python -c "import redis; print('Redis imported successfully!')"
if errorlevel 1 (
    echo ERROR: Redis import test failed
    pause
    exit /b 1
)

echo.
echo Installation complete!
echo.
echo You can now run the MCP server with:
echo   python intellisense\mcp\server_runner.py
echo.
pause