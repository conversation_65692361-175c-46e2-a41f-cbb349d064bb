# Time Synchronization System

## Overview

The Time Synchronization System measures latency between OCR-derived prices and matching market prices to provide insights into the timing relationship between OCR data capture and live market data. This enables optimization of OCR processing timing and market data synchronization.

## Architecture

### Core Components

#### 1. **time_sync.py Module**
- **Purpose**: Central coordination of time synchronization measurements
- **Key Functions**:
  - `initialize_time_sync()`: Initialize with price provider and benchmarker
  - `on_trader_price_update()`: Main entry point for latency measurement
  - `match_trader_price_and_find_delay()`: Core price matching and offset calculation

#### 2. **Enhanced RollingPriceDeque**
- **Purpose**: Advanced price matching capabilities for time synchronization
- **New Methods**:
  - `find_timestamp_for_price()`: Find timestamp within price tolerance
  - `find_closest_timestamp_for_price()`: Fallback closest price matching
  - `get_price_at_time()`: Find price at specific timestamp
  - `get_latest_price()`: Get most recent price

#### 3. **Integration Points**
- **PriceRepository**: Initializes time_sync module
- **OCRDataConditioningService**: Calls time_sync for each processed symbol
- **PerformanceBenchmarker**: Captures latency metrics

## Functionality

### Latency Measurement Process

#### 1. **OCR Data Processing**
```python
# In OCRDataConditioningService
for symbol, data in cleaned_data_snapshots.items():
    # Calculate trader live price from OCR data
    cost_basis = data.get('cost_basis', 0.0)
    pnl_per_share = data.get('pnl_per_share', 0.0)
    trader_live_price = cost_basis + pnl_per_share
    
    # Measure latency
    time_sync.on_trader_price_update(
        symbol=symbol,
        trader_live_price=trader_live_price,
        ocr_frame_timestamp=ocr_parsed_data.frame_timestamp,
        price_tolerance=0.01
    )
```

#### 2. **Price Matching Logic**
```python
# Try to find matching price within tolerance, preferably after OCR timestamp
market_timestamp = rpd.find_timestamp_for_price(
    target_price=trader_price,
    price_tolerance=price_tolerance,
    after_timestamp=ocr_frame_timestamp
)

# If no match after OCR, search all timestamps
if market_timestamp is None:
    market_timestamp = rpd.find_timestamp_for_price(
        target_price=trader_price,
        price_tolerance=price_tolerance,
        after_timestamp=None
    )
```

#### 3. **Latency Calculation**
```python
# Calculate offset: market_event_timestamp - ocr_frame_timestamp
offset_sec = market_timestamp - ocr_frame_timestamp
offset_ms = offset_sec * 1000.0  # Convert to milliseconds
```

### Price Matching Strategies

#### **Tolerance-Based Matching**
- **Primary Method**: Find prices within `±price_tolerance` of target price
- **Default Tolerance**: 0.01 (1 cent for most stocks)
- **Configurable**: Can be adjusted per symbol or globally

#### **Temporal Preference**
- **Preferred**: Market data timestamps after OCR frame timestamp
- **Fallback**: Market data timestamps before OCR frame timestamp
- **Rationale**: Prefer measuring forward latency (OCR → Market) over backward latency

#### **Strict Matching**
- **No Closest-Price Fallback**: Only exact tolerance matches are used
- **Benefit**: Ensures accurate latency measurements without false positives
- **Trade-off**: May miss some potential matches with loose price correlation

## Performance Metrics

### Primary Metric: `latency.ocr_to_matched_market_price_ms`

#### **Metric Properties**
- **Type**: Float (milliseconds)
- **Range**: Can be positive, negative, or zero
- **Frequency**: Per OCR symbol processed

#### **Context Data**
```python
context = {
    'symbol': 'AAPL',
    'trader_price': 150.25,
    'ocr_frame_timestamp': 1748670654.569,
    'price_tolerance': 0.01
}
```

#### **Interpretation**
- **Positive Latency**: Market data timestamp > OCR timestamp (normal case)
- **Negative Latency**: Market data timestamp < OCR timestamp (OCR processing delay)
- **Zero Latency**: Exact timestamp match (rare)

### Statistical Analysis

#### **Universal Offset List**
- **Purpose**: Store all latency measurements for trend analysis
- **Size Limit**: Configurable maximum entries to prevent memory issues
- **Content**: All offsets (positive, negative, zero) when performance tracking enabled

#### **Performance Tracking Integration**
```python
# Captured automatically when benchmarker available
benchmarker.capture_metric(
    "latency.ocr_to_matched_market_price_ms", 
    offset_ms, 
    context=context
)
```

## Configuration

### Initialization

#### **In PriceRepository**
```python
# Initialize time synchronization module
try:
    from utils import time_sync
    benchmarker = getattr(config_service, '_benchmarker', None)
    time_sync.initialize_time_sync(self, benchmarker)
    logger.info("Time synchronization module initialized.")
except Exception as e:
    logger.warning(f"Failed to initialize time synchronization: {e}")
```

### Parameters

#### **Price Tolerance**
- **Default**: 0.01 (1 cent)
- **Usage**: `price_tolerance=0.01` in `on_trader_price_update()`
- **Recommendation**: Adjust based on symbol price range and volatility

#### **Window Settings**
- **RollingPriceDeque Window**: 5.0 seconds (configurable in PriceRepository)
- **Purpose**: Maintain recent price history for matching
- **Trade-off**: Larger windows = more matches, higher memory usage

## Use Cases

### 1. **System Performance Analysis**

#### **OCR Processing Latency**
```python
# Analyze OCR processing delays
latency_stats = benchmarker.get_stats("latency.ocr_to_matched_market_price_ms")
print(f"Average OCR latency: {latency_stats['avg']:.2f}ms")
print(f"95th percentile: {latency_stats['p95']:.2f}ms")
```

#### **Market Data Freshness**
```python
# Check for stale market data (negative latencies)
negative_latencies = [m for m in metrics if m['value'] < 0]
if negative_latencies:
    print(f"Stale market data detected: {len(negative_latencies)} cases")
```

### 2. **Symbol-Specific Analysis**

#### **Per-Symbol Performance**
```python
# Analyze latency by symbol
symbol_metrics = benchmarker.get_metrics_by_context(
    "latency.ocr_to_matched_market_price_ms", 
    "symbol"
)
for symbol, metrics in symbol_metrics.items():
    print(f"{symbol}: {metrics['avg']:.2f}ms average latency")
```

### 3. **Real-Time Monitoring**

#### **Latency Alerts**
```python
# Monitor for high latency conditions
if offset_ms > 1000:  # 1 second threshold
    logger.warning(f"High OCR latency detected: {offset_ms:.2f}ms for {symbol}")
```

## Benefits

### 1. **Performance Optimization**
- **OCR Timing**: Optimize OCR capture frequency and processing timing
- **Market Data**: Tune subscription timing and caching strategies
- **Pipeline Efficiency**: Identify bottlenecks in data flow

### 2. **Quality Assurance**
- **Data Freshness**: Detect stale market data conditions
- **Synchronization**: Verify OCR and market data alignment
- **Accuracy**: Ensure price matching quality

### 3. **System Tuning**
- **Tolerance Adjustment**: Optimize price matching tolerance per symbol
- **Window Sizing**: Tune rolling data window for optimal matching
- **Processing Priority**: Prioritize symbols with consistent low latency

## Troubleshooting

### Common Issues

#### **No Latency Metrics Captured**
- **Check**: Price provider initialization
- **Check**: Benchmarker availability
- **Check**: Market data availability for symbol
- **Check**: Price tolerance settings

#### **Unexpected Negative Latencies**
- **Cause**: OCR processing slower than market data updates
- **Solution**: Optimize OCR processing pipeline
- **Monitoring**: Track negative latency frequency

#### **No Price Matches Found**
- **Cause**: Price tolerance too strict
- **Solution**: Increase tolerance or check price calculation
- **Debug**: Log trader_price vs market prices for comparison

### Debug Logging

#### **Enable Debug Logging**
```python
import logging
logging.getLogger('utils.time_sync').setLevel(logging.DEBUG)
```

#### **Key Log Messages**
- `"offset for {symbol} => {offset_ms:.2f} ms"`: Successful latency measurement
- `"No matching price found for {symbol}"`: No price match within tolerance
- `"Price provider not initialized"`: Initialization issue

The Time Synchronization System provides comprehensive latency measurement capabilities for optimal OCR and market data synchronization! ⏱️📊
