#!/usr/bin/env python
"""
Startup script for IntelliSense API Server
Run this from the project root directory (C:\TESTRADE)
"""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Set PYTHONPATH environment variable as well
os.environ['PYTHONPATH'] = project_root

print(f"Python path setup:")
print(f"  Project root: {project_root}")
print(f"  Python path: {sys.path[:3]}...")

# Now import and run the API server
if __name__ == "__main__":
    try:
        from intellisense.api.main import app
        import uvicorn
        
        print(f"\nStarting IntelliSense API server...")
        print(f"API will be available at: http://localhost:8002")
        print(f"API docs available at: http://localhost:8002/docs")
        print(f"\nUsing Redis-isolated architecture")
        
        uvicorn.run(app, host="0.0.0.0", port=8002, log_level="info")
    except ImportError as e:
        print(f"\nImport Error: {e}")
        print(f"\nMake sure you're running this script from the project root:")
        print(f"  cd C:\\TESTRADE")
        print(f"  python start_intellisense_api.py")
        sys.exit(1)