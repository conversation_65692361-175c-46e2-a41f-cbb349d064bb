# minimal_monitor.ps1 - Minimal file monitor
param(
    [string]$Path = "C:\TESTRADE\data",
    [int]$Seconds = 5
)

Write-Host "TESTRADE File Monitor" -ForegroundColor Green
Write-Host "Monitoring: $Path" -ForegroundColor Cyan
Write-Host "Checking every $Seconds seconds (Ctrl+C to stop)" -ForegroundColor Cyan
Write-Host "=" * 50

$previousSizes = @{}
$count = 0

while ($true) {
    $count++
    $currentTime = Get-Date
    
    # Check for TESTRADE
    $testradeFound = $false
    $pythonProcs = Get-Process python -ErrorAction SilentlyContinue
    if ($pythonProcs) {
        foreach ($proc in $pythonProcs) {
            try {
                $cmd = (Get-CimInstance Win32_Process -Filter "ProcessId = $($proc.Id)" -ErrorAction SilentlyContinue).CommandLine
                if ($cmd -like "*testrade*" -or $cmd -like "*ApplicationCore*") {
                    $testradeFound = $true
                    break
                }
            } catch {
                # Skip
            }
        }
    }
    
    # Check files
    $currentFiles = @{}
    $growth = 0
    $newFiles = 0
    $modFiles = 0
    
    if (Test-Path $Path) {
        Get-ChildItem -Path $Path -Recurse -File -ErrorAction SilentlyContinue | ForEach-Object {
            $currentFiles[$_.FullName] = $_.Length
            
            if ($previousSizes.ContainsKey($_.FullName)) {
                $fileGrowth = $_.Length - $previousSizes[$_.FullName]
                if ($fileGrowth -gt 0) {
                    $growth += $fileGrowth
                    $modFiles++
                }
            } else {
                $growth += $_.Length
                $newFiles++
            }
        }
    }
    
    $previousSizes = $currentFiles
    
    # Calculate
    $growthMB = [math]::Round($growth / 1MB, 3)
    $growthKB = [math]::Round($growth / 1KB, 1)
    $rateMBps = [math]::Round($growthMB / $Seconds, 3)
    $rateKBps = [math]::Round($growthKB / $Seconds, 1)
    
    # Display
    Clear-Host
    Write-Host "File Monitor - Check #$count - $($currentTime.ToString('HH:mm:ss'))" -ForegroundColor Green

    Write-Host "TESTRADE: " -NoNewline
    if ($testradeFound) {
        Write-Host "RUNNING" -ForegroundColor Green
    } else {
        Write-Host "NOT FOUND" -ForegroundColor Red
    }

    Write-Host ""
    Write-Host "RESULTS (last $Seconds seconds):" -ForegroundColor Yellow
    Write-Host "  Growth: $growthMB MB ($growthKB KB)" -ForegroundColor White
    Write-Host "  Rate: $rateMBps MB/s ($rateKBps KB/s)" -ForegroundColor White
    Write-Host "  New files: $newFiles" -ForegroundColor White
    Write-Host "  Modified files: $modFiles" -ForegroundColor White
    Write-Host "  Total files: $($currentFiles.Count)" -ForegroundColor White
    
    Write-Host ""
    if ($rateMBps -gt 10) {
        Write-Host "HIGH RATE!" -ForegroundColor Red
    } elseif ($rateMBps -gt 1) {
        Write-Host "Elevated rate" -ForegroundColor Yellow
    } else {
        Write-Host "Normal rate" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "Next check in $Seconds seconds..." -ForegroundColor Gray
    
    Start-Sleep $Seconds
}
