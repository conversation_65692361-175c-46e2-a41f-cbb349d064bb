#!/usr/bin/env python3
"""
IntelliSense Consumer Health Check

Verifies that IntelliSense is properly consuming, storing, and ACKing messages.
This is critical for preventing PEL (Pending Entries List) memory buildup.
"""

import redis
import time
import json
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional

# Redis connection settings
REDIS_HOST = "**************"
REDIS_PORT = 6379
REDIS_DB = 0

# IntelliSense streams and expected consumer groups
INTELLISENSE_STREAMS = {
    "testrade:raw-ocr-events": "intellisense-group",
    "testrade:cleaned-ocr-snapshots": "intellisense-group", 
    "testrade:image-grabs": "intellisense-group",
    "testrade:order-requests": "intellisense-group",
    "testrade:validated-orders": "intellisense-group",
    "testrade:order-fills": "intellisense-group",
    "testrade:order-status": "intellisense-group",
    "testrade:market-data:active-trades": "intellisense-group",
    "testrade:market-data:active-quotes": "intellisense-group"
}

# GUI Backend streams (for comparison)
GUI_STREAMS = {
    "testrade:health:core": "gui-backend-group",
    "testrade:health:babysitter": "gui-backend-group",
    "testrade:responses:to_gui": "gui-backend-group"
}

def connect_redis():
    """Connect to Redis server."""
    try:
        client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB, decode_responses=True)
        client.ping()
        print(f"✅ Connected to Redis at {REDIS_HOST}:{REDIS_PORT}")
        return client
    except Exception as e:
        print(f"❌ Failed to connect to Redis: {e}")
        return None

def check_consumer_group_status(redis_client, stream_name: str, group_name: str) -> Dict[str, Any]:
    """Check detailed status of a consumer group."""
    try:
        # Get group info
        groups = redis_client.xinfo_groups(stream_name)
        group_info = None
        for group in groups:
            if group['name'] == group_name:
                group_info = group
                break
        
        if not group_info:
            return {"status": "GROUP_NOT_FOUND", "error": f"Group '{group_name}' not found"}
        
        # Get consumer details
        consumers = redis_client.xinfo_consumers(stream_name, group_name)
        
        # Get pending message details
        pending_summary = redis_client.xpending(stream_name, group_name)
        pending_details = []
        if pending_summary[0] > 0:  # If there are pending messages
            # Get details of up to 100 pending messages
            pending_details = redis_client.xpending_range(
                stream_name, group_name, "-", "+", count=100
            )
        
        return {
            "status": "ACTIVE",
            "group_info": group_info,
            "consumers": consumers,
            "pending_summary": pending_summary,
            "pending_details": pending_details
        }
        
    except redis.exceptions.ResponseError as e:
        if "NOGROUP" in str(e):
            return {"status": "STREAM_NOT_FOUND", "error": str(e)}
        return {"status": "ERROR", "error": str(e)}
    except Exception as e:
        return {"status": "ERROR", "error": str(e)}

def analyze_pending_messages(pending_details: List) -> Dict[str, Any]:
    """Analyze pending message patterns."""
    if not pending_details:
        return {"total": 0, "analysis": "No pending messages"}
    
    now = time.time() * 1000  # Current time in milliseconds
    old_threshold = 60000  # 1 minute in milliseconds
    very_old_threshold = 300000  # 5 minutes in milliseconds
    
    analysis = {
        "total": len(pending_details),
        "old_messages": 0,  # > 1 minute
        "very_old_messages": 0,  # > 5 minutes
        "consumers": {},
        "oldest_idle_time": 0,
        "newest_idle_time": float('inf')
    }
    
    for msg in pending_details:
        msg_id, consumer_name, idle_time, delivery_count = msg
        
        analysis["oldest_idle_time"] = max(analysis["oldest_idle_time"], idle_time)
        analysis["newest_idle_time"] = min(analysis["newest_idle_time"], idle_time)
        
        if idle_time > old_threshold:
            analysis["old_messages"] += 1
        if idle_time > very_old_threshold:
            analysis["very_old_messages"] += 1
            
        if consumer_name not in analysis["consumers"]:
            analysis["consumers"][consumer_name] = {"count": 0, "max_idle": 0}
        analysis["consumers"][consumer_name]["count"] += 1
        analysis["consumers"][consumer_name]["max_idle"] = max(
            analysis["consumers"][consumer_name]["max_idle"], idle_time
        )
    
    if analysis["newest_idle_time"] == float('inf'):
        analysis["newest_idle_time"] = 0
        
    return analysis

def check_intellisense_storage():
    """Check if IntelliSense is writing to local storage."""
    # Common IntelliSense storage locations
    storage_paths = [
        "data/intellisense",
        "intellisense/data", 
        "intellisense/storage",
        "../intellisense/data",
        "logs/intellisense"
    ]
    
    storage_status = {}
    for path in storage_paths:
        if os.path.exists(path):
            try:
                files = os.listdir(path)
                recent_files = []
                cutoff_time = time.time() - 300  # 5 minutes ago
                
                for file in files:
                    file_path = os.path.join(path, file)
                    if os.path.isfile(file_path):
                        mtime = os.path.getmtime(file_path)
                        if mtime > cutoff_time:
                            recent_files.append({
                                "name": file,
                                "modified": datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S"),
                                "size": os.path.getsize(file_path)
                            })
                
                storage_status[path] = {
                    "exists": True,
                    "total_files": len(files),
                    "recent_files": recent_files
                }
            except Exception as e:
                storage_status[path] = {"exists": True, "error": str(e)}
        else:
            storage_status[path] = {"exists": False}
    
    return storage_status

def main():
    """Main verification function."""
    print("🔍 IntelliSense Consumer Health Check")
    print("=" * 60)
    
    redis_client = connect_redis()
    if not redis_client:
        return
    
    print(f"\n📊 Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check IntelliSense consumers
    print(f"\n🧠 IntelliSense Consumer Status:")
    print("-" * 40)
    
    total_pending = 0
    problematic_streams = []
    
    for stream_name, group_name in INTELLISENSE_STREAMS.items():
        status = check_consumer_group_status(redis_client, stream_name, group_name)
        
        if status["status"] == "ACTIVE":
            group_info = status["group_info"]
            consumers = status["consumers"]
            pending_count = group_info["pending"]
            total_pending += pending_count
            
            # Status indicator
            if pending_count == 0:
                indicator = "✅"
            elif pending_count < 10:
                indicator = "⚠️"
            else:
                indicator = "❌"
                problematic_streams.append(stream_name)
            
            print(f"{indicator} {stream_name}")
            print(f"   📋 Pending: {pending_count}")
            print(f"   👥 Consumers: {len(consumers)}")
            
            if consumers:
                for consumer in consumers:
                    last_seen = consumer.get('idle', 0) / 1000  # Convert to seconds
                    print(f"      🔹 {consumer['name']}: {consumer['pending']} pending, idle {last_seen:.1f}s")
            
            # Analyze pending messages if any
            if pending_count > 0:
                analysis = analyze_pending_messages(status["pending_details"])
                if analysis["very_old_messages"] > 0:
                    print(f"   ⚠️  {analysis['very_old_messages']} messages pending > 5 minutes!")
                if analysis["oldest_idle_time"] > 0:
                    print(f"   ⏰ Oldest pending: {analysis['oldest_idle_time']/1000:.1f}s")
        
        elif status["status"] == "STREAM_NOT_FOUND":
            print(f"⚪ {stream_name}: Stream not found (no data yet)")
        elif status["status"] == "GROUP_NOT_FOUND":
            print(f"❌ {stream_name}: Consumer group '{group_name}' not found!")
            problematic_streams.append(stream_name)
        else:
            print(f"❌ {stream_name}: Error - {status.get('error', 'Unknown')}")
            problematic_streams.append(stream_name)
    
    # Check GUI Backend consumers for comparison
    print(f"\n🖥️  GUI Backend Consumer Status:")
    print("-" * 40)
    
    for stream_name, group_name in GUI_STREAMS.items():
        status = check_consumer_group_status(redis_client, stream_name, group_name)
        if status["status"] == "ACTIVE":
            pending_count = status["group_info"]["pending"]
            indicator = "✅" if pending_count < 10 else "⚠️"
            print(f"{indicator} {stream_name}: {pending_count} pending")
        else:
            print(f"⚪ {stream_name}: {status['status']}")
    
    # Check IntelliSense storage
    print(f"\n💾 IntelliSense Local Storage Check:")
    print("-" * 40)
    
    storage_status = check_intellisense_storage()
    active_storage = False
    
    for path, info in storage_status.items():
        if info["exists"]:
            if "recent_files" in info and info["recent_files"]:
                print(f"✅ {path}: {len(info['recent_files'])} recent files")
                active_storage = True
                for file_info in info["recent_files"][:3]:  # Show first 3
                    print(f"   📄 {file_info['name']} ({file_info['size']} bytes, {file_info['modified']})")
            else:
                print(f"⚪ {path}: Exists but no recent activity")
        else:
            print(f"⚪ {path}: Not found")
    
    # Summary
    print(f"\n📋 SUMMARY:")
    print("-" * 20)
    print(f"Total IntelliSense pending messages: {total_pending}")
    print(f"Problematic streams: {len(problematic_streams)}")
    print(f"IntelliSense storage active: {'✅ Yes' if active_storage else '❌ No'}")
    
    if problematic_streams:
        print(f"\n⚠️  ISSUES FOUND:")
        for stream in problematic_streams:
            print(f"   - {stream}")
        print(f"\n💡 RECOMMENDATIONS:")
        print(f"   1. Check if IntelliSense process is running")
        print(f"   2. Check IntelliSense logs for errors")
        print(f"   3. Verify IntelliSense Redis configuration")
        print(f"   4. Consider restarting IntelliSense if consistently failing to ACK")
    
    if total_pending > 100:
        print(f"\n🚨 HIGH PENDING COUNT WARNING:")
        print(f"   Total pending messages ({total_pending}) is high!")
        print(f"   This could contribute to Redis memory usage via PELs.")
        print(f"   IntelliSense may not be ACKing messages properly.")
    
    redis_client.close()

if __name__ == "__main__":
    main()
