#!/usr/bin/env python3
"""
Create Combined JSON Files

Creates combined all_trades.json and all_quotes.json from individual symbol files.
"""

import os
import json
import glob
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def combine_json_files():
    """Combine individual JSON files into all_trades.json and all_quotes.json."""
    
    data_dir = 'data/friday_may30_2025'
    
    # Find individual JSON files (exclude combined files and old converted file)
    trades_pattern = os.path.join(data_dir, "*_trades_*.json")
    quotes_pattern = os.path.join(data_dir, "*_quotes_*.json")
    
    trades_files = [f for f in glob.glob(trades_pattern) if 'all_trades' not in f and 'converted' not in f]
    quotes_files = [f for f in glob.glob(quotes_pattern) if 'all_quotes' not in f]
    
    logger.info(f"Found {len(trades_files)} trades files: {[os.path.basename(f) for f in trades_files]}")
    logger.info(f"Found {len(quotes_files)} quotes files: {[os.path.basename(f) for f in quotes_files]}")
    
    # Combine trades files
    if trades_files:
        all_trades = []
        for trades_file in trades_files:
            try:
                with open(trades_file, 'r') as f:
                    data = json.load(f)
                    all_trades.extend(data)
                    logger.info(f"Loaded {len(data)} trades from {os.path.basename(trades_file)}")
            except Exception as e:
                logger.error(f"Error loading {trades_file}: {e}")
        
        if all_trades:
            # Sort by timestamp
            all_trades.sort(key=lambda x: x.get('timestamp', 0))
            
            # Save combined file
            combined_trades_path = os.path.join(data_dir, 'all_trades.json')
            with open(combined_trades_path, 'w') as f:
                json.dump(all_trades, f, indent=2)
            
            file_size = os.path.getsize(combined_trades_path)
            logger.info(f"✅ Created all_trades.json with {len(all_trades):,} records ({file_size:,} bytes)")
    
    # Combine quotes files
    if quotes_files:
        all_quotes = []
        for quotes_file in quotes_files:
            try:
                with open(quotes_file, 'r') as f:
                    data = json.load(f)
                    all_quotes.extend(data)
                    logger.info(f"Loaded {len(data)} quotes from {os.path.basename(quotes_file)}")
            except Exception as e:
                logger.error(f"Error loading {quotes_file}: {e}")
        
        if all_quotes:
            # Sort by timestamp
            all_quotes.sort(key=lambda x: x.get('timestamp', 0))
            
            # Save combined file
            combined_quotes_path = os.path.join(data_dir, 'all_quotes.json')
            with open(combined_quotes_path, 'w') as f:
                json.dump(all_quotes, f, indent=2)
            
            file_size = os.path.getsize(combined_quotes_path)
            logger.info(f"✅ Created all_quotes.json with {len(all_quotes):,} records ({file_size:,} bytes)")

def main():
    """Main function."""
    print("="*60)
    print("CREATE COMBINED JSON FILES")
    print("="*60)
    
    combine_json_files()
    
    print("\n" + "="*60)
    print("COMBINED FILES CREATED")
    print("="*60)

if __name__ == "__main__":
    main()
