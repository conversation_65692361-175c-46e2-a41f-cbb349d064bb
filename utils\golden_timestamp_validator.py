"""
Golden Timestamp Validator - Universal data freshness checking utility.

This module provides utilities for checking data freshness using the Golden Timestamp
system implemented across TESTRADE. All data sent to Redis includes an origin_timestamp_s
field that indicates when the data was originally captured/created.
"""

import time
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

# Default staleness thresholds (can be overridden)
DEFAULT_STALENESS_THRESHOLDS = {
    'ocr_data': 2.0,          # OCR data older than 2 seconds is stale
    'price_data': 1.0,        # Price data older than 1 second is stale
    'position_data': 5.0,     # Position data older than 5 seconds is stale
    'order_data': 3.0,        # Order data older than 3 seconds is stale
    'risk_data': 2.0,         # Risk decisions older than 2 seconds are stale
    'default': 3.0            # Default threshold for unknown data types
}

def is_data_stale(message: Dict[str, Any], 
                  event_type: Optional[str] = None,
                  custom_threshold_s: Optional[float] = None) -> bool:
    """
    Check if a Redis message contains stale data based on its Golden Timestamp.
    
    Args:
        message: Redis message dictionary with metadata and payload sections
        event_type: Optional event type to use specific staleness threshold
        custom_threshold_s: Optional custom staleness threshold in seconds
        
    Returns:
        bool: True if data is stale, False if fresh
    """
    try:
        metadata = message.get('metadata', {})
        origin_timestamp_s = metadata.get('origin_timestamp_s')
        
        if origin_timestamp_s is None:
            logger.warning(f"Message missing origin_timestamp_s: {metadata.get('eventType', 'UNKNOWN')}")
            return True  # Treat missing timestamp as stale
            
        current_time = time.time()
        age_seconds = current_time - origin_timestamp_s
        
        # Determine staleness threshold
        if custom_threshold_s is not None:
            threshold = custom_threshold_s
        elif event_type:
            # Try to match event type to threshold category
            event_lower = event_type.lower()
            if 'ocr' in event_lower:
                threshold = DEFAULT_STALENESS_THRESHOLDS['ocr_data']
            elif 'price' in event_lower or 'quote' in event_lower or 'market' in event_lower:
                threshold = DEFAULT_STALENESS_THRESHOLDS['price_data']
            elif 'position' in event_lower:
                threshold = DEFAULT_STALENESS_THRESHOLDS['position_data']
            elif 'order' in event_lower or 'fill' in event_lower:
                threshold = DEFAULT_STALENESS_THRESHOLDS['order_data']
            elif 'risk' in event_lower:
                threshold = DEFAULT_STALENESS_THRESHOLDS['risk_data']
            else:
                threshold = DEFAULT_STALENESS_THRESHOLDS['default']
        else:
            threshold = DEFAULT_STALENESS_THRESHOLDS['default']
            
        is_stale = age_seconds > threshold
        
        if is_stale:
            logger.debug(f"Stale data detected: age={age_seconds:.2f}s, threshold={threshold}s, "
                        f"eventType={metadata.get('eventType', 'UNKNOWN')}")
        
        return is_stale
        
    except Exception as e:
        logger.error(f"Error checking data staleness: {e}")
        return True  # Treat errors as stale data

def get_data_age_seconds(message: Dict[str, Any]) -> Optional[float]:
    """
    Get the age of data in seconds based on its Golden Timestamp.
    
    Args:
        message: Redis message dictionary with metadata section
        
    Returns:
        float: Age in seconds, or None if timestamp missing
    """
    try:
        metadata = message.get('metadata', {})
        origin_timestamp_s = metadata.get('origin_timestamp_s')
        
        if origin_timestamp_s is None:
            return None
            
        return time.time() - origin_timestamp_s
        
    except Exception as e:
        logger.error(f"Error calculating data age: {e}")
        return None

def log_stale_data_rejection(message: Dict[str, Any], age_seconds: float, threshold: float):
    """
    Log when stale data is rejected for debugging purposes.
    
    Args:
        message: The rejected message
        age_seconds: How old the data was
        threshold: The staleness threshold that was exceeded
    """
    metadata = message.get('metadata', {})
    logger.info(f"STALE DATA REJECTED: eventType={metadata.get('eventType', 'UNKNOWN')}, "
               f"age={age_seconds:.2f}s, threshold={threshold}s, "
               f"correlationId={metadata.get('correlationId', 'NONE')}")

# Example usage for GUI components:
def handle_redis_message(message: Dict[str, Any]) -> bool:
    """
    Example handler showing how GUI components should check for stale data.
    
    Returns:
        bool: True if message was processed, False if rejected as stale
    """
    event_type = message.get('metadata', {}).get('eventType')
    
    if is_data_stale(message, event_type=event_type):
        age = get_data_age_seconds(message)
        if age is not None:
            log_stale_data_rejection(message, age, DEFAULT_STALENESS_THRESHOLDS.get('default', 3.0))
        return False  # Reject stale data
    
    # Process fresh data
    payload = message.get('payload', {})
    # ... update GUI with fresh data ...
    
    return True