# TESTRADE ID Tracking System - Complete Guide

## Overview
TESTRADE uses a sophisticated ID tracking system to maintain end-to-end traceability of all events through the system. This document provides the definitive guide to understanding and using these IDs correctly.

## Core ID Types

### 1. Event ID (`eventId`)
- **Purpose**: Unique identifier for each individual event
- **Generation**: `get_thread_safe_uuid()` from `utils/thread_safe_uuid.py`
- **Format**: Standard UUID v4 string
- **Scope**: Every event that flows through the system gets its own unique eventId
- **Example**: `"eventId": "550e8400-e29b-41d4-a716-************"`

### 2. Correlation ID (`correlationId`)
- **Purpose**: Master tracking ID that follows a complete trading decision from OCR to execution
- **Generation**: Initially created when the first event in a chain occurs (typically OCR)
- **Propagation**: Passed through all subsequent events in the chain
- **Use Case**: Query by correlationId to see all events related to a single trading decision
- **Example**: Following YIBO trade from OCR → Signal → MAF → Order → Fill

### 3. Causation ID (`causationId`)
- **Purpose**: Links an event to the immediate parent event that caused it
- **Value**: The eventId of the parent event
- **Use Case**: Build causation chains to understand event flow
- **Example**: MAF decision has causationId = eventId of the cleaned OCR event that triggered it

### 4. Validation ID (`validation_id`)
- **Purpose**: Pipeline validation tracking (specific to OCR pipeline)
- **Generation**: Set on OCRParsedData objects
- **Scope**: OCR pipeline validation
- **Access**: `getattr(cleaned_event_data, '__validation_id', None)`

### 5. Global Sequence ID (`global_sequence_id`)
- **Purpose**: Provides global ordering across all events in the system
- **Generation**: Via `get_next_global_sequence_id()` function
- **Scope**: System-wide event ordering for IntelliSense analysis
- **Usage**: Found in correlation logs, telemetry streams, and test data
- **Format**: Incrementing integer sequence

## Trading & Order Management IDs

### 6. Local Order ID (`local_order_id`)
- **Purpose**: TESTRADE's internal order identifier
- **Generation**: Sequential integer from OrderRepository
- **Scope**: Internal order tracking, correlation with broker orders
- **Format**: Integer starting from 1
- **Example**: `local_order_id: 123`

### 7. Broker Order ID (`ls_order_id`, `broker_order_id`)
- **Purpose**: Broker-assigned order identifier
- **Generation**: Broker system (Lightspeed, Alpaca, etc.)
- **Scope**: Cross-reference between TESTRADE and broker systems
- **Format**: Broker-specific (usually integer)
- **Example**: `ls_order_id: 264958`

### 8. Ephemeral Correlation ID (`ephemeral_corr_id`)
- **Purpose**: Short-lived broker operation tracking
- **Generation**: Broker system for temporary correlation
- **Scope**: Broker message correlation during order lifecycle
- **Format**: Integer
- **Usage**: Lightspeed broker integration

### 9. Execution ID (`execution_id`)
- **Purpose**: Broker's execution identifier for individual fills
- **Generation**: Broker system when fill occurs
- **Scope**: Fill-level tracking, reconciliation with broker records
- **Format**: Broker-specific string
- **Example**: `"execution_id": "EXEC_ABC123"`

### 10. Fill ID (`fill_id`, `fill_id_broker`)
- **Purpose**: Individual fill execution identification
- **Generation**: Broker system for each fill
- **Scope**: Fill records in order management
- **Usage**: Order data types, fill tracking

### 11. Request ID (`request_id`)
- **Purpose**: Order request identification before broker submission
- **Generation**: `str(uuid4())` when order request created
- **Scope**: Order request lifecycle tracking
- **Usage**: OrderRequestEvent, API requests
- **Example**: `"request_id": "inj_req_789xyz_1748917126401"`

## Trade & Position Management IDs

### 12. Trade ID (`trade_id`)
- **Purpose**: Individual trade identification  
- **Generation**: Trade lifecycle manager
- **Scope**: Multi-order trade tracking, GUI displays
- **Usage**: Trade history, GUI backend, rejection handling

### 13. Parent Trade ID (`parent_trade_id`)
- **Purpose**: Hierarchical trade relationship tracking
- **Generation**: Trade management system
- **Scope**: Trade hierarchies, position relationships
- **Usage**: Order data types, position management

### 14. LCM Trade ID (`lcm_trade_id`)
- **Purpose**: Lifecycle manager trade tracking
- **Generation**: TradeLifecycleManager service
- **Scope**: Multi-order trade lifecycle tracking
- **Usage**: Position correlation, trade state management

### 15. Position UUID (`position_uuid`)
- **Purpose**: Unique position identifier
- **Generation**: `str(uuid.uuid4())` or derived from trade ID
- **Scope**: Position tracking across events
- **Usage**: Position events, GUI displays, correlation logging
- **Example**: `"position_uuid": "YIBO-24"`

## Pipeline & Validation IDs

### 16. Validation Pipeline ID (`validation_pipeline_id`)
- **Purpose**: Specific validation pipeline instance tracking
- **Generation**: Risk management and trade management services
- **Scope**: Risk validation workflows
- **Usage**: Trade manager service, risk service validation

### 17. Pipeline ID (`pipeline_id`)
- **Purpose**: General pipeline identification
- **Generation**: Pipeline management system
- **Scope**: Data processing pipelines
- **Usage**: Pipeline validator, monitoring systems

## OCR & Image Processing IDs

### 18. Frame ID (`frame_id`)
- **Purpose**: Individual OCR frame identification
- **Generation**: OCR capture system
- **Scope**: OCR processing pipeline, image capture
- **Usage**: OCR data models, IntelliSense testing

### 19. Image ID (`image_id`)
- **Purpose**: Captured image identification
- **Generation**: Image capture system
- **Scope**: Image processing workflows
- **Usage**: Test data, ZMQ testing

### 20. Snapshot ID (`snapshot_id`)
- **Purpose**: OCR snapshot identification
- **Generation**: OCR data processing
- **Scope**: OCR data capture and processing
- **Usage**: ZMQ testing, OCR workflows

### 21. ROI ID (`roi_id`)
- **Purpose**: Region of Interest identification for OCR
- **Generation**: OCR service components
- **Scope**: OCR region processing
- **Usage**: OCR configuration, region management

## System & Infrastructure IDs

### 22. Thread ID (`thread_id`)
- **Purpose**: Thread identification for debugging and monitoring
- **Generation**: System thread management
- **Scope**: Thread monitoring, performance tracking
- **Usage**: Event bus lifecycle, UUID deadlock prevention

### 23. Worker ID (`worker_id`)
- **Purpose**: Worker thread identification in concurrent processing
- **Generation**: Concurrent processing systems
- **Scope**: Multi-threaded operations
- **Usage**: Performance tracking, concurrent operations

### 24. Session ID (`session_id`)
- **Purpose**: User or system session identification
- **Generation**: Session management systems
- **Scope**: MCP server, IntelliSense sessions
- **Usage**: Session tracking, connection management

### 25. Client ID (`client_id`)
- **Purpose**: Client application identification
- **Generation**: Client systems or configured
- **Scope**: Order tracking, client identification
- **Usage**: Order data types for professional tracking
- **Default**: `"TESTRADE"`

## Redis & Messaging IDs

### 26. Stream ID (`stream_id`)
- **Purpose**: Redis stream entry identifiers
- **Generation**: Redis XADD operations
- **Scope**: Redis streams for message persistence
- **Usage**: GUI backend, stream processing
- **Format**: Redis timestamp-sequence format

### 27. Message ID (`message_id`)
- **Purpose**: Individual Redis stream message identification
- **Generation**: Redis stream operations
- **Scope**: Stream consumers and handlers
- **Usage**: GUI backend message handlers

## Development & Testing IDs

### 28. Job ID (`job_id`)
- **Purpose**: Scheduled job or task identification
- **Generation**: Task scheduling systems
- **Scope**: GUI task scheduling, background jobs
- **Usage**: ROI adjustment, scheduled operations

### 29. Alert ID (`alert_id`)
- **Purpose**: System alert identification
- **Generation**: Alert management systems
- **Scope**: Mission control notifications
- **Usage**: Core mission control notifier

### 30. Target Identifier (`target_identifier`)
- **Purpose**: IntelliSense validation target identification
- **Generation**: IntelliSense testing framework
- **Scope**: Validation scenarios
- **Usage**: Integration tests, scenario validation

## ID Flow Through the System

### OCR → Cleaned OCR → Signal → MAF → Order

```
1. Raw OCR Event
   - eventId: A (unique)
   - correlationId: A (starts here)
   - causationId: null (no parent)

2. Cleaned OCR Event
   - eventId: B (unique)
   - correlationId: A (inherited)
   - causationId: A (caused by raw OCR)

3. MAF Decision Event
   - eventId: C (unique)
   - correlationId: A (inherited)
   - causationId: B (caused by cleaned OCR)

4. Order Request Event
   - eventId: D (unique)
   - correlationId: A (inherited)
   - causationId: C (caused by MAF decision)
```

## Implementation Details

### Thread-Safe UUID Generation
```python
from utils.thread_safe_uuid import get_thread_safe_uuid

# Generate a new event ID
event_id = get_thread_safe_uuid()
```

### Standard Event Structure
```python
message = {
    "metadata": {
        "eventId": get_thread_safe_uuid(),  # Always new
        "correlationId": correlation_id,     # Inherited
        "causationId": parent_event_id,      # Parent's eventId
        "timestamp_ns": time.perf_counter_ns(),
        "epoch_timestamp_s": time.time(),
        "eventType": "TESTRADE_EVENT_TYPE",
        "sourceComponent": "ComponentName"
    },
    "payload": {
        # Event-specific data
    }
}
```

### Key Services and Their ID Usage

#### OCRDataConditioningService
- Creates cleaned events with new eventId
- Preserves correlationId as `origin_correlation_id`
- Sets causationId to the raw OCR event's eventId

#### MasterActionFilterService
- Receives correlationId and causationId from orchestrator
- Creates new eventId for each MAF decision
- Maintains correlation chain for traceability

#### OCRScalpingSignalOrchestratorService
- Extracts correlationId: `cleaned_event_data.origin_correlation_id`
- Determines causationId: `cleaned_event_id or cleaned_event_data.original_ocr_event_id`
- Passes both to downstream services (MAF, Order generation)

## Common Patterns

### Extracting IDs from Events
```python
# From cleaned OCR data
correlation_id = cleaned_event_data.origin_correlation_id
validation_id = getattr(cleaned_event_data, '__validation_id', None)

# From Redis message
event_data = json.loads(redis_message)
event_id = event_data['metadata']['eventId']
correlation_id = event_data['metadata']['correlationId']
causation_id = event_data['metadata']['causationId']
```

### Creating Child Events
```python
def create_child_event(parent_correlation_id, parent_event_id, event_type, payload):
    return {
        "metadata": {
            "eventId": get_thread_safe_uuid(),
            "correlationId": parent_correlation_id,  # Inherit
            "causationId": parent_event_id,          # Parent becomes cause
            "timestamp_ns": time.perf_counter_ns(),
            "epoch_timestamp_s": time.time(),
            "eventType": event_type,
            "sourceComponent": "YourComponent"
        },
        "payload": payload
    }
```

## Critical Rules

1. **NEVER** reuse an eventId - always generate a new one
2. **ALWAYS** propagate the correlationId through the entire chain
3. **ALWAYS** set causationId to the immediate parent's eventId
4. **NEVER** modify IDs when forwarding events - create new events instead
5. **ALWAYS** use `get_thread_safe_uuid()` for ID generation (not `uuid.uuid4()`)

## Debugging with IDs

### Find all events for a trading decision
```bash
redis-cli XREAD STREAMS testrade:* 0 | grep -A 10 -B 10 "correlationId.*A"
```

### Trace causation chain
Start with final event and follow causationId backwards to build the complete chain.

### Common Issues
- Missing correlationId: Breaks end-to-end tracing
- Reused eventId: Causes confusion in event ordering
- Wrong causationId: Breaks causation chain analysis

## Redis Streams Using This System

All TESTRADE Redis streams follow this ID convention:
- testrade:raw-ocr-events
- testrade:cleaned-ocr-snapshots
- testrade:order-requests
- testrade:order-fills
- testrade:trade-lifecycle-events
- testrade:maf-decisions (NEW)
- testrade:position-updates
- And all others...

## Conclusion

The ID tracking system is fundamental to TESTRADE's observability and debugging capabilities. Proper implementation ensures that IntelliSense can analyze complete trading flows and provide meaningful insights.