// pch.h: This is a precompiled header file.
// Files listed below are compiled only once, improving build performance for future builds.
// This also affects IntelliSense performance, including code completion and many code browsing features.
// However, files listed here are ALL re-compiled if any one of them is updated between builds.
// Do not add files here that you will be updating frequently as this negates the performance advantage.

#ifndef PCH_H
#define PCH_H

// --- Add Defines FIRST ---
#define WIN32_LEAN_AND_MEAN             // Exclude rarely-used stuff from Windows headers
#define NOMINMAX                        // Prevent Windows from defining min() and max() macros
#define _WINSOCK_DEPRECATED_NO_WARNINGS // Keep this if you need it

// --- Add MFC Core Headers ---
#include <afxwin.h>                     // MFC core and standard components

// --- Add Windows Headers ---
#include <windows.h>                    // Core Windows API functions (AFTER afxwin.h if using MFC)
#include <winsock2.h>                   // Winsock 2 API
#include <ws2tcpip.h>                   // TCP/IP specific helpers for Winsock

// --- Add Standard C++ Headers (Optional but often useful in PCH) ---
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <thread>
#include <atomic>
#include <memory> // Often useful
#include <stdexcept> // Often useful

// --- Add Core Lightspeed SDK Headers ---
// It's often sufficient to include the main header if it includes the others,
// but being explicit is also fine.
#include <LightspeedTrader.h> // Main SDK header (adjust path if needed)
#include <L_Observer.h>     // For L_Observer base class
#include <L_Messages.h>     // For L_Message and specific message types
#include <L_Account.h>      // For L_Account
#include <L_Order.h>        // For L_Order, L_OrderChange constants
#include <L_Position.h>     // For L_Position
#include <L_Execution.h>    // For L_Execution
#include <L_OrderErrors.h>  // Add this - Needed for L_OrderResult constants
#include <L_Summary.h>      // Add this - Might be needed by L_Account or for LULD

// --- Add other stable, frequently used third-party headers (Optional) ---
// #include "json.hpp" // You could add this, but only if it's stable and you don't change it often

#endif //PCH_H