# Alpaca Tick Data Download Scripts

This directory contains scripts to download and analyze tick data from Alpaca Markets Data API using your stored credentials.

## 🎯 Quick Start - Download Friday Data

To download the specific data you requested (Friday 5/30/25, 9:45-10:45 AM, MSFT/AAPL/SPY/NVDA/UBER):

```bash
cd TESTRADE
python scripts/download_friday_data.py
```

This will download both trade (tick) and quote (NBBO) data to `data/friday_may30_2025/`.

## 📁 Scripts Overview

### 1. `download_friday_data.py` - Simple Runner
**Purpose**: Downloads the specific data you requested with minimal configuration.

**What it downloads**:
- **Symbols**: MSFT, AAPL, SPY, NVDA, UBER
- **Date**: Friday, May 30, 2025
- **Time**: 9:45 AM - 10:45 AM EDT (60 minutes)
- **Data Types**: Both trades (tick data) and quotes (NBBO)
- **Output**: `data/friday_may30_2025/`

### 2. `download_alpaca_tick_data.py` - Full-Featured Downloader
**Purpose**: Flexible script for downloading any symbols/dates/times.

**Usage Examples**:
```bash
# Default (same as download_friday_data.py)
python scripts/download_alpaca_tick_data.py

# Custom symbols and time range
python scripts/download_alpaca_tick_data.py \
    --symbols "TSLA,GOOGL,AMZN" \
    --start "2025-05-30 10:00:00" \
    --end "2025-05-30 11:00:00" \
    --output-dir "data/custom_download"

# Only trade data (skip quotes)
python scripts/download_alpaca_tick_data.py --trades-only

# Only quote data (skip trades)
python scripts/download_alpaca_tick_data.py --quotes-only
```

### 3. `analyze_alpaca_data.py` - Data Analysis
**Purpose**: Analyze downloaded data and convert to different formats.

**Usage Examples**:
```bash
# Analyze all files in directory
python scripts/analyze_alpaca_data.py --data-dir data/friday_may30_2025 --summary

# Analyze single file
python scripts/analyze_alpaca_data.py --file data/friday_may30_2025/AAPL_trades_20250530_0945_1045.csv

# Convert tick data to OHLC bars
python scripts/analyze_alpaca_data.py \
    --convert-ohlc data/friday_may30_2025/AAPL_trades_20250530_0945_1045.csv \
    --interval 1min \
    --output data/friday_may30_2025/AAPL_ohlc_1min.csv
```

## 📊 Data Formats

### Trade Data (Tick Data)
Each trade file contains:
- `timestamp`: Trade execution time
- `price`: Trade price
- `size`: Trade size (shares)
- `exchange`: Exchange where trade occurred
- `conditions`: Trade conditions/flags
- `tape`: SIP tape (A, B, or C)

### Quote Data (NBBO)
Each quote file contains:
- `timestamp`: Quote update time
- `bid_price`: Best bid price
- `bid_size`: Best bid size
- `ask_price`: Best ask price
- `ask_size`: Best ask size
- `bid_exchange`: Exchange with best bid
- `ask_exchange`: Exchange with best ask
- `conditions`: Quote conditions/flags
- `tape`: SIP tape (A, B, or C)

## 🔧 Configuration

### Alpaca Credentials
The scripts automatically use your stored Alpaca credentials from:
- `utils/global_config.py`: Contains API key, secret, and additional configuration
- `utils/control.json`: Runtime configuration that can override defaults

**Credentials are loaded from GlobalConfig system**:
- API Key: Loaded from GlobalConfig.ALPACA_API_KEY
- API Secret: Loaded from GlobalConfig.ALPACA_API_SECRET
- Data URL: `https://data.alpaca.markets`

### Time Zones
- All times are interpreted as **EDT (UTC-4)** for May 30, 2025
- Market hours: 9:30 AM - 4:00 PM EDT
- Your requested time: 9:45 AM - 10:45 AM EDT (during market hours)

## 📈 Expected Output

For the Friday data download, you should get files like:
```
data/friday_may30_2025/
├── AAPL_trades_20250530_0945_1045.csv
├── AAPL_quotes_20250530_0945_1045.csv
├── MSFT_trades_20250530_0945_1045.csv
├── MSFT_quotes_20250530_0945_1045.csv
├── SPY_trades_20250530_0945_1045.csv
├── SPY_quotes_20250530_0945_1045.csv
├── NVDA_trades_20250530_0945_1045.csv
├── NVDA_quotes_20250530_0945_1045.csv
├── UBER_trades_20250530_0945_1045.csv
└── UBER_quotes_20250530_0945_1045.csv
```

## 🚨 Important Notes

### Data Availability
- **Historical Data**: May 30, 2025 is a future date. The scripts will attempt to download but may return empty results if the date hasn't occurred yet.
- **Market Hours**: Ensure the requested time falls within market hours (9:30 AM - 4:00 PM EDT).
- **Weekends**: May 30, 2025 is a Friday (market day), so data should be available.

### API Limits
- Alpaca has rate limits on data requests
- Each request can return up to 10,000 records
- For high-frequency data, multiple requests may be needed

### Data Quality
- Tick data represents the lowest timeframe available
- Trade data shows actual executions
- Quote data shows best bid/offer (NBBO) updates
- Data includes exchange information and trade conditions

## 🔍 Troubleshooting

### Common Issues
1. **No data returned**: Check if the date/time is valid and within market hours
2. **API errors**: Verify credentials and check Alpaca API status
3. **Import errors**: Ensure you're running from the TESTRADE directory

### Debug Mode
Add `--debug` flag or set logging level to DEBUG:
```python
logging.basicConfig(level=logging.DEBUG)
```

## 📚 Dependencies

Required Python packages:
- `alpaca-trade-api`: Alpaca API client
- `pandas`: Data manipulation
- `numpy`: Numerical operations

Install with:
```bash
pip install alpaca-trade-api pandas numpy
```

## 🎯 Next Steps

After downloading data:
1. **Analyze**: Use `analyze_alpaca_data.py` to get statistics
2. **Convert**: Convert tick data to OHLC bars for charting
3. **Integrate**: Use the data for backtesting or analysis in TESTRADE
4. **Visualize**: Import into your preferred charting/analysis tool

## 📞 Support

If you encounter issues:
1. Check the log output for specific error messages
2. Verify your Alpaca credentials are valid
3. Ensure the requested date/time is valid
4. Check Alpaca API documentation for data availability
