#!/usr/bin/env python3
"""
Comprehensive debug script to test OCR accelerator step by step
"""

import os
import sys
import platform
import shutil

def check_files():
    """Check if all required files exist"""
    print("=== File Existence Check ===")
    
    files_to_check = [
        "ocr_accelerator/x64/Release/ocr_accelerator.pyd",
        "ocr_accelerator/x64/Release/tesseract55.dll",
        "ocr_accelerator/ocr_accelerator.cpp",
        "ocr_accelerator/ocr_accelerator.vcxproj"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        exists = os.path.exists(file_path)
        size = f" ({os.path.getsize(file_path):,} bytes)" if exists else ""
        print(f"{'✅' if exists else '❌'} {file_path}{size}")
        if not exists:
            all_exist = False
    
    return all_exist

def test_basic_import():
    """Test basic Python import without any DLL management"""
    print("\n=== Basic Import Test ===")
    
    build_dir = "ocr_accelerator/x64/Release"
    if not os.path.exists(build_dir):
        print(f"❌ Build directory not found: {build_dir}")
        return False
    
    # Add to sys.path
    abs_build = os.path.abspath(build_dir)
    if abs_build not in sys.path:
        sys.path.insert(0, abs_build)
        print(f"Added to sys.path: {abs_build}")
    
    # Try basic import
    original_cwd = os.getcwd()
    try:
        print(f"Changing to directory: {abs_build}")
        os.chdir(abs_build)
        
        import ocr_accelerator
        print("✅ Module imported successfully")
        
        # Check what's available
        attrs = [attr for attr in dir(ocr_accelerator) if not attr.startswith('_')]
        print(f"Available attributes: {attrs}")
        
        return True, ocr_accelerator
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None
    finally:
        os.chdir(original_cwd)

def test_with_tesseract_dll():
    """Test with tesseract55.dll copied to root"""
    print("\n=== Test with tesseract55.dll in root ===")
    
    dll_source = "ocr_accelerator/x64/Release/tesseract55.dll"
    dll_target = "tesseract55.dll"
    
    if not os.path.exists(dll_source):
        print(f"❌ Source DLL not found: {dll_source}")
        return False
    
    # Copy DLL to root
    dll_copied = False
    if not os.path.exists(dll_target):
        try:
            shutil.copy2(dll_source, dll_target)
            dll_copied = True
            print(f"✅ Copied tesseract55.dll to root")
        except Exception as e:
            print(f"❌ Failed to copy DLL: {e}")
            return False
    else:
        print(f"✅ tesseract55.dll already in root")
    
    # Test import
    success, module = test_basic_import()
    
    if success and module:
        # Test functions
        print("\n--- Function Tests ---")
        
        if hasattr(module, 'test_function'):
            try:
                result = module.test_function()
                print(f"✅ test_function(): {result}")
            except Exception as e:
                print(f"❌ test_function() failed: {e}")
        else:
            print("❌ test_function not found")
        
        if hasattr(module, 'process_image_and_ocr'):
            print("✅ process_image_and_ocr function found")
        else:
            print("❌ process_image_and_ocr function not found")
    
    # Clean up DLL if we copied it
    if dll_copied:
        try:
            os.remove(dll_target)
            print(f"✅ Cleaned up tesseract55.dll from root")
        except Exception as e:
            print(f"⚠️ Warning: Could not remove DLL: {e}")
    
    return success

def test_ocr_service_import():
    """Test importing the OCR service to see if it works"""
    print("\n=== OCR Service Import Test ===")
    
    # Ensure tesseract55.dll is in root for this test
    dll_source = "ocr_accelerator/x64/Release/tesseract55.dll"
    dll_target = "tesseract55.dll"
    
    dll_copied = False
    if os.path.exists(dll_source) and not os.path.exists(dll_target):
        try:
            shutil.copy2(dll_source, dll_target)
            dll_copied = True
            print(f"✅ Temporarily copied tesseract55.dll for test")
        except Exception as e:
            print(f"❌ Failed to copy DLL: {e}")
            return False
    
    try:
        # Try importing OCR service
        from modules.ocr.ocr_service import CPP_ACCELERATOR_AVAILABLE, CPP_ACCELERATOR_LOAD_STATUS
        
        print(f"CPP_ACCELERATOR_AVAILABLE: {CPP_ACCELERATOR_AVAILABLE}")
        print(f"CPP_ACCELERATOR_LOAD_STATUS: {CPP_ACCELERATOR_LOAD_STATUS}")
        
        return CPP_ACCELERATOR_AVAILABLE
        
    except Exception as e:
        print(f"❌ OCR service import failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        if dll_copied and os.path.exists(dll_target):
            try:
                os.remove(dll_target)
                print(f"✅ Cleaned up tesseract55.dll")
            except:
                pass

def main():
    print("=== OCR Accelerator Debug Analysis ===")
    print(f"Platform: {platform.system()}")
    print(f"Python: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    
    # Step 1: Check files
    if not check_files():
        print("\n❌ Missing required files. Please ensure build completed successfully.")
        return
    
    # Step 2: Test basic import (should fail without DLL)
    print("\n--- Testing without tesseract55.dll in root ---")
    success, _ = test_basic_import()
    if success:
        print("⚠️ Unexpected: Module imported without tesseract55.dll in root")
    else:
        print("✅ Expected: Module failed to import without tesseract55.dll")
    
    # Step 3: Test with DLL in root
    print("\n--- Testing with tesseract55.dll in root ---")
    if platform.system() == "Windows":
        success = test_with_tesseract_dll()
        if success:
            print("🎯 SUCCESS: OCR accelerator works with tesseract55.dll in root")
        else:
            print("❌ FAILED: Still not working even with tesseract55.dll in root")
    else:
        print("⚠️ Skipping DLL test on non-Windows platform")
    
    # Step 4: Test OCR service integration
    if platform.system() == "Windows":
        print("\n--- Testing OCR Service Integration ---")
        success = test_ocr_service_import()
        if success:
            print("🎯 SUCCESS: OCR service integration working")
        else:
            print("❌ FAILED: OCR service integration not working")

if __name__ == "__main__":
    main()