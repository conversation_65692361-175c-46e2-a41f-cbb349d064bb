#!/usr/bin/env python3
"""
Identify which specific DLLs are required in root for the module to work
"""

import os
import sys
import shutil
import platform

def test_import_with_dll(dll_name):
    """Test if module works with specific DLL in root"""
    if platform.system() != "Windows":
        return False, "Windows required"
    
    build_dir = "ocr_accelerator/x64/Release"
    dll_source = os.path.join(build_dir, dll_name)
    dll_root = dll_name
    
    # Check if DLL exists in build directory
    if not os.path.exists(dll_source):
        return False, f"DLL not found in build: {dll_source}"
    
    # Copy DLL to root
    try:
        shutil.copy2(dll_source, dll_root)
        print(f"Copied {dll_name} to root")
        
        # Test import
        abs_build = os.path.abspath(build_dir)
        original_cwd = os.getcwd()
        
        try:
            # Change to build directory for import
            os.chdir(abs_build)
            
            # Try import
            import ocr_accelerator
            
            # Test function availability
            has_functions = hasattr(ocr_accelerator, 'test_function') and hasattr(ocr_accelerator, 'process_image_and_ocr')
            
            if has_functions:
                # Test actual function call
                try:
                    result = ocr_accelerator.test_function()
                    return True, f"SUCCESS with {dll_name}: {result}"
                except Exception as e:
                    return False, f"Import ok but function failed: {e}"
            else:
                return False, f"Import ok but no functions available"
                
        except Exception as e:
            return False, f"Import failed: {e}"
        finally:
            os.chdir(original_cwd)
            
    except Exception as e:
        return False, f"Copy failed: {e}"
    finally:
        # Clean up - remove DLL from root
        if os.path.exists(dll_root):
            try:
                os.remove(dll_root)
                print(f"Removed {dll_name} from root")
            except:
                pass

def main():
    print("=== DLL Dependency Analysis ===")
    
    if platform.system() != "Windows":
        print("❌ This test must be run on Windows")
        return
    
    build_dir = "ocr_accelerator/x64/Release"
    
    # List all DLLs in build directory
    if not os.path.exists(build_dir):
        print(f"❌ Build directory not found: {build_dir}")
        return
    
    dll_files = [f for f in os.listdir(build_dir) if f.endswith('.dll')]
    print(f"Found {len(dll_files)} DLL files in build directory:")
    for dll in dll_files:
        print(f"  - {dll}")
    
    # Test critical DLLs first
    critical_dlls = [
        "tesseract55.dll",
        "leptonica-1.85.0.dll", 
        "opencv_core4.dll",
        "opencv_imgproc4.dll"
    ]
    
    print(f"\n=== Testing Critical DLLs ===")
    
    for dll in critical_dlls:
        if dll in dll_files:
            print(f"\nTesting {dll}...")
            success, message = test_import_with_dll(dll)
            print(f"  {'✅' if success else '❌'} {message}")
            
            if success:
                print(f"🎯 FOUND SOLUTION: {dll} is required in root!")
                return
        else:
            print(f"  ⚠️ {dll} not found in build directory")
    
    # If critical DLLs don't work, test all DLLs
    print(f"\n=== Testing All DLLs ===")
    for dll in dll_files:
        if dll not in critical_dlls:
            print(f"\nTesting {dll}...")
            success, message = test_import_with_dll(dll)
            print(f"  {'✅' if success else '❌'} {message}")
            
            if success:
                print(f"🎯 FOUND SOLUTION: {dll} is required in root!")
                return
    
    print("\n❌ No single DLL solved the problem. May need multiple DLLs.")
    
    # Test combinations of critical DLLs
    print(f"\n=== Testing DLL Combinations ===")
    test_combinations(critical_dlls, dll_files)

def test_combinations(critical_dlls, all_dlls):
    """Test combinations of DLLs"""
    # Test all critical DLLs together
    available_critical = [dll for dll in critical_dlls if dll in all_dlls]
    
    if len(available_critical) < 2:
        print("Not enough critical DLLs to test combinations")
        return
    
    print(f"Testing combination of all critical DLLs: {available_critical}")
    
    build_dir = "ocr_accelerator/x64/Release"
    copied_dlls = []
    
    try:
        # Copy all critical DLLs to root
        for dll in available_critical:
            dll_source = os.path.join(build_dir, dll)
            if os.path.exists(dll_source):
                shutil.copy2(dll_source, dll)
                copied_dlls.append(dll)
                print(f"Copied {dll} to root")
        
        # Test import
        abs_build = os.path.abspath(build_dir)
        original_cwd = os.getcwd()
        
        try:
            os.chdir(abs_build)
            import ocr_accelerator
            
            has_functions = hasattr(ocr_accelerator, 'test_function') and hasattr(ocr_accelerator, 'process_image_and_ocr')
            
            if has_functions:
                try:
                    result = ocr_accelerator.test_function()
                    print(f"🎯 SUCCESS with combination: {copied_dlls}")
                    print(f"  Result: {result}")
                except Exception as e:
                    print(f"❌ Functions available but call failed: {e}")
            else:
                print(f"❌ Import ok but no functions with combination: {copied_dlls}")
                
        except Exception as e:
            print(f"❌ Import failed with combination: {e}")
        finally:
            os.chdir(original_cwd)
            
    finally:
        # Clean up all copied DLLs
        for dll in copied_dlls:
            if os.path.exists(dll):
                try:
                    os.remove(dll)
                    print(f"Removed {dll} from root")
                except:
                    pass

if __name__ == "__main__":
    main()