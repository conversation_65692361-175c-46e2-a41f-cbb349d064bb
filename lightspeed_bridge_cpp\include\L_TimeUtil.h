#pragma once
#if !defined(LS_L_TIMEUTIL_H)
#define LS_L_TIMEUTIL_H

// Copyright (c) 2001-2018 Lightspeed Financial, Inc. All rights reserved.

#include "L_Application.h"


#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

extern "C"
{
	struct _FILETIME;
	__declspec(dllimport) void __stdcall GetSystemTimeAsFileTime(_FILETIME *);
}

namespace LightspeedTrader
{

static long long const l_hn_per_millisecond = 10000LL;

inline time_t L_GetUSEasternMidnight()
{
	static time_t midnight = L_GetApplication()->L_GetUSEasternMidnight();
	return midnight;
}

inline long long L_GetHNUSEasternMidnight()
{
	return L_GetApplication()->L_GetHNUSEasternMidnight();
}

inline long L_GetMillisSinceMidnight()
{
	const long long hn_per_millisecond = 10000LL;
	const long long hn_per_second = 10000000LL;
	const long long hn_nineteenseventy = 116444736000000000LL;
	long long fileTime;

	GetSystemTimeAsFileTime(reinterpret_cast<_FILETIME *>(&fileTime));

	return long((fileTime - L_GetUSEasternMidnight() * hn_per_second - hn_nineteenseventy) / hn_per_millisecond);
}

inline long long L_GetHNTime()
{
	long long fileTime;

	GetSystemTimeAsFileTime(reinterpret_cast<_FILETIME *>(&fileTime));

	return fileTime;
}

inline void L_GetUSEasternTime(unsigned int &h, unsigned int &m, unsigned int &s)
{
	return L_GetApplication()->L_GetUSEasternTime(h, m, s);
}

inline void L_GetUSEasternTm(tm &t)
{
	return L_GetApplication()->L_GetUSEasternTime(t);
}

inline void L_MillisToTm(long millis, tm &t)
{
	return L_GetApplication()->L_MillisToTm(millis, t);
}

}

#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

#endif // !defined(LS_L_TIMEUTIL_H)

