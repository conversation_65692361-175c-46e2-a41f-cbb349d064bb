#!/usr/bin/env python3
"""
Quick diagnostic script for C++ OCR accelerator module.
Run from Windows: python diagnose_cpp_module.py
"""

import sys
import platform
import os

def diagnose_module():
    """Diagnose the C++ module installation and capabilities."""
    print(f"Platform: {platform.system()}")
    print(f"Python: {sys.version}")
    print(f"Architecture: {platform.architecture()}")
    
    # Check if .pyd file exists
    pyd_path = "ocr_accelerator/x64/Release/ocr_accelerator.pyd"
    if os.path.exists(pyd_path):
        size = os.path.getsize(pyd_path)
        print(f"✅ Module file exists: {pyd_path} ({size:,} bytes)")
    else:
        print(f"❌ Module file not found: {pyd_path}")
        return
    
    # Try to import with proper path setup
    try:
        cpp_module_path = os.path.join("ocr_accelerator", "x64", "Release")
        if os.path.exists(cpp_module_path):
            if cpp_module_path not in sys.path:
                sys.path.insert(0, cpp_module_path)
                print(f"✅ Added to sys.path: {os.path.abspath(cpp_module_path)}")
            
            # Add to DLL search path on Windows
            if hasattr(os, 'add_dll_directory'):
                abs_cpp_module_path = os.path.abspath(cpp_module_path)
                os.add_dll_directory(abs_cpp_module_path)
                print(f"✅ Added to DLL search path: {abs_cpp_module_path}")
            else:
                # Fallback: add to PATH environment variable
                current_path = os.environ.get('PATH', '')
                if cpp_module_path not in current_path:
                    os.environ['PATH'] = cpp_module_path + os.pathsep + current_path
                    print(f"✅ Added to PATH: {os.path.abspath(cpp_module_path)}")
        
        import ocr_accelerator
        print("✅ Module imported successfully")
        
        # Check module attributes
        attrs = dir(ocr_accelerator)
        print(f"✅ Module attributes: {attrs}")
        
        # Check for expected functions
        expected_functions = ['process_image_and_ocr', 'test_function']
        for func in expected_functions:
            if hasattr(ocr_accelerator, func):
                print(f"✅ Function '{func}' found")
                
                # Try to call test_function if it exists
                if func == 'test_function':
                    try:
                        result = getattr(ocr_accelerator, func)()
                        print(f"✅ test_function() returned: {result}")
                    except Exception as e:
                        print(f"❌ test_function() failed: {e}")
            else:
                print(f"❌ Function '{func}' not found")
        
        # Check module docstring
        if hasattr(ocr_accelerator, '__doc__') and ocr_accelerator.__doc__:
            print(f"✅ Module doc: {ocr_accelerator.__doc__}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== C++ OCR Accelerator Module Diagnostic ===")
    diagnose_module()