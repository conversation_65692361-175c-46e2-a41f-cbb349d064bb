# tank_multi_baseline_compare.ps1 - Multi-process baseline comparison
param(
    [string]$ProcessPattern = "python",
    [string]$BaselineFile = "tank_multi_baseline.json",
    [int]$MonitorMinutes = 60,
    [string]$ComparisonLogFile = "tank_multi_baseline_comparison.csv",
    [switch]$EnableCSVLogging = $true,
    [switch]$CreateNewBaseline = $false
)

Write-Host "🔍 TANK Multi-Process Baseline Comparison Tool" -ForegroundColor Green
Write-Host "Process Pattern: $ProcessPattern | Monitor Duration: $MonitorMinutes minutes" -ForegroundColor Cyan
Write-Host "Baseline File: $BaselineFile | CSV Logging: $EnableCSVLogging" -ForegroundColor Yellow
Write-Host "=" * 100

# Function to create multi-process baseline
function New-MultiProcessBaseline {
    param([string]$ProcessPattern, [string]$OutputFile)
    
    Write-Host "🔄 Creating multi-process baseline measurements..." -ForegroundColor Yellow
    Write-Host "This will take approximately 5 minutes (10 samples, 30 seconds apart)" -ForegroundColor Gray
    
    $measurements = @()
    $startTime = Get-Date
    
    for ($i = 1; $i -le 10; $i++) {
        try {
            $processes = Get-Process $ProcessPattern -ErrorAction SilentlyContinue
            
            if ($processes.Count -eq 0) {
                Write-Host "  ⚠️  Sample $i/10: No processes found" -ForegroundColor Yellow
                continue
            }
            
            # Calculate aggregate metrics
            $totalMemoryBytes = 0
            $totalHandles = 0
            $totalThreads = 0
            $memoryValues = @()
            
            foreach ($process in $processes) {
                try {
                    $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
                    $memoryValues += $memoryMB
                    $totalMemoryBytes += $process.WorkingSet64
                    $totalHandles += $process.HandleCount
                    $totalThreads += $process.Threads.Count
                } catch {
                    # Skip processes that can't be accessed
                }
            }
            
            $totalMemoryMB = [math]::Round($totalMemoryBytes / 1MB, 2)
            $averageMemoryMB = if ($memoryValues.Count -gt 0) { [math]::Round(($memoryValues | Measure-Object -Average).Average, 2) } else { 0 }
            $maxMemoryMB = if ($memoryValues.Count -gt 0) { [math]::Round(($memoryValues | Measure-Object -Maximum).Maximum, 2) } else { 0 }
            
            $measurement = @{
                Timestamp = Get-Date
                ProcessCount = $processes.Count
                TotalMemoryMB = $totalMemoryMB
                AverageMemoryMB = $averageMemoryMB
                MaxMemoryMB = $maxMemoryMB
                TotalHandles = $totalHandles
                TotalThreads = $totalThreads
            }
            $measurements += $measurement
            
            Write-Host "  Sample $($i.ToString().PadLeft(2))/10: $($processes.Count) processes, $totalMemoryMB MB total, $averageMemoryMB MB avg" -ForegroundColor White
            
            if ($i -lt 10) { Start-Sleep 30 }
        } catch {
            Write-Host "  ❌ Failed to get baseline sample $i`: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    if ($measurements.Count -eq 0) {
        throw "Failed to collect any baseline measurements"
    }
    
    # Calculate comprehensive baseline statistics
    $processCountStats = $measurements | Measure-Object ProcessCount -Average -Maximum -Minimum
    $totalMemoryStats = $measurements | Measure-Object TotalMemoryMB -Average -Maximum -Minimum -StandardDeviation
    $avgMemoryStats = $measurements | Measure-Object AverageMemoryMB -Average -Maximum -Minimum
    $handleStats = $measurements | Measure-Object TotalHandles -Average -Maximum -Minimum
    $threadStats = $measurements | Measure-Object TotalThreads -Average -Maximum -Minimum
    
    $baseline = @{
        CreatedDate = Get-Date
        ProcessPattern = $ProcessPattern
        SampleCount = $measurements.Count
        DurationMinutes = [math]::Round(((Get-Date) - $startTime).TotalMinutes, 1)
        
        # Process count statistics
        AverageProcessCount = [math]::Round($processCountStats.Average, 1)
        MinProcessCount = $processCountStats.Minimum
        MaxProcessCount = $processCountStats.Maximum
        
        # Total memory statistics
        AverageTotalMemoryMB = [math]::Round($totalMemoryStats.Average, 2)
        MinTotalMemoryMB = [math]::Round($totalMemoryStats.Minimum, 2)
        MaxTotalMemoryMB = [math]::Round($totalMemoryStats.Maximum, 2)
        TotalMemoryStdDev = [math]::Round($totalMemoryStats.StandardDeviation, 2)
        TotalMemoryVolatility = [math]::Round(($totalMemoryStats.StandardDeviation / $totalMemoryStats.Average) * 100, 1)
        
        # Average per-process memory statistics
        AveragePerProcessMemoryMB = [math]::Round($avgMemoryStats.Average, 2)
        MinPerProcessMemoryMB = [math]::Round($avgMemoryStats.Minimum, 2)
        MaxPerProcessMemoryMB = [math]::Round($avgMemoryStats.Maximum, 2)
        
        # Resource statistics
        AverageTotalHandles = [math]::Round($handleStats.Average, 0)
        AverageTotalThreads = [math]::Round($threadStats.Average, 0)
        
        # Alert thresholds
        TotalMemoryAlertThresholdMB = [math]::Round($totalMemoryStats.Average * 1.25, 2)  # 25% above average
        TotalMemoryWarningThresholdMB = [math]::Round($totalMemoryStats.Average * 1.15, 2)  # 15% above average
        ProcessCountAlertThreshold = [math]::Round($processCountStats.Average * 1.5, 0)  # 50% above average
        
        # Raw samples
        Samples = $measurements
    }
    
    $baseline | ConvertTo-Json -Depth 3 | Out-File $OutputFile -Encoding UTF8
    Write-Host "✅ Multi-process baseline saved to $OutputFile" -ForegroundColor Green
    Write-Host "   Average Processes: $($baseline.AverageProcessCount)" -ForegroundColor Cyan
    Write-Host "   Average Total Memory: $($baseline.AverageTotalMemoryMB) MB (±$($baseline.TotalMemoryStdDev) MB)" -ForegroundColor Cyan
    Write-Host "   Average Per-Process: $($baseline.AveragePerProcessMemoryMB) MB" -ForegroundColor Cyan
    Write-Host "   Volatility: $($baseline.TotalMemoryVolatility)%" -ForegroundColor Cyan
    
    return $baseline
}

# Load or create baseline
if ($CreateNewBaseline -or -not (Test-Path $BaselineFile)) {
    if ($CreateNewBaseline) {
        Write-Host "🔄 Creating new multi-process baseline as requested..." -ForegroundColor Yellow
    } else {
        Write-Host "📋 No multi-process baseline found. Creating new baseline..." -ForegroundColor Yellow
    }
    
    try {
        $baseline = New-MultiProcessBaseline -ProcessPattern $ProcessPattern -OutputFile $BaselineFile
    } catch {
        Write-Host "❌ Failed to create baseline: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    try {
        $baseline = Get-Content $BaselineFile -Encoding UTF8 | ConvertFrom-Json
        Write-Host "✅ Loaded multi-process baseline from $BaselineFile" -ForegroundColor Green
        Write-Host "   Created: $($baseline.CreatedDate)" -ForegroundColor Cyan
        Write-Host "   Average Processes: $($baseline.AverageProcessCount)" -ForegroundColor Cyan
        Write-Host "   Average Total Memory: $($baseline.AverageTotalMemoryMB) MB (±$($baseline.TotalMemoryStdDev) MB)" -ForegroundColor Cyan
        Write-Host "   Samples: $($baseline.SampleCount) over $($baseline.DurationMinutes) minutes" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ Failed to load baseline: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Creating new baseline..." -ForegroundColor Yellow
        $baseline = New-MultiProcessBaseline -ProcessPattern $ProcessPattern -OutputFile $BaselineFile
    }
}

# Create CSV header if logging enabled
if ($EnableCSVLogging) {
    "Timestamp,ElapsedMinutes,CurrentProcessCount,BaselineProcessCount,ProcessCountDelta,CurrentTotalMemoryMB,BaselineTotalMemoryMB,TotalMemoryDeltaMB,TotalMemoryDeltaPercent,CurrentAvgMemoryMB,BaselineAvgMemoryMB,Status,AlertLevel" | Out-File $ComparisonLogFile -Encoding UTF8
}

Write-Host ""
Write-Host "🔄 Starting multi-process performance comparison..." -ForegroundColor Green
Write-Host "Monitoring for $MonitorMinutes minutes against baseline" -ForegroundColor Cyan
Write-Host "=" * 100

$startTime = Get-Date
$endTime = $startTime.AddMinutes($MonitorMinutes)
$currentMeasurements = @()
$alertCount = 0

while ((Get-Date) -lt $endTime) {
    try {
        $processes = Get-Process $ProcessPattern -ErrorAction SilentlyContinue
        $currentTime = Get-Date
        $elapsed = ($currentTime - $startTime).TotalMinutes
        
        if ($processes.Count -eq 0) {
            Write-Host "⚠️  No processes found matching pattern '$ProcessPattern'" -ForegroundColor Yellow
            Start-Sleep 60
            continue
        }
        
        # Calculate current metrics
        $totalMemoryBytes = 0
        $totalHandles = 0
        $totalThreads = 0
        $memoryValues = @()
        
        foreach ($process in $processes) {
            try {
                $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
                $memoryValues += $memoryMB
                $totalMemoryBytes += $process.WorkingSet64
                $totalHandles += $process.HandleCount
                $totalThreads += $process.Threads.Count
            } catch {
                # Skip processes that can't be accessed
            }
        }
        
        $totalMemoryMB = [math]::Round($totalMemoryBytes / 1MB, 2)
        $averageMemoryMB = if ($memoryValues.Count -gt 0) { [math]::Round(($memoryValues | Measure-Object -Average).Average, 2) } else { 0 }
        $maxMemoryMB = if ($memoryValues.Count -gt 0) { [math]::Round(($memoryValues | Measure-Object -Maximum).Maximum, 2) } else { 0 }
        
        $current = @{
            Timestamp = $currentTime
            ProcessCount = $processes.Count
            TotalMemoryMB = $totalMemoryMB
            AverageMemoryMB = $averageMemoryMB
            MaxMemoryMB = $maxMemoryMB
            TotalHandles = $totalHandles
            TotalThreads = $totalThreads
        }
        $currentMeasurements += $current
        
        # Compare to baseline
        $processCountDelta = $current.ProcessCount - $baseline.AverageProcessCount
        $totalMemoryDelta = $current.TotalMemoryMB - $baseline.AverageTotalMemoryMB
        $totalMemoryDeltaPercent = ($totalMemoryDelta / $baseline.AverageTotalMemoryMB) * 100
        $avgMemoryDelta = $current.AverageMemoryMB - $baseline.AveragePerProcessMemoryMB
        
        # Enhanced status determination
        $alertLevel = "NORMAL"
        $status = "🟢 NORMAL"
        
        if ($current.TotalMemoryMB -gt $baseline.TotalMemoryAlertThresholdMB) {
            $alertLevel = "ALERT"
            $status = "🔴 ALERT"
            $alertCount++
        } elseif ($current.TotalMemoryMB -gt $baseline.TotalMemoryWarningThresholdMB) {
            $alertLevel = "WARNING"
            $status = "🟡 WARNING"
        } elseif ($current.ProcessCount -gt $baseline.ProcessCountAlertThreshold) {
            $alertLevel = "PROCESS_ALERT"
            $status = "🟠 PROCESS_ALERT"
            $alertCount++
        } elseif ($totalMemoryDeltaPercent -lt -20) {
            $alertLevel = "LOW"
            $status = "🔵 LOW"
        }
        
        # Display results
        Clear-Host
        Write-Host "🔍 TANK Multi-Process Baseline Comparison" -ForegroundColor Green
        Write-Host "=" * 100
        Write-Host "Elapsed: $([math]::Round($elapsed, 1)) min | Remaining: $([math]::Round(($endTime - $currentTime).TotalMinutes, 1)) min | Alerts: $alertCount" -ForegroundColor Cyan
        Write-Host ""
        
        Write-Host "📊 PROCESS COUNT COMPARISON:" -ForegroundColor Yellow
        Write-Host "  Current Processes: $($current.ProcessCount)" -ForegroundColor White
        Write-Host "  Baseline Processes: $($baseline.AverageProcessCount)" -ForegroundColor Gray
        Write-Host "  Delta: $(if($processCountDelta -ge 0){'+'})$processCountDelta" -ForegroundColor $(
            if ([math]::Abs($processCountDelta) -gt 2) { "Yellow" } else { "Green" }
        )
        Write-Host ""
        
        Write-Host "💾 TOTAL MEMORY COMPARISON:" -ForegroundColor Yellow
        Write-Host "  Current Total:     $($current.TotalMemoryMB) MB" -ForegroundColor White
        Write-Host "  Baseline Total:    $($baseline.AverageTotalMemoryMB) MB (±$($baseline.TotalMemoryStdDev) MB)" -ForegroundColor Gray
        Write-Host "  Delta:             $(if($totalMemoryDelta -ge 0){'+'})$([math]::Round($totalMemoryDelta, 2)) MB ($(if($totalMemoryDeltaPercent -ge 0){'+'})$([math]::Round($totalMemoryDeltaPercent, 1))%)" -ForegroundColor $(
            if ($totalMemoryDeltaPercent -gt 25) { "Red" } elseif ($totalMemoryDeltaPercent -gt 15) { "Yellow" } else { "Green" }
        )
        Write-Host "  Status:            $status" -ForegroundColor White
        Write-Host ""
        
        Write-Host "📈 PER-PROCESS MEMORY COMPARISON:" -ForegroundColor Yellow
        Write-Host "  Current Average:   $($current.AverageMemoryMB) MB" -ForegroundColor White
        Write-Host "  Baseline Average:  $($baseline.AveragePerProcessMemoryMB) MB" -ForegroundColor Gray
        Write-Host "  Delta:             $(if($avgMemoryDelta -ge 0){'+'})$([math]::Round($avgMemoryDelta, 2)) MB" -ForegroundColor White
        Write-Host "  Largest Process:   $($current.MaxMemoryMB) MB" -ForegroundColor White
        Write-Host ""
        
        Write-Host "🔧 RESOURCE COMPARISON:" -ForegroundColor Yellow
        Write-Host "  Total Handles:     $($current.TotalHandles) (baseline: $($baseline.AverageTotalHandles))" -ForegroundColor White
        Write-Host "  Total Threads:     $($current.TotalThreads) (baseline: $($baseline.AverageTotalThreads))" -ForegroundColor White
        Write-Host ""
        
        # Session trend analysis
        if ($currentMeasurements.Count -ge 5) {
            $sessionTotalGrowth = $current.TotalMemoryMB - $currentMeasurements[0].TotalMemoryMB
            $sessionGrowthRate = if ($elapsed -gt 0) { $sessionTotalGrowth / $elapsed } else { 0 }
            
            Write-Host "📈 SESSION ANALYSIS:" -ForegroundColor Yellow
            Write-Host "  Total Memory Growth: $(if($sessionTotalGrowth -ge 0){'+'})$([math]::Round($sessionTotalGrowth, 2)) MB" -ForegroundColor $(
                if ($sessionTotalGrowth -gt 200) { "Red" } elseif ($sessionTotalGrowth -gt 100) { "Yellow" } else { "Green" }
            )
            Write-Host "  Growth Rate:         $([math]::Round($sessionGrowthRate, 2)) MB/min" -ForegroundColor White
            
            if ($sessionGrowthRate -gt 10) {
                Write-Host "  🚨 WARNING: High system-wide growth rate!" -ForegroundColor Red -BackgroundColor Yellow
            } elseif ($sessionGrowthRate -lt 1 -and $alertLevel -eq "NORMAL") {
                Write-Host "  ✅ GOOD: System-wide memory usage stable" -ForegroundColor Green
            }
        }
        
        # CSV Logging
        if ($EnableCSVLogging) {
            "$($currentTime.ToString('yyyy-MM-dd HH:mm:ss')),$([math]::Round($elapsed, 2)),$($current.ProcessCount),$($baseline.AverageProcessCount),$processCountDelta,$($current.TotalMemoryMB),$($baseline.AverageTotalMemoryMB),$([math]::Round($totalMemoryDelta, 2)),$([math]::Round($totalMemoryDeltaPercent, 2)),$($current.AverageMemoryMB),$($baseline.AveragePerProcessMemoryMB),$status,$alertLevel" | Out-File $ComparisonLogFile -Append -Encoding UTF8
        }
        
        Write-Host ""
        Write-Host "Next sample in 60 seconds... (Ctrl+C to stop early)" -ForegroundColor Gray
        if ($EnableCSVLogging) {
            Write-Host "Data logged to: $ComparisonLogFile" -ForegroundColor Gray
        }
        
        Start-Sleep 60
        
    } catch {
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        Start-Sleep 10
    }
}

Write-Host ""
Write-Host "🎯 MULTI-PROCESS MONITORING COMPLETE - FINAL ANALYSIS" -ForegroundColor Green
Write-Host "=" * 70

if ($currentMeasurements.Count -gt 0) {
    $finalTotalMemory = $currentMeasurements[-1].TotalMemoryMB
    $initialTotalMemory = $currentMeasurements[0].TotalMemoryMB
    $totalGrowth = $finalTotalMemory - $initialTotalMemory
    $avgTotalMemory = ($currentMeasurements | Measure-Object TotalMemoryMB -Average).Average
    
    Write-Host "📊 SESSION SUMMARY:" -ForegroundColor Yellow
    Write-Host "  Initial Total Memory: $initialTotalMemory MB" -ForegroundColor White
    Write-Host "  Final Total Memory:   $finalTotalMemory MB" -ForegroundColor White
    Write-Host "  Total Growth:         $(if($totalGrowth -ge 0){'+'})$([math]::Round($totalGrowth, 2)) MB" -ForegroundColor $(
        if ($totalGrowth -gt 200) { "Red" } elseif ($totalGrowth -gt 100) { "Yellow" } else { "Green" }
    )
    Write-Host "  Session Average:      $([math]::Round($avgTotalMemory, 2)) MB" -ForegroundColor White
    Write-Host ""
    
    $baselineDelta = $avgTotalMemory - $baseline.AverageTotalMemoryMB
    $baselineDeltaPercent = ($baselineDelta / $baseline.AverageTotalMemoryMB) * 100
    
    Write-Host "📈 BASELINE COMPARISON SUMMARY:" -ForegroundColor Yellow
    Write-Host "  vs Baseline Total:    $(if($baselineDelta -ge 0){'+'})$([math]::Round($baselineDelta, 2)) MB ($(if($baselineDeltaPercent -ge 0){'+'})$([math]::Round($baselineDeltaPercent, 1))%)" -ForegroundColor $(
        if ($baselineDeltaPercent -gt 25) { "Red" } elseif ($baselineDeltaPercent -gt 15) { "Yellow" } else { "Green" }
    )
    Write-Host "  Alert Count:          $alertCount" -ForegroundColor $(if($alertCount -gt 0) { "Red" } else { "Green" })
    Write-Host "  Samples Collected:    $($currentMeasurements.Count)" -ForegroundColor White
    
    # System-wide performance assessment
    Write-Host ""
    Write-Host "🏥 SYSTEM-WIDE PERFORMANCE ASSESSMENT:" -ForegroundColor Yellow
    
    if ($alertCount -gt ($currentMeasurements.Count * 0.2)) {
        Write-Host "  🔴 POOR: Frequent system-wide alerts ($alertCount alerts)" -ForegroundColor Red
        Write-Host "  • Investigate system-wide memory usage patterns" -ForegroundColor Red
        Write-Host "  • Consider restarting multiple processes" -ForegroundColor Red
    } elseif ($baselineDeltaPercent -gt 20) {
        Write-Host "  🟡 ELEVATED: System memory significantly above baseline" -ForegroundColor Yellow
        Write-Host "  • Monitor for continued system-wide growth" -ForegroundColor Yellow
        Write-Host "  • Consider updating baseline if this is new normal" -ForegroundColor Yellow
    } elseif ($totalGrowth -gt 100) {
        Write-Host "  🟡 CONCERNING: Significant system-wide growth during session" -ForegroundColor Yellow
        Write-Host "  • Continue monitoring for system-wide leak patterns" -ForegroundColor Yellow
    } else {
        Write-Host "  🟢 GOOD: System-wide performance within acceptable parameters" -ForegroundColor Green
        Write-Host "  • All processes operating normally compared to baseline" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "Multi-process analysis complete! 🎉" -ForegroundColor Green
