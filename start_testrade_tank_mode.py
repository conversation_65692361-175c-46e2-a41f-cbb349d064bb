#!/usr/bin/env python3
"""
TESTRADE TANK MODE Startup Script (Redirect)
This file redirects to the correct startup script.
"""

import sys
import subprocess
import os

def main():
    """Redirect to the correct startup script."""
    print("🔄 Redirecting to start_testrade.py...")
    
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    correct_script = os.path.join(script_dir, "start_testrade.py")
    
    # Check if the correct script exists
    if not os.path.exists(correct_script):
        print(f"❌ ERROR: {correct_script} not found!")
        sys.exit(1)
    
    # Execute the correct script with the same arguments
    try:
        result = subprocess.run([sys.executable, correct_script] + sys.argv[1:], 
                              cwd=script_dir)
        sys.exit(result.returncode)
    except Exception as e:
        print(f"❌ ERROR: Failed to execute {correct_script}: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
