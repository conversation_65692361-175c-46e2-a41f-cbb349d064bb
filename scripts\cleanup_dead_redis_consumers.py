#!/usr/bin/env python3
"""
Cleanup script to remove dead Redis consumers from TESTRADE streams.
This script identifies and removes inactive consumers that weren't properly cleaned up during shutdown.
"""

import redis
import sys
import time
from collections import defaultdict

def connect_to_redis():
    """Connect to Redis server"""
    try:
        r = redis.Redis(host='**************', port=6379, db=0, decode_responses=True)
        r.ping()
        print("✅ Connected to Red<PERSON> successfully")
        return r
    except Exception as e:
        print(f"❌ Error connecting to Redis: {e}")
        return None

def get_active_processes():
    """Get list of currently active process IDs that might be using Redis consumers"""
    import psutil
    active_pids = set()
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                # Look for Python processes that might be TESTRADE components
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if any(keyword in cmdline.lower() for keyword in ['gui_backend', 'testrade', 'babysitter']):
                        active_pids.add(str(proc.info['pid']))
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    except Exception as e:
        print(f"⚠️  Warning: Could not get process list: {e}")
    
    print(f"🔍 Found {len(active_pids)} potentially active TESTRADE processes: {sorted(active_pids)}")
    return active_pids

def analyze_consumers(redis_client):
    """Analyze all consumers and identify dead ones"""
    print("\n🔍 Analyzing Redis consumers...")
    
    # Get all streams
    stream_keys = []
    for key in redis_client.keys('testrade:*'):
        if redis_client.type(key) == 'stream':
            stream_keys.append(key)
    
    print(f"📊 Found {len(stream_keys)} Redis streams")
    
    active_pids = get_active_processes()
    dead_consumers = []
    total_consumers = 0
    
    for stream in sorted(stream_keys):
        try:
            groups = redis_client.xinfo_groups(stream)
            
            for group in groups:
                group_name = group['name']
                consumers = redis_client.xinfo_consumers(stream, group_name)
                total_consumers += len(consumers)
                
                for consumer in consumers:
                    consumer_name = consumer['name']
                    pending = consumer['pending']
                    idle_time = consumer['idle']
                    
                    # Extract PID from consumer name (e.g., gui_consumer_account_summary_12345)
                    pid_from_name = None
                    if '_' in consumer_name:
                        parts = consumer_name.split('_')
                        if parts[-1].isdigit():
                            pid_from_name = parts[-1]
                    
                    # Consider consumer dead if:
                    # 1. PID not in active processes AND
                    # 2. Idle for more than 5 minutes (300,000 ms) OR has been idle for a very long time
                    is_dead = (
                        pid_from_name and 
                        pid_from_name not in active_pids and
                        (idle_time > 300000 or pending == 0)  # 5+ minutes idle or no pending messages
                    )
                    
                    if is_dead:
                        dead_consumers.append({
                            'stream': stream,
                            'group': group_name,
                            'consumer': consumer_name,
                            'pid': pid_from_name,
                            'pending': pending,
                            'idle_hours': round(idle_time / (1000 * 60 * 60), 2)
                        })
                        
        except Exception as e:
            print(f"   ❌ Error checking stream {stream}: {e}")
    
    print(f"\n📈 Analysis Results:")
    print(f"   Total consumers: {total_consumers}")
    print(f"   Dead consumers identified: {len(dead_consumers)}")
    
    return dead_consumers

def cleanup_dead_consumers(redis_client, dead_consumers, dry_run=True):
    """Remove dead consumers from Redis"""
    if not dead_consumers:
        print("✅ No dead consumers to clean up!")
        return True
    
    action = "Would remove" if dry_run else "Removing"
    print(f"\n🧹 {action} {len(dead_consumers)} dead consumers...")
    
    success_count = 0
    error_count = 0
    
    # Group by stream and group for efficient cleanup
    by_stream_group = defaultdict(list)
    for consumer in dead_consumers:
        key = (consumer['stream'], consumer['group'])
        by_stream_group[key].append(consumer)
    
    for (stream, group), consumers in by_stream_group.items():
        print(f"\n📋 {action} {len(consumers)} consumers from {stream} / {group}")
        
        for consumer in consumers:
            consumer_name = consumer['consumer']
            
            if dry_run:
                print(f"   🔍 Would remove: {consumer_name} (PID: {consumer['pid']}, idle: {consumer['idle_hours']}h)")
                success_count += 1
            else:
                try:
                    # Remove consumer from group
                    result = redis_client.xgroup_delconsumer(stream, group, consumer_name)
                    if result:
                        print(f"   ✅ Removed: {consumer_name}")
                        success_count += 1
                    else:
                        print(f"   ⚠️  Consumer {consumer_name} was already removed")
                        success_count += 1
                except Exception as e:
                    print(f"   ❌ Error removing {consumer_name}: {e}")
                    error_count += 1
    
    print(f"\n📊 Cleanup Summary:")
    print(f"   Successful: {success_count}")
    print(f"   Errors: {error_count}")
    
    return error_count == 0

def main():
    print("🧹 TESTRADE Redis Consumer Cleanup Tool")
    print("=" * 50)
    
    # Connect to Redis
    redis_client = connect_to_redis()
    if not redis_client:
        sys.exit(1)
    
    # Analyze consumers
    dead_consumers = analyze_consumers(redis_client)
    
    if not dead_consumers:
        print("\n🎉 No dead consumers found! Redis is clean.")
        return
    
    # Show what would be cleaned up
    print(f"\n📋 Dead consumers to remove:")
    for consumer in dead_consumers[:10]:  # Show first 10
        print(f"   {consumer['consumer']} (PID: {consumer['pid']}, idle: {consumer['idle_hours']}h)")
    
    if len(dead_consumers) > 10:
        print(f"   ... and {len(dead_consumers) - 10} more")
    
    # Ask for confirmation
    print(f"\n⚠️  This will remove {len(dead_consumers)} dead consumers from Redis.")
    print("   This action cannot be undone, but it's safe for dead consumers.")
    
    response = input("\nProceed with cleanup? (yes/no): ").lower().strip()
    
    if response in ['yes', 'y']:
        # Perform actual cleanup
        success = cleanup_dead_consumers(redis_client, dead_consumers, dry_run=False)
        
        if success:
            print("\n🎉 Cleanup completed successfully!")
            
            # Verify cleanup
            print("\n🔍 Verifying cleanup...")
            remaining_dead = analyze_consumers(redis_client)
            if len(remaining_dead) == 0:
                print("✅ All dead consumers removed successfully!")
            else:
                print(f"⚠️  {len(remaining_dead)} consumers still remain (may be active)")
        else:
            print("\n❌ Cleanup completed with some errors. Check the output above.")
    else:
        print("\n❌ Cleanup cancelled by user.")
        
        # Show dry run instead
        print("\n🔍 Dry run - showing what would be removed:")
        cleanup_dead_consumers(redis_client, dead_consumers, dry_run=True)

if __name__ == "__main__":
    main()
