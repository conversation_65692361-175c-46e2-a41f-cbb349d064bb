# Performance Tracking Utilities

This module provides utilities for tracking timestamps and measuring performance throughout the trading system. It allows for granular tracking of execution times at various points in the code to identify bottlenecks and optimize performance.

> **Important**: Performance tracking is **disabled by default** to minimize overhead during normal operation. You must explicitly enable it when needed for performance analysis.

## Enabling Performance Tracking

Before using any of the performance tracking utilities, you need to enable tracking:

```python
from utils.performance_tracker import enable_performance_tracking

# Enable performance tracking
enable_performance_tracking(True)

# Disable it when done
enable_performance_tracking(False)
```

## Overview

The performance tracking utilities provide several ways to measure and analyze the performance of your code:

1. **Timestamp Dictionaries**: Pass a dictionary of timestamps through your code to track when different operations occur.
2. **Performance Trackers**: Use tracker objects to record durations and calculate statistics.
3. **Context Managers**: Use context managers to easily track the duration of code blocks.

## Usage Examples

### Timestamp Dictionaries

```python
from utils.performance_tracker import create_timestamp_dict, add_timestamp, calculate_durations

# Create a timestamp dictionary
timestamps = create_timestamp_dict()  # Adds 'start' timestamp automatically

# Add timestamps at different points in your code
add_timestamp(timestamps, 'data_loading_start')
# ... load data ...
add_timestamp(timestamps, 'data_loading_end')

add_timestamp(timestamps, 'processing_start')
# ... process data ...
add_timestamp(timestamps, 'processing_end')

# Calculate durations between timestamps
durations = calculate_durations(timestamps)
print(durations)
# Output: {'start_to_data_loading_start': 0.001, 'data_loading_start_to_data_loading_end': 0.5, ...}
```

### Performance Trackers

```python
from utils.performance_tracker import get_tracker

# Get a tracker instance
tracker = get_tracker("my_module")

# Record durations
tracker.record_duration("data_loading", 0.5)
tracker.record_duration("data_loading", 0.6)
tracker.record_duration("data_loading", 0.4)

# Get statistics
stats = tracker.get_stats()
print(stats)
# Output: {'data_loading': {'count': 3, 'min': 0.4, 'max': 0.6, 'mean': 0.5, 'median': 0.5, 'std_dev': 0.1}}
```

### Context Managers

```python
from utils.performance_tracker import get_tracker, track_performance

# Using a tracker's context manager
tracker = get_tracker("my_module")
with tracker.track("data_loading"):
    # ... load data ...
    pass

# Using the global context manager
with track_performance("data_processing", "my_module"):
    # ... process data ...
    pass

# Get statistics
stats = tracker.get_stats()
print(stats)
```

## Integration with Modules

The performance tracking utilities are integrated with key modules in the trading system:

### Time and Sales Module

```python
from modules.time_and_sales_module import PriceMeltdownDetector
from utils.performance_tracker import create_timestamp_dict

detector = PriceMeltdownDetector()

# Track performance of quote processing
timestamps = create_timestamp_dict()
detector.process_quote("AAPL", 150.0, 150.1, perf_timestamps=timestamps)
print(timestamps)

# Track performance of trade processing
timestamps = create_timestamp_dict()
detector.process_trade("AAPL", 150.0, 100, perf_timestamps=timestamps)
print(timestamps)

# Track performance of symbol analysis
timestamps = create_timestamp_dict()
result = detector.analyze_symbol("AAPL", perf_timestamps=timestamps)
print(result['perf_timestamps'])
```

### Trade Management Module

```python
from modules.trade_management_module import on_new_quote, on_new_trade
from utils.performance_tracker import create_timestamp_dict

# Track performance of quote handling
timestamps = create_timestamp_dict()
on_new_quote("AAPL", 150.0, 150.1, perf_timestamps=timestamps)
print(timestamps)

# Track performance of trade handling
timestamps = create_timestamp_dict()
on_new_trade("AAPL", 150.0, 100, perf_timestamps=timestamps)
print(timestamps)
```

## Performance Analysis

To analyze the performance data, you can:

1. Print the durations directly to see how long each operation takes.
2. Use the `get_stats()` method to get statistics about recorded durations.
3. Log the performance data to a file for later analysis.
4. Use the example script in `examples/performance_tracking_example.py` to see how to use the utilities.

## Best Practices

1. **Enable Only When Needed**: Performance tracking adds overhead, so only enable it when you're actively analyzing performance.
2. **Disable When Done**: Always disable performance tracking when you're done analyzing to avoid unnecessary overhead.
3. **Be Consistent**: Use consistent naming conventions for timestamp labels.
4. **Be Granular**: Add timestamps at strategic points to identify bottlenecks.
5. **Use Context Managers**: When possible, use context managers for cleaner code.
6. **Reset When Needed**: Use `reset()` or `reset_timestamps()` to clear data when appropriate.
7. **Analyze Regularly**: Regularly analyze performance data to identify trends and issues.
8. **Export Data**: Use `export_performance_stats_to_csv()` to save performance data for offline analysis.
