# Market Data Flow - Nap<PERSON> Sketch

## CURRENT STATE (Broken Publishing)

```
                    🌊 ALPACA FIREHOSE 🌊
                           |
                           | (WebSocket: trades + quotes)
                           |
                           ▼
                 ┌─────────────────────┐
                 │ PriceFetchingService│ ← "The Firehose Consumer"
                 │                     │
                 │ • Subscribes to CSV │
                 │   symbols at start  │
                 │ • receiver = Filter │
                 └──────────┬──────────┘
                            |
                            | on_trade(symbol, price, ...)
                            | on_quote(symbol, bid, ask, ...)
                            |
                            ▼
                 ┌─────────────────────┐
                 │MarketDataFilterSvc  │ ← "The Bouncer"
                 │                     │
                 │ if (ActiveSymbols.  │
                 │     is_relevant(sym))│
                 │   forward()         │
                 │ else                │
                 │   DROP              │
                 └──────────┬──────────┘
                            |
                            | (only relevant symbols)
                            |
                            ▼
                 ┌─────────────────────┐
                 │  PriceRepository    │ ← "The Cache"
                 │                     │
                 │ • Updates cache     │
                 │ • REST fallback     │
                 │ • BUT DOESN'T       │
                 │   PUBLISH! 💀       │
                 └─────────────────────┘
                            
                            ❌ NO CONNECTION!
                            
                 ┌─────────────────────┐
                 │FilteredDataPublisher│ ← "The Publisher"
                 │                     │    (sitting idle!)
                 │ • Has publish method│
                 │ • Sends to Redis    │
                 │ • Via TelemetryService│
                 └─────────────────────┘
                            |
                            | (if it were connected)
                            ▼
                    📡 REDIS STREAMS 📡
                testrade:filtered:market-trades
                testrade:filtered:market-quotes
                            |
                            ▼
                        🖥️ GUI 🖥️
```

## THE MISSING LINK

PriceRepository receives filtered data but has NO REFERENCE to FilteredMarketDataPublisher!

### Where Publishing Should Happen:

```python
# In PriceRepository.on_quote():
def on_quote(self, symbol, bid, ask, ...):
    # Current code:
    if ask > 0:
        self._update_cache(symbol, ask, "stream_ask")
    
    # MISSING:
    if self._market_data_publisher:  # <-- Doesn't exist!
        self._market_data_publisher.publish_market_data(
            symbol=symbol,
            data_type="QUOTE",
            payload_dict={...},
            source_event_metadata={...}
        )
```

## DEPENDENCY TREE

```
DI Container creates:
│
├─► ActiveSymbolsService
│   └─ Tracks what symbols matter
│
├─► FilteredMarketDataPublisher ✓
│   └─ Ready to publish
│
├─► PriceRepository (IPriceProvider) ✓
│   ├─ Has: REST client, config
│   └─ Missing: Publisher reference! ❌
│
├─► MarketDataFilterService ✓
│   ├─ Has: ActiveSymbolsService ref ✓
│   ├─ Has: PriceRepository ref ✓
│   └─ Has: Publisher ref ✓ (but doesn't use it!)
│
└─► PriceFetchingService ✓
    └─ Has: MarketDataFilterService as receiver ✓
```

## WHY IT'S BROKEN

1. **Wiring Issue**: PriceRepository is created WITHOUT a publisher reference
2. **Unused Injection**: MarketDataFilterService HAS the publisher but doesn't forward data to it
3. **Design Mismatch**: PriceRepository acts as end-of-line when it should be a passthrough

## QUICK FIX OPTIONS

### A) Inject Publisher into PriceRepository
```python
# In di_registration.py:
def price_repository_factory(container):
    return PriceRepository(
        config_service=config,
        rest_client=alpaca_rest_client,
        market_data_publisher=container.resolve(IMarketDataPublisher)  # ADD THIS
    )
```

### B) Make Filter Service Forward to BOTH
```python
# In MarketDataFilterService:
def on_quote(self, ...):
    if self.active_symbols_service.is_relevant_for_publishing(symbol):
        self.price_repository.on_quote(...)  # Cache update
        if self.market_data_publisher:        # ADD THIS
            self.market_data_publisher.publish_market_data(...)  # Publish
```

### C) Create Composite Receiver
```python
class CompositeMarketDataReceiver:
    def __init__(self, receivers: List[IMarketDataReceiver]):
        self.receivers = receivers
    
    def on_quote(self, ...):
        for receiver in self.receivers:
            receiver.on_quote(...)
```

## THE REAL ISSUE

The current architecture treats PriceRepository as a TERMINAL node when it should be either:
1. A passthrough node (receives → caches → forwards)
2. A sibling node (filter forwards to both cache AND publisher)

The "warehouse model" suggests option #1 is cleaner - PriceRepository becomes the central warehouse that both stores AND ships data.