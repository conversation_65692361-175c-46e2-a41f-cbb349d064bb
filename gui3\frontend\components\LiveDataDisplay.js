/**
 * Live Data Display Component
 * Auto-updates when receiving data from backend
 */

import { CommandService } from '../services/CommandService.js';

export class LiveDataDisplay extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
        this.commandService = CommandService.getInstance();
        this.dataType = this.getAttribute('data-type') || 'all';
    }

    connectedCallback() {
        this.render();
        this.subscribeToUpdates();
    }

    disconnectedCallback() {
        this.unsubscribeFromUpdates();
    }

    render() {
        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    display: block;
                    background: #1a1a1a;
                    border-radius: 8px;
                    padding: 15px;
                    color: white;
                }
                
                .header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;
                }
                
                .title {
                    font-size: 18px;
                    font-weight: bold;
                }
                
                .status {
                    font-size: 12px;
                    padding: 2px 8px;
                    border-radius: 4px;
                    background: #28a745;
                }
                
                .content {
                    font-family: monospace;
                    font-size: 14px;
                    white-space: pre-wrap;
                    max-height: 400px;
                    overflow-y: auto;
                }
                
                .update-time {
                    font-size: 10px;
                    color: #888;
                    margin-top: 5px;
                }
            </style>
            
            <div class="header">
                <div class="title">${this.dataType.toUpperCase()} Data</div>
                <div class="status">LIVE</div>
            </div>
            <div class="content">Waiting for data...</div>
            <div class="update-time"></div>
        `;
    }

    subscribeToUpdates() {
        this.messageHandler = (message) => {
            if (this.shouldDisplayMessage(message)) {
                this.updateDisplay(message);
            }
        };
        
        this.commandService.on('message', this.messageHandler);
    }

    unsubscribeFromUpdates() {
        if (this.messageHandler) {
            this.commandService.off('message', this.messageHandler);
        }
    }

    shouldDisplayMessage(message) {
        if (this.dataType === 'all') return true;
        return message.type === this.dataType;
    }

    updateDisplay(message) {
        const content = this.shadowRoot.querySelector('.content');
        const updateTime = this.shadowRoot.querySelector('.update-time');
        
        // Format the message
        const formatted = this.formatMessage(message);
        
        // Update content (hot reload)
        content.textContent = formatted;
        
        // Update timestamp
        updateTime.textContent = `Last update: ${new Date().toLocaleTimeString()}`;
        
        // Flash effect
        content.style.animation = 'flash 0.3s';
        setTimeout(() => {
            content.style.animation = '';
        }, 300);
    }

    formatMessage(message) {
        // Custom formatting based on message type
        switch (message.type) {
            case 'position_update':
                return this.formatPosition(message);
            case 'ocr_update':
                return this.formatOCR(message);
            case 'account_update':
                return this.formatAccount(message);
            default:
                return JSON.stringify(message, null, 2);
        }
    }

    formatPosition(data) {
        return `Symbol: ${data.symbol}
Quantity: ${data.quantity}
Avg Price: $${data.average_price?.toFixed(2)}
P&L: $${data.realized_pnl?.toFixed(2)}
Status: ${data.is_open ? 'OPEN' : 'CLOSED'}`;
    }

    formatOCR(data) {
        return `Frame: ${data.frame_number}
Confidence: ${(data.confidence * 100).toFixed(1)}%
Positions: ${data.position_count}
${data.positions.map(p => `  ${p.symbol}: ${p.shares} @ $${p.cost_basis}`).join('\n')}`;
    }

    formatAccount(data) {
        return `Account: ${data.account_id}
Equity: $${data.equity?.toFixed(2)}
Buying Power: $${data.buying_power?.toFixed(2)}
Cash: $${data.cash?.toFixed(2)}
Day P&L: $${data.realized_pnl_day?.toFixed(2)}`;
    }
}

customElements.define('live-data-display', LiveDataDisplay);