- Check the windows side
- GUI, GUI backend should never get data from core...just REDIS
- Unless explicitly told, don't start any processes like headless core or baby sitter unless told...when you start it in Linux, it is in the background and holds the ports and causes crashes due to conflicts!
- Claude code runs in WSL. Redis is in WSL. Rest of code is in Windows.

# TESTRADE Clean Architecture Documentation

## 🏗️ **CRITICAL: Architectural Integrity Guidelines**

**This section documents the clean architecture achieved after the major refactoring from 197KB ApplicationCore to 11KB. Follow these guidelines religiously to prevent regression.**

### 🚫 **FORBIDDEN ANTI-PATTERNS** 

**Never introduce these patterns - they were eliminated during the clean architecture refactor:**

1. **Service Locator Anti-Pattern**
   ```python
   # ❌ FORBIDDEN - Service locator pattern
   def get_service(self, service_type):
       return self._di_container.resolve(service_type)
   
   # ❌ FORBIDDEN - Calling service locator
   some_service = app_core.get_service(ISomeService)
   ```

2. **ApplicationCore God Object Pattern**
   ```python
   # ❌ FORBIDDEN - Direct ApplicationCore dependency injection
   def __init__(self, app_core: ApplicationCore):
       self._app_core = app_core
   
   # ❌ FORBIDDEN - Using ApplicationCore for config access
   config_value = self._app_core.config.some_setting
   
   # ❌ FORBIDDEN - Using ApplicationCore for Redis publishing
   self._app_core._create_redis_message_json(...)
   self._app_core.babysitter_ipc_client.publish(...)
   ```

3. **Manual Service Wiring in ApplicationCore**
   ```python
   # ❌ FORBIDDEN - Manual service instantiation in ApplicationCore
   self.risk_service = RiskService(self.config, self.event_bus)
   self.trade_executor = TradeExecutor(self.broker, self.risk_service)
   ```

### ✅ **REQUIRED PATTERNS - Clean Architecture**

**Always use these patterns:**

1. **Pure Constructor Dependency Injection**
   ```python
   # ✅ REQUIRED - Interface-based dependency injection
   def __init__(self, 
                config_service: IConfigService,
                telemetry_service: ITelemetryService,
                event_bus: IEventBus):
       self._config = config_service
       self._telemetry_service = telemetry_service
       self._event_bus = event_bus
   ```

2. **DI Container Service Registration**
   ```python
   # ✅ REQUIRED - Use service factories in di_registration.py
   def register_all_services(container: DIContainer):
       container.register_factory(ISomeService, some_service_factory)
   
   def some_service_factory(container: DIContainer):
       return SomeService(
           config_service=container.resolve(IConfigService),
           telemetry_service=container.resolve(ITelemetryService)
       )
   ```

3. **Telemetry Service for All Publishing**
   ```python
   # ✅ REQUIRED - Use telemetry service for Redis publishing
   success = self._telemetry_service.enqueue(
       source_component="ServiceName",
       event_type="EVENT_TYPE",
       payload=event_data,
       correlation_id=correlation_id,
       stream_override=target_stream
   )
   ```

4. **ServiceLifecycleManager for Orchestration**
   ```python
   # ✅ REQUIRED - Let ServiceLifecycleManager handle complex startup
   lifecycle_manager = container.resolve(IServiceLifecycleManager)
   lifecycle_manager.start_all_services()
   ```

## 🎯 **Current Clean Architecture (POST-REFACTOR)**

### **ApplicationCore (11KB - Lean Coordinator)**
- **Role**: Minimal startup coordinator only
- **Dependencies**: External DI container (ideal pattern)
- **Responsibilities**:
  - Initialize ServiceLifecycleManager  
  - Delegate all service management
  - Provide `is_ready` property for compatibility
- **Anti-Pattern Eliminated**: No `get_service()` method
- **Size**: ~60 lines of actual logic

### **ServiceLifecycleManager (The Real Orchestrator)**
- **Role**: Complete service lifecycle management
- **Responsibilities**:
  - Multi-phase startup orchestration (7 phases)
  - Dependency-ordered service startup/shutdown
  - Service health monitoring and readiness checking
  - Thread health verification for critical services
- **Pattern**: Proper separation of concerns

### **Entry Points (main.py & run_headless_core.py)**
```python
# ✅ IDEAL PATTERN - 100% DI Container
def main():
    container = DIContainer()
    register_all_services(container)
    app_core = container.resolve(DI_ApplicationCore)
    app_core.start()
    # Event loop...
```

### **Service Architecture Principles**

1. **Interface Segregation**: All services depend on `I*Service` interfaces
2. **Dependency Inversion**: Services receive dependencies via constructor
3. **Single Responsibility**: Each service has one clear purpose
4. **Telemetry Centralization**: All Redis publishing via `ITelemetryService`
5. **Configuration Injection**: Services receive `IConfigService`, not ApplicationCore

## 🔍 **Code Review Checklist**

**Before merging any PR, verify:**

- [ ] No `get_service()` calls anywhere in production code
- [ ] No direct ApplicationCore dependencies in service constructors  
- [ ] No `app_core.config` references (use injected `IConfigService`)
- [ ] No `_create_redis_message_json` or `babysitter_ipc_client` usage
- [ ] All Redis publishing goes through `telemetry_service.enqueue()`
- [ ] New services registered in `di_registration.py` with proper factories
- [ ] Service constructors only accept interface dependencies
- [ ] ApplicationCore remains under 15KB (current: 11KB)

## 📈 **Metrics to Track**

**Regression Indicators:**
- ApplicationCore file size > 15KB = **REGRESSION ALERT** 
- Any `get_service` method existence = **ANTI-PATTERN ALERT**
- Direct ApplicationCore dependencies = **COUPLING ALERT**

**Health Indicators:**
- All services use constructor DI = ✅ **HEALTHY**
- ServiceLifecycleManager handles orchestration = ✅ **HEALTHY** 
- Telemetry service centralizes publishing = ✅ **HEALTHY**

## 🚀 **Architecture Benefits Achieved**

1. **Testability**: Services can be unit tested in isolation
2. **Maintainability**: Clear separation of concerns and dependencies  
3. **Flexibility**: Easy to swap implementations via DI container
4. **Scalability**: ServiceLifecycleManager handles complex orchestration
5. **Debugging**: Clean dependency chains and error isolation
6. **Onboarding**: Self-documenting through interface contracts

## ⚠️ **When Adding New Services**

1. **Define Interface First**: Create `I*Service` interface
2. **Constructor DI Only**: Accept interfaces, not concrete types
3. **Register in DI**: Add factory to `di_registration.py`
4. **Use Telemetry**: Publish events via `ITelemetryService.enqueue()`
5. **No ApplicationCore**: Never inject or reference ApplicationCore directly

**Remember: This architecture represents months of refactoring work. Protect it vigilantly!**

- All .md files of fixes go in the docs/fixes folder