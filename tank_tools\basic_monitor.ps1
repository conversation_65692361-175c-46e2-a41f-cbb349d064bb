# basic_monitor.ps1 - Very simple file monitor
param(
    [string]$Path = "C:\TESTRADE\data",
    [int]$Interval = 5,
    [int]$Duration = 3
)

Write-Host "🔍 Basic File Monitor" -ForegroundColor Green
Write-Host "Path: $Path" -ForegroundColor Cyan
Write-Host "Interval: $Interval seconds, Duration: $Duration minutes" -ForegroundColor Cyan
Write-Host "=" * 50

$startTime = Get-Date
$endTime = $startTime.AddMinutes($Duration)
$previousSizes = @{}
$totalGrowth = 0
$intervalCount = 0

while ((Get-Date) -lt $endTime) {
    $intervalCount++
    $currentTime = Get-Date
    $elapsed = [math]::Round(($currentTime - $startTime).TotalMinutes, 1)
    
    # Check for TESTRADE process
    $testradeRunning = $false
    try {
        $pythonProcs = Get-Process python -ErrorAction SilentlyContinue
        foreach ($proc in $pythonProcs) {
            $cmd = (Get-CimInstance Win32_Process -Filter "ProcessId = $($proc.Id)" -ErrorAction SilentlyContinue).CommandLine
            if ($cmd -like "*testrade*" -or $cmd -like "*ApplicationCore*") {
                $testradeRunning = $true
                break
            }
        }
    } catch {
        # Process check failed
    }
    
    # Check files
    $currentFiles = @{}
    $intervalGrowth = 0
    $newFiles = 0
    $modifiedFiles = 0
    
    try {
        if (Test-Path $Path) {
            Get-ChildItem -Path $Path -Recurse -File -ErrorAction SilentlyContinue | ForEach-Object {
                $currentFiles[$_.FullName] = $_.Length
                
                if ($previousSizes.ContainsKey($_.FullName)) {
                    $growth = $_.Length - $previousSizes[$_.FullName]
                    if ($growth -gt 0) {
                        $intervalGrowth += $growth
                        $modifiedFiles++
                    }
                } else {
                    $intervalGrowth += $_.Length
                    $newFiles++
                }
            }
        }
    } catch {
        Write-Host "File access error" -ForegroundColor Red
    }
    
    $previousSizes = $currentFiles
    $totalGrowth += $intervalGrowth
    
    # Calculate rates
    $growthMB = [math]::Round($intervalGrowth / 1MB, 3)
    $growthKB = [math]::Round($intervalGrowth / 1KB, 1)
    $rateKBps = [math]::Round($growthKB / $Interval, 1)
    $rateMBps = [math]::Round($growthMB / $Interval, 3)
    
    # Display results
    Clear-Host
    Write-Host "🔍 File Monitor - Interval $intervalCount" -ForegroundColor Green
    Write-Host "Time: $($currentTime.ToString('HH:mm:ss')) | Elapsed: $elapsed min" -ForegroundColor Cyan
    
    Write-Host "TESTRADE Status: " -NoNewline
    if ($testradeRunning) {
        Write-Host "🟢 RUNNING" -ForegroundColor Green
    } else {
        Write-Host "🔴 NOT FOUND" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "📊 INTERVAL RESULTS:" -ForegroundColor Yellow
    Write-Host "  Data Growth: $growthMB MB ($growthKB KB)" -ForegroundColor White
    Write-Host "  Growth Rate: $rateMBps MB/s ($rateKBps KB/s)" -ForegroundColor White
    Write-Host "  New Files: $newFiles" -ForegroundColor White
    Write-Host "  Modified Files: $modifiedFiles" -ForegroundColor White
    Write-Host "  Total Files: $($currentFiles.Count)" -ForegroundColor White
    
    Write-Host ""
    $totalMB = [math]::Round($totalGrowth / 1MB, 2)
    Write-Host "📈 SESSION TOTALS:" -ForegroundColor Yellow
    Write-Host "  Total Growth: $totalMB MB" -ForegroundColor White
    
    if ($elapsed -gt 0) {
        $sessionRate = [math]::Round($totalMB / $elapsed, 2)
        Write-Host "  Session Rate: $sessionRate MB/min" -ForegroundColor White
    }
    
    # Simple alerts
    Write-Host ""
    if ($rateMBps -gt 10) {
        Write-Host "🚨 HIGH OUTPUT RATE DETECTED!" -ForegroundColor Red
    }
    if ($rateMBps -gt 5 -and $rateMBps -le 10) {
        Write-Host "⚠️  Elevated output rate" -ForegroundColor Yellow
    }
    if ($rateMBps -le 5) {
        Write-Host "✅ Normal output rate" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "Next check in $Interval seconds... (Ctrl+C to stop)" -ForegroundColor Gray
    
    Start-Sleep $Interval
}

# Final summary
Write-Host ""
Write-Host "📋 MONITORING COMPLETE!" -ForegroundColor Green
$finalMB = [math]::Round($totalGrowth / 1MB, 2)
$finalMinutes = [math]::Round(((Get-Date) - $startTime).TotalMinutes, 1)

Write-Host "Results:" -ForegroundColor White
Write-Host "  Total file growth: $finalMB MB" -ForegroundColor White
Write-Host "  Session duration: $finalMinutes minutes" -ForegroundColor White
Write-Host "  Intervals completed: $intervalCount" -ForegroundColor White

if ($finalMinutes -gt 0) {
    $avgRate = [math]::Round($finalMB / $finalMinutes, 2)
    Write-Host "  Average rate: $avgRate MB/min" -ForegroundColor White
}

Write-Host ""
Write-Host "File monitoring completed successfully!" -ForegroundColor Green
