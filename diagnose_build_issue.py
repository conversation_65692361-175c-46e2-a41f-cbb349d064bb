#!/usr/bin/env python3
"""
Diagnose why the rebuilt module still has no functions.
"""

import os
from datetime import datetime

def diagnose_build_issue():
    """Check various aspects of the build to identify the issue."""
    print("=== Diagnosing Build Issue ===")
    
    # Check project file
    project_file = "ocr_accelerator/ocr_accelerator.vcxproj"
    print(f"\n1. Checking project file: {project_file}")
    
    if os.path.exists(project_file):
        with open(project_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if our fix is in place
        if "COMMENTED OUT TO FIX PYBIND11 EXPORTS" in content:
            print("✅ Project file fix is present")
        else:
            print("❌ Project file fix is NOT present")
            
        # Check for any remaining ModuleDefinitionFile references
        if "<ModuleDefinitionFile>" in content and "<!--" not in content.split("<ModuleDefinitionFile>")[1].split("</ModuleDefinitionFile>")[0]:
            print("❌ Active ModuleDefinitionFile still found in project")
        else:
            print("✅ No active ModuleDefinitionFile found")
    
    # Check the .pyd file
    pyd_path = "ocr_accelerator/x64/Release/ocr_accelerator.pyd"
    print(f"\n2. Checking .pyd file: {pyd_path}")
    
    if os.path.exists(pyd_path):
        stat = os.stat(pyd_path)
        mod_time = datetime.fromtimestamp(stat.st_mtime)
        size = stat.st_size
        print(f"✅ File exists")
        print(f"📅 Last modified: {mod_time}")
        print(f"📏 Size: {size} bytes")
        
        # Check if it was built recently (within last hour)
        now = datetime.now()
        age_minutes = (now - mod_time).total_seconds() / 60
        if age_minutes < 60:
            print(f"🕒 File is recent ({age_minutes:.1f} minutes old)")
        else:
            print(f"⚠️ File might be old ({age_minutes:.1f} minutes old)")
    else:
        print("❌ .pyd file not found")
    
    # Check build log
    log_path = "ocr_accelerator/ocr_accelerator/x64/Release/ocr_accelerator.log"
    print(f"\n3. Checking build log: {log_path}")
    
    if os.path.exists(log_path):
        print("✅ Build log exists")
        with open(log_path, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        # Look for key indicators
        if "exports.def" in log_content:
            print("⚠️ Build log mentions exports.def - check if VS used cached settings")
        
        if "error" in log_content.lower():
            print("❌ Errors found in build log")
            # Show last few lines with errors
            lines = log_content.split('\n')
            error_lines = [line for line in lines if 'error' in line.lower()]
            for line in error_lines[-3:]:  # Show last 3 error lines
                print(f"   ERROR: {line.strip()}")
        else:
            print("✅ No errors in build log")
            
        # Check if linking succeeded
        if "ocr_accelerator.pyd" in log_content and "Successfully" in log_content:
            print("✅ Linking appears successful")
        else:
            print("⚠️ Linking status unclear")
    else:
        print("❌ Build log not found")
    
    # Check C++ source
    cpp_file = "ocr_accelerator/ocr_accelerator.cpp"
    print(f"\n4. Checking C++ source: {cpp_file}")
    
    if os.path.exists(cpp_file):
        with open(cpp_file, 'r', encoding='utf-8') as f:
            cpp_content = f.read()
        
        if "PYBIND11_MODULE(ocr_accelerator, m)" in cpp_content:
            print("✅ PYBIND11_MODULE definition found")
        else:
            print("❌ PYBIND11_MODULE definition NOT found")
            
        if "test_function" in cpp_content:
            print("✅ test_function definition found")
        else:
            print("❌ test_function definition NOT found")
            
        if "process_image_and_ocr" in cpp_content:
            print("✅ process_image_and_ocr definition found")
        else:
            print("❌ process_image_and_ocr definition NOT found")
    
    print(f"\n=== RECOMMENDATIONS ===")
    print("1. Try 'Clean Solution' then 'Rebuild Solution' in Visual Studio")
    print("2. Check if Visual Studio is using the correct configuration (Release x64)")
    print("3. Verify the project was actually rebuilt and not using cached files")
    print("4. If still failing, try manually deleting all build artifacts and rebuilding")

if __name__ == "__main__":
    diagnose_build_issue()