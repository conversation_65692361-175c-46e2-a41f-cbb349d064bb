#!/bin/bash
# Safe commit script to prevent work loss

echo "🛡️ Safe Commit Script - Protecting your work"
echo "==========================================="

# Check for untracked files
echo "📋 Checking for untracked files..."
untracked=$(git ls-files --others --exclude-standard)
if [ -n "$untracked" ]; then
    echo "⚠️  Found untracked files:"
    echo "$untracked" | head -20
    echo ""
    read -p "Add all untracked files? (y/n): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git add .
        echo "✅ Added all files"
    fi
fi

# Show what will be committed
echo ""
echo "📝 Files to be committed:"
git status --short

# Get commit message
echo ""
read -p "Enter commit message: " commit_msg

# Commit and push
git commit -m "$commit_msg"
git push

echo ""
echo "✅ Work safely committed and pushed!"
echo "🛡️ Your work is now protected in git"