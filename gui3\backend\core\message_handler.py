"""
Base message handler system for GUI backend.
Follows the Horseshoe Architecture pattern.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Type
import logging
import json
from datetime import datetime

from .horseshoe_architecture import HorseshoeConsumer


class MessageHandler(HorseshoeConsumer, ABC):
    """
    Base class for all Redis stream message handlers in the GUI backend.
    
    As a Horseshoe Consumer, this class ONLY READS from Redis streams
    and sends commands through designated command streams.
    """
    
    def __init__(self, stream_name: str, app_state: Any):
        super().__init__(f"GUI.{stream_name}")
        self.stream_name = stream_name
        self.app_state = app_state
        self.processed_count = 0
        self.error_count = 0
        
    async def consume_stream(self, stream_name: str, message: Dict[str, Any]) -> None:
        """
        Main entry point for processing messages from Redis streams.
        Implements the HorseshoeConsumer interface.
        """
        try:
            message_id = message.get('id', 'unknown')
            data = message.get('data', {})
            
            self.logger.debug(f"Processing message {message_id} from {stream_name}")
            
            # Pre-process the message
            processed_data = await self.pre_process(data)
            
            # Handle the message (implemented by subclasses)
            result = await self.handle(message_id, processed_data)
            
            # Post-process and broadcast to GUI
            if result:
                await self.post_process(result)
                
            self.processed_count += 1
            
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"Error processing message from {stream_name}: {e}", exc_info=True)
            await self.handle_error(message, e)
    
    async def pre_process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Pre-process the message data. Override in subclasses if needed."""
        return data
    
    @abstractmethod
    async def handle(self, message_id: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Handle the message. Must be implemented by subclasses.
        Returns data to be broadcast to GUI clients.
        """
        pass
    
    async def post_process(self, result: Dict[str, Any]) -> None:
        """Post-process and broadcast result to GUI clients."""
        if self.app_state and hasattr(self.app_state, 'ws_manager'):
            await self.app_state.ws_manager.broadcast(result)
    
    async def handle_error(self, message: Dict[str, Any], error: Exception) -> None:
        """Handle errors during message processing."""
        error_data = {
            'type': 'error',
            'source': self.stream_name,
            'error': str(error),
            'message_id': message.get('id', 'unknown'),
            'timestamp': datetime.utcnow().isoformat()
        }
        await self.post_process(error_data)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get handler statistics."""
        return {
            'stream': self.stream_name,
            'processed': self.processed_count,
            'errors': self.error_count,
            'error_rate': self.error_count / max(1, self.processed_count)
        }


class MessageRouter:
    """
    Routes messages from Redis streams to appropriate handlers.
    Central component for managing all message handlers.
    """
    
    def __init__(self, app_state: Any):
        self.app_state = app_state
        self.handlers: Dict[str, MessageHandler] = {}
        self.logger = logging.getLogger("GUI.MessageRouter")
        
    def register_handler(self, stream_name: str, handler_class: Type[MessageHandler]) -> None:
        """Register a handler for a specific stream."""
        if stream_name in self.handlers:
            self.logger.warning(f"Handler for {stream_name} already registered, replacing")
        
        handler = handler_class(stream_name, self.app_state)
        self.handlers[stream_name] = handler
        self.logger.info(f"Registered handler {handler_class.__name__} for stream {stream_name}")
    
    async def route_message(self, stream_name: str, message: Dict[str, Any]) -> None:
        """Route a message to the appropriate handler."""
        handler = self.handlers.get(stream_name)
        
        if handler:
            await handler.consume_stream(stream_name, message)
        else:
            self.logger.warning(f"No handler registered for stream {stream_name}")
            # Optionally use a default handler
            if hasattr(self, 'default_handler'):
                await self.default_handler.consume_stream(stream_name, message)
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all handlers."""
        return {
            stream: handler.get_stats()
            for stream, handler in self.handlers.items()
        }
    
    def get_registered_streams(self) -> List[str]:
        """Get list of all registered streams."""
        return list(self.handlers.keys())