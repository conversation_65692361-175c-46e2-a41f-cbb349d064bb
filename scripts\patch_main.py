import tracemalloc
import time
import os
import sys
import logging
import psutil

# Configure logging
logging.basicConfig(level=logging.ERROR, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# Create mem leak directory if it doesn't exist
os.makedirs('mem leak', exist_ok=True)

# Get the current process
process = psutil.Process()

# Start tracemalloc
tracemalloc.start()

# Take initial snapshot and record RSS
initial_snapshot = tracemalloc.take_snapshot()
initial_rss = process.memory_info().rss
logger.error(f"PATCH_MAIN: Initial RSS: {initial_rss / (1024 * 1024):.2f} MB")

# Record start time
start_time = time.time()

# The main script will run here
# ...

# After the main script finishes (this will be executed when the script exits)
def on_exit():
    # Take final snapshot and record RSS
    final_snapshot = tracemalloc.take_snapshot()
    final_rss = process.memory_info().rss

    # Calculate elapsed time
    elapsed_time = time.time() - start_time

    # Calculate RSS growth
    rss_growth = final_rss - initial_rss

    # Log results
    logger.error(f"PATCH_MAIN: Final RSS: {final_rss / (1024 * 1024):.2f} MB")
    logger.error(f"PATCH_MAIN: RSS Growth: {rss_growth / (1024 * 1024):.2f} MB")
    logger.error(f"PATCH_MAIN: Elapsed Time: {elapsed_time:.2f} seconds")

    # Compare snapshots and get top statistics
    stats = final_snapshot.compare_to(initial_snapshot, 'lineno')

    # Write results to file
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    with open(os.path.join('mem leak', f'native_isolate19_tracemalloc_{timestamp}.log'), 'w') as f:
        f.write(f"NATIVE-ISOLATE19: Tracemalloc Results\n")
        f.write(f"Initial RSS: {initial_rss / (1024 * 1024):.2f} MB\n")
        f.write(f"Final RSS: {final_rss / (1024 * 1024):.2f} MB\n")
        f.write(f"RSS Growth: {rss_growth / (1024 * 1024):.2f} MB\n")
        f.write(f"Elapsed Time: {elapsed_time:.2f} seconds\n\n")

        f.write("Top 100 memory allocations by size:\n")
        for stat in stats[:100]:
            f.write(f"{stat.size_diff / 1024:.1f} KB: {stat.traceback.format()[0]}\n")
            for line in stat.traceback.format()[1:]:
                f.write(f"    {line}\n")
            f.write("\n")

# Register the on_exit function to be called when the script exits
import atexit
atexit.register(on_exit)
