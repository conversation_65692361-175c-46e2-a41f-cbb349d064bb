@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700;800&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --bg-primary: #0a0a0f;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-glass: rgba(255, 255, 255, 0.03);
    --accent-primary: #00ff88;
    --accent-secondary: #0088ff;
    --accent-danger: #ff4466;
    --accent-warning: #ffaa00;
    --accent-purple: #8b5cf6;
    --accent-success: #00ff88;
    --accent-info: #0088ff;
    --text-primary: #ffffff;
    --text-secondary: #b8bcc8;
    --text-muted: #6b7280;
    --border-subtle: rgba(0, 255, 136, 0.2);
    --border-bright: rgba(0, 255, 136, 0.5);
    --glow-primary: 0 0 20px rgba(0, 255, 136, 0.3);
    --glow-danger: 0 0 20px rgba(255, 68, 102, 0.3);
}

body {
    font-family: 'JetBrains Mono', 'Monaco', 'Consolas', monospace;
    background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
    color: var(--text-primary);
    min-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
}

/* Animated matrix background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 1px 1px, rgba(0, 255, 136, 0.15) 1px, transparent 0);
    background-size: 40px 40px;
    animation: matrixRain 30s linear infinite;
    z-index: -1;
}

@keyframes matrixRain {
    0% { transform: translateY(-40px); }
    100% { transform: translateY(100vh); }
}

/* Compact Portrait Layout */
.main-container {
    display: grid;
    grid-template-areas:
        "header"
        "content"
        "footer";
    grid-template-rows: 60px auto 40px;
    min-height: 100vh;
    gap: 6px;
    padding: 6px;
}

/* Compact Header */
.header {
    grid-area: header;
    background: var(--bg-glass);
    backdrop-filter: blur(25px);
    border: 1px solid var(--border-subtle);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--glow-primary);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo {
    font-size: 24px;
    font-weight: 800;
    color: var(--accent-primary);
    text-transform: uppercase;
    letter-spacing: 3px;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.7);
}

.intellisense-badge {
    background: linear-gradient(45deg, var(--accent-purple), var(--accent-secondary));
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.status-indicators {
    display: flex;
    gap: 15px;
    align-items: center;
}

.status-pill {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: var(--bg-glass);
    border: 1px solid var(--border-subtle);
    border-radius: 20px;
    font-size: 10px;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--accent-primary);
    box-shadow: 0 0 10px var(--accent-primary);
    animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.2); }
}

/* Content Area - Stacked Layout */
.content-area {
    grid-area: content;
    display: grid;
    grid-template-columns: calc(100% - 224px) 220px;
    gap: 4px;
    overflow: visible;
}

/* Main Content - Stacked Panels */
.main-content {
    display: grid;
    grid-template-rows: 240px 200px 180px auto auto;
    gap: 8px;
}

/* Panel Base Styles */
.panel {
    background: var(--bg-glass);
    backdrop-filter: blur(25px);
    border: 1px solid var(--border-subtle);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
}

.panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
    opacity: 0.8;
}

.panel-header {
    padding: 12px 16px 8px 16px;
    border-bottom: 1px solid var(--border-subtle);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(0, 0, 0, 0.3);
}

.panel-title {
    font-size: 12px;
    font-weight: 800;
    color: var(--accent-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.panel-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    min-height: 200px; /* Compact space for ROI controls + smaller image + confidence */
}

/* OCR Preview Panel - Compact */
.preview-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 6px 0;
    min-height: 80px; /* Compact height for smaller image */
}

.preview-canvas-container {
    position: relative;
    background: #000;
    border-radius: 6px;
    overflow: hidden; /* This might clip buttons if they are positioned outside, ensure they are inside */
    border: 1px solid var(--border-subtle);
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 350px; /* Smaller max width */
    min-height: 80px; /* Match image min-height for consistency */
}

#previewImage {
    width: 100%;
    height: auto;
    display: block;
    min-height: 120px;
}

.roi-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.roi-box {
    position: absolute;
    border: 2px solid var(--accent-primary);
    background: rgba(0, 255, 136, 0.1);
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.4);
    border-radius: 4px;
    /* Default size and position - will be updated by JS */
    left: 100px; 
    top: 30px;
    width: 300px;
    height: 60px;
}

/* ROI Control Buttons - Positioned relative to canvas container */
.roi-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none; /* Container doesn't intercept, buttons will */
    z-index: 15;
}

/* ROI Layout - Ultra Compact Buttons Around Image */
.roi-layout {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    margin-bottom: 6px;
}

.roi-controls-top, .roi-controls-bottom {
    display: flex;
    gap: 3px;
    justify-content: center;
}

.roi-controls-middle {
    display: flex;
    align-items: center;
    gap: 3px;
}

.roi-controls-left, .roi-controls-right {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

/* External ROI Buttons - Compact Triangular Design */
.roi-btn-external {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #00ff88, #00cc66);
    border: 1px solid #00ff88;
    border-radius: 3px;
    color: #000;
    font-size: 0;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 1px 4px rgba(0, 255, 136, 0.3);
}

/* Compact Triangular Arrow Indicators */
.roi-btn-external::before {
    content: '';
    width: 0;
    height: 0;
    border-style: solid;
}

/* Up Arrow (Y1-) */
.roi-btn-external[onclick*="'y1', '-'"]:before {
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-bottom: 6px solid #000;
}

/* Down Arrow (Y1+) */
.roi-btn-external[onclick*="'y1', '+'"]:before {
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 6px solid #000;
}

/* Left Arrow (X1-) */
.roi-btn-external[onclick*="'x1', '-'"]:before {
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    border-right: 6px solid #000;
}

/* Right Arrow (X1+) */
.roi-btn-external[onclick*="'x1', '+'"]:before {
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    border-left: 6px solid #000;
}

/* Left Arrow (X2-) */
.roi-btn-external[onclick*="'x2', '-'"]:before {
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    border-right: 6px solid #000;
}

/* Right Arrow (X2+) */
.roi-btn-external[onclick*="'x2', '+'"]:before {
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    border-left: 6px solid #000;
}

/* Up Arrow (Y2-) */
.roi-btn-external[onclick*="'y2', '-'"]:before {
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-bottom: 6px solid #000;
}

/* Down Arrow (Y2+) */
.roi-btn-external[onclick*="'y2', '+'"]:before {
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-top: 6px solid #000;
}

.roi-btn-external:hover {
    background: linear-gradient(135deg, #00ff88, #00aa44);
    border-color: #00ff88;
    transform: scale(1.15);
    box-shadow: 0 2px 8px rgba(0, 255, 136, 0.5);
}

.roi-btn-external:active {
    transform: scale(0.9);
    background: linear-gradient(135deg, #00cc66, #008833);
    box-shadow: 0 1px 4px rgba(0, 255, 136, 0.6);
}

/* Legacy ROI buttons (for JS-created buttons, if any remain) */
.roi-btn {
    position: absolute;
    width: 32px;
    height: 32px;
    background: rgba(0, 0, 0, 0.8);
    pointer-events: all;
}

/* Confidence Display - Compact but Visible */
.confidence-display {
    text-align: center;
    padding: 8px;
    background: var(--bg-secondary);
    border-radius: 6px;
    margin: 8px 0;
    border: 1px solid var(--border-subtle);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.confidence-bar {
    height: 8px;
    background: var(--border-subtle);
    border-radius: 4px;
    overflow: hidden;
    margin: 8px 0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-danger), var(--accent-warning), var(--accent-primary));
    transition: width 0.3s ease;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Recording Controls */
.recording-options {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 10px;
    color: var(--text-primary);
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: 12px;
    height: 12px;
    accent-color: var(--accent-primary);
}

.checkbox-label:hover {
    color: var(--accent-primary);
}

/* Stream Content - Compact */
.stream-content {
    font-family: 'JetBrains Mono', monospace;
    font-size: 10px;
    line-height: 1.3;
    background: var(--bg-primary);
    border-radius: 6px;
    overflow-y: auto;
    border: 1px solid var(--border-subtle);
    color: var(--accent-primary);
    padding: 8px;
    height: 100%;
}

/* Trading Table - Compact */
.positions-table {
    width: 100%;
    font-size: 9px;
    border-collapse: collapse;
}

.positions-table th,
.positions-table td {
    padding: 4px 6px;
    text-align: left;
    border-bottom: 1px solid var(--border-subtle);
}

.positions-table th {
    background: var(--bg-primary);
    color: var(--accent-primary);
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 8px;
}

.pnl-positive { color: var(--accent-primary); font-weight: 700; }
.pnl-negative { color: var(--accent-danger); font-weight: 700; }

.health-component {
    margin-bottom: 8px;
    padding: 4px;
    border-left: 2px solid var(--border-color);
    font-size: 10px;
}

.health-issues {
    color: var(--accent-danger);
    font-size: 9px;
    margin-top: 2px;
}

.health-metrics {
    margin-top: 4px;
}

.health-metrics .metric {
    display: inline-block;
    margin-right: 8px;
    font-size: 9px;
    color: var(--text-muted);
}

/* Side Panel - Compact Controls */
.side-panel {
    display: flex;
    flex-direction: column;
    gap: 8px;
    overflow-y: auto;
}

.control-section {
    background: var(--bg-glass);
    backdrop-filter: blur(25px);
    border: 1px solid var(--border-subtle);
    border-radius: 10px;
    padding: 12px;
}

.control-section-title {
    font-size: 10px;
    font-weight: 800;
    color: var(--accent-primary);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px solid var(--border-subtle);
}

.btn-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
}

.btn-group-single {
    display: grid;
    grid-template-columns: 1fr;
    gap: 6px;
}

.btn {
    padding: 8px 10px;
    border: 1px solid var(--border-subtle);
    border-radius: 6px;
    background: var(--bg-glass);
    color: var(--text-primary);
    font-size: 9px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
}

.btn:hover {
    background: var(--accent-primary);
    color: var(--bg-primary);
    border-color: var(--accent-primary);
    transform: translateY(-1px);
    box-shadow: var(--glow-primary);
}

.btn-primary {
    background: var(--accent-primary);
    color: var(--bg-primary);
    border-color: var(--accent-primary);
}

.btn-danger {
    background: var(--accent-danger);
    color: var(--text-primary);
    border-color: var(--accent-danger);
}

.btn-warning {
    background: var(--accent-warning);
    color: var(--bg-primary);
    border-color: var(--accent-warning);
}

/* Collapsible OCR Settings */
.collapsible {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 0;
}

.collapsible::after {
    content: '';
    font-size: 8px;
    color: var(--accent-primary);
    transition: transform 0.3s ease;
}

.collapsible.collapsed::after {
    transform: rotate(-90deg);
}

.collapsible-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.collapsible-content.expanded {
    max-height: 800px; /* Increased for 22 OCR parameters */
}

/* Form Elements - Compact */
.form-group {
    margin-bottom: 8px;
}

.form-label {
    display: block;
    margin-bottom: 4px;
    font-size: 9px;
    font-weight: 600;
    color: var(--text-secondary);
}

.form-input {
    width: 100%;
    padding: 6px 8px;
    background: var(--bg-primary);
    border: 1px solid var(--border-subtle);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 9px;
    font-family: 'JetBrains Mono', monospace;
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(0, 255, 136, 0.2);
}

/* Footer - Compact */
.footer {
    grid-area: footer;
    background: var(--bg-glass);
    backdrop-filter: blur(25px);
    border: 1px solid var(--border-subtle);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
}

.status-info {
    display: flex;
    gap: 20px;
    font-size: 9px;
    color: var(--text-secondary);
}

.status-info span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-value {
    color: var(--accent-primary);
    font-weight: 700;
}

/* Trading Metrics - Compact */
.trading-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 8px;
}

.metric {
    text-align: center;
    padding: 8px;
    background: var(--bg-primary);
    border-radius: 6px;
    border: 1px solid var(--border-subtle);
}

.metric-value {
    font-size: 12px;
    font-weight: 800;
    color: var(--accent-primary);
    margin-bottom: 2px;
}

.metric-label {
    font-size: 8px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Scrollbar Styles */
.panel-content::-webkit-scrollbar,
.stream-content::-webkit-scrollbar,
.side-panel::-webkit-scrollbar {
    width: 4px;
}

.panel-content::-webkit-scrollbar-track,
.stream-content::-webkit-scrollbar-track,
.side-panel::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.panel-content::-webkit-scrollbar-thumb,
.stream-content::-webkit-scrollbar-thumb,
.side-panel::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 2px;
    opacity: 0.7;
}

/* CSS for hierarchy display */
.parent-trade {
    background-color: rgba(0, 255, 136, 0.05);
    border-left: 3px solid var(--accent-primary);
}

.child-trade {
    background-color: rgba(0, 136, 255, 0.05);
    border-left: 3px solid var(--accent-secondary);
}

.child-trade td:first-child {
    position: relative;
}

.child-trade td:first-child::before {
    content: '';
    position: absolute;
    left: 5px;
    color: var(--accent-secondary);
    font-weight: bold;
}

/* Compact Position Summary Cards - Exact Copy from openpositions2.html */
.position-summary-card {
    background: var(--bg-glass);
    backdrop-filter: blur(25px);
    border: 1px solid var(--border-subtle);
    border-radius: 6px;
    margin: 4px;
    margin-bottom: 4px;
    overflow: hidden;
    overflow-x: hidden;
}

.position-summary-header {
    background: rgba(0, 0, 0, 0.3);
    padding: 12px 24px;
    border-bottom: 1px solid var(--border-subtle);
    display: grid;
    grid-template-rows: auto auto;
    grid-template-columns: 160px 130px 130px 140px 130px 130px 130px 130px;
    gap: 18px 26px;
    font-family: 'JetBrains Mono', monospace;
    align-items: start;
    min-height: 64px;
    width: 100%;
    min-width: fit-content;
    overflow: hidden;
}

.summary-section {
    display: flex;
    flex-direction: column;
    gap: 2px;
    white-space: nowrap;
    overflow: hidden;
}

.summary-section-label {
    color: var(--text-muted);
    text-transform: uppercase;
    font-size: 7px;
    font-weight: 600;
    letter-spacing: 0.3px;
    line-height: 1;
}

.summary-section-value {
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.1;
    font-size: 11px; /* Default font size for consistency */
}

.summary-section-subtitle {
    font-size: 7px;
    color: var(--text-secondary);
    font-family: 'JetBrains Mono', monospace;
    line-height: 1;
}

/* Primary section styling */
.primary-section .summary-section-value {
    font-size: 12px;
    font-weight: 800;
}

.primary-section .summary-section-label {
    font-size: 8px;
}

/* Secondary section styling */
.secondary-section .summary-section-value {
    font-size: 9px;
    font-weight: 600;
}

.secondary-section .summary-section-label {
    font-size: 6px;
}

/* Symbol section styling */
.symbol-section .summary-section-value {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 0.8px;
}

.symbol-section .summary-section-subtitle {
    color: var(--accent-primary);
    font-weight: 700;
    text-transform: uppercase;
    font-size: 8px;
}

/* P&L section styling */
.pnl-section .summary-section-value {
    font-size: 14px;
    font-weight: 800;
}

/* P&L Color Classes - Green/Red for realized, unrealized, P&L per share */
.pnl-positive {
    color: var(--accent-primary) !important;
    font-weight: 700;
}

.pnl-negative {
    color: var(--accent-danger) !important;
    font-weight: 700;
}

.pnl-neutral {
    color: var(--text-secondary) !important;
    font-weight: 700;
}

/* Trade Hierarchy Section */
.trade-hierarchy-section {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-subtle);
}

.hierarchy-header {
    padding: 8px 16px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-subtle);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 9px;
    font-weight: 600;
    color: var(--text-secondary);
}

.hierarchy-header:hover {
    background: rgba(0, 255, 136, 0.05);
}

.hierarchy-title {
    font-family: 'JetBrains Mono', monospace;
}

.hierarchy-toggle {
    background: none;
    border: none;
    color: var(--accent-primary);
    font-size: 12px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.hierarchy-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.hierarchy-content:not(.collapsed) {
    max-height: 1000px;
}

.trade-entry {
    border-bottom: 1px solid var(--border-subtle);
    font-size: 8px;
}

.trade-header {
    padding: 6px 16px;
    background: var(--bg-tertiary);
    display: flex;
    gap: 12px;
    align-items: center;
    font-weight: 600;
}

.trade-details {
    padding: 8px 16px;
    background: var(--bg-secondary);
}

.detail-row {
    display: flex;
    margin-bottom: 4px;
    font-size: 7px;
}

.detail-label {
    color: var(--text-muted);
    min-width: 80px;
    font-weight: 600;
}

.detail-value {
    color: var(--text-primary);
    font-family: 'JetBrains Mono', monospace;
}

/* Trade type badges */
.trade-type {
    background: var(--accent-primary);
    color: var(--bg-primary);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 6px;
    font-weight: 800;
}

.trade-id {
    color: var(--accent-secondary);
    font-family: 'JetBrains Mono', monospace;
}

.trade-symbol {
    color: var(--text-primary);
    font-weight: 800;
}

.trade-action {
    color: var(--accent-warning);
    font-weight: 600;
}

/* Trade status styling */
.trade-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 6px;
    font-weight: 800;
}

.status-filled {
    background: var(--accent-primary);
    color: var(--bg-primary);
}

.status-pending {
    background: var(--accent-warning);
    color: var(--bg-primary);
}

.status-cancelled {
    background: var(--accent-danger);
    color: var(--bg-primary);
}

/* Parent and child trade styling */
.parent-trade {
    background: rgba(0, 255, 136, 0.05);
    border-left: 3px solid var(--accent-primary);
}

.child-trade {
    background: rgba(0, 136, 255, 0.03);
    border-left: 3px solid var(--accent-secondary);
}

/* Live indicator */
.live-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 9px;
    color: var(--text-secondary);
}

.live-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--accent-primary);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.2); }
}

/* Enhanced Historical Trades with Tabs */
.history-panel {
    position: relative;
}

.history-tabs {
    display: flex;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-subtle);
}

.history-tab {
    padding: 8px 16px;
    font-size: 9px;
    font-weight: 600;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
    flex: 1;
    text-align: center;
}

.history-tab:hover {
    background: rgba(0, 255, 136, 0.1);
}

.history-tab.active {
    background: rgba(0, 255, 136, 0.1);
    border-bottom-color: var(--accent-primary);
    color: var(--accent-primary);
}

.history-content {
    transition: all 0.4s ease;
    overflow-y: auto;
}

.history-content.collapsed {
    display: none;
}

/* Rejection-specific styling */
.rejection-entry {
    border-left: 3px solid var(--accent-danger);
    background: rgba(255, 68, 102, 0.05);
}

.rejection-entry .trade-status {
    color: var(--accent-danger);
    font-weight: 600;
}

.rejection-details {
    background: rgba(255, 68, 102, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    margin-top: 4px;
}

/* Fill-specific styling */
.fill-entry {
    border-left: 3px solid var(--accent-success);
    background: rgba(0, 255, 136, 0.05);
}

.fill-entry .trade-status {
    color: var(--accent-success);
    font-weight: 600;
}

/* Enhanced Position Card Styling - Matching Mockup */
.position-summary-card {
    background: var(--bg-glass);
    backdrop-filter: blur(25px);
    border: 1px solid var(--border-subtle);
    border-radius: 6px;
    margin: 4px;
    margin-bottom: 4px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.position-summary-card.rejection-card {
    border-color: var(--accent-danger);
    background: rgba(255, 68, 102, 0.05);
}

.position-summary-card.profit-card {
    border-color: var(--accent-success);
}

.position-summary-card.loss-card {
    border-color: var(--accent-danger);
}

.position-summary-header {
    background: rgba(0, 0, 0, 0.3);
    padding: 12px 24px;
    border-bottom: 1px solid var(--border-subtle);
    display: grid;
    grid-template-rows: auto auto;
    grid-template-columns: repeat(8, 1fr);
    gap: 18px 26px;
    font-family: 'JetBrains Mono', monospace;
    align-items: start;
    min-height: 64px;
    width: 100%;
}

.summary-section {
    display: flex;
    flex-direction: column;
    gap: 2px;
    white-space: nowrap;
    overflow: hidden;
}

.summary-section-label {
    color: var(--text-muted);
    text-transform: uppercase;
    font-size: 7px;
    font-weight: 600;
    letter-spacing: 0.3px;
    line-height: 1;
}

.summary-section-value {
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.1;
}

.summary-section-subtitle {
    font-size: 7px;
    color: var(--text-secondary);
    font-family: 'JetBrains Mono', monospace;
    line-height: 1;
}

/* Top row - Primary info (bigger fonts) */
.primary-section .summary-section-value {
    font-size: 12px;
    font-weight: 800;
}

.primary-section .summary-section-label {
    font-size: 8px;
}

/* Bottom row - Secondary info (smaller fonts) */
.secondary-section .summary-section-value {
    font-size: 9px;
    font-weight: 600;
}

.secondary-section .summary-section-label {
    font-size: 6px;
}

/* Symbol styling - white and prominent */
.symbol-section .summary-section-value {
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 0.8px;
}

.symbol-section .summary-section-subtitle {
    color: var(--accent-primary);
    font-weight: 700;
    text-transform: uppercase;
    font-size: 8px;
}

/* P&L styling with larger font */
.pnl-section .summary-section-value {
    font-size: 14px;
    font-weight: 800;
}

/* Rejection banner */
.rejection-details-banner {
    background: rgba(255, 68, 102, 0.2);
    border-top: 1px solid var(--accent-danger);
    padding: 8px 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 10px;
}

.rejection-icon {
    font-size: 14px;
}

.rejection-text {
    flex: 1;
    color: var(--accent-danger);
    font-weight: 600;
}

.rejection-source {
    color: var(--text-muted);
    font-size: 9px;
}

.historical-trades-container {
    padding: 12px;
    overflow-y: auto;
    max-height: 400px;
}

/* Toggle button */
.toggle-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-subtle);
    border-radius: 6px;
    color: var(--accent-primary);
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 8px;
}

.toggle-btn:hover {
    background: var(--accent-primary);
    color: var(--bg-primary);
    transform: scale(1.1);
    box-shadow: var(--glow-primary);
}

.toggle-btn.expanded {
    transform: rotate(45deg);
    background: var(--accent-primary);
    color: var(--bg-primary);
}

/* Stats Summary Row */
.stats-row {
    padding: 8px 16px;
    background: var(--bg-primary);
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    font-size: 8px;
    border-top: 1px solid var(--border-subtle);
}

.stat-item {
    text-align: center;
}

.stat-label {
    color: var(--text-muted);
    font-size: 7px;
    text-transform: uppercase;
}

.stat-value {
    font-weight: 700;
    margin-top: 2px;
}

/* Additional Trade Status Styles */
.status-filled {
    background: rgba(0, 200, 100, 0.3);
    color: var(--accent-primary);
}

.status-pending {
    background: rgba(255, 200, 0, 0.3);
    color: var(--accent-warning);
}

.status-cancelled {
    background: rgba(200, 0, 0, 0.3);
    color: var(--accent-danger);
}

.status-partial {
    background: rgba(100, 150, 255, 0.3);
    color: var(--accent-secondary);
}

/* Trade Action Styling */
.trade-action {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 8px;
    font-weight: 700;
    min-width: 70px;
    text-align: center;
    background: rgba(100, 150, 255, 0.3);
    color: var(--accent-secondary);
    font-family: 'JetBrains Mono', monospace;
}

/* Top and Bottom Row Layout */
.top-row {
    display: contents;
}

.bottom-row {
    display: contents;
}

/* Enhanced Parent-Child Trade Styling */
.parent-row {
    background: rgba(0, 255, 136, 0.05);
    border-left: 3px solid var(--accent-primary);
}

.child-row {
    background: rgba(0, 136, 255, 0.03);
    border-left: 3px solid var(--accent-secondary);
}

.child-symbol {
    position: relative;
    color: var(--accent-secondary);
    font-style: italic;
    padding-left: 24px;
}

.child-symbol::before {
    content: '└─';
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--accent-secondary);
    font-weight: bold;
    font-size: 10px;
}

.child-quantity {
    position: relative;
    padding-left: 16px;
    color: var(--accent-secondary);
}

.child-quantity::before {
    content: '↳';
    position: absolute;
    left: 2px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--accent-secondary);
    font-weight: bold;
}

/* Market Data Styling */
.market-data {
    font-family: 'JetBrains Mono', monospace;
    font-size: 7px;
    line-height: 1.1;
}

.market-data div {
    margin-bottom: 1px;
}

.market-data-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 8px;
    font-size: 10px;
    line-height: 1.4;
}

.market-data-item {
    display: flex;
    justify-content: space-between;
}

/* Responsive Position Summary Adjustments */
@media (max-width: 1600px) {
    .position-summary-header {
        grid-template-columns: 120px 90px 80px 120px 80px 90px 70px 70px;
        font-size: 7px;
    }
}

@media (max-width: 1400px) {
    .position-summary-header {
        grid-template-columns: 100px 80px 70px 100px 70px 80px 60px 60px;
        font-size: 7px;
    }
}

@media (max-width: 1200px) {
    .position-summary-header {
        grid-template-columns: 90px 70px 60px 90px 60px 70px 50px 50px;
        font-size: 6px;
    }
}

/* Remove duplicate - keeping the first definition above */

/* Trade Hierarchy Styling - Match Raw OCR Output */
.trade-hierarchy-section {
    margin-top: 8px;
    background: var(--bg-primary);
    border: 1px solid var(--border-subtle);
    border-radius: 4px;
    overflow: hidden;
}

.hierarchy-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.4);
    border-bottom: 1px solid var(--border-subtle);
    cursor: pointer;
}

.hierarchy-header:hover {
    background: rgba(0, 0, 0, 0.5);
}

.hierarchy-title {
    font-size: 9px;
    font-weight: 600;
    color: var(--text-primary);
    font-family: 'JetBrains Mono', monospace;
}

.hierarchy-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 12px;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 2px;
    transition: all 0.2s ease;
}

.hierarchy-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.hierarchy-content {
    max-height: 300px;
    overflow-y: auto;
    padding: 8px;
    font-family: 'JetBrains Mono', monospace;
    font-size: 9px;
    background: var(--bg-primary);
}

.hierarchy-content.collapsed {
    display: none;
}

/* Trade Entry Styling */
.trade-entry {
    margin: 6px 0;
    border-radius: 4px;
    overflow: hidden;
    border-left: 3px solid transparent;
    background: rgba(0, 0, 0, 0.3);
}

.parent-trade {
    border-left-color: var(--accent-primary);
}

.child-trade {
    border-left-color: var(--accent-secondary);
    margin-left: 20px;
}

.trade-header {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.5);
    font-size: 9px;
    font-weight: 600;
    font-family: 'JetBrains Mono', monospace;
    color: var(--text-primary);
}

.trade-type {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 8px;
    font-weight: 700;
    min-width: 50px;
    text-align: center;
    font-family: 'JetBrains Mono', monospace;
}

.parent-trade .trade-type {
    background: rgba(0, 100, 200, 0.8);
    color: white;
    border: 1px solid var(--accent-primary);
}

.child-trade .trade-type {
    background: rgba(0, 0, 0, 0.8);
    color: var(--accent-secondary);
    border: 1px solid var(--accent-secondary);
}

.trade-id {
    font-family: 'JetBrains Mono', monospace;
    font-size: 9px;
    color: var(--text-secondary);
    min-width: 90px;
}

.trade-symbol {
    font-weight: 700;
    color: var(--text-primary);
    min-width: 50px;
    font-family: 'JetBrains Mono', monospace;
}

.trade-action {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 8px;
    font-weight: 700;
    min-width: 70px;
    text-align: center;
    background: rgba(100, 150, 255, 0.3);
    color: var(--accent-info);
    font-family: 'JetBrains Mono', monospace;
}

.trade-status {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 8px;
    font-weight: 700;
    min-width: 70px;
    text-align: center;
    font-family: 'JetBrains Mono', monospace;
}

.status-filled {
    background: rgba(0, 200, 100, 0.3);
    color: var(--accent-success);
}

.status-pending {
    background: rgba(255, 200, 0, 0.3);
    color: var(--accent-warning);
}

.status-cancelled {
    background: rgba(200, 0, 0, 0.3);
    color: var(--accent-danger);
}

.status-partial {
    background: rgba(100, 150, 255, 0.3);
    color: var(--accent-info);
}

.trade-details {
    padding: 12px;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row {
    display: flex;
    margin: 4px 0;
    font-size: 9px;
    font-family: 'JetBrains Mono', monospace;
}

.detail-label {
    color: var(--text-secondary);
    font-weight: 600;
    min-width: 100px;
    margin-right: 12px;
    font-size: 10px;
}

.detail-value {
    color: var(--text-primary);
    font-family: 'JetBrains Mono', monospace;
    flex: 1;
    font-weight: 500;
}

/* Enhanced Historical Trades Panel - Exact Copy from openpositions2.html */
.history-panel {
    position: relative;
}

.history-tabs {
    display: flex;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-subtle);
}

.history-tab {
    flex: 1;
    padding: 8px 12px;
    text-align: center;
    font-size: 9px;
    font-weight: 600;
    color: var(--text-secondary);
    cursor: pointer;
    border-right: 1px solid var(--border-subtle);
    transition: all 0.2s ease;
    font-family: 'JetBrains Mono', monospace;
}

.history-tab:last-child {
    border-right: none;
}

.history-tab:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.history-tab.active {
    background: var(--accent-primary);
    color: white;
    font-weight: 700;
}

.history-content {
    background: var(--bg-primary);
    /* Remove max-height and overflow to prevent double scrollbars */
}

.historical-trades-container {
    padding: 8px;
    /* Remove max-height here too to prevent double scrollbars */
}

/* Enhanced Toggle Button - Exact Copy from openpositions2.html */
.toggle-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-subtle);
    border-radius: 6px;
    color: var(--accent-primary);
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 8px;
}

.toggle-btn:hover {
    background: var(--accent-primary);
    color: var(--bg-primary);
    transform: scale(1.1);
    box-shadow: var(--glow-primary);
}

.toggle-btn.expanded {
    transform: rotate(45deg);
    background: var(--accent-primary);
    color: var(--bg-primary);
}

/* Stats Row Styling */
.stats-row {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    padding: 12px;
    background: rgba(0, 0, 0, 0.4);
    border-top: 1px solid var(--border-subtle);
    font-family: 'JetBrains Mono', monospace;
}

.stat-item {
    text-align: center;
    padding: 6px 4px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    border: 1px solid var(--border-subtle);
}

.stat-label {
    font-size: 7px;
    font-weight: 700;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 10px;
    font-weight: 700;
    color: var(--text-primary);
    font-family: 'JetBrains Mono', monospace;
}
