# tank_leak_analyzer.ps1 - Historical leak analysis and reporting
param(
    [string]$CsvFile = "tank_leak_analysis.csv",
    [switch]$GenerateReport = $true,
    [string]$ReportFile = "tank_leak_report.html",
    [int]$AnalysisHours = 24
)

Write-Host "🔍 TANK Leak Analysis Tool" -ForegroundColor Green
Write-Host "Analyzing: $CsvFile" -ForegroundColor Cyan
Write-Host "=" * 80

if (-not (Test-Path $CsvFile)) {
    Write-Host "❌ Error: CSV file '$CsvFile' not found!" -ForegroundColor Red
    Write-Host "Run tank_leak_detector.ps1 first to generate data." -ForegroundColor Yellow
    exit 1
}

# Import and process data
try {
    $data = Import-Csv $CsvFile
    Write-Host "✅ Loaded $($data.Count) data points" -ForegroundColor Green
} catch {
    Write-Host "❌ Error reading CSV file: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

if ($data.Count -eq 0) {
    Write-Host "❌ No data found in CSV file!" -ForegroundColor Red
    exit 1
}

# Convert data types
$data | ForEach-Object {
    $_.MemoryMB = [double]$_.MemoryMB
    $_.MemoryGB = [double]$_.MemoryGB
    $_.HandleCount = [int]$_.HandleCount
    $_.ThreadCount = [int]$_.ThreadCount
    $_.PageFaultsMB = [double]$_.PageFaultsMB
    $_.GrowthRate = [double]$_.GrowthRate
    $_.Confidence = [double]$_.Confidence
    $_.Timestamp = [datetime]$_.Timestamp
    $_.LeakDetected = [bool]::Parse($_.LeakDetected)
}

# Filter to analysis window
$cutoffTime = (Get-Date).AddHours(-$AnalysisHours)
$recentData = $data | Where-Object { $_.Timestamp -gt $cutoffTime }

Write-Host "Analyzing last $AnalysisHours hours: $($recentData.Count) samples" -ForegroundColor Cyan

# Calculate comprehensive statistics
$memoryStats = $recentData | Measure-Object MemoryMB -Average -Maximum -Minimum -Sum
$growthStats = $recentData | Measure-Object GrowthRate -Average -Maximum -Minimum
$confidenceStats = $recentData | Measure-Object Confidence -Average -Maximum -Minimum

# Leak detection analysis
$leakEvents = $recentData | Where-Object { $_.LeakDetected -eq $true }
$criticalEvents = $recentData | Where-Object { $_.Severity -eq "CRITICAL" }
$highEvents = $recentData | Where-Object { $_.Severity -eq "HIGH" }

# Trend analysis by type
$trendCounts = $recentData | Group-Object TrendType | Sort-Object Count -Descending

# Time-based analysis
$duration = if ($recentData.Count -gt 0) { 
    ($recentData[-1].Timestamp - $recentData[0].Timestamp).TotalHours 
} else { 0 }

Write-Host ""
Write-Host "📊 COMPREHENSIVE LEAK ANALYSIS" -ForegroundColor Yellow
Write-Host "=" * 60

Write-Host ""
Write-Host "⏱️  ANALYSIS PERIOD:" -ForegroundColor Yellow
Write-Host "  Duration:         $([math]::Round($duration, 1)) hours" -ForegroundColor White
Write-Host "  Sample Count:     $($recentData.Count)" -ForegroundColor White
Write-Host "  Data Points:      $($data.Count) total" -ForegroundColor White

Write-Host ""
Write-Host "💾 MEMORY STATISTICS:" -ForegroundColor Yellow
Write-Host "  Average Memory:   $([math]::Round($memoryStats.Average, 2)) MB" -ForegroundColor White
Write-Host "  Peak Memory:      $([math]::Round($memoryStats.Maximum, 2)) MB" -ForegroundColor White
Write-Host "  Minimum Memory:   $([math]::Round($memoryStats.Minimum, 2)) MB" -ForegroundColor White
Write-Host "  Memory Range:     $([math]::Round($memoryStats.Maximum - $memoryStats.Minimum, 2)) MB" -ForegroundColor White

Write-Host ""
Write-Host "📈 GROWTH RATE ANALYSIS:" -ForegroundColor Yellow
Write-Host "  Average Growth:   $([math]::Round($growthStats.Average, 3)) MB/interval" -ForegroundColor White
Write-Host "  Max Growth Rate:  $([math]::Round($growthStats.Maximum, 3)) MB/interval" -ForegroundColor White
Write-Host "  Min Growth Rate:  $([math]::Round($growthStats.Minimum, 3)) MB/interval" -ForegroundColor White
Write-Host "  Avg Confidence:   $([math]::Round($confidenceStats.Average, 1))%" -ForegroundColor White

Write-Host ""
Write-Host "🚨 LEAK DETECTION SUMMARY:" -ForegroundColor Yellow
Write-Host "  Total Leak Events:    $($leakEvents.Count)" -ForegroundColor $(if($leakEvents.Count -gt 0) { "Red" } else { "Green" })
Write-Host "  Critical Events:      $($criticalEvents.Count)" -ForegroundColor $(if($criticalEvents.Count -gt 0) { "Red" } else { "Green" })
Write-Host "  High Severity Events: $($highEvents.Count)" -ForegroundColor $(if($highEvents.Count -gt 0) { "Yellow" } else { "Green" })
Write-Host "  Leak Detection Rate:  $([math]::Round(($leakEvents.Count / $recentData.Count) * 100, 1))%" -ForegroundColor $(if($leakEvents.Count -gt 0) { "Red" } else { "Green" })

Write-Host ""
Write-Host "📊 TREND TYPE DISTRIBUTION:" -ForegroundColor Yellow
$trendCounts | ForEach-Object {
    $percentage = [math]::Round(($_.Count / $recentData.Count) * 100, 1)
    $color = switch ($_.Name) {
        "CRITICAL_LEAK" { "Red" }
        "LEAK_DETECTED" { "Red" }
        "MODERATE_GROWTH" { "Yellow" }
        "SLOW_GROWTH" { "Yellow" }
        "STABLE" { "Green" }
        "DECREASING" { "Cyan" }
        default { "White" }
    }
    Write-Host "  $($_.Name.PadRight(15)): $($_.Count.ToString().PadLeft(3)) samples ($percentage%)" -ForegroundColor $color
}

# System health assessment
Write-Host ""
Write-Host "🏥 SYSTEM HEALTH ASSESSMENT:" -ForegroundColor Yellow

$healthScore = 100
$healthIssues = @()

if ($leakEvents.Count -gt 0) {
    $leakPercentage = ($leakEvents.Count / $recentData.Count) * 100
    if ($leakPercentage -gt 20) {
        $healthScore -= 50
        $healthIssues += "High leak detection rate ($([math]::Round($leakPercentage, 1))%)"
    } elseif ($leakPercentage -gt 10) {
        $healthScore -= 30
        $healthIssues += "Moderate leak detection rate ($([math]::Round($leakPercentage, 1))%)"
    } else {
        $healthScore -= 15
        $healthIssues += "Low leak detection rate ($([math]::Round($leakPercentage, 1))%)"
    }
}

if ($criticalEvents.Count -gt 0) {
    $healthScore -= 30
    $healthIssues += "$($criticalEvents.Count) critical leak events detected"
}

if ($growthStats.Average -gt 5) {
    $healthScore -= 20
    $healthIssues += "High average growth rate ($([math]::Round($growthStats.Average, 2)) MB/interval)"
}

$memoryVolatility = if ($memoryStats.Average -gt 0) { 
    (($memoryStats.Maximum - $memoryStats.Minimum) / $memoryStats.Average) * 100 
} else { 0 }

if ($memoryVolatility -gt 30) {
    $healthScore -= 15
    $healthIssues += "High memory volatility ($([math]::Round($memoryVolatility, 1))%)"
}

$healthScore = [math]::Max(0, $healthScore)

$healthRating = if ($healthScore -ge 90) { "EXCELLENT" }
                elseif ($healthScore -ge 80) { "GOOD" }
                elseif ($healthScore -ge 70) { "FAIR" }
                elseif ($healthScore -ge 50) { "POOR" }
                else { "CRITICAL" }

$healthColor = if ($healthScore -ge 80) { "Green" }
               elseif ($healthScore -ge 60) { "Yellow" }
               else { "Red" }

Write-Host "  Health Score:     $healthScore/100" -ForegroundColor $healthColor
Write-Host "  Health Rating:    $healthRating" -ForegroundColor $healthColor
Write-Host "  Memory Volatility: $([math]::Round($memoryVolatility, 1))%" -ForegroundColor White

if ($healthIssues.Count -gt 0) {
    Write-Host ""
    Write-Host "⚠️  HEALTH ISSUES IDENTIFIED:" -ForegroundColor Red
    $healthIssues | ForEach-Object {
        Write-Host "  • $_" -ForegroundColor Yellow
    }
}

# Recommendations
Write-Host ""
Write-Host "💡 RECOMMENDATIONS:" -ForegroundColor Yellow

if ($healthScore -ge 90) {
    Write-Host "  ✅ System is operating excellently" -ForegroundColor Green
    Write-Host "  • Continue current monitoring schedule" -ForegroundColor Green
    Write-Host "  • No immediate action required" -ForegroundColor Green
} elseif ($healthScore -ge 70) {
    Write-Host "  ⚠️  System shows some concerning patterns" -ForegroundColor Yellow
    Write-Host "  • Increase monitoring frequency" -ForegroundColor Yellow
    Write-Host "  • Review recent system changes" -ForegroundColor Yellow
    Write-Host "  • Consider memory profiling" -ForegroundColor Yellow
} else {
    Write-Host "  🚨 System requires immediate attention" -ForegroundColor Red
    Write-Host "  • Plan for TANK restart" -ForegroundColor Red
    Write-Host "  • Investigate memory leaks immediately" -ForegroundColor Red
    Write-Host "  • Review code for memory management issues" -ForegroundColor Red
    Write-Host "  • Consider emergency maintenance window" -ForegroundColor Red
}

# Generate HTML report
if ($GenerateReport) {
    Write-Host ""
    Write-Host "📄 Generating HTML Report..." -ForegroundColor Cyan
    
    $htmlContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>TANK Memory Leak Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .header { background-color: #2c3e50; color: white; padding: 20px; border-radius: 5px; text-align: center; }
        .section { margin: 20px 0; padding: 20px; background-color: white; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric { display: inline-block; margin: 10px; padding: 15px; background-color: #ecf0f1; border-radius: 5px; min-width: 200px; }
        .health-excellent { background-color: #d5f4e6; border-left: 5px solid #27ae60; }
        .health-good { background-color: #fef9e7; border-left: 5px solid #f39c12; }
        .health-poor { background-color: #fadbd8; border-left: 5px solid #e74c3c; }
        .trend-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .trend-table th, .trend-table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        .trend-table th { background-color: #34495e; color: white; }
        .leak-critical { color: #e74c3c; font-weight: bold; }
        .leak-high { color: #f39c12; font-weight: bold; }
        .leak-none { color: #27ae60; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 TANK Memory Leak Analysis Report</h1>
        <p>Generated: $(Get-Date)</p>
        <p>Analysis Period: $([math]::Round($duration, 1)) hours | Samples: $($recentData.Count)</p>
    </div>
    
    <div class="section">
        <h2>📊 Executive Summary</h2>
        <div class="metric health-$(if($healthScore -ge 80){'excellent'}elseif($healthScore -ge 60){'good'}else{'poor'})">
            <h3>System Health</h3>
            <p><strong>Score:</strong> $healthScore/100</p>
            <p><strong>Rating:</strong> $healthRating</p>
        </div>
        <div class="metric">
            <h3>Memory Usage</h3>
            <p><strong>Average:</strong> $([math]::Round($memoryStats.Average, 2)) MB</p>
            <p><strong>Peak:</strong> $([math]::Round($memoryStats.Maximum, 2)) MB</p>
        </div>
        <div class="metric">
            <h3>Leak Detection</h3>
            <p><strong>Events:</strong> <span class="$(if($leakEvents.Count -eq 0){'leak-none'}elseif($criticalEvents.Count -gt 0){'leak-critical'}else{'leak-high'})">$($leakEvents.Count)</span></p>
            <p><strong>Rate:</strong> $([math]::Round(($leakEvents.Count / $recentData.Count) * 100, 1))%</p>
        </div>
    </div>
    
    <div class="section">
        <h2>📈 Trend Analysis</h2>
        <table class="trend-table">
            <tr><th>Trend Type</th><th>Count</th><th>Percentage</th></tr>
            $(($trendCounts | ForEach-Object { 
                $percentage = [math]::Round(($_.Count / $recentData.Count) * 100, 1)
                "<tr><td>$($_.Name)</td><td>$($_.Count)</td><td>$percentage%</td></tr>"
            }) -join "`n            ")
        </table>
    </div>
    
    <div class="section">
        <h2>💡 Recommendations</h2>
        $(if ($healthScore -ge 90) {
            "<p class='leak-none'>✅ System is operating excellently. Continue current monitoring.</p>"
        } elseif ($healthScore -ge 70) {
            "<p class='leak-high'>⚠️ System shows concerning patterns. Increase monitoring frequency.</p>"
        } else {
            "<p class='leak-critical'>🚨 System requires immediate attention. Plan for TANK restart.</p>"
        })
        $(if ($healthIssues.Count -gt 0) {
            "<h3>Issues Identified:</h3><ul>" + (($healthIssues | ForEach-Object { "<li>$_</li>" }) -join "") + "</ul>"
        })
    </div>
</body>
</html>
"@
    
    $htmlContent | Out-File $ReportFile -Encoding UTF8
    Write-Host "✅ HTML report saved to: $ReportFile" -ForegroundColor Green
}

Write-Host ""
Write-Host "Analysis complete! 🎉" -ForegroundColor Green
