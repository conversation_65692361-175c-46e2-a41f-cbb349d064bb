# tank_multi_monitor_launcher.ps1 - Comprehensive launcher for multi-process monitoring
param(
    [string]$Mode = "monitor",  # monitor, leak, analyze, demo
    [string]$ProcessPattern = "python",
    [int]$DemoMinutes = 10,
    [switch]$ShowHelp = $false
)

if ($ShowHelp) {
    Write-Host "🔍 TANK Multi-Process Monitoring Suite" -ForegroundColor Green
    Write-Host "=" * 60
    Write-Host ""
    Write-Host "MODES:" -ForegroundColor Yellow
    Write-Host "  monitor  - Real-time monitoring of all Python processes" -ForegroundColor White
    Write-Host "  leak     - Advanced leak detection across all processes" -ForegroundColor White
    Write-Host "  analyze  - Analyze historical monitoring data" -ForegroundColor White
    Write-Host "  demo     - Quick demonstration mode" -ForegroundColor White
    Write-Host ""
    Write-Host "EXAMPLES:" -ForegroundColor Cyan
    Write-Host "  .\tank_multi_monitor_launcher.ps1 -Mode monitor" -ForegroundColor Gray
    Write-Host "  .\tank_multi_monitor_launcher.ps1 -Mode leak" -ForegroundColor Gray
    Write-Host "  .\tank_multi_monitor_launcher.ps1 -Mode demo -DemoMinutes 5" -ForegroundColor Gray
    Write-Host "  .\tank_multi_monitor_launcher.ps1 -Mode analyze" -ForegroundColor Gray
    exit 0
}

Write-Host "🚀 TANK Multi-Process Monitoring Suite Launcher" -ForegroundColor Green
Write-Host "=" * 70

# Function to check for Python processes
function Test-PythonProcesses {
    param([string]$Pattern)
    
    $processes = Get-Process $Pattern -ErrorAction SilentlyContinue
    if ($processes.Count -eq 0) {
        Write-Host "❌ No processes found matching pattern '$Pattern'!" -ForegroundColor Red
        Write-Host ""
        Write-Host "Available processes that might be relevant:" -ForegroundColor Yellow
        Get-Process | Where-Object { 
            $_.ProcessName -like "*python*" -or 
            $_.ProcessName -like "*application*" -or 
            $_.ProcessName -like "*core*" -or
            $_.ProcessName -like "*tank*"
        } | Format-Table ProcessName, Id, WorkingSet -AutoSize
        return $false
    }
    return $true
}

switch ($Mode.ToLower()) {
    "monitor" {
        Write-Host "📊 Starting Multi-Process Real-Time Monitoring..." -ForegroundColor Cyan
        Write-Host "Process Pattern: $ProcessPattern" -ForegroundColor Yellow
        Write-Host "Press Ctrl+C to stop monitoring" -ForegroundColor Gray
        Write-Host ""
        
        if (-not (Test-PythonProcesses $ProcessPattern)) {
            exit 1
        }
        
        Write-Host "✅ Found $((Get-Process $ProcessPattern).Count) processes matching '$ProcessPattern'" -ForegroundColor Green
        Write-Host "Starting comprehensive multi-process monitor..." -ForegroundColor Cyan
        & .\tank_multi_process_monitor.ps1 -ProcessPattern $ProcessPattern -SampleIntervalSeconds 30 -EnableCSVLogging -ShowIndividualProcesses
    }
    
    "leak" {
        Write-Host "🔍 Starting Multi-Process Leak Detection..." -ForegroundColor Cyan
        Write-Host "Process Pattern: $ProcessPattern" -ForegroundColor Yellow
        Write-Host "This will perform advanced statistical analysis for leak detection" -ForegroundColor Gray
        Write-Host ""
        
        if (-not (Test-PythonProcesses $ProcessPattern)) {
            exit 1
        }
        
        Write-Host "✅ Found $((Get-Process $ProcessPattern).Count) processes matching '$ProcessPattern'" -ForegroundColor Green
        Write-Host "Starting advanced multi-process leak detector..." -ForegroundColor Cyan
        & .\tank_multi_leak_detector.ps1 -ProcessPattern $ProcessPattern -SampleIntervalSeconds 60 -AnalysisWindowMinutes 30 -EnableAlerts -EnableCSVLogging
    }
    
    "analyze" {
        Write-Host "📈 Starting Historical Data Analysis..." -ForegroundColor Cyan
        
        $dataFiles = @()
        if (Test-Path "tank_multi_process_monitor.csv") {
            $dataFiles += "tank_multi_process_monitor.csv"
        }
        if (Test-Path "tank_multi_leak_analysis.csv") {
            $dataFiles += "tank_multi_leak_analysis.csv"
        }
        
        if ($dataFiles.Count -eq 0) {
            Write-Host "❌ No historical data found!" -ForegroundColor Red
            Write-Host "Available data files to analyze:" -ForegroundColor Yellow
            Write-Host "  • tank_multi_process_monitor.csv (from monitor mode)" -ForegroundColor Gray
            Write-Host "  • tank_multi_leak_analysis.csv (from leak mode)" -ForegroundColor Gray
            Write-Host ""
            Write-Host "Run monitoring first:" -ForegroundColor Cyan
            Write-Host "  .\tank_multi_monitor_launcher.ps1 -Mode monitor" -ForegroundColor Yellow
            Write-Host "  .\tank_multi_monitor_launcher.ps1 -Mode leak" -ForegroundColor Yellow
            exit 1
        }
        
        Write-Host "✅ Found historical data files:" -ForegroundColor Green
        $dataFiles | ForEach-Object {
            $fileInfo = Get-Item $_
            Write-Host "  • $_ ($([math]::Round($fileInfo.Length / 1KB, 1)) KB, modified: $($fileInfo.LastWriteTime.ToString('HH:mm:ss')))" -ForegroundColor White
        }
        Write-Host ""
        
        # Analyze each data file
        foreach ($dataFile in $dataFiles) {
            Write-Host "📊 Analyzing $dataFile..." -ForegroundColor Cyan
            
            try {
                $data = Import-Csv $dataFile
                Write-Host "✅ Loaded $($data.Count) data points from $dataFile" -ForegroundColor Green
                
                # Basic analysis
                if ($dataFile -like "*multi_process_monitor*") {
                    # Multi-process monitor analysis
                    $data | ForEach-Object {
                        $_.TotalMemoryMB = [double]$_.TotalMemoryMB
                        $_.ProcessCount = [int]$_.ProcessCount
                    }
                    
                    $memoryStats = $data | Measure-Object TotalMemoryMB -Average -Maximum -Minimum
                    $processStats = $data | Measure-Object ProcessCount -Average -Maximum -Minimum
                    
                    Write-Host "  📊 Multi-Process Monitor Analysis:" -ForegroundColor Yellow
                    Write-Host "    Duration: $([math]::Round(((Get-Date) - [datetime]$data[0].Timestamp).TotalHours, 1)) hours" -ForegroundColor White
                    Write-Host "    Avg Total Memory: $([math]::Round($memoryStats.Average, 2)) MB" -ForegroundColor White
                    Write-Host "    Peak Total Memory: $([math]::Round($memoryStats.Maximum, 2)) MB" -ForegroundColor White
                    Write-Host "    Avg Process Count: $([math]::Round($processStats.Average, 1))" -ForegroundColor White
                    Write-Host "    Max Process Count: $($processStats.Maximum)" -ForegroundColor White
                    
                } elseif ($dataFile -like "*leak_analysis*") {
                    # Leak analysis
                    $data | ForEach-Object {
                        $_.TotalMemoryMB = [double]$_.TotalMemoryMB
                        $_.GrowthRate = [double]$_.GrowthRate
                        $_.LeakDetected = [bool]::Parse($_.LeakDetected)
                    }
                    
                    $leakEvents = $data | Where-Object { $_.LeakDetected -eq $true }
                    $avgGrowthRate = ($data | Measure-Object GrowthRate -Average).Average
                    
                    Write-Host "  🔍 Leak Detection Analysis:" -ForegroundColor Yellow
                    Write-Host "    Leak Events: $($leakEvents.Count)/$($data.Count) samples" -ForegroundColor $(if($leakEvents.Count -gt 0) { "Red" } else { "Green" })
                    Write-Host "    Avg Growth Rate: $([math]::Round($avgGrowthRate, 3)) MB/interval" -ForegroundColor White
                    Write-Host "    Leak Detection Rate: $([math]::Round(($leakEvents.Count / $data.Count) * 100, 1))%" -ForegroundColor $(if($leakEvents.Count -gt 0) { "Red" } else { "Green" })
                }
                
            } catch {
                Write-Host "❌ Error analyzing $dataFile`: $($_.Exception.Message)" -ForegroundColor Red
            }
            Write-Host ""
        }
        
        Write-Host "📄 For detailed analysis, use individual analyzer scripts:" -ForegroundColor Cyan
        Write-Host "  .\tank_monitor_analyzer.ps1 -CsvFile tank_multi_process_monitor.csv" -ForegroundColor Yellow
    }
    
    "demo" {
        Write-Host "🎮 Starting Demo Mode..." -ForegroundColor Cyan
        Write-Host "Demo Duration: $DemoMinutes minutes" -ForegroundColor Yellow
        Write-Host "This will run both monitoring and leak detection with fast sampling" -ForegroundColor Gray
        Write-Host ""
        
        if (-not (Test-PythonProcesses $ProcessPattern)) {
            exit 1
        }
        
        Write-Host "✅ Found $((Get-Process $ProcessPattern).Count) processes matching '$ProcessPattern'" -ForegroundColor Green
        Write-Host ""
        
        # Run demo monitoring
        Write-Host "🔄 Phase 1: Multi-Process Monitoring Demo (fast sampling)..." -ForegroundColor Cyan
        
        # Simple demo without background jobs to avoid syntax issues
        Write-Host "Running multi-process monitoring for $($DemoMinutes / 2) minutes..." -ForegroundColor Yellow

        $demoEndTime = (Get-Date).AddMinutes($DemoMinutes / 2)
        $sampleCount = 0

        while ((Get-Date) -lt $demoEndTime) {
            $sampleCount++
            $processes = Get-Process $ProcessPattern -ErrorAction SilentlyContinue

            if ($processes.Count -gt 0) {
                $totalMemoryMB = [math]::Round(($processes | Measure-Object WorkingSet64 -Sum).Sum / 1MB, 2)
                Write-Host "  Sample $sampleCount`: $($processes.Count) processes, $totalMemoryMB MB total" -ForegroundColor White
            }

            Start-Sleep 10
        }

        Write-Host ""
        Write-Host "🔄 Phase 2: Leak Detection Demo (basic analysis)..." -ForegroundColor Cyan

        $demoEndTime2 = (Get-Date).AddMinutes($DemoMinutes / 2)
        $sampleCount2 = 0
        $memoryHistory = @()

        while ((Get-Date) -lt $demoEndTime2) {
            $sampleCount2++
            $processes = Get-Process $ProcessPattern -ErrorAction SilentlyContinue

            if ($processes.Count -gt 0) {
                $totalMemoryMB = [math]::Round(($processes | Measure-Object WorkingSet64 -Sum).Sum / 1MB, 2)
                $memoryHistory += $totalMemoryMB

                if ($memoryHistory.Count -gt 1) {
                    $growth = $totalMemoryMB - $memoryHistory[0]
                    $growthColor = if ($growth -gt 10) { "Red" } elseif ($growth -gt 5) { "Yellow" } else { "Green" }
                    Write-Host "  Sample $sampleCount2`: $totalMemoryMB MB (Growth: $([math]::Round($growth, 2)) MB)" -ForegroundColor $growthColor
                } else {
                    Write-Host "  Sample $sampleCount2`: $totalMemoryMB MB (Baseline)" -ForegroundColor White
                }
            }

            Start-Sleep 10
        }
        
        Write-Host ""
        Write-Host "🎮 Demo completed!" -ForegroundColor Green
        Write-Host "To run full monitoring:" -ForegroundColor Cyan
        Write-Host "  .\tank_multi_monitor_launcher.ps1 -Mode monitor" -ForegroundColor Yellow
        Write-Host "  .\tank_multi_monitor_launcher.ps1 -Mode leak" -ForegroundColor Yellow
    }
    
    default {
        Write-Host "❌ Invalid mode: $Mode" -ForegroundColor Red
        Write-Host ""
        Write-Host "Available modes:" -ForegroundColor Yellow
        Write-Host "  monitor  - Real-time monitoring of all Python processes" -ForegroundColor White
        Write-Host "  leak     - Advanced leak detection across all processes" -ForegroundColor White
        Write-Host "  analyze  - Analyze historical monitoring data" -ForegroundColor White
        Write-Host "  demo     - Quick demonstration mode" -ForegroundColor White
        Write-Host ""
        Write-Host "For detailed help:" -ForegroundColor Cyan
        Write-Host "  .\tank_multi_monitor_launcher.ps1 -ShowHelp" -ForegroundColor Gray
        exit 1
    }
}
