"""
Dummy Decorators Module

This module provides no-op implementations of decorators used in the application.
These implementations are used when decorators are disabled to minimize overhead.
"""

from functools import wraps
import logging

# Set up logging
logger = logging.getLogger("dummy_decorators")
logger.info("Dummy decorators module loaded")

def log_function_call(module_key: str):
    """
    No-op implementation of log_function_call decorator.
    
    Args:
        module_key: The module key to log under (ignored in dummy implementation)
        
    Returns:
        A decorator function that does nothing but call the wrapped function
    """
    def decorator(func):
        @wraps(func)  # Keep wraps for function signature preservation
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        return wrapper
    return decorator

def set_module_logging(module_key: str, enabled: bool):
    """
    No-op implementation of set_module_logging function.
    
    Args:
        module_key: The module key to set logging for (ignored in dummy implementation)
        enabled: Whether to enable logging (ignored in dummy implementation)
    """
    pass
