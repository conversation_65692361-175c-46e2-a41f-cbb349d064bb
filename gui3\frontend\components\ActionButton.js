/**
 * ActionButton Web Component
 * A reusable button component that follows the Horseshoe Architecture
 * by only sending commands through the proper channels.
 */

import { ActionConfig } from '../config/actions.js';
import { CommandService } from '../services/CommandService.js';

export class ActionButton extends HTMLElement {
    constructor() {
        super();
        this.attachShadow({ mode: 'open' });
        this.commandService = CommandService.getInstance();
    }

    static get observedAttributes() {
        return ['action', 'label', 'symbol', 'quantity', 'disabled'];
    }

    connectedCallback() {
        this.render();
        this.attachEventListeners();
    }

    attributeChangedCallback(name, oldValue, newValue) {
        if (oldValue !== newValue) {
            this.render();
        }
    }

    render() {
        const action = this.getAttribute('action');
        const label = this.getAttribute('label') || action;
        const disabled = this.hasAttribute('disabled');
        const config = ActionConfig[action] || {};

        this.shadowRoot.innerHTML = `
            <style>
                :host {
                    display: inline-block;
                }
                
                button {
                    padding: 8px 16px;
                    font-size: 14px;
                    font-weight: 500;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    background-color: var(--primary-color, #007bff);
                    color: white;
                }
                
                button:hover:not(:disabled) {
                    background-color: var(--primary-hover, #0056b3);
                    transform: translateY(-1px);
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                }
                
                button:active:not(:disabled) {
                    transform: translateY(0);
                    box-shadow: none;
                }
                
                button:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }
                
                button.success {
                    background-color: #28a745;
                }
                
                button.error {
                    background-color: #dc3545;
                }
                
                button.warning {
                    background-color: #ffc107;
                    color: #212529;
                }
                
                .spinner {
                    display: inline-block;
                    width: 14px;
                    height: 14px;
                    border: 2px solid #ffffff;
                    border-radius: 50%;
                    border-top-color: transparent;
                    animation: spin 0.8s linear infinite;
                    margin-right: 8px;
                    vertical-align: middle;
                }
                
                @keyframes spin {
                    to { transform: rotate(360deg); }
                }
            </style>
            
            <button ${disabled ? 'disabled' : ''}>
                <span class="button-content">${label}</span>
            </button>
        `;
    }

    attachEventListeners() {
        const button = this.shadowRoot.querySelector('button');
        button.addEventListener('click', () => this.handleClick());
    }

    async handleClick() {
        const action = this.getAttribute('action');
        const config = ActionConfig[action];
        
        if (!config) {
            console.error(`No configuration found for action: ${action}`);
            return;
        }

        // Gather data from attributes
        const data = this.gatherActionData(config);

        // Show confirmation if required
        if (config.confirmation) {
            const confirmed = await this.showConfirmation(action, data);
            if (!confirmed) return;
        }

        // Disable button and show loading
        this.setLoading(true);

        try {
            // Send command through CommandService
            const result = await this.commandService.sendCommand(action, data);
            
            // Show success
            this.setStatus('success', config.successMessage || 'Success!');
            
            // Emit custom event
            this.dispatchEvent(new CustomEvent('action-complete', {
                detail: { action, data, result },
                bubbles: true
            }));
            
        } catch (error) {
            // Show error
            this.setStatus('error', error.message || 'Action failed');
            
            // Emit error event
            this.dispatchEvent(new CustomEvent('action-error', {
                detail: { action, data, error },
                bubbles: true
            }));
        }
    }

    gatherActionData(config) {
        const data = {};
        
        if (config.fields) {
            config.fields.forEach(field => {
                const value = this.getAttribute(field);
                if (value !== null) {
                    // Convert numeric fields
                    if (['quantity', 'price', 'limit_price'].includes(field)) {
                        data[field] = parseFloat(value);
                    } else {
                        data[field] = value;
                    }
                }
            });
        }
        
        return data;
    }

    async showConfirmation(action, data) {
        // Simple confirmation - can be replaced with a modal
        const message = `Confirm ${action}?\n${JSON.stringify(data, null, 2)}`;
        return confirm(message);
    }

    setLoading(loading) {
        const button = this.shadowRoot.querySelector('button');
        const content = button.querySelector('.button-content');
        
        if (loading) {
            button.disabled = true;
            content.innerHTML = '<span class="spinner"></span>Processing...';
        } else {
            button.disabled = this.hasAttribute('disabled');
            content.textContent = this.getAttribute('label') || this.getAttribute('action');
        }
    }

    setStatus(status, message) {
        const button = this.shadowRoot.querySelector('button');
        const content = button.querySelector('.button-content');
        
        // Remove all status classes
        button.classList.remove('success', 'error', 'warning');
        
        // Add new status class
        if (status) {
            button.classList.add(status);
        }
        
        // Update content
        content.textContent = message;
        
        // Reset after delay
        setTimeout(() => {
            this.setLoading(false);
            button.classList.remove('success', 'error', 'warning');
        }, 2000);
    }
}

// Register the component
customElements.define('action-button', ActionButton);