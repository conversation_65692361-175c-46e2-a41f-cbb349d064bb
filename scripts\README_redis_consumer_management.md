# Redis Consumer Management Scripts

This directory contains scripts for managing Redis consumers in the TESTRADE system.

## Scripts

### `cleanup_dead_redis_consumers.py`

**Purpose**: Removes orphaned Redis consumers that weren't properly cleaned up during shutdown.

**Problem Solved**: 
- Prevents accumulation of dead consumers in Redis consumer groups
- Reduces Redis memory usage and improves performance
- Fixes issues where hundreds or thousands of dead consumers build up over time

**Usage**:
```bash
# Run the cleanup script
python scripts/cleanup_dead_redis_consumers.py

# The script will:
# 1. Analyze all TESTRADE Redis streams
# 2. Identify dead consumers (inactive processes)
# 3. Show what will be removed
# 4. Ask for confirmation
# 5. Remove dead consumers from Redis
# 6. Verify cleanup was successful
```

**How it works**:
1. **Process Detection**: Identifies active TESTRADE processes (GUI backend, babysitter, etc.)
2. **Consumer Analysis**: Examines all Redis consumer groups and identifies consumers from inactive processes
3. **Safe Removal**: Only removes consumers that are:
   - From processes that are no longer running
   - Idle for more than 5 minutes OR have no pending messages
4. **Verification**: Confirms cleanup was successful

**Example Output**:
```
🧹 TESTRADE Redis Consumer Cleanup Tool
==================================================
✅ Connected to Redis successfully
🔍 Found 5 potentially active TESTRADE processes: ['28324', '37176']
📊 Found 27 Redis streams
📈 Analysis Results:
   Total consumers: 1203
   Dead consumers identified: 1176

⚠️  This will remove 1176 dead consumers from Redis.
Proceed with cleanup? (yes/no): yes

🎉 Cleanup completed successfully!
✅ All dead consumers removed successfully!
```

### `test_enhanced_shutdown.py`

**Purpose**: Tests the enhanced shutdown process without running the full GUI backend.

**Usage**:
```bash
python scripts/test_enhanced_shutdown.py
```

**What it tests**:
- Consumer cleanup logging
- Error handling during shutdown
- Proper shutdown sequence
- Reference to cleanup script in error messages

## Integration with GUI Backend

The GUI backend (`gui/gui_backend.py`) has been enhanced with:

### Enhanced Shutdown Process
- **Proper Consumer Cleanup**: Removes consumers from Redis consumer groups before stopping threads
- **Detailed Logging**: Shows cleanup progress with emojis and status messages
- **Error Tracking**: Counts successful vs failed cleanups
- **Script Reference**: Points users to the cleanup script if errors occur

### Key Features
```python
async def cleanup_redis_consumer(consumer, consumer_name: str):
    """Properly remove a Redis consumer from its consumer group"""
    # Uses xgroup_delconsumer to remove from Redis before stopping thread

@app.on_event("shutdown")
async def shutdown_event():
    # Enhanced cleanup process:
    # 1. Remove from Redis consumer group  
    # 2. Stop consumer thread
    # 3. Track success/error counts
    # 4. Reference cleanup script if needed
```

### Logging Examples
```
🔄 Shutting down TESTRADE GUI Backend...
🧹 Cleaning up 25 Redis consumers...
🔄 Stopping consumer account_summary...
✅ Removed consumer 'gui_consumer_account_summary_37176' from Redis group
✅ Consumer account_summary stopped and cleaned up from Redis.
📊 Consumer cleanup summary: 25 successful, 0 errors
🎉 TESTRADE GUI Backend shutdown complete - Redis consumers properly cleaned up
💡 If you notice Redis consumer buildup, run: python scripts/cleanup_dead_redis_consumers.py
```

## Monitoring

### Check Current Consumer Count
```bash
# Quick check of Redis consumer status
python -c "
import redis
r = redis.Redis(host='**************', port=6379, db=0, decode_responses=True)
total = 0
for key in r.keys('testrade:*'):
    if r.type(key) == 'stream':
        for group in r.xinfo_groups(key):
            total += len(r.xinfo_consumers(key, group['name']))
print(f'Total Redis consumers: {total}')
"
```

### Expected Consumer Counts
- **Healthy system**: 25-30 consumers (1 per stream for active GUI backend + a few service consumers)
- **Moderate buildup**: 50-100 consumers (2-4 GUI backend instances worth)
- **Heavy buildup**: 200+ consumers (indicates cleanup issues)
- **Critical buildup**: 1000+ consumers (major cleanup needed)

## Troubleshooting

### High Consumer Count
If you see hundreds or thousands of consumers:
1. Run `python scripts/cleanup_dead_redis_consumers.py`
2. Check if multiple GUI backend instances are running
3. Verify GUI backend shutdown process is working correctly

### Cleanup Script Issues
- **Redis connection failed**: Ensure Redis is running on **************:6379
- **Permission errors**: Run from TESTRADE root directory
- **Process detection issues**: Script will warn but continue with cleanup

### GUI Backend Shutdown Issues
- Check logs for consumer cleanup errors
- Verify Redis connectivity during shutdown
- Run cleanup script manually if automatic cleanup fails

## Best Practices

1. **Regular Monitoring**: Check consumer count periodically
2. **Proper Shutdown**: Always shut down GUI backend gracefully (Ctrl+C, not kill -9)
3. **Cleanup After Issues**: Run cleanup script after any abnormal shutdowns
4. **Log Review**: Check shutdown logs for cleanup errors

## Related Files

- `gui/gui_backend.py` - Enhanced shutdown process
- `intellisense/capture/redis_stream_consumer_base.py` - Base consumer class
- `scripts/monitor_redis_memory.py` - Redis memory monitoring
- `scripts/redis_memory_audit.py` - Redis memory analysis
