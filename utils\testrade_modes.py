"""
TESTRADE Runtime Mode Detection and Configuration

Defines the three operational modes and provides utilities for mode detection.
"""

import os
from enum import Enum
from typing import Optional

class TestradeMode(Enum):
    """
    TESTRADE operational modes
    """
    TANK_SEALED = "TANK_SEALED"    # Pure isolation, no telemetry interfaces
    TANK_BUFFERED = "TANK_BUFFERED"  # Telemetry interfaces but local buffering only
    LIVE = "LIVE"                  # Full system with external publishing

def get_current_mode() -> TestradeMode:
    """
    Detect current TESTRADE mode from environment variables and config
    
    Priority:
    1. TESTRADE_MODE environment variable
    2. Legacy environment variables (for compatibility)
    3. Config file settings
    4. Default to LIVE
    """
    
    # Primary mode detection
    mode_env = os.environ.get('TESTRADE_MODE', '').upper()
    if mode_env in [m.value for m in TestradeMode]:
        return TestradeMode(mode_env)
    
    # Legacy environment variable compatibility
    if os.environ.get('TESTRADE_TANK_MODE', '0').strip().lower() in ('1', 'true', 'yes'):
        return TestradeMode.TANK_SEALED
        
    if os.environ.get('TESTRADE_OFFLINE_MODE', '0').strip().lower() in ('1', 'true', 'yes'):
        return TestradeMode.TANK_BUFFERED
    
    # Check config file (if available)
    try:
        from utils.global_config import GlobalConfig
        config = GlobalConfig()
        
        # If IPC data dump is disabled, assume TANK_BUFFERED
        if not getattr(config, 'ENABLE_IPC_DATA_DUMP', True):
            return TestradeMode.TANK_BUFFERED
            
    except Exception:
        pass  # Config not available or error reading
    
    # Default to LIVE mode
    return TestradeMode.LIVE

def is_tank_sealed() -> bool:
    """Check if running in TANK_SEALED mode (no telemetry interfaces)"""
    return get_current_mode() == TestradeMode.TANK_SEALED

def is_tank_buffered() -> bool:
    """Check if running in TANK_BUFFERED mode (telemetry interfaces but local buffering)"""
    return get_current_mode() == TestradeMode.TANK_BUFFERED

def is_live_mode() -> bool:
    """Check if running in LIVE mode (full system active)"""
    return get_current_mode() == TestradeMode.LIVE

def requires_telemetry_service() -> bool:
    """Check if current mode requires telemetry service dependency"""
    mode = get_current_mode()
    return mode in [TestradeMode.TANK_BUFFERED, TestradeMode.LIVE]

def requires_ipc_services() -> bool:
    """Check if current mode requires IPC services (babysitter, ZMQ)"""
    return get_current_mode() == TestradeMode.LIVE

def requires_external_publishing() -> bool:
    """Check if current mode should publish to external services"""
    return get_current_mode() == TestradeMode.LIVE

def get_mode_description() -> str:
    """Get human-readable description of current mode"""
    mode = get_current_mode()
    descriptions = {
        TestradeMode.TANK_SEALED: "Pure isolation - no telemetry interfaces, no publishing",
        TestradeMode.TANK_BUFFERED: "Telemetry interfaces with local buffering only", 
        TestradeMode.LIVE: "Full system with external publishing and services"
    }
    return descriptions[mode]

# For backwards compatibility with existing code
def get_tank_mode_from_config(config) -> bool:
    """Legacy function for backwards compatibility"""
    return not requires_external_publishing()