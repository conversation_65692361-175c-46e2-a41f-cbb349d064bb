#pragma once
#if !defined(LS_L_MEMORY_H)
#define LS_L_MEMORY_H

// Copyright (c) 2001-2018 Lightspeed Financial, Inc. All rights reserved.

#include "L_Application.h"
#include "L_MessageIds.h"

#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
#include <utility>
#include <type_traits>
#include <assert.h>
#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

namespace LightspeedTrader
{


namespace L_BufferType
{
	const long Unspecified					= L_MessageRanges::interface_start;
	const long ObserverImpl					= L_MessageRanges::interface_start + 1;
	const long LibSortOperator				= L_MessageRanges::interface_start + 2;
	const long LibSymbolFilter				= L_MessageRanges::interface_start + 3;
	const long LibCompletionPort			= L_MessageRanges::interface_start + 4;
	const long LibThreadLoopWork			= L_MessageRanges::interface_start + 5;
}

class L_Buffer
{
public:
	virtual long L_BufferType() const = 0;
	virtual void const *L_Storage() const = 0;
	virtual void const *L_Object() const = 0;
	virtual void const *L_Interface() const = 0;
	virtual bool L_IncRef() const = 0;
	virtual void L_DecRef() const = 0;
	virtual void L_Init(long typeInit, size_t offset, size_t ioffset, void (*destruct)(void *memory)) = 0;
};


#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

template<typename T>
void C_Destructor(void *memory)
{
	reinterpret_cast<T *>(memory)->~T();
}

template<typename T>
void C_StdDelete(T *o)
{
	delete o;
}

#pragma push_macro("new")
#undef new

#if defined(_MSC_VER) && _MSC_VER < 1800

template<typename T>
void C_Constructor(T *res)
{
	new(res) T();
}
template<typename T, typename A1>
void C_Constructor(T *res, A1 &&carg1)
{
	new(res) T(std::forward<A1>(carg1));
}
template<typename T, typename A1, typename A2>
void C_Constructor(T *res, A1 &&carg1, A2 &&carg2)
{
	new(res) T(std::forward<A1>(carg1), std::forward<A2>(carg2));
}
template<typename T, typename A1, typename A2, typename A3>
void C_Constructor(T *res, A1 &&carg1, A2 &&carg2, A3 &&carg3)
{
	new(res) T(std::forward<A1>(carg1), std::forward<A2>(carg2), std::forward<A3>(carg3));
}
template<typename T, typename A1, typename A2, typename A3, typename A4>
void C_Constructor(T *res, A1 &&carg1, A2 &&carg2, A3 &&carg3, A4 &&carg4)
{
	new(res) T(std::forward<A1>(carg1), std::forward<A2>(carg2), std::forward<A3>(carg3), std::forward<A4>(carg4));
}
template<typename T, typename A1, typename A2, typename A3, typename A4, typename A5>
void C_Constructor(T *res, A1 &&carg1, A2 &&carg2, A3 &&carg3, A4 &&carg4, A5 &&carg5)
{
	new(res) T(std::forward<A1>(carg1), std::forward<A2>(carg2), std::forward<A3>(carg3), std::forward<A4>(carg4), std::forward<A5>(carg5));
}

#else

template<typename T, typename... Args>
void C_Constructor(T *res, Args &&...cargs)
{
	new(res) T(std::forward<Args>(cargs)...);
}

#endif

template<typename T>
bool ls_ptr_inc_ref(T *p) { return p->l_Buffer->L_IncRef(); }
template<typename T>
void ls_ptr_dec_ref(T *p) { p->l_Buffer->L_DecRef(); }


#pragma pop_macro("new")


template<typename T>
class C_Ptr
{
public:

	typedef T element_type;
	typedef T *pointer;
	typedef T &reference;

	template<typename Y>
	explicit C_Ptr(Y *ptr, bool addref = true) : p_(ptr)
	{
		if (addref && p_ && !ls_ptr_inc_ref(p_))
		{
			p_ = 0;
		}
	}

	~C_Ptr()
	{
		if (p_)
		{
			ls_ptr_dec_ref(p_);
		}
	}

	C_Ptr() : p_(0)
	{
	}

	C_Ptr(C_Ptr const &r) : p_(r.p_)
	{
		if (p_ && !ls_ptr_inc_ref(p_))
		{
			p_ = 0;
		}
	}

	template<typename Y>
	C_Ptr(C_Ptr<Y> const &r) : p_(r.p_)
	{
		if (p_ && !ls_ptr_inc_ref(p_))
		{
			p_ = 0;
		}
	}

	C_Ptr &operator=(C_Ptr const &r)
	{
		C_Ptr tmp(r);
		swap(tmp);

		return *this;
	}

	template<typename Y>
	C_Ptr &operator=(C_Ptr<Y> const &r)
	{
		C_Ptr tmp(r);
		swap(tmp);

		return *this;
	}

	C_Ptr &operator=(T *r)
	{
		C_Ptr tmp(r);
		swap(tmp);

		return *this;
	}

#if !(defined(_MSC_VER) && _MSC_VER < 1900)

	C_Ptr(C_Ptr &&r) noexcept : p_(r.p_)
	{
		r.p_ = 0;
	}

	template<typename Y>
	C_Ptr(C_Ptr<Y> &&r) noexcept : p_(r.p_)
	{
		r.p_ = 0;
	}

	C_Ptr &operator=(C_Ptr &&r) noexcept
	{
		swap(r);

		return *this;
	}

	template<typename Y>
	C_Ptr &operator=(C_Ptr<Y> &&r) noexcept
	{
		swap(r);

		return *this;
	}

	explicit operator bool() const
	{
		return p_ != 0;
	}

#else

	typedef pointer C_Ptr::*unspecified_bool_type;

	operator unspecified_bool_type() const
	{
		return p_ == 0 ? 0 : &C_Ptr::p_;
	}

#endif // !(defined(_MSC_VER) && _MSC_VER < 1910)


	void reset()
	{
		C_Ptr emptyHandle;
		swap(emptyHandle);
	}

	void reset(T *r)
	{
		C_Ptr tmp(r, false);
		swap(tmp);
	}

	reference operator*() const
	{
		assert(p_ != 0);
		return *p_;
	}

	pointer operator->() const
	{
		assert(p_ != 0);
		return p_;
	}

	pointer get() const
	{
		return p_;
	}

	void swap(C_Ptr &other)
	{
		T *tmp = p_;
		p_ = other.p_;
		other.p_ = tmp;
	}

	T *p_;
};  // C_Ptr

template<typename T, typename U>
bool operator==(C_Ptr<T> const &a, C_Ptr<U> const &b)
{
	return a.get() == b.get();
}

template<typename T>
bool operator==(C_Ptr<T> const &a, C_Ptr<T> const &b)
{
	return a.get() == b.get();
}

template<typename T>
bool operator!=(C_Ptr<T> const &a, C_Ptr<T> const &b)
{
	return a.get() != b.get();
}

template<typename T>
bool operator<(C_Ptr<T> const &a, C_Ptr<T> const &b)
{
	return a.get() < b.get();
}

template<typename T>
void swap(C_Ptr<T> &a, C_Ptr<T> &b)
{
	a.swap(b);
}

template<typename T, typename U>
C_Ptr<T> static_pointer_cast(C_Ptr<U> const &p)
{
	return static_cast<T *>(p.get());
}

template<typename T, typename U>
C_Ptr<T> const_pointer_cast(C_Ptr<U> const &p)
{
	return const_cast<T *>(p.get());
}

template<typename T, typename U>
C_Ptr<T> dynamic_pointer_cast(C_Ptr<U> const &p)
{
	return dynamic_cast<T *>(p.get());
}

template<typename T>
T *get_pointer(C_Ptr<T> const &p)
{
	return p.get();
}

template<typename T>
T *get_pointer(T *p)
{
	return p;
}


#if defined(_MSC_VER) && _MSC_VER < 1800

template<typename T>
C_Ptr<T> C_Alloc()
{
	static_assert(T::id >= L_MessageRanges::interface_start, "C_Alloc: The buffer id should be greater than or equal to L_MessageRanges::id_start.");
	static_assert(std::is_base_of<typename T::alloc_interface_type, T>::value, "C_Alloc: The buffer type doesn't extend the interface type.");
	T *res = 0;
	L_Buffer *p = L_Alloc(__alignof(T) + sizeof(T));
	if (p)
	{
		char const *storage = reinterpret_cast<char const *>(p->L_Storage());
		size_t offset = ( __alignof(T) - ( reinterpret_cast<size_t>(storage) & ( __alignof(T) - 1 ) ) ) & ( __alignof(T) - 1 );
		p->L_Init(T::id, offset, reinterpret_cast<size_t>(static_cast<typename T::alloc_interface_type *>(reinterpret_cast<T *>(offset))), C_Destructor<T>);
		res = const_cast<T *>(reinterpret_cast<T const *>(storage + offset));
		C_Constructor(res);
		res->l_Buffer = p;
	}
	return C_Ptr<T>(res);
}
template<typename T, typename A1>
C_Ptr<T> C_Alloc(A1 &&carg1)
{
	static_assert(T::id >= L_MessageRanges::interface_start, "C_Alloc: The buffer id should be greater than or equal to L_MessageRanges::id_start.");
	static_assert(std::is_base_of<typename T::alloc_interface_type, T>::value, "C_Alloc: The buffer type doesn't extend the interface type.");
	T *res = 0;
	L_Buffer *p = L_Alloc(__alignof(T) + sizeof(T));
	if (p)
	{
		char const *storage = reinterpret_cast<char const *>(p->L_Storage());
		size_t offset = ( __alignof(T) - ( reinterpret_cast<size_t>(storage) & ( __alignof(T) - 1 ) ) ) & ( __alignof(T) - 1 );
		p->L_Init(T::id, offset, reinterpret_cast<size_t>(static_cast<typename T::alloc_interface_type *>(reinterpret_cast<T *>(offset))), C_Destructor<T>);
		res = const_cast<T *>(reinterpret_cast<T const *>(storage + offset));
		C_Constructor(res, std::forward<A1>(carg1));
		res->l_Buffer = p;
	}
	return C_Ptr<T>(res);
}
template<typename T, typename A1, typename A2>
C_Ptr<T> C_Alloc(A1 &&carg1, A2 &&carg2)
{
	static_assert(T::id >= L_MessageRanges::interface_start, "C_Alloc: The buffer id should be greater than or equal to L_MessageRanges::id_start.");
	static_assert(std::is_base_of<typename T::alloc_interface_type, T>::value, "C_Alloc: The buffer type doesn't extend the interface type.");
	T *res = 0;
	L_Buffer *p = L_Alloc(__alignof(T) + sizeof(T));
	if (p)
	{
		char const *storage = reinterpret_cast<char const *>(p->L_Storage());
		size_t offset = ( __alignof(T) - ( reinterpret_cast<size_t>(storage) & ( __alignof(T) - 1 ) ) ) & ( __alignof(T) - 1 );
		p->L_Init(T::id, offset, reinterpret_cast<size_t>(static_cast<typename T::alloc_interface_type *>(reinterpret_cast<T *>(offset))), C_Destructor<T>);
		res = const_cast<T *>(reinterpret_cast<T const *>(storage + offset));
		C_Constructor(res, std::forward<A1>(carg1), std::forward<A2>(carg2));
		res->l_Buffer = p;
	}
	return C_Ptr<T>(res);
}
template<typename T, typename A1, typename A2, typename A3>
C_Ptr<T> C_Alloc(A1 &&carg1, A2 &&carg2, A3 &&carg3)
{
	static_assert(T::id >= L_MessageRanges::interface_start, "C_Alloc: The buffer id should be greater than or equal to L_MessageRanges::id_start.");
	static_assert(std::is_base_of<typename T::alloc_interface_type, T>::value, "C_Alloc: The buffer type doesn't extend the interface type.");
	T *res = 0;
	L_Buffer *p = L_Alloc(__alignof(T) + sizeof(T));
	if (p)
	{
		char const *storage = reinterpret_cast<char const *>(p->L_Storage());
		size_t offset = ( __alignof(T) - ( reinterpret_cast<size_t>(storage) & ( __alignof(T) - 1 ) ) ) & ( __alignof(T) - 1 );
		p->L_Init(T::id, offset, reinterpret_cast<size_t>(static_cast<typename T::alloc_interface_type *>(reinterpret_cast<T *>(offset))), C_Destructor<T>);
		res = const_cast<T *>(reinterpret_cast<T const *>(storage + offset));
		C_Constructor(res, std::forward<A1>(carg1), std::forward<A2>(carg2), std::forward<A3>(carg3));
		res->l_Buffer = p;
	}
	return C_Ptr<T>(res);
}
template<typename T, typename A1, typename A2, typename A3, typename A4>
C_Ptr<T> C_Alloc(A1 &&carg1, A2 &&carg2, A3 &&carg3, A4 &&carg4)
{
	static_assert(T::id >= L_MessageRanges::interface_start, "C_Alloc: The buffer id should be greater than or equal to L_MessageRanges::id_start.");
	static_assert(std::is_base_of<typename T::alloc_interface_type, T>::value, "C_Alloc: The buffer type doesn't extend the interface type.");
	T *res = 0;
	L_Buffer *p = L_Alloc(__alignof(T) + sizeof(T));
	if (p)
	{
		char const *storage = reinterpret_cast<char const *>(p->L_Storage());
		size_t offset = ( __alignof(T) - ( reinterpret_cast<size_t>(storage) & ( __alignof(T) - 1 ) ) ) & ( __alignof(T) - 1 );
		p->L_Init(T::id, offset, reinterpret_cast<size_t>(static_cast<typename T::alloc_interface_type *>(reinterpret_cast<T *>(offset))), C_Destructor<T>);
		res = const_cast<T *>(reinterpret_cast<T const *>(storage + offset));
		C_Constructor(res, std::forward<A1>(carg1), std::forward<A2>(carg2), std::forward<A3>(carg3), std::forward<A4>(carg4));
		res->l_Buffer = p;
	}
	return C_Ptr<T>(res);
}
template<typename T, typename A1, typename A2, typename A3, typename A4, typename A5>
C_Ptr<T> C_Alloc(A1 &&carg1, A2 &&carg2, A3 &&carg3, A4 &&carg4, A5 &&carg5)
{
	static_assert(T::id >= L_MessageRanges::interface_start, "C_Alloc: The buffer id should be greater than or equal to L_MessageRanges::id_start.");
	static_assert(std::is_base_of<typename T::alloc_interface_type, T>::value, "C_Alloc: The buffer type doesn't extend the interface type.");
	T *res = 0;
	L_Buffer *p = L_Alloc(__alignof(T) + sizeof(T));
	if (p)
	{
		char const *storage = reinterpret_cast<char const *>(p->L_Storage());
		size_t offset = ( __alignof(T) - ( reinterpret_cast<size_t>(storage) & ( __alignof(T) - 1 ) ) ) & ( __alignof(T) - 1 );
		p->L_Init(T::id, offset, reinterpret_cast<size_t>(static_cast<typename T::alloc_interface_type *>(reinterpret_cast<T *>(offset))), C_Destructor<T>);
		res = const_cast<T *>(reinterpret_cast<T const *>(storage + offset));
		C_Constructor(res, std::forward<A1>(carg1), std::forward<A2>(carg2), std::forward<A3>(carg3), std::forward<A4>(carg4), std::forward<A5>(carg5));
		res->l_Buffer = p;
	}
	return C_Ptr<T>(res);
}

#else

template<typename T, typename... Args>
C_Ptr<T> C_Alloc(Args &&...cargs)
{
	static_assert(T::id >= L_MessageRanges::interface_start, "C_Alloc: The buffer id should be greater than or equal to L_MessageRanges::id_start.");
	static_assert(std::is_base_of<typename T::alloc_interface_type, T>::value, "C_Alloc: The buffer type doesn't extend the interface type.");
	T *res = 0;
	L_Buffer *p = L_Alloc(__alignof(T) + sizeof(T));
	if (p)
	{
		char const *storage = reinterpret_cast<char const *>(p->L_Storage());
		size_t offset = ( __alignof(T) - ( reinterpret_cast<size_t>(storage) & ( __alignof(T) - 1 ) ) ) & ( __alignof(T) - 1 );
		p->L_Init(T::id, offset, reinterpret_cast<size_t>(static_cast<typename T::alloc_interface_type *>(reinterpret_cast<T *>(offset))), C_Destructor<T>);
		res = const_cast<T *>(reinterpret_cast<T const *>(storage + offset));
		C_Constructor(res, std::forward<Args>(cargs)...);
		res->l_Buffer = p;
	}
	return C_Ptr<T>(res);
}

#endif

#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

}

#endif // !defined(LS_L_MEMORY_H)
