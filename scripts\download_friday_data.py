#!/usr/bin/env python3
"""
Download Friday 5/30/25 Market Data

Simple script to download the specific data requested:
- Symbols: MSFT, AAPL, SPY, NVDA, UBER
- Date: Friday, May 30, 2025
- Time: 9:45 AM to 10:45 AM EST
- Data: Lowest timeframe (tick data) from Alpaca

Usage:
    python download_friday_data.py
"""

import os
import sys
import logging
from datetime import datetime, timezone, timedelta

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import the downloader
from download_alpaca_tick_data import AlpacaTickDataDownloader

# Import credentials from GlobalConfig
try:
    from utils.global_config import GlobalConfig
    config = GlobalConfig()
    ALPACA_API_KEY = config.ALPACA_API_KEY
    ALPACA_API_SECRET = config.ALPACA_API_SECRET
except ImportError as e:
    print(f"Error importing Alpaca credentials: {e}")
    sys.exit(1)

# Set up logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Download the specific data requested."""
    
    # Configuration for the specific request
    SYMBOLS = ['MSFT', 'AAPL', 'SPY', 'NVDA', 'UBER']
    DATE = '2025-05-30'
    START_TIME = '09:45:00'
    END_TIME = '10:45:00'
    OUTPUT_DIR = 'data/friday_may30_2025'
    
    # Create timezone-aware datetime objects (EDT = UTC-4 for May 30, 2025)
    edt = timezone(timedelta(hours=-4))
    start_datetime = datetime.strptime(f"{DATE} {START_TIME}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=edt)
    end_datetime = datetime.strptime(f"{DATE} {END_TIME}", "%Y-%m-%d %H:%M:%S").replace(tzinfo=edt)
    
    print("="*80)
    print("ALPACA TICK DATA DOWNLOAD")
    print("="*80)
    print(f"Symbols: {', '.join(SYMBOLS)}")
    print(f"Date: Friday, May 30, 2025")
    print(f"Time: {START_TIME} - {END_TIME} EDT")
    print(f"Duration: 60 minutes")
    print(f"Output: {OUTPUT_DIR}")
    print("="*80)
    
    # Initialize downloader
    try:
        downloader = AlpacaTickDataDownloader(
            api_key=ALPACA_API_KEY,
            api_secret=ALPACA_API_SECRET
        )
        logger.info("Alpaca downloader initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize downloader: {e}")
        return 1
    
    # Create output directory
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Download trade data (tick data)
    print("\n📈 Downloading TRADE data (tick data)...")
    trade_files = downloader.download_trades(
        symbols=SYMBOLS,
        start_time=start_datetime,
        end_time=end_datetime,
        output_dir=OUTPUT_DIR
    )
    
    # Download quote data (NBBO)
    print("\n📊 Downloading QUOTE data (NBBO)...")
    quote_files = downloader.download_quotes(
        symbols=SYMBOLS,
        start_time=start_datetime,
        end_time=end_datetime,
        output_dir=OUTPUT_DIR
    )
    
    # Summary
    print("\n" + "="*80)
    print("DOWNLOAD COMPLETE")
    print("="*80)
    
    if trade_files:
        print(f"\n✅ TRADE FILES ({len(trade_files)}):")
        for symbol, filepath in trade_files.items():
            filename = os.path.basename(filepath)
            file_size = os.path.getsize(filepath) if os.path.exists(filepath) else 0
            print(f"   {symbol}: {filename} ({file_size:,} bytes)")
    
    if quote_files:
        print(f"\n✅ QUOTE FILES ({len(quote_files)}):")
        for symbol, filepath in quote_files.items():
            filename = os.path.basename(filepath)
            file_size = os.path.getsize(filepath) if os.path.exists(filepath) else 0
            print(f"   {symbol}: {filename} ({file_size:,} bytes)")
    
    total_files = len(trade_files) + len(quote_files)
    print(f"\n📁 Total files downloaded: {total_files}")
    print(f"📂 Output directory: {OUTPUT_DIR}")
    
    # Quick analysis suggestion
    if total_files > 0:
        print(f"\n💡 To analyze the downloaded data, run:")
        print(f"   python scripts/analyze_alpaca_data.py --data-dir {OUTPUT_DIR} --summary")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
