"""
flicker_filter.py

Step 2: Implement the actual flicker-filter logic using the FlickerFilterParams
from flicker_filter_config.py. This file handles:
 - Weighted-average feasibility check
 - Re-check logic if mismatch
 - Tolerance in cents
 - Price check mode (how we choose the fill price)
"""

import time
import logging
from typing import Optional, Dict, Any

# Import the dataclass FlickerFilterParams from your config file
from utils.flicker_filter_config import FlickerFilterParams

# <<< ADD Import near top >>>
try:
    # Adjust path if necessary based on your project structure
    from interfaces.data.services import IPriceProvider
except ImportError:
    print("ERROR: Could not import IPriceProvider in flicker_filter.py")
    class IPriceProvider: # Dummy class
        def get_latest_price(self, symbol: str) -> float: return 0.0
        def get_reference_price(self, symbol: str, perf_timestamps: Optional[Dict[str, float]] = None) -> float: return 0.0
        def get_reliable_price(self, symbol: str, side: Optional[str] = None) -> float: return 0.0
# --- End ADD ---

# Initialize logger
logger = logging.getLogger(__name__)

###############################################
# Weighted-average cost basis helper
###############################################
def expected_cost_basis(S_old: int, C_old: float, S_add: int, fill_price: float) -> float:
    """
    Weighted-average calculation if you add `S_add` shares at `fill_price`.
    new_cost = [ (S_old*C_old) + (S_add*fill_price) ] / (S_old + S_add)
    """
    numerator = (S_old * C_old) + (S_add * fill_price)
    denominator = (S_old + S_add)
    if denominator <= 0:
        # fallback if no shares
        return C_old
    return numerator / denominator

###############################################
# Decide which price to use based on price_check_mode
###############################################
# <<< MODIFY Signature >>> Add price_provider argument
def choose_fill_price(price_provider: IPriceProvider, # <<< ADD ARGUMENT
                      symbol: str,
                      flicker_params: FlickerFilterParams,
                      trader_price: float=0.0,
                      C_new_ocr: float=0.0) -> float:
    # --- End MODIFY ---
    """
    Chooses the fill price used in Weighted Average based on flicker_params.price_check_mode.
    - "trader_live_price": if trader_price>0, use that
    - "synced_alpaca_price": e.g. time-synced local feed
    - "alpaca_live_price": direct sub-based feed

    Args:
        price_provider: The price provider instance to use for getting prices
        symbol: The stock symbol
        flicker_params: Parameters for the flicker filter
        trader_price: Optional trader price to use
        C_new_ocr: The new OCR cost basis value (used as fallback)

    Returns:
        The chosen fill price
    """
    if flicker_params is None:
        flicker_params = FlickerFilterParams()

    mode = flicker_params.price_check_mode

    # First try trader_price if available and appropriate
    if mode == "trader_live_price" and trader_price > 0:
        return trader_price

    # For other modes, try to get price from the price provider
    live_price = None
    try:
        # Use the robust price fetching method
        if symbol and symbol != "UNKNOWN":
            live_price = price_provider.get_reliable_price(symbol)
    except Exception as e:
        # Log error getting price from provider
        logger.error(f"Flicker Filter: Error getting price via provider for {symbol}: {e}")
        live_price = None  # Fallback if provider fails

    # DEFENSIVE PROGRAMMING: Handle None case explicitly and validate numeric value
    if live_price is None:
        logger.warning(f"Flicker Filter: get_reliable_price returned None for {symbol}. Using fallback logic.")
        live_price = 0.0
    elif not isinstance(live_price, (int, float)):
        logger.warning(f"Flicker Filter: get_reliable_price returned non-numeric value {live_price} (type: {type(live_price)}) for {symbol}. Setting to 0.0.")
        live_price = 0.0
    elif live_price < 0:
        logger.warning(f"Flicker Filter: get_reliable_price returned negative value {live_price} for {symbol}. Setting to 0.0.")
        live_price = 0.0

    # If we got a valid live price, use it
    if live_price > 0:
        return live_price

    # If trader_price is available as fallback, use it
    if trader_price > 0:
        return trader_price

    # If we have a valid OCR value, use it as last resort
    if C_new_ocr > 0:
        logger.warning(f"Flicker Filter: No valid price for {symbol}. Using OCR value {C_new_ocr} as fallback.")
        return C_new_ocr

    # Absolute last resort - no valid price available
    logger.warning(f"Flicker Filter: No valid price available for {symbol}. Returning 0.")
    return 0.0

###############################################
# Main flicker-check function
###############################################
# <<< MODIFY Signature >>> Add price_provider argument
def flicker_check_new_cost_basis(
    price_provider: IPriceProvider, # <<< ADD ARGUMENT
    S_old: int,
    C_old: float,
    C_new_ocr: float,
    flicker_params: FlickerFilterParams,
    trader_price: float=0.0,
    symbol: str = "UNKNOWN"
) -> bool:
    # --- End MODIFY ---
    """
    Returns True if we accept C_new_ocr as a feasible new cost basis,
    or False if we consider it a flicker (discard).

    Args:
        price_provider: The price provider instance to use for getting prices
        S_old: The old number of shares
        C_old: The old cost basis
        C_new_ocr: The new OCR cost basis
        flicker_params: Parameters for the flicker filter
        trader_price: Optional trader price to use
        symbol: The stock symbol

    Returns:
        True if the new cost basis is accepted, False if it's considered a flicker

    Steps:
      1) For up to (recheck_count+1) attempts:
         - pick fill_price based on flicker_params.price_check_mode
         - compute Weighted Average => expected_cb
         - compare with C_new_ocr using accept_tolerance_cents => if within => True
         - else if we still can re-check => sleep recheck_delay_sec
      2) If after all tries mismatch => False => flicker
    """

    # If flicker filter is disabled => always accept
    if not flicker_params.enable_flicker_filter:
        return True

    # Convert from cents => dollars
    tolerance_dollars = flicker_params.accept_tolerance_cents / 100.0

    for attempt in range(flicker_params.recheck_count + 1):
        # 1) pick fill price
        # <<< MODIFY CALL >>> Pass price_provider
        fill_price = choose_fill_price(
            price_provider=price_provider, # <<< PASS ARGUMENT
            symbol=symbol,
            flicker_params=flicker_params,
            trader_price=trader_price,
            C_new_ocr=C_new_ocr
        )
        # --- End MODIFY ---

        if fill_price <= 0:
            # If we can't get a valid fill price => choose to accept or skip
            # We'll accept in this example
            return True

        # 2) compute Weighted Average
        calc_cb = expected_cost_basis(S_old, C_old, flicker_params.known_add_shares, fill_price)

        # 3) compare
        diff = abs(calc_cb - C_new_ocr)
        if diff < tolerance_dollars:
            return True  # accepted => no flicker

        # mismatch => if we still have attempts left, wait
        if attempt < flicker_params.recheck_count:
            time.sleep(flicker_params.recheck_delay_sec)

    # after all attempts => flicker
    return False


###############################################
# Optional: is_flicker => Return the opposite
###############################################
# <<< MODIFY Signature >>> Add price_provider argument
def is_flicker(
    price_provider: IPriceProvider, # <<< ADD ARGUMENT
    S_old: int,
    C_old: float,
    C_new_ocr: float,
    flicker_params: FlickerFilterParams,
    trader_price: float=0.0,
    symbol: str = "UNKNOWN"
) -> bool:
    # --- End MODIFY ---
    """
    Returns True if it's a flicker, False if accepted.
    (just a wrapper in case you prefer this naming)

    Args:
        price_provider: The price provider instance to use for getting prices
        S_old: The old number of shares
        C_old: The old cost basis
        C_new_ocr: The new OCR cost basis
        flicker_params: Parameters for the flicker filter
        trader_price: Optional trader price to use
        symbol: The stock symbol

    Returns:
        True if it's a flicker, False if accepted
    """
    # <<< MODIFY CALL >>> Pass price_provider
    accepted = flicker_check_new_cost_basis(
        price_provider=price_provider, # <<< PASS ARGUMENT
        S_old=S_old,
        C_old=C_old,
        C_new_ocr=C_new_ocr,
        flicker_params=flicker_params,
        trader_price=trader_price,
        symbol=symbol
    )
    # --- End MODIFY ---
    return not accepted

"""
Usage Example in processed_ocr_module.py (Step 3):
-------------------------------------------------

# from flicker_filter import flicker_check_new_cost_basis
# from flicker_filter_config import FlickerFilterParams
# from modules.price_fetching.interfaces import IPriceProvider

flicker_params = FlickerFilterParams(
    enable_flicker_filter=True,
    known_add_shares=10000,
    time_sync_offset_sec=0.0,
    accept_tolerance_cents=5.0,
    recheck_count=1,
    recheck_delay_sec=0.2,
    price_check_mode="synced_alpaca_price"
)

def handle_new_cost_basis(symbol, old_cost, new_cost_ocr, price_provider, trader_price=0.0):
    if flicker_check_new_cost_basis(
        price_provider=price_provider,  # Pass the price provider instance
        S_old=10000,
        C_old=old_cost,
        C_new_ocr=new_cost_ocr,
        flicker_params=flicker_params,
        trader_price=trader_price,
        symbol=symbol  # Pass the symbol
    ):
        # Accept => update stable cost
        stable_cost_map[symbol] = new_cost_ocr
    else:
        # Flicker => discard
        log_discard(symbol, old_cost, new_cost_ocr)
"""
