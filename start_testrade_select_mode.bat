@echo off
REM ============================================================================
REM TESTRADE Mode Selection Startup Script
REM Allows user to select execution mode before starting
REM ============================================================================

setlocal enabledelayedexpansion
cd /d C:\TESTRADE

echo.
echo ============================================================================
echo                     TESTRADE MODE SELECTION
echo ============================================================================
echo.
echo Please select startup mode:
echo.
echo   1. BROADCAST MODE (Full System)
echo      - Babysitter Service (IPC buffering + Redis publishing)
echo      - ApplicationCore (OCR + Trading)
echo      - GUI Backend + Frontend
echo      - All data published to Redis streams
echo.
echo   2. HEADLESS WITH IPC BUFFERING
echo      - Babysitter Service (IPC buffering + Redis publishing)
echo      - ApplicationCore (OCR + Trading)
echo      - NO GUI components
echo      - Data still published to Redis
echo.
echo   3. HEADLESS NO BROADCAST
echo      - ApplicationCore only (OCR + Trading)
echo      - NO Babysitter Service
echo      - NO GUI components
echo      - NO Redis publishing
echo      - Minimal mode for testing
echo.
echo   4. EXIT
echo.
echo ============================================================================
echo.

:prompt_mode
set /P MODE_CHOICE="Enter your choice (1-4): "

if "%MODE_CHOICE%"=="1" goto :broadcast_mode
if "%MODE_CHOICE%"=="2" goto :headless_buffered
if "%MODE_CHOICE%"=="3" goto :headless_nobcast
if "%MODE_CHOICE%"=="4" goto :exit_script

echo Invalid choice. Please enter 1, 2, 3, or 4.
goto :prompt_mode

:broadcast_mode
echo.
echo ============================================================================
echo Starting BROADCAST MODE (Full System)...
echo ============================================================================
echo.

REM Ensure Babysitter and IPC are enabled
call :update_config "BABYSITTER_ENABLED" "true"
call :update_config "ENABLE_IPC_DATA_DUMP" "true"

REM Check/Start Babysitter
call :check_start_babysitter

REM Now start the full system
echo Starting full TESTRADE system...
call .venv\Scripts\activate.bat
python start_testrade.py
goto :end

:headless_buffered
echo.
echo ============================================================================
echo Starting HEADLESS WITH IPC BUFFERING MODE...
echo ============================================================================
echo.

REM Enable IPC data dumping but DO NOT start Babysitter
call :update_config "BABYSITTER_ENABLED" "false"
call :update_config "ENABLE_IPC_DATA_DUMP" "true"

REM Do NOT start Babysitter - data will buffer to disk via mmap
echo Babysitter Service: DISABLED (data will buffer to disk)
echo IPC Data Dumping: ENABLED (data will be buffered, not sent)

REM Start core only
echo Starting TESTRADE Core (headless with IPC buffering to disk)...
call .venv\Scripts\activate.bat
python run_headless_core.py
goto :end

:headless_nobcast
echo.
echo ============================================================================
echo Starting HEADLESS NO BROADCAST MODE...
echo ============================================================================
echo.

REM Disable Babysitter and IPC
call :update_config "BABYSITTER_ENABLED" "false"
call :update_config "ENABLE_IPC_DATA_DUMP" "false"

REM Start core only
echo Starting TESTRADE Core (headless, no IPC)...
call .venv\Scripts\activate.bat
python run_headless_core.py
goto :end

:check_start_babysitter
REM Check if Babysitter is running
tasklist /FI "IMAGENAME eq python.exe" 2>nul | findstr /i "babysitter_service.py" >nul
if errorlevel 1 (
    echo.
    echo Babysitter not running. Starting Babysitter Service...
    start /B cmd /c "cd /d C:\TESTRADE && .venv\Scripts\python.exe core\babysitter_service.py > logs\babysitter_service.log 2>&1"
    echo Waiting for Babysitter to initialize...
    timeout /t 3 /nobreak >nul
    echo Babysitter Service started.
) else (
    echo Babysitter Service is already running.
)
exit /b

:update_config
REM Update a single config value
echo Updating config: %~1 = %~2
python -c "import json; config=json.load(open('utils/control.json')); config['%~1']=%~2; json.dump(config, open('utils/control.json','w'), indent=2)" 2>nul
if errorlevel 1 (
    echo WARNING: Could not update config value %~1
)
exit /b

:exit_script
echo.
echo Exiting without starting TESTRADE.
pause
exit /b 0

:end
echo.
echo ============================================================================
echo TESTRADE session ended.
echo ============================================================================
pause