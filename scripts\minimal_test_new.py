# minimal_test.py
import time
import logging 
import threading 
import psutil # For patch_main.py
import tracemalloc # For patch_main.py
import atexit # For patch_main.py
from datetime import datetime # For patch_main.py
from pathlib import Path # For patch_main.py

# ABSOLUTELY NO IMPORTS FROM YOUR 'TESTRADE' PROJECT HERE
# (no gui, no modules, no utils other than what patch_main might need if it's complex)

logging.basicConfig(level=logging.ERROR)
logger = logging.getLogger("MinimalTest")

def list_active_threads(context_msg=""):
    logger.error(f"--- {context_msg} ACTIVE THREADS ---")
    for thread in threading.enumerate():
        logger.error(f"Thread Name: {thread.name}, ID: {thread.ident}, Daemon: {thread.daemon}, Alive: {thread.is_alive()}")
    logger.error(f"--- END {context_msg} ACTIVE THREADS ---")


# Initialize tracemalloc
tracemalloc.start()
print("Tracemalloc started for memory tracking")

# Function to take a memory snapshot after a delay
def take_memory_snapshot():
    # Wait for the specified duration
    print(f"Will take memory snapshot after {WAIT_DURATION_MINUTES} minutes...")
    time.sleep(WAIT_DURATION_MINUTES * 60)
    
    # Take the snapshot
    snapshot = tracemalloc.take_snapshot()
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    snapshot_path = f"tracemalloc_minimal_{timestamp}.log"
    
    print(f"Taking memory snapshot after {WAIT_DURATION_MINUTES} minutes...")
    
    # Save top statistics
    with open(snapshot_path, 'w') as f:
        f.write(f"=== Memory snapshot after {WAIT_DURATION_MINUTES} minutes ===
")
        f.write(f"Timestamp: {datetime.datetime.now()}

")
        
        # Overall statistics
        f.write("=== Overall Statistics ===
")
        f.write(f"Current traced memory: {tracemalloc.get_traced_memory()[0] / 1024 / 1024:.2f} MB
")
        f.write(f"Peak traced memory: {tracemalloc.get_traced_memory()[1] / 1024 / 1024:.2f} MB

")
        
        # Top statistics by line
        f.write("=== Top 100 memory allocations by line ===
")
        top_stats = snapshot.statistics('lineno')
        for i, stat in enumerate(top_stats[:100], 1):
            f.write(f"{i}. {stat.count} allocations: {stat.size / 1024:.1f} KiB
")
            f.write(f"   {stat.traceback.format()[0]}
")
        
        # Top statistics by file
        f.write("
=== Top 50 memory allocations by file ===
")
        file_stats = snapshot.statistics('filename')
        for i, stat in enumerate(file_stats[:50], 1):
            f.write(f"{i}. {stat.count} allocations: {stat.size / 1024:.1f} KiB - {os.path.basename(stat.traceback[0].filename)}
")
        
        # Top statistics by traceback
        f.write("
=== Top 50 memory allocations by traceback ===
")
        trace_stats = snapshot.statistics('traceback')
        for i, stat in enumerate(trace_stats[:50], 1):
            f.write(f"{i}. {stat.count} allocations: {stat.size / 1024:.1f} KiB
")
            for line in stat.traceback.format():
                f.write(f"   {line}
")
    
    print(f"Memory snapshot saved to {snapshot_path}")

# Start the snapshot thread
WAIT_DURATION_MINUTES = {WAIT_DURATION_MINUTES}  # Minutes to wait before taking snapshot
snapshot_thread = threading.Thread(target=take_memory_snapshot, daemon=True)
snapshot_thread.start()


if __name__ == "__main__":
    logger.error("MINIMAL_TEST: Starting.")
    list_active_threads("STARTUP")

    # patch_main.py will inject tracemalloc start, initial snapshot, etc.
    # This script just needs to keep the process alive for 15 minutes.
    
    end_time = time.time() + 15 * 60
    snapshot_interval_seconds = 5 * 60 # Log threads every 5 minutes
    next_thread_log_time = time.time() + snapshot_interval_seconds

    while time.time() < end_time:
        # Check if it's time to log threads
        if time.time() >= next_thread_log_time:
            logger.error(f"MINIMAL_TEST: Still alive at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            list_active_threads("PERIODIC")
            next_thread_log_time += snapshot_interval_seconds
        
        # Sleep for a short duration to allow loop to exit if end_time is reached
        # and to make the loop responsive to KeyboardInterrupt if run manually.
        try:
            time.sleep(1) 
        except KeyboardInterrupt:
            logger.error("MINIMAL_TEST: KeyboardInterrupt received.")
            break
    
    logger.error("MINIMAL_TEST: Finished 15-minute duration.")
    list_active_threads("SHUTDOWN")
