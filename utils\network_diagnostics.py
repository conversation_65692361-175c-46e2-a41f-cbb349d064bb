"""
Network Diagnostics Module

This module provides utilities for diagnosing network traffic issues and health monitoring.
It can be enabled/disabled via a simple flag and provides detailed logging
of network traffic from various components of the application.

Usage:
    from utils.network_diagnostics import enable_network_diagnostics, log_network_send, NetworkDiagnostics

    # Enable diagnostics
    enable_network_diagnostics(True)

    # Log network sends
    log_network_send("ALPACA_WS", data, include_data=False)

    # Use NetworkDiagnostics for health monitoring
    diagnostics = NetworkDiagnostics()
    connectivity = diagnostics.check_connectivity()
    latency = diagnostics.measure_latency()

    # Ensure cleanup on application exit
    from utils.network_diagnostics import cleanup_network_diagnostics
    import atexit
    atexit.register(cleanup_network_diagnostics)
"""

import threading
import time
import json
import logging
import psutil
import weakref
import atexit
from typing import Any, Dict, Optional, Union, List

# Global flag to enable/disable network diagnostics
NETWORK_DIAGNOSTICS_ENABLED = False

# Logger for network diagnostics
logger = logging.getLogger("network_diagnostics")

# Constants for resource management
MAX_COMPONENT_COUNTERS = 100  # Maximum number of components to track
COMPONENT_CLEANUP_INTERVAL = 300  # Cleanup inactive components every 5 minutes (in seconds)
INACTIVE_THRESHOLD = 300  # Consider components inactive after 5 minutes of no activity
THREAD_JOIN_TIMEOUT = 2.0  # Timeout for joining monitor thread (in seconds)
LARGE_PACKET_THRESHOLD = 10240  # Size threshold for logging large packets (10KB)
SAMPLE_SIZE_THRESHOLD = 1000  # Threshold for using sampling to estimate data size
SAMPLE_SIZE = 100  # Number of items to sample when estimating data size

# Counters for different components
_component_counters = {}
_component_lock = threading.Lock()

# Last network IO counters
_last_io = None
_last_time = None
_io_lock = threading.Lock()

# Monitor thread
_monitor_thread = None
_stop_event = threading.Event()
_is_shutting_down = False  # Flag to prevent thread restart during shutdown


def enable_network_diagnostics(enabled: bool = True) -> bool:
    """
    Enable or disable network diagnostics.

    Args:
        enabled: True to enable diagnostics, False to disable

    Returns:
        bool: True if the state was changed, False if it was already in the requested state
    """
    global NETWORK_DIAGNOSTICS_ENABLED, _monitor_thread, _stop_event, _last_io, _last_time, _is_shutting_down

    if _is_shutting_down:
        logger.warning("Network diagnostics cannot be enabled during shutdown")
        return False

    if enabled == NETWORK_DIAGNOSTICS_ENABLED:
        return False  # No change

    NETWORK_DIAGNOSTICS_ENABLED = enabled

    if enabled:
        logger.critical("Network diagnostics ENABLED")

        # Initialize counters
        with _component_lock:
            _component_counters.clear()

        # Initialize IO counters
        with _io_lock:
            try:
                _last_io = psutil.net_io_counters()
                _last_time = time.time()
            except Exception as e:
                logger.error(f"Failed to initialize network IO counters: {e}")
                _last_io = None
                _last_time = None

        # Start monitor thread if not running
        if _monitor_thread is None or not _monitor_thread.is_alive():
            try:
                _stop_event.clear()
                _monitor_thread = threading.Thread(
                    target=_network_monitor_thread,
                    name="NetworkDiagnosticsMonitor",
                    daemon=True
                )
                _monitor_thread.start()
                logger.info(f"Started network diagnostics monitor thread: {_monitor_thread.name}")
            except Exception as e:
                logger.error(f"Failed to start network diagnostics monitor thread: {e}")
                NETWORK_DIAGNOSTICS_ENABLED = False
                return False
    else:
        logger.critical("Network diagnostics DISABLED")
        _stop_monitor_thread()
        _clear_resources()

    return True


def _stop_monitor_thread() -> None:
    """Stop the network diagnostics monitor thread safely."""
    global _monitor_thread

    if _monitor_thread and _monitor_thread.is_alive():
        try:
            logger.info("Stopping network diagnostics monitor thread...")
            _stop_event.set()
            _monitor_thread.join(timeout=THREAD_JOIN_TIMEOUT)

            if _monitor_thread.is_alive():
                logger.warning("Network diagnostics monitor thread did not terminate within timeout")
        except Exception as e:
            logger.error(f"Error stopping network diagnostics monitor thread: {e}")
        finally:
            # Always nullify the thread reference, even if join failed
            _monitor_thread = None
            logger.info("Network diagnostics monitor thread reference cleared")


def _clear_resources() -> None:
    """Clear all resources used by network diagnostics."""
    global _last_io, _last_time

    # Clear component counters
    with _component_lock:
        _component_counters.clear()

    # Clear IO counters
    with _io_lock:
        _last_io = None
        _last_time = None

    logger.info("Network diagnostics resources cleared")


def cleanup_network_diagnostics() -> None:
    """
    Clean up all network diagnostics resources.
    This function should be called when the application is shutting down.
    It can be registered with atexit to ensure proper cleanup.
    """
    global NETWORK_DIAGNOSTICS_ENABLED, _is_shutting_down

    if _is_shutting_down:
        return  # Prevent multiple cleanup calls

    _is_shutting_down = True
    logger.info("Network diagnostics cleanup started")

    # Disable diagnostics (which stops the thread and clears resources)
    if NETWORK_DIAGNOSTICS_ENABLED:
        NETWORK_DIAGNOSTICS_ENABLED = False
        _stop_monitor_thread()
        _clear_resources()

    logger.info("Network diagnostics cleanup completed")


def _estimate_data_size(data: Any) -> int:
    """
    Estimate the size of data in bytes, using sampling for large collections.

    Args:
        data: The data to estimate the size of

    Returns:
        int: Estimated size in bytes
    """
    try:
        if isinstance(data, str):
            return len(data.encode('utf-8'))
        elif isinstance(data, bytes):
            return len(data)
        elif isinstance(data, (dict, list)) and len(data) > SAMPLE_SIZE_THRESHOLD:
            # For large collections, estimate size based on a sample
            if isinstance(data, dict):
                keys = list(data.keys())[:SAMPLE_SIZE]
                sample = {k: data[k] for k in keys}
            else:  # list
                sample = data[:SAMPLE_SIZE]

            sample_size = len(json.dumps(sample).encode('utf-8'))
            return int(sample_size * (len(data) / len(sample)))
        elif isinstance(data, (dict, list)):
            # For smaller collections, calculate exact size
            return len(json.dumps(data).encode('utf-8'))
        else:
            # For other types, convert to string
            return len(str(data).encode('utf-8'))
    except Exception:
        return -1  # Unknown size


def log_network_send(component: str, data: Any, include_data: bool = False) -> None:
    """
    Log a network send operation.

    Args:
        component: Component name (e.g., "ALPACA_WS", "BROKER_BRIDGE")
        data: Data being sent
        include_data: Whether to include the actual data in the log (be careful with sensitive data)
    """
    if not NETWORK_DIAGNOSTICS_ENABLED:
        return

    try:
        # Calculate data size using the helper function
        data_size = _estimate_data_size(data)

        # Update component counter
        with _component_lock:
            # Limit the number of components we track
            if component not in _component_counters:
                if len(_component_counters) >= MAX_COMPONENT_COUNTERS:
                    # Remove the least recently used component
                    oldest_component = min(_component_counters.items(),
                                         key=lambda x: x[1]['last_log_time'])
                    del _component_counters[oldest_component[0]]
                    logger.debug(f"Removed oldest component from tracking: {oldest_component[0]}")

                _component_counters[component] = {
                    'bytes_sent': 0,
                    'count': 0,
                    'last_log_time': time.time()
                }

            _component_counters[component]['bytes_sent'] += data_size
            _component_counters[component]['count'] += 1

            # Log individual large packets
            if data_size > LARGE_PACKET_THRESHOLD:
                logger.critical(
                    f"LARGE_PACKET: Component={component}, Size={data_size} bytes, "
                    f"Thread={threading.current_thread().name}"
                )
                if include_data:
                    # Limit the amount of data we log
                    data_str = str(data)
                    logger.debug(f"PACKET_DATA: {data_str[:1000]}..." if len(data_str) > 1000 else data_str)

    except Exception as e:
        logger.error(f"Error in log_network_send: {e}")


def log_network_recv(component: str, data: Any, include_data: bool = False) -> None:
    """
    Log a network receive operation.

    Args:
        component: Component name (e.g., "ALPACA_WS", "BROKER_BRIDGE")
        data: Data being received
        include_data: Whether to include the actual data in the log (be careful with sensitive data)
    """
    if not NETWORK_DIAGNOSTICS_ENABLED:
        return

    try:
        # Calculate data size using the helper function
        data_size = _estimate_data_size(data)

        # Log large packets
        if data_size > LARGE_PACKET_THRESHOLD:
            logger.critical(
                f"LARGE_RECV_PACKET: Component={component}, Size={data_size} bytes, "
                f"Thread={threading.current_thread().name}"
            )
            if include_data:
                # Limit the amount of data we log
                data_str = str(data)
                logger.debug(f"RECV_PACKET_DATA: {data_str[:1000]}..." if len(data_str) > 1000 else data_str)

    except Exception as e:
        logger.error(f"Error in log_network_recv: {e}")


def _network_monitor_thread() -> None:
    """Background thread that monitors network traffic and logs component statistics."""
    global _last_io, _last_time

    logger.info("Network diagnostics monitor thread started")

    # Initialize cleanup counter
    cleanup_counter = 0

    while not _stop_event.is_set():
        try:
            # Sleep for 5 seconds, checking stop event every 0.1 seconds
            for _ in range(50):
                if _stop_event.is_set():
                    break
                time.sleep(0.1)

            if _stop_event.is_set():
                break

            # Log component statistics
            with _component_lock:
                current_time = time.time()

                # Clean up inactive components periodically
                cleanup_counter += 1
                if cleanup_counter >= (COMPONENT_CLEANUP_INTERVAL // 5):  # Every 5 minutes (assuming 5-second intervals)
                    cleanup_counter = 0
                    inactive_components = []
                    for comp, stats in _component_counters.items():
                        if current_time - stats['last_log_time'] > INACTIVE_THRESHOLD:
                            inactive_components.append(comp)

                    for comp in inactive_components:
                        del _component_counters[comp]

                    if inactive_components:
                        logger.info(f"Cleaned up {len(inactive_components)} inactive component counters")

                # Log active component statistics
                for component, stats in list(_component_counters.items()):
                    time_diff = current_time - stats['last_log_time']
                    if time_diff >= 5.0 and stats['bytes_sent'] > 0:  # Log every 5 seconds if there's activity
                        try:
                            bytes_per_sec = stats['bytes_sent'] / time_diff
                            logger.critical(
                                f"COMPONENT_TRAFFIC: {component}: "
                                f"Sent={stats['bytes_sent']/1024:.2f} KB in {time_diff:.1f}s "
                                f"({bytes_per_sec/1024:.2f} KB/s), "
                                f"Count={stats['count']} ({stats['count']/time_diff:.1f} msgs/s)"
                            )

                            # Reset counters
                            stats['bytes_sent'] = 0
                            stats['count'] = 0
                            stats['last_log_time'] = current_time
                        except Exception as e:
                            logger.error(f"Error logging component statistics for {component}: {e}")

            # Log overall network statistics
            try:
                with _io_lock:
                    if _last_io is None or _last_time is None:
                        # Initialize if not already done
                        _last_io = psutil.net_io_counters()
                        _last_time = time.time()
                        continue

                    try:
                        current_io = psutil.net_io_counters()
                        current_time = time.time()

                        time_diff = current_time - _last_time
                        if time_diff < 0.1:  # Avoid division by very small numbers
                            continue

                        bytes_sent = max(0, current_io.bytes_sent - _last_io.bytes_sent)  # Ensure non-negative
                        bytes_recv = max(0, current_io.bytes_recv - _last_io.bytes_recv)
                        packets_sent = max(0, current_io.packets_sent - _last_io.packets_sent)
                        packets_recv = max(0, current_io.packets_recv - _last_io.packets_recv)

                        # Only log if there's significant traffic
                        if bytes_sent > LARGE_PACKET_THRESHOLD or bytes_recv > LARGE_PACKET_THRESHOLD:
                            avg_packet_sent = bytes_sent / packets_sent if packets_sent else 0
                            avg_packet_recv = bytes_recv / packets_recv if packets_recv else 0

                            logger.critical(
                                f"OVERALL_NET_IO: "
                                f"Sent={bytes_sent/1024/time_diff:.2f} KB/s ({packets_sent} packets), "
                                f"Recv={bytes_recv/1024/time_diff:.2f} KB/s ({packets_recv} packets), "
                                f"Avg packet: Sent={avg_packet_sent:.1f} bytes, Recv={avg_packet_recv:.1f} bytes"
                            )

                        _last_io = current_io
                        _last_time = current_time
                    except Exception as e:
                        logger.error(f"Error getting network IO counters: {e}")
                        # Reset counters on error
                        _last_io = None
                        _last_time = None
            except Exception as e:
                logger.error(f"Error in network IO monitoring: {e}")

        except Exception as e:
            logger.error(f"Error in network monitor thread: {e}")
            # Sleep briefly to avoid tight error loops
            time.sleep(1.0)

    logger.info("Network diagnostics monitor thread stopped")


# Register cleanup function with atexit to ensure proper cleanup on application exit
atexit.register(cleanup_network_diagnostics)


class NetworkDiagnostics:
    """
    Network health monitoring and diagnostics class.

    Provides connectivity checking, latency measurement, and network health assessment
    for the SystemHealthMonitoringService.
    """

    def __init__(self):
        """Initialize NetworkDiagnostics."""
        self.logger = logging.getLogger(f"{__name__}.NetworkDiagnostics")

    def check_connectivity(self) -> Dict[str, Any]:
        """
        Check network connectivity to key services.

        Returns:
            Dict containing connectivity status for various services
        """
        try:
            connectivity_status = {
                'redis_reachable': False,
                'internet_reachable': False,
                'local_network_reachable': False,
                'dns_resolution_working': False,
            }

            # Check Redis connectivity (basic socket test)
            try:
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2.0)
                result = sock.connect_ex(('127.0.0.1', 6379))
                connectivity_status['redis_reachable'] = (result == 0)
                sock.close()
            except Exception as e:
                self.logger.debug(f"Redis connectivity check failed: {e}")
                connectivity_status['redis_reachable'] = False

            # Check internet connectivity (ping Google DNS)
            try:
                import subprocess
                result = subprocess.run(['ping', '-n', '1', '8.8.8.8'],
                                      capture_output=True, timeout=5)
                connectivity_status['internet_reachable'] = (result.returncode == 0)
            except Exception as e:
                self.logger.debug(f"Internet connectivity check failed: {e}")
                connectivity_status['internet_reachable'] = False

            # Check local network (ping gateway)
            try:
                import subprocess
                result = subprocess.run(['ping', '-n', '1', '127.0.0.1'],
                                      capture_output=True, timeout=3)
                connectivity_status['local_network_reachable'] = (result.returncode == 0)
            except Exception as e:
                self.logger.debug(f"Local network connectivity check failed: {e}")
                connectivity_status['local_network_reachable'] = False

            # Check DNS resolution
            try:
                import socket
                socket.gethostbyname('google.com')
                connectivity_status['dns_resolution_working'] = True
            except Exception as e:
                self.logger.debug(f"DNS resolution check failed: {e}")
                connectivity_status['dns_resolution_working'] = False

            return connectivity_status

        except Exception as e:
            self.logger.error(f"Error in check_connectivity: {e}")
            return {'error': str(e)}

    def measure_latency(self) -> Dict[str, Any]:
        """
        Measure network latency to key endpoints.

        Returns:
            Dict containing latency measurements in milliseconds
        """
        try:
            latency_results = {
                'redis_latency_ms': None,
                'internet_latency_ms': None,
                'local_latency_ms': None,
                'average_ms': 0,
            }

            # Measure Redis latency (TCP connect time)
            try:
                import socket
                import time
                start_time = time.time()
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2.0)
                result = sock.connect_ex(('127.0.0.1', 6379))
                end_time = time.time()
                if result == 0:
                    latency_results['redis_latency_ms'] = (end_time - start_time) * 1000
                sock.close()
            except Exception as e:
                self.logger.debug(f"Redis latency measurement failed: {e}")

            # Measure internet latency (ping)
            try:
                import subprocess
                import re
                result = subprocess.run(['ping', '-n', '1', '8.8.8.8'],
                                      capture_output=True, timeout=5, text=True)
                if result.returncode == 0:
                    # Parse ping output for latency
                    match = re.search(r'time[<=](\d+)ms', result.stdout)
                    if match:
                        latency_results['internet_latency_ms'] = int(match.group(1))
            except Exception as e:
                self.logger.debug(f"Internet latency measurement failed: {e}")

            # Measure local latency (localhost ping)
            try:
                import subprocess
                import re
                result = subprocess.run(['ping', '-n', '1', '127.0.0.1'],
                                      capture_output=True, timeout=3, text=True)
                if result.returncode == 0:
                    # Parse ping output for latency
                    match = re.search(r'time[<=](\d+)ms', result.stdout)
                    if match:
                        latency_results['local_latency_ms'] = int(match.group(1))
            except Exception as e:
                self.logger.debug(f"Local latency measurement failed: {e}")

            # Calculate average latency
            valid_latencies = [v for v in latency_results.values() if isinstance(v, (int, float))]
            if valid_latencies:
                latency_results['average_ms'] = sum(valid_latencies) / len(valid_latencies)

            return latency_results

        except Exception as e:
            self.logger.error(f"Error in measure_latency: {e}")
            return {'error': str(e)}
