# Benchmarker Deadlock Fix - June 27, 2025

## Problem
EventBus.publish() was hanging when publishing OrderRequestEvent for trades. The system would freeze after creating the order but before it reached RiskManagementService.

## Root Cause
The refactoring added IPC publishing to the PerformanceBenchmarker's capture_metric() method. In headless/TANK mode:
1. Orchestrator worker thread calls benchmarker.capture_metric()
2. Benchmarker acquires its lock and attempts IPC publish
3. IPC client tries to send to non-existent Babysitter via ZMQ
4. System deadlocks waiting for Babysitter response

## Investigation Process
1. Initially suspected UUID generator deadlock - not the issue
2. Disabled correlation logger in events.py - not the issue  
3. Systematically disabled sections in orchestrator:
   - Pipeline validator section 1: SAFE
   - Benchmarker: CAUSES HANG
   - Pipeline validator section 2: SAFE
   - Pipeline validator section 3: Not tested

## Solution
Disabled IPC publishing in benchmarker by changing line 121 in utils/performance_benchmarker.py:
```python
# DISABLED: IPC publishing causing deadlocks in headless/TANK mode
if False:  # self.ipc_client and not self.ipc_client.offline_mode:
```

## TODO
- Implement proper headless/offline mode detection
- Make benchmarker IPC publishing configurable
- Ensure IPC client properly detects when Babysitter is unavailable

## Lessons Learned
- Performance measurement should be lightweight and non-blocking
- Benchmarker should never block the code it's measuring
- Refactoring that adds I/O operations to frequently-called code paths is dangerous