"""
Command and response handling for GUI backend.
Implements the command/response pattern via Redis.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, Callable
from datetime import datetime
import uuid


class CommandHandler:
    """
    Handles command sending and response correlation.
    Following Horseshoe Architecture - sends commands and listens for responses.
    """
    
    def __init__(self, app_state):
        self.app_state = app_state
        self.logger = logging.getLogger("GUI.CommandHandler")
        self.pending_commands: Dict[str, dict] = {}
        self.response_timeout = 30  # seconds
        
    async def send_command_and_wait(
        self, 
        command: str, 
        data: Dict[str, Any],
        timeout: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Send a command and wait for response.
        Implements request/response pattern over Redis.
        """
        command_id = f"cmd_{uuid.uuid4().hex[:8]}_{int(datetime.now().timestamp())}"
        
        # Create command message
        command_msg = {
            "command_id": command_id,
            "command": command,
            "data": data,
            "timestamp": datetime.now().isoformat(),
            "source": "gui_backend",
            "requires_response": True
        }
        
        # Set up response future
        response_future = asyncio.Future()
        self.pending_commands[command_id] = {
            "future": response_future,
            "command": command,
            "sent_at": asyncio.get_event_loop().time()
        }
        
        try:
            # Send command to Redis
            await self.app_state.redis_client.xadd(
                "testrade:gui-commands",
                command_msg
            )
            
            self.logger.info(f"Sent command {command} with ID {command_id}")
            
            # Wait for response
            response = await asyncio.wait_for(
                response_future,
                timeout=timeout or self.response_timeout
            )
            
            return response
            
        except asyncio.TimeoutError:
            self.logger.error(f"Command {command_id} timed out")
            raise TimeoutError(f"Command {command} timed out after {timeout}s")
            
        finally:
            # Clean up
            self.pending_commands.pop(command_id, None)
    
    async def handle_command_response(self, message_id: str, data: Dict[str, Any]):
        """
        Handle command responses from Redis.
        Called when a response is received on the response stream.
        """
        command_id = data.get("command_id")
        if not command_id:
            self.logger.warning("Received response without command_id")
            return
        
        pending = self.pending_commands.get(command_id)
        if not pending:
            self.logger.warning(f"Received response for unknown command {command_id}")
            return
        
        # Complete the future
        pending["future"].set_result(data)
        self.logger.info(f"Received response for command {command_id}")
        
        # Broadcast to WebSocket clients
        await self.app_state.ws_manager.broadcast({
            "type": "command_response",
            "command_id": command_id,
            "command": pending["command"],
            "response": data.get("payload", {}),
            "status": data.get("status", "unknown"),
            "timestamp": datetime.now().isoformat()
        })
    
    def cleanup_stale_commands(self):
        """Remove timed-out commands."""
        current_time = asyncio.get_event_loop().time()
        stale = []
        
        for cmd_id, pending in self.pending_commands.items():
            if current_time - pending["sent_at"] > self.response_timeout:
                stale.append(cmd_id)
                if not pending["future"].done():
                    pending["future"].set_exception(
                        TimeoutError(f"Command {cmd_id} timed out")
                    )
        
        for cmd_id in stale:
            self.pending_commands.pop(cmd_id, None)
            
        if stale:
            self.logger.warning(f"Cleaned up {len(stale)} stale commands")