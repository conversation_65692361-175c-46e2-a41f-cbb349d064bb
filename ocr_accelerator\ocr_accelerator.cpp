#include "ocr_accelerator.h"

// Specific, correct headers
#include <opencv2/core.hpp>
#include <opencv2/imgproc.hpp>
#include <opencv2/highgui.hpp>
#include <tesseract/baseapi.h>

// Standard C++ library headers
#include <stdexcept>
#include <chrono>
#include <vector>
#include <algorithm>

namespace py = pybind11;

// Custom deleter for Tesseract's memory
struct TessTextDeleter {
    void operator()(char* text) const { if (text) { delete[] text; } }
};
using TessTextPtr = std::unique_ptr<char[], TessTextDeleter>;


// --- Implementation of Standalone Functions ---

py::dict test_function() {
    py::dict result;
    result["status"] = "success";
    result["message"] = "C++ accelerator module loaded successfully";
    return result;
}

double get_golden_timestamp() {
    auto now = std::chrono::system_clock::now();
    auto ns = std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count();
    return static_cast<double>(ns) / 1e9;
}

int64_t get_golden_timestamp_ns() {
    auto now = std::chrono::system_clock::now();
    return std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count();
}


// --- Implementation of OcrEngine Class ---

OcrEngine::OcrEngine(const std::string& lang) {
    tess_api_ = std::make_unique<tesseract::TessBaseAPI>();
    if (tess_api_->Init(nullptr, lang.c_str())) {
        throw std::runtime_error("Could not initialize Tesseract engine.");
    }
    tess_api_->SetPageSegMode(tesseract::PSM_SINGLE_BLOCK);
}

OcrEngine::~OcrEngine() {}

py::dict OcrEngine::process_image(
    py::array_t<unsigned char> input_frame,
    double upscale_factor, bool force_black_text_on_white, double unsharp_strength,
    int threshold_block_size, int threshold_c, double red_boost, double green_boost,
    bool apply_text_mask_cleaning, int text_mask_min_contour_area, int text_mask_min_width, int text_mask_min_height,
    bool enhance_small_symbols, int symbol_max_height_for_enhancement_upscaled,
    std::pair<double, double> period_comma_aspect_ratio_range_upscaled, int period_comma_draw_radius_upscaled,
    double hyphen_like_min_aspect_ratio_upscaled, int hyphen_like_draw_min_height_upscaled)
{
    // This entire function body is identical to the previous version
    auto t0_entry = std::chrono::system_clock::now();
    py::buffer_info buf = input_frame.request();
    if (buf.ndim != 3 || buf.shape[2] != 3) { throw std::runtime_error("Input must be a 3-channel BGR NumPy array."); }
    cv::Mat frame(buf.shape[0], buf.shape[1], CV_8UC3, buf.ptr);
    cv::Mat current_image;
    if (upscale_factor > 1.0) { cv::resize(frame, current_image, cv::Size(), upscale_factor, upscale_factor, cv::INTER_CUBIC); }
    else { current_image = frame; }
    cv::Mat gray;
    cv::cvtColor(current_image, gray, cv::COLOR_BGR2GRAY);
    cv::Mat sharpened;
    if (unsharp_strength != 1.0 && gray.rows >= 3 && gray.cols >= 3) {
        cv::Mat blur;
        cv::GaussianBlur(gray, blur, cv::Size(3, 3), 0);
        cv::addWeighted(gray, unsharp_strength, blur, -(unsharp_strength - 1.0), 0, sharpened);
    }
    else { sharpened = gray; }
    cv::Mat bin_img;
    int safe_block_size = (threshold_block_size % 2 == 0) ? threshold_block_size + 1 : threshold_block_size;
    int min_dim = std::min(sharpened.rows, sharpened.cols);
    if (safe_block_size >= min_dim && min_dim > 2) { safe_block_size = (min_dim - 1) % 2 != 0 ? (min_dim - 1) : (min_dim - 2); }
    if (safe_block_size <= 1) { cv::threshold(sharpened, bin_img, 0, 255, cv::THRESH_BINARY + cv::THRESH_OTSU); }
    else { cv::adaptiveThreshold(sharpened, bin_img, 255, cv::ADAPTIVE_THRESH_GAUSSIAN_C, cv::THRESH_BINARY, safe_block_size, threshold_c); }
    if (force_black_text_on_white && cv::mean(bin_img)[0] < 127) { cv::bitwise_not(bin_img, bin_img); }
    auto t1_preproc_done = std::chrono::system_clock::now();
    if (apply_text_mask_cleaning) {
        cv::Mat img_for_contours = ~bin_img;
        std::vector<std::vector<cv::Point>> contours;
        std::vector<cv::Vec4i> hierarchy;
        cv::findContours(img_for_contours, contours, hierarchy, cv::RETR_CCOMP, cv::CHAIN_APPROX_SIMPLE);
        cv::Mat final_canvas = cv::Mat(bin_img.size(), CV_8U, cv::Scalar(255));
        if (!hierarchy.empty()) {
            for (size_t i = 0; i < contours.size(); ++i) {
                cv::Rect bbox = cv::boundingRect(contours[i]);
                double area = cv::contourArea(contours[i]);
                bool is_external = (hierarchy[i][3] == -1);
                bool drew_enhanced = false;
                if (enhance_small_symbols && is_external && bbox.height <= symbol_max_height_for_enhancement_upscaled) {
                    double aspect_ratio = (bbox.height > 0) ? static_cast<double>(bbox.width) / bbox.height : 0.0;
                    if (aspect_ratio >= period_comma_aspect_ratio_range_upscaled.first && aspect_ratio <= period_comma_aspect_ratio_range_upscaled.second) {
                        cv::Moments M = cv::moments(contours[i]);
                        if (M.m00 != 0) {
                            cv::Point center(static_cast<int>(M.m10 / M.m00), static_cast<int>(M.m01 / M.m00));
                            cv::circle(final_canvas, center, period_comma_draw_radius_upscaled, cv::Scalar(0), -1);
                            drew_enhanced = true;
                        }
                    }
                    else if (aspect_ratio >= hyphen_like_min_aspect_ratio_upscaled) {
                        int draw_h = std::max(bbox.height, hyphen_like_draw_min_height_upscaled);
                        int y_new = bbox.y - (draw_h - bbox.height) / 2;
                        cv::rectangle(final_canvas, cv::Point(bbox.x, y_new), cv::Point(bbox.x + bbox.width, y_new + draw_h), cv::Scalar(0), -1);
                        drew_enhanced = true;
                    }
                }
                if (!drew_enhanced) {
                    if (is_external && (area < text_mask_min_contour_area || bbox.width < text_mask_min_width || bbox.height < text_mask_min_height)) continue;
                    cv::drawContours(final_canvas, contours, static_cast<int>(i), cv::Scalar(is_external ? 0 : 255), cv::FILLED);
                }
            }
        }
        bin_img = final_canvas;
    }
    auto t2_contours_done = std::chrono::system_clock::now();
    tess_api_->SetImage(bin_img.data, bin_img.cols, bin_img.rows, bin_img.channels(), bin_img.step);
    auto t3_tess_start = std::chrono::system_clock::now();
    TessTextPtr out_text(tess_api_->GetUTF8Text());
    std::string result_text = std::string(out_text.get());
    float total_confidence = 0.0f;
    int word_count = 0;
    tesseract::ResultIterator* ri = tess_api_->GetIterator();
    if (ri != nullptr) {
        do {
            const char* word = ri->GetUTF8Text(tesseract::RIL_WORD);
            if (word == nullptr) { continue; }
            float conf = ri->Confidence(tesseract::RIL_WORD);
            if (std::string(word).length() > 0) { total_confidence += conf; word_count++; }
            delete[] word;
        } while (ri->Next(tesseract::RIL_WORD));
        delete ri;
    }
    auto t4_tess_done = std::chrono::system_clock::now();
    py::dict result_dict;
    result_dict["text"] = result_text;
    result_dict["confidence"] = (word_count > 0) ? (total_confidence / word_count) : 0.0f;
    result_dict["t0_entry_ns"] = std::chrono::duration_cast<std::chrono::nanoseconds>(t0_entry.time_since_epoch()).count();
    result_dict["t1_preproc_done_ns"] = std::chrono::duration_cast<std::chrono::nanoseconds>(t1_preproc_done.time_since_epoch()).count();
    result_dict["t2_contours_done_ns"] = std::chrono::duration_cast<std::chrono::nanoseconds>(t2_contours_done.time_since_epoch()).count();
    result_dict["t3_tess_start_ns"] = std::chrono::duration_cast<std::chrono::nanoseconds>(t3_tess_start.time_since_epoch()).count();
    result_dict["t4_tess_done_ns"] = std::chrono::duration_cast<std::chrono::nanoseconds>(t4_tess_done.time_since_epoch()).count();
    return result_dict;
}


// --- Final pybind11 Module Definition ---
PYBIND11_MODULE(ocr_accelerator, m) {
    m.doc() = "High-performance stateful OCR accelerator module";

    // Expose the standalone functions
    m.def("test_function", &test_function, "Test function to verify module loading");
    m.def("get_golden_timestamp", &get_golden_timestamp, "Get high-precision timestamp in seconds");
    m.def("get_golden_timestamp_ns", &get_golden_timestamp_ns, "Get high-precision timestamp in nanoseconds");

    // Expose the OcrEngine class and its methods
    py::class_<OcrEngine>(m, "OcrEngine")
        .def(py::init<const std::string&>(), py::arg("lang"))
        .def("process_image", &OcrEngine::process_image, "Processes an image using the persistent engine",
            py::arg("input_frame"),
            py::arg("upscale_factor"), py::arg("force_black_text_on_white"), py::arg("unsharp_strength"),
            py::arg("threshold_block_size"), py::arg("threshold_c"), py::arg("red_boost"), py::arg("green_boost"),
            py::arg("apply_text_mask_cleaning"), py::arg("text_mask_min_contour_area"),
            py::arg("text_mask_min_width"), py::arg("text_mask_min_height"), py::arg("enhance_small_symbols"),
            py::arg("symbol_max_height_for_enhancement_upscaled"), py::arg("period_comma_aspect_ratio_range_upscaled"),
            py::arg("period_comma_draw_radius_upscaled"), py::arg("hyphen_like_min_aspect_ratio_upscaled"),
            py::arg("hyphen_like_draw_min_height_upscaled"));
}