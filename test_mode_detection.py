#!/usr/bin/env python3
"""
Test script to verify TESTRADE mode detection works correctly
"""

import os
import sys
import logging

# Add TESTRADE to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.testrade_modes import get_current_mode, TestradeMode
from utils.global_config import GlobalConfig

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_mode_detection():
    """Test mode detection with various environment configurations"""
    
    print("=== TESTRADE Mode Detection Test ===\n")
    
    # Save original environment
    original_env = {
        'TESTRADE_MODE': os.environ.get('TESTRADE_MODE', ''),
        'TESTRADE_TANK_MODE': os.environ.get('TESTRADE_TANK_MODE', ''),
        'TESTRADE_OFFLINE_MODE': os.environ.get('TESTRADE_OFFLINE_MODE', '')
    }
    
    try:
        # Test 1: TANK_SEALED mode via TESTRADE_MODE
        print("Test 1: TANK_SEALED mode via TESTRADE_MODE")
        os.environ['TESTRADE_MODE'] = 'TANK_SEALED'
        os.environ.pop('TESTRADE_TANK_MODE', None)
        os.environ.pop('TESTRADE_OFFLINE_MODE', None)
        
        mode = get_current_mode()
        assert mode == TestradeMode.TANK_SEALED, f"Expected TANK_SEALED, got {mode}"
        print(f"✓ Mode detected: {mode.value}")
        print(f"  Description: {mode.value}")
        
        # Test 2: TANK_BUFFERED mode via TESTRADE_MODE
        print("\nTest 2: TANK_BUFFERED mode via TESTRADE_MODE")
        os.environ['TESTRADE_MODE'] = 'TANK_BUFFERED'
        
        mode = get_current_mode()
        assert mode == TestradeMode.TANK_BUFFERED, f"Expected TANK_BUFFERED, got {mode}"
        print(f"✓ Mode detected: {mode.value}")
        
        # Test 3: LIVE mode via TESTRADE_MODE
        print("\nTest 3: LIVE mode via TESTRADE_MODE")
        os.environ['TESTRADE_MODE'] = 'LIVE'
        
        mode = get_current_mode()
        assert mode == TestradeMode.LIVE, f"Expected LIVE, got {mode}"
        print(f"✓ Mode detected: {mode.value}")
        
        # Test 4: Legacy TESTRADE_TANK_MODE compatibility
        print("\nTest 4: Legacy TESTRADE_TANK_MODE=1")
        os.environ.pop('TESTRADE_MODE', None)
        os.environ['TESTRADE_TANK_MODE'] = '1'
        
        mode = get_current_mode()
        assert mode == TestradeMode.TANK_SEALED, f"Expected TANK_SEALED, got {mode}"
        print(f"✓ Mode detected: {mode.value} (via legacy flag)")
        
        # Test 5: Legacy TESTRADE_OFFLINE_MODE compatibility
        print("\nTest 5: Legacy TESTRADE_OFFLINE_MODE=1")
        os.environ.pop('TESTRADE_TANK_MODE', None)
        os.environ['TESTRADE_OFFLINE_MODE'] = '1'
        
        mode = get_current_mode()
        assert mode == TestradeMode.TANK_BUFFERED, f"Expected TANK_BUFFERED, got {mode}"
        print(f"✓ Mode detected: {mode.value} (via legacy flag)")
        
        # Test 6: Default mode (no environment variables)
        print("\nTest 6: Default mode (no environment variables)")
        os.environ.pop('TESTRADE_MODE', None)
        os.environ.pop('TESTRADE_TANK_MODE', None)
        os.environ.pop('TESTRADE_OFFLINE_MODE', None)
        
        mode = get_current_mode()
        assert mode == TestradeMode.LIVE, f"Expected LIVE, got {mode}"
        print(f"✓ Mode detected: {mode.value} (default)")
        
        # Test 7: GlobalConfig integration
        print("\nTest 7: GlobalConfig integration")
        os.environ['TESTRADE_MODE'] = 'TANK_SEALED'
        
        config = GlobalConfig()
        print(f"✓ config.get_current_mode(): {config.get_current_mode().value}")
        print(f"✓ config.is_tank_mode(): {config.is_tank_mode()}")
        print(f"✓ config.is_tank_sealed(): {config.is_tank_sealed()}")
        print(f"✓ config.should_enable_telemetry(): {config.should_enable_telemetry()}")
        print(f"✓ config.should_enable_ipc(): {config.should_enable_ipc()}")
        print(f"✓ config.is_feature_enabled('ENABLE_IPC_DATA_DUMP'): {config.is_feature_enabled('ENABLE_IPC_DATA_DUMP')}")
        
        # Test 8: Feature flag overrides based on mode
        print("\nTest 8: Feature flag overrides")
        
        # In TANK_SEALED mode
        os.environ['TESTRADE_MODE'] = 'TANK_SEALED'
        config = GlobalConfig()
        config.ENABLE_IPC_DATA_DUMP = True  # Set to True in config
        assert not config.is_feature_enabled('ENABLE_IPC_DATA_DUMP'), "IPC data dump should be disabled in TANK_SEALED"
        print("✓ IPC data dump correctly disabled in TANK_SEALED despite config=True")
        
        # In LIVE mode
        os.environ['TESTRADE_MODE'] = 'LIVE'
        config = GlobalConfig()
        config.ENABLE_IPC_DATA_DUMP = True
        assert config.is_feature_enabled('ENABLE_IPC_DATA_DUMP'), "IPC data dump should be enabled in LIVE"
        print("✓ IPC data dump correctly enabled in LIVE mode")
        
        print("\n✅ All tests passed!")
        
    finally:
        # Restore original environment
        for key, value in original_env.items():
            if value:
                os.environ[key] = value
            else:
                os.environ.pop(key, None)

if __name__ == "__main__":
    test_mode_detection()