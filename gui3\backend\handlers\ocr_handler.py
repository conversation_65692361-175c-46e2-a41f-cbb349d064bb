"""
OCR message handler for GUI backend.
Handles OCR-related messages from Redis streams.
"""

from typing import Dict, Any, Optional
from ..core.message_handler import MessageHandler


class OCRHandler(MessageHandler):
    """Handles raw and cleaned OCR messages."""
    
    async def handle(self, message_id: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process OCR data and format for GUI display."""
        
        # Extract relevant fields
        frame_number = data.get('payload', {}).get('frame_number', 0)
        confidence = data.get('payload', {}).get('aggregate_confidence', 0)
        timestamp = data.get('timestamp', 0)
        
        # Format positions data
        positions = []
        positions_data = data.get('payload', {}).get('positions', {})
        
        for symbol, pos_data in positions_data.items():
            positions.append({
                'symbol': symbol,
                'shares': pos_data.get('shares', 0),
                'cost_basis': pos_data.get('cost_basis', 0),
                'pnl': pos_data.get('pnl', 0),
                'pnl_percent': pos_data.get('pnl_percent', 0)
            })
        
        # Return formatted data for GUI
        return {
            'type': 'ocr_update',
            'message_id': message_id,
            'frame_number': frame_number,
            'confidence': confidence,
            'timestamp': timestamp,
            'positions': positions,
            'position_count': len(positions)
        }


class CleanedOCRHandler(MessageHandler):
    """Handles cleaned OCR snapshot messages."""
    
    async def handle(self, message_id: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process cleaned OCR snapshots."""
        
        payload = data.get('payload', {})
        snapshots = payload.get('snapshots', {})
        
        # Format snapshot data
        formatted_snapshots = []
        for symbol, snapshot in snapshots.items():
            formatted_snapshots.append({
                'symbol': symbol,
                'strategy_hint': snapshot.get('strategy_hint', ''),
                'cost_basis': snapshot.get('cost_basis', 0),
                'shares': snapshot.get('shares', 0),
                'pl': snapshot.get('pl', 0),
                'pl_percent': snapshot.get('pl_percent', 0)
            })
        
        return {
            'type': 'cleaned_ocr_update',
            'message_id': message_id,
            'frame_timestamp': payload.get('frame_timestamp', 0),
            'snapshots': formatted_snapshots,
            'snapshot_count': len(formatted_snapshots),
            'confidence': payload.get('raw_ocr_confidence', 0)
        }