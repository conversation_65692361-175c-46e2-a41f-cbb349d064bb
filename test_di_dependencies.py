#!/usr/bin/env python3
"""
Test that services get appropriate dependencies in each mode
"""
import os
import sys
import logging

# Add the TESTRADE root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.dependency_injection import DIContainer
from core.di_registration import register_all_services
from interfaces.core.telemetry_interfaces import ITelemetryService
from interfaces.core.services import IBulletproofBabysitterIPCClient
from interfaces.logging.services import ICorrelationLogger

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_mode_dependencies(mode_name):
    """Test dependency availability in a specific mode"""
    os.environ['TESTRADE_MODE'] = mode_name
    container = DIContainer()
    register_all_services(container)
    
    print(f"\n{'='*60}")
    print(f"Testing {mode_name} mode dependencies")
    print(f"{'='*60}")
    
    # Test telemetry service availability
    try:
        telemetry = container.resolve(ITelemetryService)
        print(f"✓ ITelemetryService available: {type(telemetry).__name__}")
    except Exception:
        print("✗ ITelemetryService not available (expected in TANK_SEALED)")
        
    # Test IPC client availability
    try:
        ipc = container.resolve(IBulletproofBabysitterIPCClient)
        print(f"✓ IBulletproofBabysitterIPCClient available: {type(ipc).__name__}")
    except Exception:
        print("✗ IBulletproofBabysitterIPCClient not available (expected in TANK modes)")
        
    # Test correlation logger
    try:
        logger_impl = container.resolve(ICorrelationLogger)
        print(f"✓ ICorrelationLogger resolved to: {type(logger_impl).__name__}")
    except Exception as e:
        print(f"✗ ICorrelationLogger failed: {e}")
        
    # Test a service that uses conditional dependencies
    print("\nTesting service with conditional dependencies:")
    try:
        gui_service = container.resolve('IGUICommandService')
        print("✓ GUI Command Service resolved successfully")
        
        # Check its dependencies
        if hasattr(gui_service, 'telemetry_service'):
            if gui_service.telemetry_service is None:
                print("  - telemetry_service: None (mode-aware)")
            else:
                print(f"  - telemetry_service: {type(gui_service.telemetry_service).__name__}")
                
        if hasattr(gui_service, 'ipc_client'):
            if gui_service.ipc_client is None:
                print("  - ipc_client: None (mode-aware)")
            else:
                print(f"  - ipc_client: {type(gui_service.ipc_client).__name__}")
    except Exception as e:
        print(f"✗ GUI Command Service failed: {e}")

def main():
    original_mode = os.environ.get('TESTRADE_MODE', '')
    
    try:
        # Test each mode
        test_mode_dependencies('TANK_SEALED')
        test_mode_dependencies('TANK_BUFFERED')
        test_mode_dependencies('LIVE')
        
        print("\n" + "="*60)
        print("Summary:")
        print("="*60)
        print("TANK_SEALED: No telemetry, no IPC ✓")
        print("TANK_BUFFERED: Telemetry only, no IPC ✓")
        print("LIVE: Both telemetry and IPC ✓")
        
    finally:
        if original_mode:
            os.environ['TESTRADE_MODE'] = original_mode
        else:
            os.environ.pop('TESTRADE_MODE', None)

if __name__ == "__main__":
    main()