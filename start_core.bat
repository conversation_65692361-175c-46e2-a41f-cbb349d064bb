@echo off
CLS
REM =================================================================
REM  TESTRADE Interactive Core Startup Script
REM =================================================================
REM
REM  This script prompts the user to select a runtime mode,
REM  sets the appropriate environment variables, and then launches
REM  the headless core application.
REM
REM =================================================================

:MENU
CLS
echo.
echo ========================================
echo  SELECT TESTRADE RUNTIME MODE
echo ========================================
echo.
echo  [1] Headless (TANK MODE)
echo      - No IPC data publishing at all.
echo      - Ideal for core logic testing and development.
echo.
echo  [2] Headless Broadcast (DATA CAPTURE)
echo      - No network connections (ZMQ).
echo      - Buffers all IPC data to disk (mmap files).
echo      - Ideal for capturing live data for later analysis.
echo.
echo  [3] Full Online
echo      - All services active.
echo      - Connects to Babysitter, Redis, etc.
echo      - For live trading or full system testing.
echo.
echo ========================================
echo.

REM Set default choice in case of invalid input
SET "CHOICE="

REM Prompt for user input
CHOICE /C 123 /M "Enter your choice (1, 2, or 3): "

REM The ERRORLEVEL is set by the CHOICE command (1 for first choice, 2 for second, etc.)
IF ERRORLEVEL 3 SET MODE=full
IF ERRORLEVEL 2 SET MODE=broadcast
IF ERRORLEVEL 1 SET MODE=headless

echo.
echo You selected Mode %ERRORLEVEL%: %MODE%
echo Preparing environment...
timeout /t 2 >nul

REM --- Environment Variable Setup ---
REM Reset variables first to ensure a clean state
SET "TESTRADE_TANK_MODE=0"
SET "TESTRADE_OFFLINE_MODE=0"

IF /I "%MODE%"=="headless" (
    echo Setting TESTRADE_TANK_MODE=1
    SET "TESTRADE_TANK_MODE=1"
)

IF /I "%MODE%"=="broadcast" (
    echo Setting TESTRADE_OFFLINE_MODE=1
    SET "TESTRADE_OFFLINE_MODE=1"
)

IF /I "%MODE%"=="full" (
    echo No special environment variables needed for full mode.
)

REM --- Application Launch ---
echo.
echo ========================================
echo  Starting Application...
echo ========================================

cd /d "C:\TESTRADE"
call .venv\Scripts\activate.bat

REM --- Launch appropriate Python script based on mode ---
IF /I "%MODE%"=="headless" (
    echo Launching in TANK MODE - bypassing babysitter and network dependencies...
    python main.py
) ELSE IF /I "%MODE%"=="broadcast" (
    echo Launching in DATA CAPTURE MODE - offline buffering enabled...
    python main.py
) ELSE (
    echo Launching in FULL ONLINE MODE - all services active...
    python start_testrade.py
)

REM --- Error Handling and Exit ---
if errorlevel 1 (
    echo.
    echo ========================================
    echo  TESTRADE Core exited with ERROR
    echo  Error level: %errorlevel%
    echo ========================================
    pause
) else (
    echo.
    echo ========================================
    echo  TESTRADE Core shutdown complete
    echo ========================================
    timeout /t 5 >nul
)