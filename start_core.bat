@echo off
CLS
REM =================================================================
REM  TESTRADE Interactive Core Startup Script
REM =================================================================
REM
REM  This script prompts the user to select a runtime mode,
REM  sets the appropriate environment variables, and then launches
REM  the headless core application.
REM
REM =================================================================

:MENU
CLS
echo.
echo ========================================
echo  SELECT TESTRADE RUNTIME MODE
echo ========================================
echo.
echo  [1] TANK SEALED
echo      - Pure isolation mode, no telemetry interfaces
echo      - No publishing, no IPC, pure core logic testing
echo.
echo  [2] TANK BUFFERED  
echo      - Has telemetry interfaces but buffers locally
echo      - No external publishing, captures data for analysis
echo.
echo  [3] LIVE MODE
echo      - Full system with all services active
echo      - Babysitter, Redis, ZMQ listeners, live trading
echo.
echo ========================================
echo.

REM Set default choice in case of invalid input
SET "CHOICE="

REM Prompt for user input
CHOICE /C 123 /M "Enter your choice (1, 2, or 3): "

REM The ERRORLEVEL is set by the CHOICE command (1 for first choice, 2 for second, etc.)
IF ERRORLEVEL 3 SET MODE=LIVE
IF ERRORLEVEL 2 SET MODE=TANK_BUFFERED
IF ERRORLEVEL 1 SET MODE=TANK_SEALED

echo.
echo You selected Mode %ERRORLEVEL%: %MODE%
echo Preparing environment...
timeout /t 2 >nul

REM --- Environment Variable Setup ---
REM Reset variables first to ensure a clean state
SET "TESTRADE_MODE=LIVE"

IF /I "%MODE%"=="TANK_SEALED" (
    echo Setting TANK_SEALED mode - no telemetry interfaces
    SET "TESTRADE_MODE=TANK_SEALED"
)

IF /I "%MODE%"=="TANK_BUFFERED" (
    echo Setting TANK_BUFFERED mode - telemetry buffered locally  
    SET "TESTRADE_MODE=TANK_BUFFERED"
)

IF /I "%MODE%"=="LIVE" (
    echo Setting LIVE mode - full system active
    SET "TESTRADE_MODE=LIVE"
)

REM --- Application Launch ---
echo.
echo ========================================
echo  Starting Application...
echo ========================================

cd /d "C:\TESTRADE"
call .venv\Scripts\activate.bat

REM --- Launch appropriate Python script based on mode ---
IF /I "%MODE%"=="TANK_SEALED" (
    echo Launching TANK_SEALED - pure isolation, no telemetry interfaces...
    python main.py
) ELSE IF /I "%MODE%"=="TANK_BUFFERED" (
    echo Launching TANK_BUFFERED - telemetry interfaces with local buffering...
    python main.py
) ELSE (
    echo Launching LIVE MODE - full system with babysitter and external services...
    python start_testrade.py
)

REM --- Error Handling and Exit ---
if errorlevel 1 (
    echo.
    echo ========================================
    echo  TESTRADE Core exited with ERROR
    echo  Error level: %errorlevel%
    echo ========================================
    pause
) else (
    echo.
    echo ========================================
    echo  TESTRADE Core shutdown complete
    echo ========================================
    timeout /t 5 >nul
)