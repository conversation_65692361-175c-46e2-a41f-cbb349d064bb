# tank_multi_process_monitor.ps1 - Monitor all Python processes for TESTRADE
param(
    [string]$ProcessPattern = "python",
    [int]$SampleIntervalSeconds = 30,
    [string]$LogFile = "tank_multi_process_monitor.csv",
    [switch]$EnableCSVLogging = $true,
    [switch]$ShowIndividualProcesses = $true
)

Write-Host "🔍 TANK Multi-Process Memory Monitor" -ForegroundColor Green
Write-Host "Process Pattern: $ProcessPattern | Sample Interval: $SampleIntervalSeconds seconds" -ForegroundColor Cyan
Write-Host "CSV Logging: $EnableCSVLogging | Show Individual: $ShowIndividualProcesses" -ForegroundColor Yellow
Write-Host "=" * 100

# Create CSV header if logging enabled
if ($EnableCSVLogging) {
    "Timestamp,TotalProcesses,TotalMemoryMB,TotalMemoryGB,AverageMemoryMB,MaxMemoryMB,MinMemoryMB,TotalHandles,TotalThreads,ProcessDetails" | Out-File $LogFile
}

$sampleCount = 0
$startTime = Get-Date

while ($true) {
    try {
        $timestamp = Get-Date
        $sampleCount++
        
        # Get all matching processes
        $processes = Get-Process $ProcessPattern -ErrorAction SilentlyContinue
        
        if ($processes.Count -eq 0) {
            Write-Host "❌ No processes found matching pattern '$ProcessPattern'" -ForegroundColor Red
            Start-Sleep $SampleIntervalSeconds
            continue
        }
        
        # Collect detailed metrics for each process
        $processDetails = @()
        $totalMemoryBytes = 0
        $totalHandles = 0
        $totalThreads = 0
        $memoryValues = @()
        
        foreach ($process in $processes) {
            try {
                $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
                $memoryValues += $memoryMB
                $totalMemoryBytes += $process.WorkingSet64
                $totalHandles += $process.HandleCount
                $totalThreads += $process.Threads.Count
                
                $processDetail = [PSCustomObject]@{
                    PID = $process.Id
                    ProcessName = $process.ProcessName
                    MemoryMB = $memoryMB
                    HandleCount = $process.HandleCount
                    ThreadCount = $process.Threads.Count
                    StartTime = $process.StartTime
                    CPUTime = $process.TotalProcessorTime.TotalSeconds
                }
                $processDetails += $processDetail
            } catch {
                Write-Host "Warning: Could not collect data for process $($process.Id)" -ForegroundColor Yellow
            }
        }
        
        # Calculate aggregate statistics
        $totalMemoryMB = [math]::Round($totalMemoryBytes / 1MB, 2)
        $totalMemoryGB = [math]::Round($totalMemoryBytes / 1GB, 3)
        $averageMemoryMB = if ($memoryValues.Count -gt 0) { [math]::Round(($memoryValues | Measure-Object -Average).Average, 2) } else { 0 }
        $maxMemoryMB = if ($memoryValues.Count -gt 0) { [math]::Round(($memoryValues | Measure-Object -Maximum).Maximum, 2) } else { 0 }
        $minMemoryMB = if ($memoryValues.Count -gt 0) { [math]::Round(($memoryValues | Measure-Object -Minimum).Minimum, 2) } else { 0 }
        
        # Display results
        Clear-Host
        Write-Host "🔍 TANK Multi-Process Memory Monitor - Real Time Analysis" -ForegroundColor Green
        Write-Host "=" * 100
        Write-Host "Current Time: $($timestamp.ToString('HH:mm:ss')) | Sample #$sampleCount | Runtime: $([math]::Round(((Get-Date) - $startTime).TotalMinutes, 1)) minutes" -ForegroundColor Cyan
        Write-Host ""
        
        # Aggregate metrics
        Write-Host "📊 AGGREGATE METRICS:" -ForegroundColor Yellow
        Write-Host "  Total Processes:  $($processes.Count)" -ForegroundColor White
        Write-Host "  Total Memory:     $totalMemoryMB MB ($totalMemoryGB GB)" -ForegroundColor White
        Write-Host "  Average Memory:   $averageMemoryMB MB per process" -ForegroundColor White
        Write-Host "  Memory Range:     $minMemoryMB - $maxMemoryMB MB" -ForegroundColor White
        Write-Host "  Total Handles:    $totalHandles" -ForegroundColor White
        Write-Host "  Total Threads:    $totalThreads" -ForegroundColor White
        Write-Host ""
        
        # Memory distribution analysis
        $largeProcesses = $processDetails | Where-Object { $_.MemoryMB -gt 100 } | Sort-Object MemoryMB -Descending
        $mediumProcesses = $processDetails | Where-Object { $_.MemoryMB -ge 50 -and $_.MemoryMB -le 100 }
        $smallProcesses = $processDetails | Where-Object { $_.MemoryMB -lt 50 }
        
        Write-Host "📈 MEMORY DISTRIBUTION:" -ForegroundColor Yellow
        Write-Host "  Large (>100MB):   $($largeProcesses.Count) processes" -ForegroundColor $(if($largeProcesses.Count -gt 0) { "Red" } else { "Green" })
        Write-Host "  Medium (50-100MB): $($mediumProcesses.Count) processes" -ForegroundColor $(if($mediumProcesses.Count -gt 3) { "Yellow" } else { "Green" })
        Write-Host "  Small (<50MB):    $($smallProcesses.Count) processes" -ForegroundColor Green
        Write-Host ""
        
        # Show individual processes if enabled
        if ($ShowIndividualProcesses) {
            Write-Host "🔍 INDIVIDUAL PROCESS DETAILS:" -ForegroundColor Yellow
            
            # Show top 5 memory consumers
            $topProcesses = $processDetails | Sort-Object MemoryMB -Descending | Select-Object -First 5
            Write-Host "  Top Memory Consumers:" -ForegroundColor Cyan
            foreach ($proc in $topProcesses) {
                $color = if ($proc.MemoryMB -gt 200) { "Red" } 
                        elseif ($proc.MemoryMB -gt 100) { "Yellow" } 
                        else { "White" }
                Write-Host "    PID $($proc.PID.ToString().PadLeft(5)): $($proc.MemoryMB.ToString().PadLeft(7)) MB | Handles: $($proc.HandleCount.ToString().PadLeft(4)) | Threads: $($proc.ThreadCount.ToString().PadLeft(3))" -ForegroundColor $color
            }
            
            # Show processes with high handle/thread counts
            $highResourceProcesses = $processDetails | Where-Object { $_.HandleCount -gt 500 -or $_.ThreadCount -gt 20 } | Sort-Object HandleCount -Descending
            if ($highResourceProcesses.Count -gt 0) {
                Write-Host ""
                Write-Host "  High Resource Usage:" -ForegroundColor Cyan
                foreach ($proc in $highResourceProcesses | Select-Object -First 3) {
                    Write-Host "    PID $($proc.PID.ToString().PadLeft(5)): Handles: $($proc.HandleCount.ToString().PadLeft(4)) | Threads: $($proc.ThreadCount.ToString().PadLeft(3)) | Memory: $($proc.MemoryMB) MB" -ForegroundColor Yellow
                }
            }
        }
        
        # System health assessment
        Write-Host ""
        Write-Host "🏥 SYSTEM HEALTH ASSESSMENT:" -ForegroundColor Yellow
        
        $healthIssues = @()
        $healthScore = 100
        
        # Check for memory issues
        if ($totalMemoryGB -gt 2.0) {
            $healthScore -= 30
            $healthIssues += "High total memory usage ($totalMemoryGB GB)"
        } elseif ($totalMemoryGB -gt 1.0) {
            $healthScore -= 15
            $healthIssues += "Moderate total memory usage ($totalMemoryGB GB)"
        }
        
        # Check for too many processes
        if ($processes.Count -gt 10) {
            $healthScore -= 20
            $healthIssues += "High process count ($($processes.Count) processes)"
        } elseif ($processes.Count -gt 5) {
            $healthScore -= 10
            $healthIssues += "Moderate process count ($($processes.Count) processes)"
        }
        
        # Check for individual large processes
        if ($largeProcesses.Count -gt 0) {
            $healthScore -= 25
            $healthIssues += "$($largeProcesses.Count) processes using >100MB each"
        }
        
        # Check for high resource usage
        if ($totalHandles -gt 5000) {
            $healthScore -= 15
            $healthIssues += "High total handle count ($totalHandles)"
        }
        
        if ($totalThreads -gt 200) {
            $healthScore -= 15
            $healthIssues += "High total thread count ($totalThreads)"
        }
        
        $healthScore = [math]::Max(0, $healthScore)
        $healthRating = if ($healthScore -ge 90) { "EXCELLENT" }
                       elseif ($healthScore -ge 80) { "GOOD" }
                       elseif ($healthScore -ge 70) { "FAIR" }
                       elseif ($healthScore -ge 50) { "POOR" }
                       else { "CRITICAL" }
        
        $healthColor = if ($healthScore -ge 80) { "Green" }
                      elseif ($healthScore -ge 60) { "Yellow" }
                      else { "Red" }
        
        Write-Host "  Health Score:     $healthScore/100" -ForegroundColor $healthColor
        Write-Host "  Health Rating:    $healthRating" -ForegroundColor $healthColor
        
        if ($healthIssues.Count -gt 0) {
            Write-Host ""
            Write-Host "⚠️  HEALTH ISSUES IDENTIFIED:" -ForegroundColor Red
            $healthIssues | ForEach-Object {
                Write-Host "  • $_" -ForegroundColor Yellow
            }
        }
        
        # Recommendations
        Write-Host ""
        Write-Host "💡 RECOMMENDATIONS:" -ForegroundColor Yellow
        
        if ($healthScore -ge 90) {
            Write-Host "  ✅ System is operating excellently" -ForegroundColor Green
            Write-Host "  • Continue current monitoring schedule" -ForegroundColor Green
        } elseif ($healthScore -ge 70) {
            Write-Host "  ⚠️  System shows some concerning patterns" -ForegroundColor Yellow
            Write-Host "  • Monitor individual process growth" -ForegroundColor Yellow
            Write-Host "  • Consider process consolidation if possible" -ForegroundColor Yellow
        } else {
            Write-Host "  🚨 System requires attention" -ForegroundColor Red
            Write-Host "  • Investigate high memory processes" -ForegroundColor Red
            Write-Host "  • Consider restarting some processes" -ForegroundColor Red
            Write-Host "  • Check for memory leaks in individual processes" -ForegroundColor Red
        }
        
        # CSV Logging
        if ($EnableCSVLogging) {
            $processDetailsJson = ($processDetails | ConvertTo-Json -Compress) -replace '"', '""'
            "$($timestamp.ToString('yyyy-MM-dd HH:mm:ss')),$($processes.Count),$totalMemoryMB,$totalMemoryGB,$averageMemoryMB,$maxMemoryMB,$minMemoryMB,$totalHandles,$totalThreads,`"$processDetailsJson`"" | Out-File $LogFile -Append
        }
        
        Write-Host ""
        Write-Host "Next sample in $SampleIntervalSeconds seconds... (Ctrl+C to stop)" -ForegroundColor Gray
        if ($EnableCSVLogging) {
            Write-Host "Data logged to: $LogFile" -ForegroundColor Gray
        }
        
        Start-Sleep $SampleIntervalSeconds
        
    } catch {
        Write-Host "❌ Error during monitoring: $($_.Exception.Message)" -ForegroundColor Red
        Start-Sleep 10
    }
}
