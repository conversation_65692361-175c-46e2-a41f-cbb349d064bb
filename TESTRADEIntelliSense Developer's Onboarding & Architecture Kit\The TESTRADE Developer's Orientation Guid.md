The TESTRADE Developer's Orientation Guide
Purpose: To provide a developer or LLM with the essential architectural context needed to navigate the TESTRADE codebase effectively, make safe modifications, and understand the flow of data. Read this before modifying any code.
Section 1: The Core Philosophy - The "TANK"
Before you write a single line of code, understand this: TESTRADE is a fortress (a "TANK"). Its core logic is completely isolated.
Rule #1: The Core Does Not Wait. The core trading engine must never block on network I/O or wait for an external system. It does its job and moves on.
Rule #2: Egress is One-Way. The core's only connection to the outside world is via a single, buffered, non-blocking publisher: the BulletproofBabysitterIPCClient.
Rule #3: Consumers are Separate. All other systems (GUI, Intellisense) are consumers. They do not talk to TESTRADE directly. They read from the central message bus (Redis).
Section 2: The Architectural Blueprint - The "Horseshoe"
Data flows in a "U" shape. Memorize this path.
Producer (Right Side): The TESTRADE ApplicationCore produces an event.
Egress (Right Side): The event is handed to the BulletproofBabysitterIPCClient.
Router (Top of the U): The babysitter_service.py process receives the event via ZMQ and writes it to Redis.
Message Bus (Top of the U): Redis holds the event in a named stream.
Consumers (Left Side): The GUI Backend and Intellisense read the event from the Redis stream.
Generated text
// Simplified Data Flow
TESTRADE Core -> IPC Client -> Babysitter Service -> Redis -> Consumers
Use code with caution.
Text
Section 3: The Two Event Buses (Critical Distinction)
TESTRADE uses two separate and distinct event buses. Do not confuse them.
The Internal Bus (EventBus in event_bus.py)
Scope: In-memory, in-process only.
Purpose: The "central nervous system" inside the TESTRADE ApplicationCore. Used for high-speed communication between internal services (e.g., RiskManagementService talking to TradeExecutor).
When to Use: For communication between components that live within the same ApplicationCore process.
The External Bus (Redis via BulletproofBabysitterIPCClient)
Scope: System-wide, inter-process communication.
Purpose: The "public broadcast system." Used to publish the results of internal processing to the outside world (GUI, Intellisense, etc.).
When to Use: When a service has completed a significant action and needs to inform the rest of the ecosystem. This is the ONLY way data leaves the TANK.
Section 4: Navigating the Codebase - Where to Look
Use this map to find what you need quickly.
If you need to change the application's startup or service wiring...
Look in: core/application_core.py (the orchestrator)
Look in: core/di_registration.py (defines how services are built)
Look in: core/service_factories.py (the blueprints for building services)
If you need to add or change an event that goes to Redis...
1. Find the service that should be producing the event (e.g., modules/risk_management/risk_service.py).
2. Ensure it has a dependency on IBulletproofBabysitterIPCClient. This is injected via the factories.
3. In that service, call: self.babysitter_ipc_client.send_data(target_stream, data_json).
4. You are responsible for creating data_json. This includes the metadata wrapper with the correct eventType and correlationId.
If you need to add or change communication between internal services...
1. Find the producing service (e.g., OCRScalpingSignalOrchestratorService).
2. Ensure it has a dependency on IEventBus.
3. In that service, call: self.event_bus.publish(MyEvent(...)).
4. Find the consuming service (e.g., RiskManagementService).
5. Subscribe it to the event: self.event_bus.subscribe(MyEvent, self.handle_my_event).
If you need to understand the "contract" for a service...
Look in: core/service_interfaces.py. This is the blueprint for all major services.
