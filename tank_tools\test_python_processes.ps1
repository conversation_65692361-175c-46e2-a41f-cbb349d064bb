# test_python_processes.ps1 - Simple test for Python process detection
Write-Host "Testing Python Process Detection" -ForegroundColor Green
Write-Host "=" * 40

$processes = Get-Process python -ErrorAction SilentlyContinue

if ($processes.Count -eq 0) {
    Write-Host "No Python processes found" -ForegroundColor Red
} else {
    Write-Host "Found $($processes.Count) Python processes:" -ForegroundColor Green
    
    $totalMemoryBytes = 0
    foreach ($process in $processes) {
        $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
        $totalMemoryBytes += $process.WorkingSet64
        Write-Host "  PID $($process.Id): $memoryMB MB" -ForegroundColor White
    }
    
    $totalMemoryMB = [math]::Round($totalMemoryBytes / 1MB, 2)
    Write-Host ""
    Write-Host "Total Memory Usage: $totalMemoryMB MB" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Test completed!" -ForegroundColor Green
