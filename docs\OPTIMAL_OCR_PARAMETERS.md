# Optimal OCR Preprocessing Parameters

## Overview
This document contains the optimal OCR preprocessing parameters discovered through extensive testing. These parameters have been fine-tuned to produce the best raw OCR output for trading signal generation.

## Core OCR Parameters

### Basic Preprocessing
```json
{
  "ocr_upscale_factor": 3.0,
  "ocr_force_black_text_on_white": true,
  "ocr_unsharp_strength": 1.7,
  "ocr_threshold_block_size": 25,
  "ocr_threshold_c": -6,
  "ocr_red_boost": 1.0,
  "ocr_green_boost": 1.0
}
```

### Text Mask Cleaning
```json
{
  "ocr_apply_text_mask_cleaning": true,
  "ocr_text_mask_min_contour_area": 5,
  "ocr_text_mask_min_width": 2,
  "ocr_text_mask_min_height": 1
}
```

### Symbol Enhancement
```json
{
  "ocr_enhance_small_symbols": true,
  "ocr_symbol_max_height": 10,
  "ocr_period_comma_ratio_min": 0.2,
  "ocr_period_comma_ratio_max": 1.2,
  "ocr_period_comma_radius": 5,
  "ocr_hyphen_min_ratio": 3.0,
  "ocr_hyphen_min_height": 3
}
```

## Key Optimizations

1. **Maximum Upscale (3.0)**: Provides highest detail extraction
2. **Aggressive Thresholding (-6)**: Better text/background separation
3. **No Color Boosting (1.0)**: Source has good contrast already
4. **Small Contour Preservation (5)**: Catches smaller text elements
5. **Precise Symbol Detection**: Tight ranges for better character recognition

## Tesseract Configuration
- **Command**: `C:\Program Files\Tesseract-OCR\tesseract.exe`
- **PSM Mode**: 6 (Uniform block of text)
- **OEM Mode**: 3 (Default, based on what is available)

## ROI Configuration
- **Default ROI**: [64, 159, 681, 296]
- **Size**: 617 × 137 pixels
- **Position**: Top-left at (64, 159)

## Video Input Configuration
- **Live Capture Mode**: `OCR_INPUT_VIDEO_FILE_PATH: null`
- **Video Loop**: `video_loop_enabled: false`

## Performance Notes
- These parameters are optimized for trading platform screenshots
- Upscale factor of 3.0 provides best detail but uses more memory
- Text mask cleaning with small contour areas preserves fine details
- Symbol enhancement helps with periods, commas, and hyphens in numbers

## Usage
Copy these parameters to your `utils/control.json` file to apply optimal OCR settings.
