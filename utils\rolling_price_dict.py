import time
import bisect
import random
from collections import deque
from typing import Deque, List, Tuple, Dict, Optional

from utils.performance_tracker import create_timestamp_dict, add_timestamp, calculate_durations, add_to_stats
# Add module-level logger
import logging
module_logger = logging.getLogger(__name__)

class RollingPriceDeque:
    """
    A lightweight rolling store for (timestamp, price) that:
      - Assumes new timestamps are >= the last timestamp (monotonically increasing).
      - Keeps only the last `window_seconds` of data.
      - Supports O(log n) lookup for "closest time t".
      - For finding "closest price," we still do a linear scan (since we store by time).
    """

    def __init__(self, window_seconds: float = 5.0):
        self.window_seconds = window_seconds
        # We'll store timestamps in a separate deque for binary searching,
        # and keep (ts, px) in a parallel deque for quick pops from left.
        self.timestamps: Deque[float] = deque()
        self.data: Deque[Tuple[float, float]] = deque()

    def add_price(self, timestamp: float, price: float, perf_timestamps: Optional[Dict[str, float]] = None):
        """
        Append (timestamp, price) at the right.
        Prune any (ts, px) older than (timestamp - window_seconds).
        Assume new timestamp >= last timestamp for correct chronological order.
        """
        # Create or update performance timestamps
        try:
            if perf_timestamps is None:
                perf_timestamps = create_timestamp_dict()
            add_timestamp(perf_timestamps, 'rolling_price.add_price_start')
        except Exception as e:
            module_logger.warning(f"Error initializing performance tracking in add_price: {e}")
            perf_timestamps = create_timestamp_dict()

        # 1) Append new entry
        add_timestamp(perf_timestamps, 'rolling_price.append_start')
        self.data.append((timestamp, price))
        self.timestamps.append(timestamp)
        add_timestamp(perf_timestamps, 'rolling_price.append_end')

        # 2) Prune old entries from the left
        add_timestamp(perf_timestamps, 'rolling_price.prune_start')
        cutoff = timestamp - self.window_seconds
        # While the left-most entry is older than cutoff, pop it
        while self.data and self.data[0][0] < cutoff:
            _, _ = self.data[0]  # Unpack but don't use the values
            self.data.popleft()
            # Also remove from self.timestamps (kept in sync with data)
            self.timestamps.popleft()  # Changed from pop(0) to popleft() for O(1) operation
        add_timestamp(perf_timestamps, 'rolling_price.prune_end')

        try:
            add_timestamp(perf_timestamps, 'rolling_price.add_price_end')

            # Calculate and log performance metrics
            durations = calculate_durations(perf_timestamps)
            add_to_stats(perf_timestamps)

            # Log only occasionally to avoid spamming the console
            if random.random() < 0.001:  # Log approximately 0.1% of the time (since this is called very frequently)
                module_logger.debug(f"RollingPriceDeque.add_price PERF: " +
                           f"Total={durations.get('total_measured', 0)*1000:.3f}ms, " +
                           f"Append={durations.get('rolling_price.append_start_to_rolling_price.append_end', 0)*1000:.3f}ms, " +
                           f"Prune={durations.get('rolling_price.prune_start_to_rolling_price.prune_end', 0)*1000:.3f}ms, " +
                           f"SizeData={len(self.data)}, SizeTS={len(self.timestamps)}")
        except Exception as e:
            module_logger.warning(f"Error calculating performance metrics in add_price: {e}")

    def get_price_at_time(self, t: float, perf_timestamps: Optional[Dict[str, float]] = None) -> float:
        """
        Return the price whose timestamp is closest to 't'.
        If empty, returns 0.0.
        Uses binary search in self.timestamps to find the closest index.
        """
        # Create or update performance timestamps
        try:
            if perf_timestamps is None:
                perf_timestamps = create_timestamp_dict()
            add_timestamp(perf_timestamps, 'rolling_price.get_price_start')
        except Exception as e:
            module_logger.warning(f"Error initializing performance tracking in get_price_at_time: {e}")
            perf_timestamps = create_timestamp_dict()

        if not self.data:
            try:
                add_timestamp(perf_timestamps, 'rolling_price.get_price_end')
            except Exception:
                pass
            return 0.0

        # Binary search for position
        add_timestamp(perf_timestamps, 'rolling_price.binary_search_start')
        pos = bisect.bisect_left(self.timestamps, t)
        add_timestamp(perf_timestamps, 'rolling_price.binary_search_end')

        # 'pos' is where t would be inserted to maintain sorted order.
        # Candidate indices for the closest time: pos-1 or pos (if valid).
        add_timestamp(perf_timestamps, 'rolling_price.candidate_check_start')
        candidates = []
        if pos > 0:
            candidates.append(pos - 1)
        if pos < len(self.timestamps):
            candidates.append(pos)
        add_timestamp(perf_timestamps, 'rolling_price.candidate_check_end')

        # Compare which candidate is closer
        add_timestamp(perf_timestamps, 'rolling_price.find_closest_start')
        closest_idx = -1  # Changed from None to int for direct indexing
        closest_diff = float('inf')
        for idx in candidates:
            # Check bounds for safety, though bisect_left should ensure `idx` is valid if candidates are derived correctly
            if 0 <= idx < len(self.timestamps):
                ts_candidate = self.timestamps[idx]
                diff = abs(ts_candidate - t)
                if diff < closest_diff:
                    closest_diff = diff
                    closest_idx = idx
            # else: This case should ideally not happen if pos and candidates are correct
            #     module_logger.warning(f"get_price_at_time: Candidate index {idx} out of bounds for timestamps (len: {len(self.timestamps)})")
        add_timestamp(perf_timestamps, 'rolling_price.find_closest_end')

        # Return the price from self.data at that index
        result = 0.0
        if closest_idx != -1 and 0 <= closest_idx < len(self.data):  # Check bounds for self.data as well
            result = self.data[closest_idx][1]  # (timestamp, price)
        # else:
        #     module_logger.warning(f"get_price_at_time: No valid closest_idx found or out of bounds for data. Pos: {pos}, Candidates: {candidates}, ClosestIdx: {closest_idx}, DataLen: {len(self.data)}")

        try:
            add_timestamp(perf_timestamps, 'rolling_price.get_price_end')

            # Calculate and log performance metrics
            durations = calculate_durations(perf_timestamps)
            add_to_stats(perf_timestamps)

            # Log only occasionally to avoid spamming the console
            if random.random() < 0.001:  # Log approximately 0.1% of the time (since this is called very frequently)
                module_logger.debug(f"RollingPriceDeque.get_price PERF: " +
                           f"Total={durations.get('total_measured', 0)*1000:.3f}ms, " +
                           f"Search={durations.get('rolling_price.binary_search_start_to_rolling_price.binary_search_end', 0)*1000:.3f}ms, " +
                           f"Closest={durations.get('rolling_price.find_closest_start_to_rolling_price.find_closest_end', 0)*1000:.3f}ms")
        except Exception as e:
            module_logger.warning(f"Error calculating performance metrics in get_price_at_time: {e}")

        return result

    def find_timestamp_for_price(self, target_price: float, price_tolerance: float = 0.01,
                                 after_timestamp: Optional[float] = None,
                                 perf_timestamps: Optional[Dict[str, float]] = None) -> Optional[float]:
        """
        Find the timestamp of the first entry whose price is within target_price +/- price_tolerance.
        If after_timestamp is provided, only consider entries after that timestamp.

        Args:
            target_price: The price to match
            price_tolerance: Tolerance for price matching (default 0.01)
            after_timestamp: Only consider entries after this timestamp (optional)
            perf_timestamps: Performance tracking timestamps (optional)

        Returns:
            Timestamp of matching entry, or None if no match found
        """
        # Create or update performance timestamps
        try:
            if perf_timestamps is None:
                perf_timestamps = create_timestamp_dict()
            add_timestamp(perf_timestamps, 'rolling_price.find_timestamp_start')
        except Exception as e:
            module_logger.warning(f"Error initializing performance tracking in find_timestamp_for_price: {e}")
            perf_timestamps = create_timestamp_dict()

        if not self.data:
            try:
                add_timestamp(perf_timestamps, 'rolling_price.find_timestamp_end')
            except Exception:
                pass
            return None

        # Linear scan to find first matching price within tolerance
        add_timestamp(perf_timestamps, 'rolling_price.linear_scan_start')
        for (ts, px) in self.data:
            # Skip entries before the after_timestamp if specified
            if after_timestamp is not None and ts <= after_timestamp:
                continue

            # Check if price is within tolerance
            if abs(px - target_price) <= price_tolerance:
                add_timestamp(perf_timestamps, 'rolling_price.linear_scan_end')
                try:
                    add_timestamp(perf_timestamps, 'rolling_price.find_timestamp_end')
                    # Calculate and log performance metrics
                    durations = calculate_durations(perf_timestamps)
                    add_to_stats(perf_timestamps)
                except Exception as e:
                    module_logger.warning(f"Error calculating performance metrics in find_timestamp_for_price: {e}")
                return ts

        add_timestamp(perf_timestamps, 'rolling_price.linear_scan_end')

        # No match found within tolerance
        try:
            add_timestamp(perf_timestamps, 'rolling_price.find_timestamp_end')
            # Calculate and log performance metrics
            durations = calculate_durations(perf_timestamps)
            add_to_stats(perf_timestamps)
            # Log only occasionally to avoid spamming the console
            if random.random() < 0.001:  # Log approximately 0.1% of the time
                module_logger.debug(f"RollingPriceDeque.find_ts PERF: " +
                           f"Total={durations.get('total_measured', 0)*1000:.3f}ms, " +
                           f"Scan={durations.get('rolling_price.linear_scan_start_to_rolling_price.linear_scan_end', 0)*1000:.3f}ms")
        except Exception as e:
            module_logger.warning(f"Error calculating performance metrics in find_timestamp_for_price: {e}")

        return None  # No match found within tolerance

    def find_closest_timestamp_for_price(self, target_price: float,
                                        after_timestamp: Optional[float] = None,
                                        perf_timestamps: Optional[Dict[str, float]] = None) -> Optional[float]:
        """
        Find the timestamp of the entry whose price is closest to target_price.
        If after_timestamp is provided, only consider entries after that timestamp.
        This is the fallback method when exact tolerance matching fails.

        Args:
            target_price: The price to match
            after_timestamp: Only consider entries after this timestamp (optional)
            perf_timestamps: Performance tracking timestamps (optional)

        Returns:
            Timestamp of closest matching entry, or None if no data
        """
        # Create or update performance timestamps
        try:
            if perf_timestamps is None:
                perf_timestamps = create_timestamp_dict()
            add_timestamp(perf_timestamps, 'rolling_price.find_closest_timestamp_start')
        except Exception as e:
            module_logger.warning(f"Error initializing performance tracking in find_closest_timestamp_for_price: {e}")
            perf_timestamps = create_timestamp_dict()

        if not self.data:
            try:
                add_timestamp(perf_timestamps, 'rolling_price.find_closest_timestamp_end')
            except Exception:
                pass
            return None

        # Linear scan to find closest price
        add_timestamp(perf_timestamps, 'rolling_price.closest_scan_start')
        best_ts = None
        closest_diff = float('inf')
        for (ts, px) in self.data:
            # Skip entries before the after_timestamp if specified
            if after_timestamp is not None and ts <= after_timestamp:
                continue

            diff = abs(px - target_price)
            if diff < closest_diff:
                closest_diff = diff
                best_ts = ts
        add_timestamp(perf_timestamps, 'rolling_price.closest_scan_end')

        try:
            add_timestamp(perf_timestamps, 'rolling_price.find_closest_timestamp_end')
            # Calculate and log performance metrics
            durations = calculate_durations(perf_timestamps)
            add_to_stats(perf_timestamps)
            # Log only occasionally to avoid spamming the console
            if random.random() < 0.001:  # Log approximately 0.1% of the time
                module_logger.debug(f"RollingPriceDeque.find_closest_ts PERF: " +
                           f"Total={durations.get('total_measured', 0)*1000:.3f}ms, " +
                           f"Scan={durations.get('rolling_price.closest_scan_start_to_rolling_price.closest_scan_end', 0)*1000:.3f}ms")
        except Exception as e:
            module_logger.warning(f"Error calculating performance metrics in find_closest_timestamp_for_price: {e}")

        return best_ts

    def get_price_at_time(self, target_timestamp: float,
                         perf_timestamps: Optional[Dict[str, float]] = None) -> Optional[float]:
        """
        Find the price from an entry whose timestamp is closest to target_timestamp.

        Args:
            target_timestamp: The timestamp to find the closest price for
            perf_timestamps: Performance tracking timestamps (optional)

        Returns:
            Price of the entry with timestamp closest to target_timestamp, or None if no data
        """
        # Create or update performance timestamps
        try:
            if perf_timestamps is None:
                perf_timestamps = create_timestamp_dict()
            add_timestamp(perf_timestamps, 'rolling_price.get_price_at_time_start')
        except Exception as e:
            module_logger.warning(f"Error initializing performance tracking in get_price_at_time: {e}")
            perf_timestamps = create_timestamp_dict()

        if not self.data:
            try:
                add_timestamp(perf_timestamps, 'rolling_price.get_price_at_time_end')
            except Exception:
                pass
            return None

        # Linear scan to find closest timestamp
        add_timestamp(perf_timestamps, 'rolling_price.time_scan_start')
        best_price = None
        closest_diff = float('inf')
        for (ts, px) in self.data:
            diff = abs(ts - target_timestamp)
            if diff < closest_diff:
                closest_diff = diff
                best_price = px
        add_timestamp(perf_timestamps, 'rolling_price.time_scan_end')

        try:
            add_timestamp(perf_timestamps, 'rolling_price.get_price_at_time_end')
            # Calculate and log performance metrics
            durations = calculate_durations(perf_timestamps)
            add_to_stats(perf_timestamps)
            # Log only occasionally to avoid spamming the console
            if random.random() < 0.001:  # Log approximately 0.1% of the time
                module_logger.debug(f"RollingPriceDeque.get_price_at_time PERF: " +
                           f"Total={durations.get('total_measured', 0)*1000:.3f}ms, " +
                           f"Scan={durations.get('rolling_price.time_scan_start_to_rolling_price.time_scan_end', 0)*1000:.3f}ms")
        except Exception as e:
            module_logger.warning(f"Error calculating performance metrics in get_price_at_time: {e}")

        return best_price

    def get_latest_price(self) -> float:
        """
        Return the most recent price in the deque.
        If empty, returns 0.0
        """
        if not self.data:
            return 0.0
        # The deque is ordered by timestamp, so the last entry is the most recent
        return self.data[-1][1]  # Return price from (timestamp, price) tuple
