#!/usr/bin/env python3
"""
Test script for mode-aware dependency injection
"""
import os
import sys
import logging

# Add the TESTRADE root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.dependency_injection import DIContainer
from core.di_registration import register_all_services
from utils.testrade_modes import get_current_mode, TestradeMode

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_mode_aware_di():
    """Test DI container in different modes"""
    
    # Save original mode
    original_mode = os.environ.get('TESTRADE_MODE', '')
    
    try:
        # Test 1: TANK_SEALED mode
        logger.info("\n=== Testing TANK_SEALED mode ===")
        os.environ['TESTRADE_MODE'] = 'TANK_SEALED'
        
        container = DIContainer()
        register_all_services(container)
        
        # In TANK_SEALED mode, telemetry and IPC services should not be registered
        from interfaces.core.telemetry_interfaces import ITelemetryService
        from interfaces.core.services import IBulletproofBabysitterIPCClient
        
        try:
            telemetry_service = container.resolve(ITelemetryService)
            logger.error("ERROR: ITelemetryService should not be available in TANK_SEALED mode!")
        except Exception:
            logger.info("✓ ITelemetryService correctly not available in TANK_SEALED mode")
            
        try:
            ipc_client = container.resolve(IBulletproofBabysitterIPCClient)
            logger.error("ERROR: IBulletproofBabysitterIPCClient should not be available in TANK_SEALED mode!")
        except Exception:
            logger.info("✓ IBulletproofBabysitterIPCClient correctly not available in TANK_SEALED mode")
            
        # Test 2: TANK_BUFFERED mode
        logger.info("\n=== Testing TANK_BUFFERED mode ===")
        os.environ['TESTRADE_MODE'] = 'TANK_BUFFERED'
        
        container = DIContainer()
        register_all_services(container)
        
        # In TANK_BUFFERED mode, telemetry should be available but IPC should not
        try:
            telemetry_service = container.resolve(ITelemetryService)
            logger.info("✓ ITelemetryService correctly available in TANK_BUFFERED mode")
        except Exception as e:
            logger.error(f"ERROR: ITelemetryService should be available in TANK_BUFFERED mode: {e}")
            
        try:
            ipc_client = container.resolve(IBulletproofBabysitterIPCClient)
            logger.error("ERROR: IBulletproofBabysitterIPCClient should not be available in TANK_BUFFERED mode!")
        except Exception:
            logger.info("✓ IBulletproofBabysitterIPCClient correctly not available in TANK_BUFFERED mode")
            
        # Test 3: LIVE mode
        logger.info("\n=== Testing LIVE mode ===")
        os.environ['TESTRADE_MODE'] = 'LIVE'
        
        container = DIContainer()
        register_all_services(container)
        
        # In LIVE mode, both should be available (if ZMQ is installed)
        try:
            telemetry_service = container.resolve(ITelemetryService)
            logger.info("✓ ITelemetryService correctly available in LIVE mode")
        except Exception as e:
            logger.error(f"ERROR: ITelemetryService should be available in LIVE mode: {e}")
            
        try:
            import zmq
            ipc_client = container.resolve(IBulletproofBabysitterIPCClient)
            logger.info("✓ IBulletproofBabysitterIPCClient correctly available in LIVE mode")
        except ImportError:
            logger.info("✓ ZMQ not installed, IBulletproofBabysitterIPCClient correctly not available")
        except Exception as e:
            logger.error(f"ERROR: IBulletproofBabysitterIPCClient should be available in LIVE mode: {e}")
            
        # Test correlation logger
        logger.info("\n=== Testing Correlation Logger ===")
        from interfaces.logging.services import ICorrelationLogger
        
        # Enable intellisense logging for testing
        from utils.global_config import config as global_config
        global_config.ENABLE_INTELLISENSE_LOGGING = True
        
        container = DIContainer()
        register_all_services(container)
        
        correlation_logger = container.resolve(ICorrelationLogger)
        logger.info(f"✓ Resolved correlation logger: {type(correlation_logger).__name__}")
        
    finally:
        # Restore original mode
        if original_mode:
            os.environ['TESTRADE_MODE'] = original_mode
        else:
            os.environ.pop('TESTRADE_MODE', None)
            
    logger.info("\n=== Mode-aware DI testing complete ===")

if __name__ == "__main__":
    test_mode_aware_di()