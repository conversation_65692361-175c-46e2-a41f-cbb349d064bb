Document 4 of 4: The "IntelliSense Operating Modes" Playbook
Purpose: To define the two valid operating states for the Intellisense application and the rules governing the lifecycle of its core engine. This playbook is the definitive guide to preventing resource leaks and "zombie" processes. Adherence to this model is mandatory.
The Core Problem This Solves
The Intellisense platform includes its own instance of the trading engine (IntelliSenseApplicationCore) to perform offline analysis and replay. This engine is resource-intensive and launches its own subprocesses. The previous system instability was caused by this engine being launched incorrectly and not being shut down properly.
The Golden Rule: The IntelliSenseApplicationCore is not a persistent, long-running service. It is a temporary, on-demand tool that must be created for a specific analysis task and completely destroyed afterward.
Mode 1: "Passive Consumer" (Default State)
When to Use: This is the default state of the intellisense/main.py API server. It should be in this mode when it is idle or when it is actively capturing live trading data to create Correlation Logs.
Architectural State:
The FastAPI/Uvicorn server process is running.
The server is connected to Redis, consuming live event streams from the main TESTRADE system.
Crucially, NO IntelliSenseApplicationCore or IntelliSenseMasterController instance exists in memory. The server is lightweight and is only performing its consumer duties.
How to Verify: Running wmic process where "name='python.exe'" get processid,commandline should show only the main API server process for Intellisense. There should be no child processes spawned from it.
Mode 2: "Active Simulator" (On-Demand State)
When to Use: This mode is used only when a user initiates an offline analysis or replay task.
Architectural State:
This mode is triggered exclusively by an API call to POST /sessions/.
This API endpoint is responsible for creating a new, dedicated instance of IntelliSenseApplicationCore and IntelliSenseMasterController.
This controller instance is stored in a global dictionary (app_state.active_sessions) keyed by its unique session_id.
How to Verify: After a successful POST /sessions/ call, running wmic process will now show the main API server process and one or more new python.exe child processes. These are the components of the now-active core engine for that specific session.
The Shutdown Protocol (The Most Critical Rule)
Transitioning from "Active Simulator" back to "Passive Consumer" mode is the most critical phase and must be handled precisely to prevent zombie processes.
Trigger: This process is triggered exclusively by an API call to DELETE /sessions/{session_id}.
The Protocol:
The API endpoint retrieves the correct IntelliSenseMasterController instance from the app_state.active_sessions dictionary using the session_id.
It calls the controller.shutdown() method. This is a non-negotiable step. The shutdown() method is responsible for telling its IntelliSenseApplicationCore to stop all threads and subprocesses.
After calling shutdown(), the endpoint must remove the controller instance from the dictionary: del app_state.active_sessions[session_id]. This releases the last reference to the object, allowing Python's garbage collector to clean it up.
How to Verify: After a successful DELETE /sessions/{id} call, wait a few seconds, then run wmic process. The child processes associated with that session must be gone. The system should have returned to the "Passive Consumer" state.