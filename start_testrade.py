#!/usr/bin/env python3
"""
TESTRADE TANK MODE Startup Script
Automates the startup sequence as per TANK_MODE_STARTUP_GUIDE.md.
"""

import subprocess
import time
import psutil
import json
import os
import sys
import signal
import argparse
import webbrowser
from typing import Optional, List, Dict, Any

# --- Configuration ---
# These should match your environment and TANK_MODE_STARTUP_GUIDE.md
REDIS_HOST = "**************"  # Explicit IP, no DNS
REDIS_PORT = 6379
CONFIG_FILE_PATH = "utils/control.json" # Relative to where this script is run
LOG_DIR = "logs" # Ensure this directory exists
DATA_DIR = "data" # Ensure this directory exists

# WSL Configuration for Redis diagnostics
WSL_DISTRO_NAME = "Ubuntu"
REDIS_PID_COMMAND_WSL = ["pgrep", "redis-server"]

# Paths to the scripts to be started (relative to where this script is run or absolute)
# Adjust these if your project structure is different
BABYSITTER_SCRIPT = "core/babysitter_service.py"
# Assuming start_babysitter_with_config.py is preferred and located alongside this script
BABYSITTER_STARTUP_SCRIPT = "start_babysitter_with_config.py"
CORE_SCRIPT = "run_headless_core.py"
GUI_BACKEND_SCRIPT = "run_gui_backend.py"
# For GUI Frontend, we'll offer a choice or default to one
GUI_FRONTEND_NPM_COMMAND = ["npm", "start"]
GUI_FRONTEND_PYTHON_COMMAND = ["python", "-m", "http.server", "3000"]
GUI_COMPONENT_DIR = "gui"

# Timeouts and delays
PROCESS_START_TIMEOUT = 10  # seconds to wait for a process to appear
PORT_LISTEN_TIMEOUT = 10    # seconds to wait for a port to start listening
REDIS_PING_TIMEOUT = 5      # seconds for redis ping
HEALTH_STREAM_TIMEOUT = 15  # seconds to wait for initial health message

# Global list to keep track of started processes for cleanup
started_processes: List[psutil.Process] = []
started_processes_psutil: List[psutil.Process] = []
app_config: Dict[str, Any] = {}

# Python executable for child processes
PYTHON_EXECUTABLE_FOR_CHILDREN = sys.executable

# Global flag for showing console windows
global_show_consoles = False

# --- Helper Functions ---

def print_step(num: int, message: str):
    print(f"\n--- STEP {num}: {message} ---")

def print_info(message: str):
    print(f"  [INFO]  {message}")

def print_success(message: str):
    print(f"  [SUCCESS] {message}")

def print_warning(message: str):
    print(f"  [WARNING] {message}")

def print_error(message: str):
    print(f"  [ERROR] {message}")
    sys.exit(1)

def ensure_dir_exists(dir_path: str):
    if not os.path.exists(dir_path):
        print_info(f"Creating directory: {dir_path}")
        try:
            os.makedirs(dir_path, exist_ok=True)
        except OSError as e:
            print_error(f"Could not create directory {dir_path}: {e}")

def load_app_config() -> Dict[str, Any]:
    global app_config
    print_info(f"Loading configuration from {CONFIG_FILE_PATH}...")
    if not os.path.exists(CONFIG_FILE_PATH):
        print_error(f"Configuration file not found: {CONFIG_FILE_PATH}")
    try:
        with open(CONFIG_FILE_PATH, 'r') as f:
            app_config = json.load(f)
        print_success("Configuration loaded.")
        # Override Redis host/port if present in config, but guide uses specific IP
        if "BABYSITTER_REDIS_HOST" in app_config:
            print_info(f"Using BABYSITTER_REDIS_HOST from config: {app_config['BABYSITTER_REDIS_HOST']}")
            # global REDIS_HOST # Not needed if we use app_config directly
            # REDIS_HOST = app_config['BABYSITTER_REDIS_HOST']
        if "BABYSITTER_REDIS_PORT" in app_config:
            print_info(f"Using BABYSITTER_REDIS_PORT from config: {app_config['BABYSITTER_REDIS_PORT']}")
            # global REDIS_PORT
            # REDIS_PORT = int(app_config['BABYSITTER_REDIS_PORT'])

        # Extract expected ports
        app_config['_babysitter_port'] = int(app_config.get("babysitter_ipc_address", "tcp://localhost:5555").split(":")[-1])
        app_config['_core_port'] = int(app_config.get("core_ipc_command_pull_address", "tcp://localhost:5556").split(":")[-1])
        # GUI Backend port is often 8001 by convention, but let's make it configurable if needed
        app_config['_gui_backend_port'] = 8001 # As per typical setup
        app_config['_gui_frontend_port'] = 3000 # As per typical setup
        return app_config
    except json.JSONDecodeError as e:
        print_error(f"Error decoding JSON from {CONFIG_FILE_PATH}: {e}")
    except Exception as e:
        print_error(f"Error loading configuration: {e}")
    return {}


def check_redis_connection(host: str, port: int) -> bool:
    print_info(f"Checking Redis connection to {host}:{port}...")
    try:
        import redis # Ensure redis library is imported here
        r = redis.Redis(host=host, port=port, socket_connect_timeout=REDIS_PING_TIMEOUT, socket_timeout=REDIS_PING_TIMEOUT)
        if r.ping():
            print_success("Redis connection: OK")
            return True
        else:
            print_warning("Redis ping failed (returned False).")
            return False
    except ImportError:
        print_warning("Python 'redis' library not found. Falling back to redis-cli.")
        try:
            # Fallback to redis-cli if Python redis library is not available
            cmd = ["redis-cli", "-h", host, "-p", str(port), "ping"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=REDIS_PING_TIMEOUT)
            if result.returncode == 0 and "PONG" in result.stdout:
                print_success("Redis connection (via redis-cli): OK")
                return True
            else:
                print_warning(f"redis-cli ping failed. Output: {result.stdout.strip()} {result.stderr.strip()}")
                return False
        except FileNotFoundError:
            print_error("redis-cli command not found. Cannot check Redis status.")
        except subprocess.TimeoutExpired:
            print_warning(f"redis-cli ping to {host}:{port} timed out.")
        except Exception as e:
            print_warning(f"Error checking Redis with redis-cli: {e}")
    except redis.exceptions.ConnectionError as e:
        print_warning(f"Cannot connect to Redis at {host}:{port}: {e}")
    except Exception as e:
        print_warning(f"An unexpected error occurred while checking Redis: {e}")
    return False

def is_redis_running_in_wsl() -> bool:
    """Check if Redis process is running within WSL distro (diagnostic function)."""
    print_info(f"Checking if Redis process is running in WSL distro '{WSL_DISTRO_NAME}'...")
    try:
        result = subprocess.run(
            ['wsl', '-d', WSL_DISTRO_NAME, '--'] + REDIS_PID_COMMAND_WSL,
            capture_output=True, text=True, timeout=5
        )
        if result.returncode == 0 and result.stdout.strip():
            print_success(f"Redis process found running in WSL (PIDs: {result.stdout.strip()}).")
            return True
        print_info("Redis process not found running in WSL (or pgrep failed).")
        return False
    except subprocess.TimeoutExpired:
        print_warning("Timeout checking Redis process status in WSL.")
        return False
    except FileNotFoundError:
        # Only print error if wsl.exe itself is not found.
        # If pgrep is not found inside WSL, that's a different issue.
        print_error("`wsl.exe` not found. Is WSL installed and in PATH?")
        return False
    except Exception as e:
        print_warning(f"Error checking Redis process status in WSL: {e}")
        return False

def is_process_running(cmd_keywords: List[str]) -> Optional[psutil.Process]:
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline']:
                cmdline_str = ' '.join(proc.info['cmdline']).lower()
                if all(keyword.lower() in cmdline_str for keyword in cmd_keywords):
                    return proc
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return None

def is_port_listening(port: int, host: str = '127.0.0.1', expected_pid: Optional[int] = None) -> bool:
    """Checks if a port is in LISTEN state, optionally for a specific PID."""
    for conn in psutil.net_connections(kind='tcp'): # Check tcp, tcp4, tcp6 if needed
        if conn.status == psutil.CONN_LISTEN and conn.laddr.port == port:
            # If host is '0.0.0.0', it means listen on all interfaces.
            # If specific host is given, check if laddr.ip matches or is '0.0.0.0'
            if host == '0.0.0.0' or conn.laddr.ip == host or conn.laddr.ip == '0.0.0.0':
                if expected_pid is None or conn.pid == expected_pid:
                    return True
    return False

def start_process(command: List[str], log_filename: str, cwd: Optional[str] = None,
                  env_vars: Optional[Dict[str, str]] = None,
                  expected_keywords: List[str] = [],
                  show_console: bool = False) -> Optional[psutil.Process]:
    """Starts a process and returns the psutil.Process object."""
    full_log_path = os.path.join(LOG_DIR, log_filename)
    print_info(f"Starting: {' '.join(command)}")
    print_info(f"  Logging to: {full_log_path}")
    if cwd:
        print_info(f"  Working directory: {os.path.abspath(cwd)}")

    # Check if already running by keywords if provided
    if expected_keywords:
        existing_proc = is_process_running(expected_keywords)
        if existing_proc:
            print_warning(f"{' '.join(expected_keywords)} seems to be already running (PID {existing_proc.pid}). Skipping start.")
            started_processes.append(existing_proc) # Add to list for completeness if it's meant to be managed
            return existing_proc

    process_env = os.environ.copy()
    if env_vars:
        process_env.update(env_vars)

    try:
        py_executable = sys.executable # Path to current python interpreter

        # If command is a python script, prepend with python interpreter
        actual_command = command
        if command[0].endswith(".py") and command[0] != py_executable :
            actual_command = [py_executable] + command

        # --- PREPARE FOR SUBPROCESS LAUNCH ---
        stdout_dest = None
        stderr_dest = None
        creationflags = 0 # For Popen

        if show_console and os.name == 'nt': # Windows: Launch in a new console window
            print_info(f"  Attempting to start in NEW CONSOLE: {' '.join(actual_command)}")
            # For Windows, subprocess.CREATE_NEW_CONSOLE will give it its own console.
            creationflags |= subprocess.CREATE_NEW_CONSOLE
            # stdout and stderr will go to that new console by default if not redirected
            stdout_dest = None # Goes to its own new console
            stderr_dest = None
        elif show_console: # Linux/macOS or if not creating a new window on Windows
            print_info(f"  Attempting to start with INHERITED CONSOLE: {' '.join(actual_command)}")
            # Let it inherit stdout/stderr from this script
            stdout_dest = None # Inherits if None
            stderr_dest = None # Inherits if None
            # No special creation flags needed here for console inheritance
        else: # Default: Redirect to log file
            print_info(f"  Attempting to start (logging to {log_filename}): {' '.join(actual_command)}")
            log_file_handle = open(full_log_path, 'a')
            stdout_dest = log_file_handle
            stderr_dest = log_file_handle

        # ALWAYS set CREATE_NEW_PROCESS_GROUP on Windows to enable group signaling
        # This is critical for trying to get child processes of your direct children.
        if os.name == 'nt':
            creationflags |= subprocess.CREATE_NEW_PROCESS_GROUP

        sub_proc = subprocess.Popen(
            actual_command,
            stdout=stdout_dest,
            stderr=stderr_dest,
            cwd=cwd,
            env=process_env,
            creationflags=creationflags,
            # start_new_session=True # For better signal handling on non-Windows
        )
        time.sleep(1) # Give it a moment to start

        # Try to find the process using psutil
        # This is more reliable than just sub_proc.pid if the script launches other scripts
        # However, for direct python scripts, sub_proc.pid is the direct child.
        # If expected_keywords are specific enough, use them.
        ps_proc = None
        if expected_keywords:
            for _ in range(PROCESS_START_TIMEOUT):
                ps_proc = is_process_running(expected_keywords)
                if ps_proc: break
                time.sleep(1)
        
        if not ps_proc: # Fallback to PID from Popen if keywords didn't find it or weren't specific
            try:
                ps_proc = psutil.Process(sub_proc.pid)
            except psutil.NoSuchProcess:
                pass # Process might have died quickly

        if ps_proc and ps_proc.is_running():
            proc_display_name = actual_command[1] if actual_command[0].endswith("python.exe") or actual_command[0].endswith("python") else actual_command[0]
            log_msg_part = f"Log: {full_log_path}" if not show_console else "Console output in its own window/inherited."
            print_success(f"Started {proc_display_name} (PID {ps_proc.pid}) - {log_msg_part}")
            started_processes.append(ps_proc)

            # Enhanced tracking: store process with command line info for better GUI detection
            ps_proc._testrade_command = actual_command  # Store original command for GUI detection
            ps_proc._testrade_display_name = proc_display_name  # Store display name
            started_processes_psutil.append(ps_proc)  # Add to new tracking list
            return ps_proc
        else:
            print_warning(f"Process may not have started correctly or died quickly. Check log: {full_log_path}")
            # If sub_proc is available and has a return code, show it
            if sub_proc.poll() is not None:
                print_warning(f"  Process exited with code: {sub_proc.poll()}")
            return None

    except FileNotFoundError:
        print_error(f"Command not found: {command[0]}. Ensure it's in PATH or path is correct.")
    except Exception as e:
        print_error(f"Failed to start process {' '.join(command)}: {e}")
    return None

def verify_component(
    name: str,
    proc: Optional[psutil.Process],
    expected_port: Optional[int] = None,
    listen_host: str = '127.0.0.1', # For port check: 0.0.0.0 for all, 127.0.0.1 for localhost
    redis_health_stream: Optional[str] = None
) -> bool:
    if not proc or not proc.is_running():
        print_warning(f"{name} process not found or not running.")
        return False
    print_success(f"{name} process is running (PID {proc.pid}).")

    if expected_port:
        print_info(f"  Verifying {name} listening on port {expected_port}...")
        for _ in range(PORT_LISTEN_TIMEOUT):
            # Check if the specific process is listening (if PID is available)
            if is_port_listening(expected_port, host=listen_host, expected_pid=proc.pid):
                print_success(f"  {name} is listening on port {expected_port}.")
                break
            time.sleep(1)
        else:
            # Fallback: check if *any* process is listening if PID specific check failed
            if is_port_listening(expected_port, host=listen_host):
                print_warning(f"  Port {expected_port} is listening, but NOT confirmed by {name} PID {proc.pid}. (Might be another process or PID mismatch)")
            else:
                print_warning(f"  {name} (PID {proc.pid}) is NOT listening on port {expected_port} after {PORT_LISTEN_TIMEOUT}s.")
                return False # Or be more lenient

    if redis_health_stream:
        print_info(f"  Checking Redis health stream '{redis_health_stream}' for {name}...")
        try:
            import redis
            r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)
            # Check for a recent message. '$' means latest. We want to see if it *starts* producing.
            # This is a basic check; a real health check would parse the message content.
            # Wait for up to HEALTH_STREAM_TIMEOUT seconds for the first message.
            messages = r.xread({redis_health_stream: '0-0'}, count=1, block=HEALTH_STREAM_TIMEOUT * 1000)
            if messages:
                # msg_id, msg_data = messages[0][1][0]
                print_success(f"  Received message on {redis_health_stream}.")
            else:
                print_warning(f"  No message received on {redis_health_stream} for {name} within {HEALTH_STREAM_TIMEOUT}s.")
                # return False # This might be too strict for initial startup
        except ImportError:
            print_warning("  Python 'redis' library not found. Cannot check health stream.")
        except Exception as e:
            print_warning(f"  Error checking Redis stream {redis_health_stream}: {e}")

    return True


def cleanup_all_started_processes(signum=None, frame=None):
    """Enhanced 3-phase process cleanup: graceful termination, psutil.kill, then taskkill."""
    print_info("\n[STOP] Ctrl+C or SIGTERM received. Cleaning up all started TESTRADE processes...")

    if not started_processes_psutil:
        print_info("  No processes were tracked for cleanup.")
        if signum is not None:
             print_info("  Exiting now due to signal.")
             sys.exit(0)
        return

    # Phase 1: Graceful Termination (SIGTERM / CTRL_BREAK_EVENT to process group)
    print_info("  Phase 1: Attempting graceful shutdown (SIGTERM/CTRL_BREAK_EVENT to groups)...")
    for proc_psutil_obj in reversed(list(started_processes_psutil)): # Iterate a copy
        try:
            if proc_psutil_obj.is_running():
                proc_name = "Unknown"; pid = proc_psutil_obj.pid
                try: proc_name = proc_psutil_obj.name()
                except: pass

                # Enhanced GUI detection using stored command info
                display_name = getattr(proc_psutil_obj, '_testrade_display_name', proc_name)
                command_info = getattr(proc_psutil_obj, '_testrade_command', [])
                command_str = ' '.join(command_info).lower() if command_info else ''

                print_info(f"    Terminating PID {pid} ({display_name})...")

                # Enhanced GUI component detection
                is_gui_backend = (
                    'gui_backend' in display_name.lower() or
                    'gui_backend' in command_str or
                    'uvicorn' in command_str
                )
                is_gui_frontend = (
                    'npm' in display_name.lower() or
                    'npm' in command_str or
                    'node' in proc_name.lower() or
                    'start' in command_str and 'gui' in command_str
                )

                if os.name == 'nt':
                    try:
                        # Requires CREATE_NEW_PROCESS_GROUP during Popen
                        print_info(f"      Attempting CTRL_BREAK_EVENT to PID {pid} group ({proc_name})...")
                        os.kill(pid, signal.CTRL_BREAK_EVENT)
                        print_info(f"      ✅ Sent CTRL_BREAK_EVENT to PID {pid} group.")

                        # For GUI components, give extra time for graceful shutdown
                        if is_gui_backend or is_gui_frontend:
                            print_info(f"      GUI component detected - allowing extra shutdown time...")

                    except ProcessLookupError: # Process might have died between is_running and os.kill
                        print_info(f"      PID {pid} group not found (already exited).")
                    except Exception as e_break:
                        print_warning(f"      Failed to send CTRL_BREAK_EVENT to PID {pid} group: {e_break}. Falling back to terminate process.")
                        try:
                            print_info(f"      Attempting proc.terminate() for PID {pid}...")
                            proc_psutil_obj.terminate()
                            print_info(f"      ✅ Sent terminate signal to PID {pid}.")
                        except Exception as e_term:
                            print_warning(f"      Failed to terminate PID {pid}: {e_term}")
                else: # Linux/macOS
                    try:
                        pgid = os.getpgid(pid)
                        os.killpg(pgid, signal.SIGTERM)
                        print_info(f"      Sent SIGTERM to process group {pgid} (for PID {pid}).")
                    except ProcessLookupError:
                         print_info(f"      PID {pid} or group {pgid} not found (already exited).")
                    except Exception as e_pg:
                        print_warning(f"      Failed to send SIGTERM to process group for PID {pid}: {e_pg}. Falling back to terminate process.")
                        try: proc_psutil_obj.terminate()
                        except Exception: pass
            else:
                # Remove already stopped processes from the list if iterating a copy
                if proc_psutil_obj in started_processes_psutil:
                    started_processes_psutil.remove(proc_psutil_obj)
        except Exception:
            print_info(f"    Process for PID (was {proc_psutil_obj.pid if proc_psutil_obj else 'unknown'}) already gone or access denied during termination attempt.")
            if proc_psutil_obj in started_processes_psutil:
                 started_processes_psutil.remove(proc_psutil_obj)
        except Exception as e:
            print_warning(f"    Error during initial termination attempt for PID (was {proc_psutil_obj.pid if proc_psutil_obj else 'unknown'}): {e}")

    # Phase 2: Wait for graceful exit & psutil.kill for those that didn't stop
    print_info("  Phase 2: Waiting for graceful exits & using psutil.kill() if needed...")
    still_running_procs = []
    # Use a delay before checking, as signals are asynchronous
    # Give GUI components extra time for graceful shutdown
    print_info("    Waiting 3 seconds for graceful shutdown (GUI components need extra time)...")
    time.sleep(3) # Give signals a moment to propagate, especially for GUI components

    for proc_psutil_obj in reversed(list(started_processes_psutil)): # Iterate a copy of what might be left
        try:
            if proc_psutil_obj.is_running():
                proc_name = "Unknown"; pid = proc_psutil_obj.pid
                try: proc_name = proc_psutil_obj.name()
                except: pass
                # Enhanced GUI detection for Phase 2
                display_name = getattr(proc_psutil_obj, '_testrade_display_name', proc_name)
                command_info = getattr(proc_psutil_obj, '_testrade_command', [])
                command_str = ' '.join(command_info).lower() if command_info else ''

                is_gui_backend = (
                    'gui_backend' in display_name.lower() or
                    'gui_backend' in command_str or
                    'uvicorn' in command_str
                )
                is_gui_frontend = (
                    'npm' in display_name.lower() or
                    'npm' in command_str or
                    'node' in proc_name.lower() or
                    'start' in command_str and 'gui' in command_str
                )
                wait_timeout = 10 if (is_gui_backend or is_gui_frontend) else 5

                try:
                    print_info(f"    Waiting for PID {pid} ({proc_name}) to exit (max {wait_timeout}s)...")
                    proc_psutil_obj.wait(timeout=wait_timeout)
                    print_info(f"      ✅ PID {pid} ({proc_name}) terminated gracefully.")
                    if proc_psutil_obj in started_processes_psutil: started_processes_psutil.remove(proc_psutil_obj)
                except psutil.TimeoutExpired:
                    print_warning(f"      [WARNING]  PID {pid} ({proc_name}) timed out after {wait_timeout}s. Attempting psutil.kill()...")
                    try:
                        print_info(f"        Attempting psutil.kill() for PID {pid} ({proc_name})...")
                        proc_psutil_obj.kill()
                        print_info(f"        ✅ Sent kill signal to PID {pid}. Waiting briefly...")
                        proc_psutil_obj.wait(timeout=3) # Wait for kill to take effect
                        if not proc_psutil_obj.is_running():
                             print_info(f"        ✅ PID {pid} confirmed killed.")
                             if proc_psutil_obj in started_processes_psutil: started_processes_psutil.remove(proc_psutil_obj)
                        else:
                             print_warning(f"        [ERROR] PID {pid} still running after psutil.kill().")
                             still_running_procs.append(proc_psutil_obj) # Add to list for taskkill
                    except Exception:
                         print_info(f"        ✅ PID {pid} gone before/during psutil.kill().")
                         if proc_psutil_obj in started_processes_psutil: started_processes_psutil.remove(proc_psutil_obj)
                    except Exception as e_kill:
                         print_warning(f"        [ERROR] Error during psutil.kill() for PID {pid}: {e_kill}")
                         still_running_procs.append(proc_psutil_obj) # Add to list for taskkill
            else: # Not running
                 if proc_psutil_obj in started_processes_psutil: started_processes_psutil.remove(proc_psutil_obj)

        except Exception:
            if proc_psutil_obj in started_processes_psutil: started_processes_psutil.remove(proc_psutil_obj)
        except Exception as e_wait:
            print_warning(f"    Error during wait/kill phase for PID (was {proc_psutil_obj.pid if proc_psutil_obj else 'unknown'}): {e_wait}")
            if proc_psutil_obj and proc_psutil_obj.is_running(): # Check if it's valid and running
                 still_running_procs.append(proc_psutil_obj)

    # Phase 3: Taskkill for remaining stubborn processes (Windows only)
    if os.name == 'nt' and still_running_procs: # Use the explicitly collected list
        print_info("  Phase 3: Using taskkill /F /T for stubborn processes (Windows)...")
        for proc_psutil_obj in still_running_procs:
            try:
                if proc_psutil_obj.is_running(): # Check again
                    pid = proc_psutil_obj.pid; proc_name = "Unknown"
                    try: proc_name = proc_psutil_obj.name()
                    except: pass

                    # Enhanced GUI detection for Phase 3
                    display_name = getattr(proc_psutil_obj, '_testrade_display_name', proc_name)
                    command_info = getattr(proc_psutil_obj, '_testrade_command', [])
                    command_str = ' '.join(command_info).lower() if command_info else ''

                    is_gui_backend = (
                        'gui_backend' in display_name.lower() or
                        'gui_backend' in command_str or
                        'uvicorn' in command_str
                    )
                    is_gui_frontend = (
                        'npm' in display_name.lower() or
                        'npm' in command_str or
                        'node' in proc_name.lower() or
                        'start' in command_str and 'gui' in command_str
                    )

                    if is_gui_backend:
                        print_warning(f"    [FORCE] FORCE-KILLING GUI Backend PID {pid} ({proc_name}) /F /T...")
                    elif is_gui_frontend:
                        print_warning(f"    [FORCE] FORCE-KILLING GUI Frontend PID {pid} ({proc_name}) /F /T...")
                    else:
                        print_warning(f"    [FORCE] FORCE-KILLING PID {pid} ({proc_name}) /F /T...")

                    tk_result = subprocess.run(
                        ["taskkill", "/PID", str(pid), "/F", "/T"],
                        capture_output=True, text=True, check=False, timeout=10
                    )
                    if tk_result.returncode == 0:
                        print_info(f"      ✅ taskkill /F /T succeeded for PID {pid}")
                    else:
                        print_warning(f"      [WARNING]  taskkill /F /T for PID {pid} returned code {tk_result.returncode}: {tk_result.stderr.strip()}")

                    # For GUI components, also try to kill any remaining node.exe processes
                    if is_gui_frontend:
                        print_info(f"      [CLEANUP] Additional cleanup: killing any remaining node.exe processes...")
                        subprocess.run(["taskkill", "/IM", "node.exe", "/F"],
                                     capture_output=True, check=False)

            except Exception:
                print_info(f"    ✅ PID (was {proc_psutil_obj.pid if proc_psutil_obj else 'unknown'}) gone before taskkill.")
            except Exception as e_tk:
                print_error(f"    [ERROR] Error during taskkill for PID (was {proc_psutil_obj.pid if proc_psutil_obj else 'unknown'}): {e_tk}")

    # Phase 4: Nuclear cleanup for any remaining GUI processes not in tracking
    print_info("  [CLEANUP] Phase 4: Nuclear cleanup for any remaining GUI processes...")
    try:
        # Find and kill any remaining npm processes
        npm_check = subprocess.run(["tasklist", "/FI", "IMAGENAME eq npm.exe"],
                                 capture_output=True, text=True, check=False)
        if "No tasks are running" not in npm_check.stdout:
            print_warning("  [FORCE] Found remaining npm processes - force killing...")
            subprocess.run(["taskkill", "/IM", "npm.exe", "/F", "/T"],
                         capture_output=True, check=False)

        # Find and kill any remaining node processes
        node_check = subprocess.run(["tasklist", "/FI", "IMAGENAME eq node.exe"],
                                  capture_output=True, text=True, check=False)
        if "No tasks are running" not in node_check.stdout:
            print_warning("  [FORCE] Found remaining node processes - force killing...")
            subprocess.run(["taskkill", "/IM", "node.exe", "/F", "/T"],
                         capture_output=True, check=False)

        # Find and kill any remaining Python processes that might be GUI backend
        python_check = subprocess.run(["tasklist", "/FI", "IMAGENAME eq python.exe"],
                                    capture_output=True, text=True, check=False)
        if "No tasks are running" not in python_check.stdout:
            # Try to identify GUI backend processes by port usage
            try:
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    if proc.info['name'] == 'python.exe':
                        cmdline = proc.info['cmdline'] or []
                        cmdline_str = ' '.join(cmdline).lower()
                        if 'gui_backend' in cmdline_str or 'uvicorn' in cmdline_str:
                            print_warning(f"  [FORCE] Found remaining GUI backend process PID {proc.info['pid']} - force killing...")
                            subprocess.run(["taskkill", "/F", "/PID", str(proc.info['pid'])],
                                         capture_output=True, check=False)
            except Exception as e:
                print_warning(f"  [WARNING]  Could not scan for remaining GUI backend processes: {e}")

    except Exception as e:
        print_warning(f"  [WARNING]  Error during nuclear cleanup: {e}")

    # Final verification - check for any remaining GUI processes
    print_info("  [CHECKING] Final verification: Checking for remaining GUI processes...")
    try:
        # Check for remaining Python processes (GUI backend)
        python_check = subprocess.run(["tasklist", "/FI", "IMAGENAME eq python.exe"],
                                    capture_output=True, text=True, check=False)
        python_clean = "No tasks are running" in python_check.stdout

        # Check for remaining Node.js processes (GUI frontend)
        node_check = subprocess.run(["tasklist", "/FI", "IMAGENAME eq node.exe"],
                                  capture_output=True, text=True, check=False)
        node_clean = "No tasks are running" in node_check.stdout

        # Check for remaining npm processes
        npm_check = subprocess.run(["tasklist", "/FI", "IMAGENAME eq npm.exe"],
                                 capture_output=True, text=True, check=False)
        npm_clean = "No tasks are running" in npm_check.stdout

        if python_clean and node_clean and npm_clean:
            print_info("  ✅ All GUI processes successfully terminated!")
        else:
            if not python_clean:
                print_warning("  [WARNING]  Some Python processes still running (may include GUI backend)")
            if not node_clean:
                print_warning("  [WARNING]  Some Node.js processes still running (may include GUI frontend)")
            if not npm_clean:
                print_warning("  [WARNING]  Some npm processes still running")

    except Exception as e:
        print_warning(f"  [WARNING]  Could not verify final cleanup status: {e}")

    # Final clear, though it should be mostly empty if removals worked
    started_processes_psutil.clear()
    started_processes.clear()  # Also clear the old list
    print_info("✅ Enhanced process cleanup finished.")

    if signum is not None:
        print_info(f"  Exiting script now due to signal {signum}.")
        sys.exit(0)


def main():
    global global_show_consoles # Make it accessible

    parser = argparse.ArgumentParser(description="TESTRADE TANK MODE - Main Orchestrator")
    parser.add_argument(
        "--show-consoles",
        action="store_true", # Means if flag is present, set to True
        help="Show individual console windows for child processes (Windows only for new windows, otherwise inherits)."
    )
    args = parser.parse_args()
    global_show_consoles = args.show_consoles

    print("TESTRADE TANK MODE Startup Script")
    print("=" * 40)

    # Determine Project Root for TESTRADE Core CWD
    # Since start_testrade.py is located inside the testrade_project_root directory
    PROJECT_ROOT_FOR_TESTRADE = os.path.dirname(os.path.abspath(__file__))
    print_info(f"Determined TESTRADE Project Root for Core CWD: {PROJECT_ROOT_FOR_TESTRADE}")

    if global_show_consoles:
        print_info("[CONSOLE MODE] Show Consoles mode ENABLED. Child processes will open in new windows or inherit console.")
    else:
        print_info("[LOG MODE] Log mode ENABLED. Child processes will output to log files.")

    # CRITICAL: Set up signal handlers EARLY in the main execution path
    signal.signal(signal.SIGINT, cleanup_all_started_processes)  # Ctrl+C
    signal.signal(signal.SIGTERM, cleanup_all_started_processes) # Termination request
    if os.name == 'nt': # Windows-specific
        try:
            signal.signal(signal.SIGBREAK, cleanup_all_started_processes) # Ctrl+Break
        except AttributeError:
            print_warning("SIGBREAK not available on this Python version/OS variant.")

    ensure_dir_exists(LOG_DIR)
    ensure_dir_exists(DATA_DIR) # For babysitter disk buffer, etc.
    ensure_dir_exists(os.path.join(DATA_DIR, "babysitter_disk_buffer"))


    global app_config
    app_config = load_app_config()
    
    # Use Redis host/port from loaded config if available, otherwise use script global
    current_redis_host = app_config.get("BABYSITTER_REDIS_HOST", REDIS_HOST)
    current_redis_port = int(app_config.get("BABYSITTER_REDIS_PORT", REDIS_PORT))


    print_step(0, "Prerequisites Check: Redis")

    # Step 0.1: Check if Redis process is running within WSL (diagnostic)
    # This step is optional but provides more insight.
    if not is_redis_running_in_wsl():
        print_warning(f"Redis process does not appear to be running inside WSL distro '{WSL_DISTRO_NAME}'.")
        print_warning("  This might be okay if it's running but not detected by pgrep, or if WSL is still starting.")
        print_warning("  The next step (network connection check) is more critical.")
    else:
        print_info("Redis process check in WSL passed.")

    # Step 0.2: Critical Check - Network connectivity to Redis
    print_info(f"Verifying network connection to Redis at {current_redis_host}:{current_redis_port}...")
    redis_accessible = False
    for i in range(3):  # Try a few times in case WSL/Redis is still initializing
        if check_redis_connection(current_redis_host, current_redis_port):
            redis_accessible = True
            break
        if i < 2:  # Don't print wait message on the last attempt
            print_info(f"  Redis not yet accessible, retrying in 5 seconds (attempt {i+1}/3)...")
            time.sleep(5)

    if not redis_accessible:
        print_error(f"Redis at {current_redis_host}:{current_redis_port} is NOT accessible from this script.")
        print_error("  Please ensure Redis is running in WSL, configured to listen on an IP accessible from Windows (e.g., bind 0.0.0.0 in redis.conf),")
        print_error(f"  and that WSL networking/firewall allows connections to {current_redis_host} on port {current_redis_port}.")
        # Optional: You could still include a more detailed WSL process check here if the network fails
        if not is_redis_running_in_wsl():
            print_info(f"  Further check: Redis process also not found running within WSL distro '{WSL_DISTRO_NAME}'.")
        sys.exit(1)  # Exit if Redis is not accessible.
    else:
        print_success(f"Redis at {current_redis_host}:{current_redis_port} is accessible.")

    # --- Step 1: Start Babysitter Service ---
    print_step(1, "Start Babysitter Service")
    # Use direct script only - simpler and more reliable
    babysitter_proc = None
    if os.path.exists(BABYSITTER_SCRIPT):
        env = {
            "BABYSITTER_REDIS_HOST": current_redis_host,
            "BABYSITTER_REDIS_PORT": str(current_redis_port),
            "BABYSITTER_REDIS_DB": app_config.get("BABYSITTER_REDIS_DB", "0"),
            "BABYSITTER_DISK_BUFFER_PATH": app_config.get("BABYSITTER_DISK_BUFFER_PATH", os.path.join(DATA_DIR, "babysitter_disk_buffer"))
        }
        babysitter_proc = start_process(
            command=[BABYSITTER_SCRIPT],
            log_filename="babysitter_service.log",
            env_vars=env,
            expected_keywords=["babysitter_service.py"],
            show_console=global_show_consoles
        )
    else:
        print_error(f"Babysitter script {BABYSITTER_SCRIPT} not found. Cannot start Babysitter.")

    if not babysitter_proc: print_error("Babysitter service failed to start.")
    verify_component(
        "Babysitter",
        babysitter_proc,
        expected_port=app_config.get('_babysitter_port'),
        listen_host='0.0.0.0', # Babysitter typically listens on all interfaces for IPC
        redis_health_stream=app_config.get("redis_stream_babysitter_health")
    )

    # Add a delay to ensure Babysitter is fully initialized before starting Core
    print_info("Waiting 3 seconds for Babysitter to fully initialize...")
    time.sleep(3)

    # --- Step 2: Start TESTRADE Core ---
    print_step(2, "Start TESTRADE Core")

    # Verify the core script path based on the new CWD
    # This check assumes CORE_SCRIPT is a filename like "run_headless_core.py"
    # and it should exist directly inside PROJECT_ROOT_FOR_TESTRADE.
    if not os.path.exists(os.path.join(PROJECT_ROOT_FOR_TESTRADE, CORE_SCRIPT)):
        print_error(f"Core script '{CORE_SCRIPT}' not found in project root '{PROJECT_ROOT_FOR_TESTRADE}'.")

    core_proc = start_process(
        command=[CORE_SCRIPT],  # The command itself is just the script name, Popen will find it in the new cwd
        log_filename="core.log",
        cwd=PROJECT_ROOT_FOR_TESTRADE,  # <--- ADD THIS LINE: Set working directory to project root
        expected_keywords=["run_headless_core.py"], # or more specific if core script name changes
        show_console=global_show_consoles
    )
    if not core_proc: print_error("TESTRADE Core failed to start.")
    verify_component(
        "Core",
        core_proc,
        expected_port=app_config.get('_core_port'), # Core ZMQ pull socket
        listen_host='0.0.0.0',
        redis_health_stream=app_config.get("redis_stream_core_health")
    )

    # --- Step 3: Start GUI Backend ---
    print_step(3, "Start GUI Backend")
    if not os.path.exists(GUI_BACKEND_SCRIPT):
        print_error(f"GUI Backend script {GUI_BACKEND_SCRIPT} not found.")

    gui_backend_proc = start_process(
        command=[GUI_BACKEND_SCRIPT], # Run from project root
        log_filename="gui_backend.log",
        cwd=PROJECT_ROOT_FOR_TESTRADE, # Run from project root, not gui directory
        expected_keywords=["run_gui_backend.py"],
        show_console=global_show_consoles
    )
    if not gui_backend_proc: print_error("GUI Backend failed to start.")
    verify_component(
        "GUI Backend",
        gui_backend_proc,
        expected_port=app_config.get('_gui_backend_port'), # Typically 8001
        listen_host='127.0.0.1' # GUI Backend often listens on localhost by default
    )
    
    # --- Step 4: GUI Frontend (served by GUI Backend) ---
    print_step(4, "GUI Frontend (integrated with Backend)")
    print_info("GUI Frontend is now served directly by the GUI Backend at http://localhost:8001/gui")
    print_info("No separate frontend process needed - the backend serves portrait_trading_gui.html")
    
    # Check if GUI Backend is serving the frontend
    gui_backend_port = app_config.get('_gui_backend_port', 8001)
    gui_frontend_proc = gui_backend_proc  # Frontend is part of backend now
    
    if gui_backend_proc and gui_backend_proc.is_running():
        # --- Auto-open GUI in browser after successful verification ---
        print_info(f"  Waiting for GUI Backend to be ready...")
        time.sleep(2)  # Give backend a moment to fully start
        
        # Construct the URL for the TESTRADE GUI
        gui_url = f"http://localhost:{gui_backend_port}/gui"
        
        print_info(f"Attempting to open GUI in browser: {gui_url}")
        try:
            webbrowser.open_new_tab(gui_url)
            print_success(f"  GUI URL should be opening in your default web browser.")
            print_success(f"  TESTRADE GUI: {gui_url}")
        except Exception as e_wb:
            print_warning(f"  Could not automatically open web browser: {e_wb}")
            print_warning(f"  Please manually open: {gui_url}")
    else:
        print_warning("GUI Backend did not start successfully. Frontend unavailable.")


    print("\n" + "=" * 50)

    # Detailed component status summary
    running_components_details = []
    if babysitter_proc and babysitter_proc.is_running():
        running_components_details.append("  [OK] Babysitter Service")
    elif babysitter_proc:
        running_components_details.append("  [WARN] Babysitter Service (Started but may have exited)")
    else:
        running_components_details.append("  [FAIL] Babysitter Service (Failed to start)")

    if core_proc and core_proc.is_running():
        running_components_details.append("  [OK] TESTRADE Core")
    elif core_proc:
        running_components_details.append("  [WARN] TESTRADE Core (Started but may have exited)")
    else:
        running_components_details.append("  [FAIL] TESTRADE Core (Failed to start)")

    if gui_backend_proc and gui_backend_proc.is_running():
        backend_port = app_config.get('_gui_backend_port', 8001)
        running_components_details.append(f"  ✅ GUI Backend (API on port {backend_port})")
        running_components_details.append(f"     🔧 API Access: http://localhost:{backend_port}")
    elif gui_backend_proc:
        running_components_details.append("  [WARNING] GUI Backend (Started but may have exited)")
    else:
        running_components_details.append("  [ERROR] GUI Backend (Failed to start)")

    frontend_port_for_url = app_config.get('_gui_frontend_port', 3000)
    gui_url_to_display = f"http://localhost:{frontend_port_for_url}/portrait_trading_gui.html"

    if gui_frontend_proc and gui_frontend_proc.is_running():
        running_components_details.append(f"  ✅ GUI Frontend (Serving on port {frontend_port_for_url})")
        running_components_details.append(f"     🌐 TESTRADE GUI: {gui_url_to_display}")
    elif gui_frontend_proc:
        running_components_details.append(f"  [WARNING] GUI Frontend (Attempted, check status. Expected URL: {gui_url_to_display})")
    else:
        running_components_details.append(f"  [INFO] GUI Frontend (Not started or attempt failed. Expected URL: {gui_url_to_display})")

    # Count successful components (both ✅ and [OK] status)
    successful_components = sum(1 for detail in running_components_details if ("✅" in detail or "[OK]" in detail))

    if successful_components >= 3:  # At least 3 core components running
        print("🎉 TESTRADE TANK MODE Startup Sequence Completed Successfully! 🎉")
    elif successful_components >= 1:
        print("[WARNING] TESTRADE TANK MODE Startup Sequence Partially Completed")
    else:
        print("[ERROR] TESTRADE TANK MODE Startup Sequence Failed")

    print(f"[STATS] Component Status ({successful_components}/3 components running):")
    for detail in running_components_details:
        print(detail)

    print()
    if not global_show_consoles:
        print("[LOG] Monitor log files in the 'logs' directory for detailed component output.")
    else:
        print("[VIEW] Check individual console windows for detailed component output.")

    print("[TIP] Press Ctrl+C in this window to gracefully shutdown all components.")
    print("=" * 50)
    
    # Keep the script running to manage processes, or exit if they are fully detached
    # For now, we'll wait indefinitely, allowing Ctrl+C to trigger cleanup.
    try:
        while True:
            time.sleep(60) # Keep main thread alive
            # Could add periodic health checks here if desired
            any_critical_process_died = False
            for i, proc_obj in enumerate(list(started_processes_psutil)): # Iterate a copy
                if not proc_obj.is_running():
                    try:
                        proc_name = proc_obj.name()
                    except psutil.NoSuchProcess:
                        proc_name = f"Process previously PID {proc_obj.pid}"
                    print_warning(f"   Monitored process {proc_name} (PID {proc_obj.pid}) is no longer running!")
                    started_processes_psutil.remove(proc_obj)  # Remove from tracking
                    if proc_obj in started_processes:
                        started_processes.remove(proc_obj)  # Also remove from old list
                    any_critical_process_died = True # Decide if this component's death is critical

            if any_critical_process_died and not started_processes_psutil : # If all tracked processes died
                print_error("  All monitored TESTRADE processes have stopped. Initiating shutdown.")
                break # Exit the while loop, finally block will run cleanup

            if not any(p.is_running() for p in started_processes_psutil) and len(started_processes_psutil) > 0:
                 #This condition is likely met if the above is true too
                 print_error("  No tracked processes are running. Initiating shutdown.")
                 break

    except KeyboardInterrupt:
        # This block will be executed if Ctrl+C is pressed AND the signal handler
        # for SIGINT either wasn't set, or it ran and didn't exit the script.
        # Ideally, the signal handler itself calls sys.exit().
        print_info("KeyboardInterrupt caught in main loop. Cleanup should have been triggered by SIGINT handler.")
        cleanup_all_started_processes()  # Call without signum so it won't sys.exit itself
    except Exception as e:
        print_error(f"  Unexpected error in main monitoring loop: {e}")
        cleanup_all_started_processes()  # Ensure cleanup on unexpected errors
    finally:
        # This finally block ensures cleanup is ATTEMPTED if the loop exits for any reason
        # (signal, all processes died, unexpected exception).
        # If the signal handler already ran and exited, this might not run, which is fine.
        # If the loop broke for other reasons, this is the last chance for cleanup.
        print_info("Main loop ended or error occurred. Ensuring final cleanup...")
        cleanup_all_started_processes() # Call it without signum, so it won't sys.exit itself
        print_info("Exiting TESTRADE Python orchestrator script.")


if __name__ == "__main__":
    # Basic check for psutil
    try:
        import psutil
    except ImportError:
        print("Error: psutil library is not installed. Please install it: pip install psutil")
        sys.exit(1)
    # Basic check for redis python client
    try:
        import redis
    except ImportError:
        print("Warning: Python 'redis' library not installed (pip install redis). Some checks may be limited.")
        # Not exiting, as redis-cli fallback is attempted for basic ping

    if not os.path.exists(CONFIG_FILE_PATH):
        print(f"Error: Main configuration file '{CONFIG_FILE_PATH}' not found.")
        print("Please ensure the configuration file exists or update CONFIG_FILE_PATH in the script.")
        sys.exit(1)

    main()