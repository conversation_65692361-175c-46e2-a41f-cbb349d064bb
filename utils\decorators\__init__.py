"""
Decorators Package

This package provides decorators used throughout the application.
It includes both real and dummy implementations of decorators.

The real implementations are used when decorators are enabled, while the dummy
implementations are used when decorators are disabled to minimize overhead.

Usage:
    # Import the interface module to get the appropriate implementation
    from utils.decorators.interface import log_function_call, set_module_logging
    
    # Or import the package and use the interface module
    from utils import decorators
    @decorators.log_function_call('module_key')
    def my_function():
        pass
"""

# Import the interface module to make it available at the package level
from . import interface

# Re-export the interface functions for convenience
from .interface import (
    log_function_call,
    set_module_logging
)

# Log that the decorators package has been initialized
import logging
logger = logging.getLogger(__name__)
logger.info("decorators package initialized")
