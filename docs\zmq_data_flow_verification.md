# ZMQ Multi-Socket Data Flow Verification Plan

## 🎯 Critical Issues Found

### 1. Missing Stream Configurations in control.json
- `redis_stream_position_updates` - Used by Posi<PERSON><PERSON>anager (HIGH PRIORITY)
- `REDIS_STREAM_CORE_HEALTH` - Used by health publishing  
- `REDIS_STREAM_RESPONSES_TO_GUI` - Used by command responses

### 2. Stream Name Casing Inconsistencies
- Application<PERSON><PERSON> uses UPPERCASE config keys
- control.j<PERSON> uses lowercase keys
- Need to verify getattr() fallbacks work correctly

## 📊 Data Flow Verification Matrix

| Data Type | ApplicationCore Send | Socket Type | Target Stream | GUI Backend Consumer | Status |
|-----------|---------------------|-------------|---------------|---------------------|---------|
| **OCR Data** | ✅ `send_data()` | `bulk` (auto) | `testrade:raw-ocr-events` | ✅ `raw_ocr` | ✅ MATCH |
| **Position Updates** | ✅ `send_data(high_priority=True)` | `trading` (auto) | `testrade:position-updates` | ❌ MISSING | 🚨 BROKEN |
| **Order Fills** | ✅ `send_data()` | `trading` (auto) | `testrade:order-fills` | ❌ MISSING | 🚨 BROKEN |
| **Health Data** | ✅ `send_data()` | `system` (auto) | `testrade:health:core` | ✅ `core_health` | ✅ MATCH |
| **Command Responses** | ✅ `send_data()` | `system` (auto) | `testrade:responses:to_gui` | ✅ `phase2_responses` | ✅ MATCH |
| **GUI Commands** | ✅ `send_data()` | `system` (auto) | `testrade:commands:from_gui` | N/A (Core consumes) | ✅ MATCH |

## 🔍 Detailed Send_Data Analysis

### ApplicationCore Send Patterns:

#### 1. OCR Data (BULK Socket)
```python
# File: core/application_core.py:394
target_stream = getattr(self.config, 'redis_stream_raw_ocr', 'testrade:raw-ocr-events')
success = self.babysitter_ipc_client.send_data(target_stream, redis_message_json_str)
# Socket Type: 'bulk' (auto-determined by stream name 'ocr')
```

#### 2. Position Updates (TRADING Socket - HIGH PRIORITY)
```python
# File: modules/trade_management/position_manager.py:284
target_stream = getattr(self.app_core_ref.config, 'redis_stream_position_updates', 'testrade:position-updates')
success = self.app_core_ref.babysitter_ipc_client.send_data(target_stream, redis_message_json_str, high_priority=True)
# Socket Type: 'trading' (high_priority=True forces trading socket)
```

#### 3. Order Fills (TRADING Socket)
```python
# File: modules/order_management/order_repository.py:4028
target_stream = getattr(self._app_core.config, 'redis_stream_order_fills', 'testrade:order-fills')
success = self._app_core.babysitter_ipc_client.send_data(target_stream, redis_message_json_str)
# Socket Type: 'trading' (auto-determined by stream name 'fill')
```

#### 4. Health Data (SYSTEM Socket)
```python
# File: core/application_core.py:427
target_stream = getattr(self.config, 'REDIS_STREAM_CORE_HEALTH', 'testrade:health:core')
success = self.babysitter_ipc_client.send_data(target_stream, redis_message_json_str)
# Socket Type: 'system' (default for health streams)
```

#### 5. Command Responses (SYSTEM Socket)
```python
# File: core/application_core.py:1437
target_stream = getattr(self.config, 'REDIS_STREAM_RESPONSES_TO_GUI', 'testrade:responses:to_gui')
success = self.babysitter_ipc_client.send_data(target_stream, redis_message_json_str)
# Socket Type: 'system' (default for command/response streams)
```

## 🎯 Socket Type Routing Logic

### Automatic Socket Determination (ipc_publishers.py:172-182)
```python
def _determine_socket_type(self, target_redis_stream, explicit_socket_type, high_priority):
    # Priority 1: Explicit socket type
    if explicit_socket_type: return explicit_socket_type
    
    # Priority 2: High priority flag → 'trading'
    if high_priority: return 'trading'
    
    # Priority 3: Stream name analysis
    stream_lower = target_redis_stream.lower()
    if 'ocr' or 'image' in stream_lower: return 'bulk'
    if 'position' or 'trade-' or 'fill' in stream_lower: return 'trading'
    else: return 'system'  # Default
```

## 🚨 Critical Fixes Needed

### 1. Add Missing Stream Configurations to control.json
```json
{
  "redis_stream_position_updates": "testrade:position-updates",
  "REDIS_STREAM_CORE_HEALTH": "testrade:health:core", 
  "REDIS_STREAM_RESPONSES_TO_GUI": "testrade:responses:to_gui"
}
```

### 2. Add Missing GUI Backend Consumers
- Position updates consumer for `testrade:position-updates`
- Order fills consumer for `testrade:order-fills`

### 3. Verify Stream Name Consistency
- Ensure getattr() fallbacks match actual stream names
- Test both uppercase and lowercase config key access

## 🧪 End-to-End Test Plan

### Phase 1: Socket Configuration Verification
1. ✅ Verify port alignment (5555/5556/5557 → 5558/5559)
2. ✅ Verify ZMQ socket creation and binding
3. ✅ Verify socket type routing logic

### Phase 2: Stream Name Verification  
1. 🚨 Fix missing stream configurations in control.json
2. 🚨 Add missing GUI backend consumers
3. ✅ Test getattr() fallback behavior

### Phase 3: Data Flow Testing
1. **OCR Data**: ApplicationCore → Bulk Socket → Babysitter → Redis → GUI
2. **Position Updates**: PositionManager → Trading Socket → Babysitter → Redis → GUI  
3. **Order Fills**: OrderRepository → Trading Socket → Babysitter → Redis → GUI
4. **Health Data**: ApplicationCore → System Socket → Babysitter → Redis → GUI
5. **Commands**: GUI → System Socket → Babysitter → Redis → ApplicationCore

### Phase 4: Performance Verification
1. Verify trading socket gets priority processing
2. Verify bulk socket doesn't block trading data
3. Verify emergency buffer behavior under load
