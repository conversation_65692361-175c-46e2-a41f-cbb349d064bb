#!/usr/bin/env python3
"""
UUID Generator Monitoring Script

This script monitors the health and performance of the enterprise UUID generator
and can be used for troubleshooting and alerting.
"""

import sys
import time
import json
import argparse
from datetime import datetime
from typing import Dict, Any

# Add parent directory to path
sys.path.insert(0, '..')

from utils.thread_safe_uuid import (
    get_uuid_generator_stats,
    get_uuid_generator_health,
    get_thread_safe_uuid
)

class UUIDMonitor:
    """Monitor for UUID generator health and performance."""
    
    def __init__(self, alert_thresholds: Dict[str, float]):
        self.alert_thresholds = alert_thresholds
        self.alerts_triggered = []
    
    def check_health(self) -> Dict[str, Any]:
        """Check UUID generator health and return status."""
        health = get_uuid_generator_health()
        stats = get_uuid_generator_stats()
        
        # Combine health and stats
        status = {
            'timestamp': datetime.now().isoformat(),
            'health': health,
            'stats': stats,
            'alerts': []
        }
        
        # Check against thresholds
        if stats.get('error_rate', 0) > self.alert_thresholds.get('error_rate', 0.01):
            status['alerts'].append({
                'level': 'ERROR',
                'message': f"Error rate {stats['error_rate']:.2%} exceeds threshold {self.alert_thresholds['error_rate']:.2%}"
            })
        
        if stats.get('fallback_count', 0) > self.alert_thresholds.get('fallback_count', 100):
            status['alerts'].append({
                'level': 'WARNING',
                'message': f"Fallback count {stats['fallback_count']} exceeds threshold {self.alert_thresholds['fallback_count']}"
            })
        
        if stats.get('timeout_count', 0) > self.alert_thresholds.get('timeout_count', 10):
            status['alerts'].append({
                'level': 'WARNING',
                'message': f"Timeout count {stats['timeout_count']} exceeds threshold {self.alert_thresholds['timeout_count']}"
            })
        
        # Check queue health
        queue_size = stats.get('queue_size', 0)
        max_queue_size = stats.get('max_queue_size', 1)
        queue_utilization = queue_size / max_queue_size
        
        if queue_utilization < 0.1:
            status['alerts'].append({
                'level': 'WARNING',
                'message': f"Queue utilization low: {queue_utilization:.1%}"
            })
        
        # Check health status
        if health.get('status') == 'CRITICAL':
            status['alerts'].append({
                'level': 'CRITICAL',
                'message': "UUID generator in CRITICAL state"
            })
        elif health.get('status') == 'DEGRADED':
            status['alerts'].append({
                'level': 'WARNING',
                'message': "UUID generator in DEGRADED state"
            })
        
        return status
    
    def run_performance_test(self, iterations: int = 1000) -> Dict[str, Any]:
        """Run a performance test of UUID generation."""
        print(f"Running performance test with {iterations} iterations...")
        
        start_time = time.time()
        uuids = []
        
        for i in range(iterations):
            uuid_str = get_thread_safe_uuid()
            uuids.append(uuid_str)
            
            if i % 100 == 0:
                print(f"  Generated {i}/{iterations} UUIDs...")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Verify uniqueness
        unique_count = len(set(uuids))
        
        return {
            'iterations': iterations,
            'duration_seconds': duration,
            'uuids_per_second': iterations / duration,
            'avg_time_per_uuid_ms': (duration / iterations) * 1000,
            'unique_count': unique_count,
            'duplicate_count': iterations - unique_count,
            'success': unique_count == iterations
        }

def main():
    parser = argparse.ArgumentParser(description='Monitor UUID Generator')
    parser.add_argument('--mode', choices=['health', 'monitor', 'test'], 
                       default='health', help='Operation mode')
    parser.add_argument('--interval', type=int, default=5,
                       help='Monitoring interval in seconds')
    parser.add_argument('--iterations', type=int, default=1000,
                       help='Number of iterations for performance test')
    parser.add_argument('--json', action='store_true',
                       help='Output in JSON format')
    
    args = parser.parse_args()
    
    # Default alert thresholds
    thresholds = {
        'error_rate': 0.01,      # 1%
        'fallback_count': 100,
        'timeout_count': 10
    }
    
    monitor = UUIDMonitor(thresholds)
    
    if args.mode == 'health':
        # One-time health check
        status = monitor.check_health()
        
        if args.json:
            print(json.dumps(status, indent=2))
        else:
            print(f"\nUUID Generator Health Check - {status['timestamp']}")
            print("=" * 60)
            print(f"Health Status: {status['health']['status']}")
            print(f"Healthy: {status['health'].get('healthy', 'Unknown')}")
            
            print("\nStatistics:")
            for key, value in status['stats'].items():
                if key not in ['health_status', 'last_error_time']:
                    print(f"  {key}: {value}")
            
            if status['alerts']:
                print("\nALERTS:")
                for alert in status['alerts']:
                    print(f"  [{alert['level']}] {alert['message']}")
            else:
                print("\nNo alerts")
    
    elif args.mode == 'monitor':
        # Continuous monitoring
        print(f"Starting continuous monitoring (interval: {args.interval}s)")
        print("Press Ctrl+C to stop\n")
        
        try:
            while True:
                status = monitor.check_health()
                
                if args.json:
                    print(json.dumps(status))
                else:
                    # Compact output for continuous monitoring
                    stats = status['stats']
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] "
                          f"Status: {status['health']['status']} | "
                          f"Queue: {stats.get('queue_size', 0)}/{stats.get('max_queue_size', 0)} | "
                          f"Requests: {stats.get('requested_count', 0)} | "
                          f"Errors: {stats.get('error_count', 0)} | "
                          f"Alerts: {len(status['alerts'])}")
                    
                    # Print alerts if any
                    for alert in status['alerts']:
                        print(f"    ALERT: [{alert['level']}] {alert['message']}")
                
                time.sleep(args.interval)
                
        except KeyboardInterrupt:
            print("\nMonitoring stopped")
    
    elif args.mode == 'test':
        # Performance test
        results = monitor.run_performance_test(args.iterations)
        
        if args.json:
            print(json.dumps(results, indent=2))
        else:
            print(f"\nUUID Generator Performance Test Results")
            print("=" * 60)
            print(f"Iterations: {results['iterations']}")
            print(f"Duration: {results['duration_seconds']:.3f} seconds")
            print(f"UUIDs per second: {results['uuids_per_second']:.0f}")
            print(f"Avg time per UUID: {results['avg_time_per_uuid_ms']:.3f} ms")
            print(f"Unique UUIDs: {results['unique_count']}")
            print(f"Duplicates: {results['duplicate_count']}")
            print(f"Test Result: {'PASSED' if results['success'] else 'FAILED'}")

if __name__ == '__main__':
    main()