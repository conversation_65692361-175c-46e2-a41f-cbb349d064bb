#!/usr/bin/env python3
"""
Redis Memory & Stream Monitoring Script

Monitors Redis memory usage and stream lengths to verify MAXLEN trimming is working.
Run this while TESTRADE is processing data to ensure memory stays stable.
"""

import redis
import time
import json
from datetime import datetime
from typing import Dict, List, Any

# Redis connection settings
REDIS_HOST = "**************"
REDIS_PORT = 6379
REDIS_DB = 0

# Key streams to monitor
STREAMS_TO_MONITOR = [
    "testrade:raw-ocr-events",
    "testrade:cleaned-ocr-snapshots", 
    "testrade:image-grabs",
    "testrade:commands:from_gui",
    "testrade:responses:to_gui",
    "testrade:health:core",
    "testrade:health:babysitter",
    "testrade:market-data:active-trades",
    "testrade:market-data:active-quotes"
]

def connect_redis():
    """Connect to Redis server."""
    try:
        client = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB, decode_responses=True)
        client.ping()
        print(f"✅ Connected to Redis at {REDIS_HOST}:{REDIS_PORT}")
        return client
    except Exception as e:
        print(f"❌ Failed to connect to Redis: {e}")
        return None

def get_memory_info(redis_client):
    """Get Redis memory information."""
    try:
        info = redis_client.info('memory')
        return {
            'used_memory_human': info.get('used_memory_human', 'N/A'),
            'used_memory_peak_human': info.get('used_memory_peak_human', 'N/A'),
            'used_memory_rss_human': info.get('used_memory_rss_human', 'N/A'),
            'used_memory': info.get('used_memory', 0),
            'used_memory_peak': info.get('used_memory_peak', 0)
        }
    except Exception as e:
        print(f"❌ Error getting memory info: {e}")
        return {}

def get_stream_lengths(redis_client, streams):
    """Get lengths of specified streams."""
    stream_info = {}
    for stream in streams:
        try:
            length = redis_client.xlen(stream)
            stream_info[stream] = length
        except redis.exceptions.ResponseError:
            # Stream doesn't exist
            stream_info[stream] = 0
        except Exception as e:
            print(f"❌ Error getting length for {stream}: {e}")
            stream_info[stream] = "ERROR"
    return stream_info

def check_consumer_groups(redis_client, streams):
    """Check consumer group status with PEL monitoring."""
    consumer_info = {}
    total_pending_all_streams = 0

    for stream in streams:
        try:
            groups = redis_client.xinfo_groups(stream)
            consumer_info[stream] = []
            for group in groups:
                group_name = group['name']
                pending = group['pending']
                total_pending_all_streams += pending
                consumers = redis_client.xinfo_consumers(stream, group_name)

                # Get PEL details for high pending counts
                pel_warning = ""
                if pending > 50:
                    try:
                        pel_details = redis_client.xpending_range(stream, group_name, "-", "+", count=10)
                        if pel_details:
                            oldest_idle = max(msg[2] for msg in pel_details) / 1000  # Convert to seconds
                            pel_warning = f" (oldest: {oldest_idle:.1f}s)"
                    except:
                        pel_warning = " (PEL check failed)"

                consumer_info[stream].append({
                    'group': group_name,
                    'pending': pending,
                    'consumers': len(consumers),
                    'pel_warning': pel_warning,
                    'consumer_details': [{'name': c['name'], 'pending': c['pending']} for c in consumers]
                })
        except redis.exceptions.ResponseError:
            # Stream doesn't exist or no groups
            consumer_info[stream] = []
        except Exception as e:
            print(f"❌ Error getting consumer info for {stream}: {e}")
            consumer_info[stream] = "ERROR"

    consumer_info['_total_pending'] = total_pending_all_streams
    return consumer_info

def monitor_redis(interval_seconds=10, max_iterations=None):
    """Main monitoring loop."""
    redis_client = connect_redis()
    if not redis_client:
        return
    
    print(f"\n🔍 Starting Redis monitoring (interval: {interval_seconds}s)")
    print("=" * 80)
    
    iteration = 0
    try:
        while True:
            if max_iterations and iteration >= max_iterations:
                break
                
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n📊 Redis Status at {timestamp}")
            print("-" * 50)
            
            # Memory information
            memory_info = get_memory_info(redis_client)
            if memory_info:
                print(f"💾 Memory Used: {memory_info['used_memory_human']}")
                print(f"💾 Memory Peak: {memory_info['used_memory_peak_human']}")
                print(f"💾 Memory RSS: {memory_info['used_memory_rss_human']}")
            
            # Stream lengths
            print(f"\n📈 Stream Lengths (should be ≤ 1000):")
            stream_lengths = get_stream_lengths(redis_client, STREAMS_TO_MONITOR)
            for stream, length in stream_lengths.items():
                status = "✅" if isinstance(length, int) and length <= 1000 else "⚠️"
                print(f"  {status} {stream}: {length}")
            
            # Consumer group status with PEL monitoring
            print(f"\n👥 Consumer Groups & PEL Status:")
            consumer_info = check_consumer_groups(redis_client, STREAMS_TO_MONITOR)
            total_pending = consumer_info.get('_total_pending', 0)

            if total_pending > 0:
                pel_status = "✅" if total_pending < 100 else "🚨" if total_pending > 500 else "⚠️"
                print(f"  {pel_status} TOTAL PENDING (all streams): {total_pending}")

            for stream, groups in consumer_info.items():
                if stream.startswith('_'):  # Skip metadata
                    continue
                if groups and groups != "ERROR":
                    for group_info in groups:
                        pending = group_info['pending']
                        pel_warning = group_info.get('pel_warning', '')
                        if pending > 0:
                            status = "✅" if pending < 10 else "🚨" if pending > 100 else "⚠️"
                            print(f"  {status} {stream} -> {group_info['group']}: {pending} pending{pel_warning}")
                            for consumer in group_info['consumer_details']:
                                if consumer['pending'] > 0:
                                    print(f"    📋 {consumer['name']}: {consumer['pending']} pending")
            
            iteration += 1
            time.sleep(interval_seconds)
            
    except KeyboardInterrupt:
        print(f"\n🛑 Monitoring stopped by user")
    except Exception as e:
        print(f"\n❌ Monitoring error: {e}")
    finally:
        redis_client.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Monitor Redis memory and stream lengths")
    parser.add_argument("--interval", "-i", type=int, default=10, help="Monitoring interval in seconds")
    parser.add_argument("--max-iterations", "-n", type=int, help="Maximum number of iterations")
    parser.add_argument("--once", action="store_true", help="Run once and exit")
    
    args = parser.parse_args()
    
    if args.once:
        monitor_redis(interval_seconds=0, max_iterations=1)
    else:
        monitor_redis(interval_seconds=args.interval, max_iterations=args.max_iterations)
