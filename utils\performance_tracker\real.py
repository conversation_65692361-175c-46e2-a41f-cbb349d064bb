"""
Real Performance Tracker Implementation

This module provides the real implementation of performance tracking utilities.
It is used when performance tracking is enabled.

Performance tracking is DISABLED by default to minimize overhead during normal operation.
To enable it, call `enable_performance_tracking(True)` before using any tracking functions.

Example:
    ```python
    from utils.performance_tracker.real import enable_performance_tracking, create_timestamp_dict, add_timestamp

    # Enable performance tracking
    enable_performance_tracking(True)

    # Now tracking will work
    timestamps = create_timestamp_dict()
    add_timestamp(timestamps, 'operation_start')
    # ... perform operation ...
    add_timestamp(timestamps, 'operation_end')
    ```
"""

# Global PerformanceBenchmarker instance for integration with ApplicationCore
from typing import Optional
_global_benchmarker_instance: Optional['PerformanceBenchmarker'] = None

def initialize_global_benchmarker(instance: 'PerformanceBenchmarker'):
    """Initialize the global PerformanceBenchmarker instance."""
    global _global_benchmarker_instance
    _global_benchmarker_instance = instance

def get_global_benchmarker() -> Optional['PerformanceBenchmarker']:
    """Get the global PerformanceBenchmarker instance."""
    return _global_benchmarker_instance

import time
import logging
from typing import Dict, List, Optional, Any, Union
import statistics
import threading
from contextlib import contextmanager

# Global flag to enable/disable performance tracking
PERFORMANCE_TRACKING_ENABLED = False  # Disabled by default

# AT THE VERY TOP OF performance_tracker.py
import logging
_pt_logger = logging.getLogger("PerformanceTrackerGlobalFlagCheck")
_pt_logger.error(f"PERFORMANCE_TRACKER_REAL.PY LOADED. Initial PERFORMANCE_TRACKING_ENABLED = {PERFORMANCE_TRACKING_ENABLED}")

def enable_performance_tracking(enabled=True):
    """
    Enable or disable performance tracking globally.

    Args:
        enabled: True to enable tracking, False to disable it

    Returns:
        The new state of performance tracking
    """
    global PERFORMANCE_TRACKING_ENABLED

    # Log the change
    _pt_logger.error(f"Setting performance tracking to {enabled}. Previous state: {PERFORMANCE_TRACKING_ENABLED}")

    # Update the global flag
    PERFORMANCE_TRACKING_ENABLED = enabled

    # Force garbage collection to clean up any lingering objects if disabling
    if not enabled:
        import gc
        gc.collect()

    # Return the new state
    return PERFORMANCE_TRACKING_ENABLED

def is_performance_tracking_enabled():
    """
    Check if performance tracking is enabled.

    Returns:
        True if performance tracking is enabled, False otherwise
    """
    # For extra safety, log any calls to this function at a low frequency
    if hasattr(is_performance_tracking_enabled, '_call_count'):
        is_performance_tracking_enabled._call_count += 1
        if is_performance_tracking_enabled._call_count % 100000 == 0:  # Log much less frequently, e.g., every 100,000th call
            _pt_logger.debug(f"is_performance_tracking_enabled called {is_performance_tracking_enabled._call_count} times. Returns {PERFORMANCE_TRACKING_ENABLED}.") # CHANGED TO DEBUG
    else:
        is_performance_tracking_enabled._call_count = 1
        _pt_logger.info(f"First call to is_performance_tracking_enabled. Returns {PERFORMANCE_TRACKING_ENABLED}.") # Changed to INFO for first call clarity

    # Return the current state
    return PERFORMANCE_TRACKING_ENABLED

class PerformanceTracker:
    """
    A utility class for tracking timestamps and measuring performance.

    This class maintains a dictionary of timestamps that can be used to measure
    the duration of various operations in the trading system. It provides methods
    for adding timestamps, calculating durations, and generating performance reports.
    """

    def __init__(self, name: str = "default"):
        """
        Initialize a new PerformanceTracker.

        Args:
            name: A name for this tracker instance, useful for identification in logs
        """
        self.name = name
        self.timestamps: Dict[str, float] = {}
        self.durations: Dict[str, List[float]] = {}
        self._lock = threading.RLock()  # Use RLock to allow nested acquisitions

    def mark(self, label: str) -> float:
        """
        Record a timestamp with the given label.

        Args:
            label: A descriptive label for this timestamp

        Returns:
            The recorded timestamp (seconds since epoch)
        """
        if not PERFORMANCE_TRACKING_ENABLED:
            return time.time()

        with self._lock:
            ts = time.time()
            self.timestamps[label] = ts
            return ts

    def duration(self, start_label: str, end_label: str) -> Optional[float]:
        """
        Calculate the duration between two timestamps.

        Args:
            start_label: The label of the starting timestamp
            end_label: The label of the ending timestamp

        Returns:
            The duration in seconds, or None if either timestamp is missing
        """
        with self._lock:
            if start_label not in self.timestamps or end_label not in self.timestamps:
                return None

            duration = self.timestamps[end_label] - self.timestamps[start_label]

            # Store the duration for statistics
            duration_key = f"{start_label}_to_{end_label}"
            if duration_key not in self.durations:
                self.durations[duration_key] = []
            self.durations[duration_key].append(duration)

            return duration

    def record_duration(self, label: str, duration: float) -> None:
        """
        Directly record a duration without using timestamps.

        Args:
            label: A descriptive label for this duration
            duration: The duration in seconds
        """
        with self._lock:
            if label not in self.durations:
                self.durations[label] = []
            self.durations[label].append(duration)

    def get_stats(self, label: Optional[str] = None) -> Dict[str, Dict[str, float]]:
        """
        Get statistics for recorded durations.

        Args:
            label: If provided, get stats only for this duration label

        Returns:
            A dictionary of statistics for each duration label
        """
        with self._lock:
            result = {}

            labels_to_process = [label] if label else list(self.durations.keys())

            for dur_label in labels_to_process:
                if dur_label not in self.durations or not self.durations[dur_label]:
                    continue

                values = self.durations[dur_label]

                stats = {
                    "count": len(values),
                    "min": min(values),
                    "max": max(values),
                    "mean": statistics.mean(values),
                    "median": statistics.median(values)
                }

                # Calculate standard deviation if we have enough samples
                if len(values) > 1:
                    stats["std_dev"] = statistics.stdev(values)

                result[dur_label] = stats

            return result

    def reset(self) -> None:
        """Reset all recorded timestamps and durations."""
        with self._lock:
            self.timestamps.clear()
            self.durations.clear()

    def reset_timestamps(self) -> None:
        """Reset only the timestamps, keeping the duration statistics."""
        with self._lock:
            self.timestamps.clear()

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the current state to a dictionary.

        Returns:
            A dictionary containing all timestamps and durations
        """
        with self._lock:
            return {
                "name": self.name,
                "timestamps": dict(self.timestamps),
                "durations": dict(self.durations)
            }

    @contextmanager
    def track(self, label: str):
        """
        Context manager for tracking the duration of a block of code.

        Args:
            label: A descriptive label for this duration

        Example:
            ```
            tracker = PerformanceTracker()
            with tracker.track("process_data"):
                process_data()
            ```
        """
        if not PERFORMANCE_TRACKING_ENABLED:
            yield
            return

        start_time = time.time()
        try:
            yield
        finally:
            end_time = time.time()
            self.record_duration(label, end_time - start_time)


# Dummy tracker that does nothing when performance tracking is disabled
class DummyTracker:
    """A dummy tracker that does nothing when performance tracking is disabled."""

    def __init__(self, name: str = "dummy"):
        """Initialize a new DummyTracker."""
        self.name = name

    def mark(self, label: str) -> float:
        """Return current time without recording anything."""
        return time.time()

    def duration(self, start_label: str, end_label: str) -> Optional[float]:
        """Return None without calculating any duration."""
        return None

    def record_duration(self, label: str, duration: float) -> None:
        """Do nothing."""
        pass

    def get_stats(self, label: Optional[str] = None) -> Dict[str, Dict[str, float]]:
        """Return empty stats."""
        return {}

    def reset(self) -> None:
        """Do nothing."""
        pass

    def reset_timestamps(self) -> None:
        """Do nothing."""
        pass

    def to_dict(self) -> Dict[str, Any]:
        """Return empty dict."""
        return {"name": self.name, "timestamps": {}, "durations": {}}

    @contextmanager
    def track(self, label: str):
        """Do nothing context manager."""
        yield

# Global tracker instances
_trackers: Dict[str, PerformanceTracker] = {}
_global_lock = threading.Lock()
_dummy_tracker = DummyTracker()

# Define a limit on the number of tracker instances to prevent memory leaks
MAX_TRACKER_INSTANCES = 100 # Reasonable limit to prevent excessive memory use

def get_tracker(name: str = "default") -> Union[PerformanceTracker, DummyTracker]:
    """
    Get or create a PerformanceTracker instance with the given name.
    When performance tracking is disabled, returns a DummyTracker that does nothing.
    Includes a limit on the number of concurrently stored tracker instances.

    Args:
        name: The name of the tracker to get or create

    Returns:
        A PerformanceTracker instance or DummyTracker if tracking is disabled
    """
    # Log less frequently, e.g. only for default tracker or a specific one
    if name == "default" or (hasattr(get_tracker, '_call_count_specific') and get_tracker._call_count_specific.get(name, 0) % 100 == 0):
        # Ensure _pt_logger is defined (should be at module level)
        _pt_logger.debug(f"get_tracker() called for '{name}'. PERFORMANCE_TRACKING_ENABLED = {PERFORMANCE_TRACKING_ENABLED}")
        if not hasattr(get_tracker, '_call_count_specific'): get_tracker._call_count_specific = {}
        get_tracker._call_count_specific[name] = get_tracker._call_count_specific.get(name, 0) + 1

    if not PERFORMANCE_TRACKING_ENABLED: # Check the flag from this module
        return _dummy_tracker

    with _global_lock: # _global_lock protects access to _trackers
        # ADDED: Instance limit check
        if len(_trackers) >= MAX_TRACKER_INSTANCES: # Use defined limit
            if name not in _trackers: # Only reset if this is a *new* tracker causing an overflow
                _pt_logger.warning(
                    f"PerformanceTrackerReal: Tracker instance limit ({MAX_TRACKER_INSTANCES}) reached. "
                    f"Resetting all trackers to prevent excessive memory use before creating new tracker '{name}'."
                )
                # Call the existing reset_all_trackers which clears _trackers dict
                reset_all_trackers() # This will clear _trackers, so the new one can be added.
            # If 'name' is already in _trackers, we are not exceeding the limit by returning an existing one.

        if name not in _trackers:
            _trackers[name] = PerformanceTracker(name)
        return _trackers[name]

def reset_all_trackers() -> None:
    """Clears all global PerformanceTracker instances to prevent memory leaks."""
    global _trackers # _trackers is the global dict holding PerformanceTracker instances
    with _global_lock: # _global_lock protects access to _trackers
        count_before = len(_trackers)
        for tracker in _trackers.values():
            tracker.reset()
        # Replace the dictionary instead of just clearing it
        _trackers = {}
        # Use the existing _pt_logger, or ensure one is available here
        if count_before > 0: # Only log if something was actually cleared
             _pt_logger.info(f"PerformanceTrackerReal: Cleared all ({count_before}) cached PerformanceTracker instances.")
        else:
             _pt_logger.debug("PerformanceTrackerReal: reset_all_trackers called, no active trackers to clear.")

    # Force garbage collection to clean up any lingering objects
    import gc
    gc.collect()

@contextmanager
def track_performance(label: str, tracker_name: str = "default"):
    """
    Context manager for tracking performance using a named tracker.

    Args:
        label: A descriptive label for this duration
        tracker_name: The name of the tracker to use

    Example:
        ```
        with track_performance("process_trade", "trading"):
            process_trade(data)
        ```
    """
    if not PERFORMANCE_TRACKING_ENABLED:
        yield
        return

    tracker = get_tracker(tracker_name)
    with tracker.track(label):
        yield

def create_timestamp_dict() -> Optional[Dict[str, float]]:
    """
    Create a new timestamp dictionary with the current time as the 'start' timestamp.

    When performance tracking is disabled, this returns None instead of an empty dictionary
    to prevent unnecessary object creation.

    Returns:
        A dictionary with a 'start' timestamp if tracking is enabled, or None if disabled
    """
    if not PERFORMANCE_TRACKING_ENABLED:
        return None
    return {"start": time.time()}

def add_timestamp(timestamp_dict: Optional[Dict[str, float]], label: str) -> Optional[Dict[str, float]]:
    """
    Add a timestamp to an existing timestamp dictionary.

    Args:
        timestamp_dict: The timestamp dictionary to update
        label: A descriptive label for this timestamp

    Returns:
        The updated timestamp dictionary or None if performance tracking is disabled
    """
    # Return None if performance tracking is disabled
    if not PERFORMANCE_TRACKING_ENABLED:
        return None

    # Return early if timestamp_dict is None/empty
    if timestamp_dict is None or not timestamp_dict:
        return timestamp_dict

    timestamp_dict[label] = time.time()
    return timestamp_dict

def _find_timestamp_pairs(keys: set) -> list:
    """
    Find potential start/end pairs based on naming convention.

    Args:
        keys: Set of timestamp keys

    Returns:
        List of (start_key, end_key) tuples
    """
    pairs = []
    processed_starts = set()

    for key in keys:
        if key.endswith('_start') and key not in processed_starts:
            base = key[:-6]  # Remove '_start'
            end_key = base + '_end'
            error_key = base + '_error'  # Or similar convention

            if end_key in keys:
                pairs.append((key, end_key))
                processed_starts.add(key)
            elif error_key in keys:  # Handle pairs ending in error
                pairs.append((key, error_key))
                processed_starts.add(key)

    return pairs

def calculate_durations(timestamp_dict: Optional[Dict[str, float]]) -> Dict[str, float]:
    """
    Calculate durations between timestamp pairs in a dictionary.
    Handles empty input or missing keys gracefully without raising errors.

    Args:
        timestamp_dict: A dictionary of timestamps

    Returns:
        A dictionary of durations between timestamp pairs
    """
    import logging

    durations = {}

    # Handle disabled tracking, None, or empty dictionary
    if not PERFORMANCE_TRACKING_ENABLED or timestamp_dict is None or not timestamp_dict:
        #logging.debug("Performance tracking disabled or empty timestamp dictionary")
        return durations

    # Filter out None values and ensure all values are valid floats
    filtered_dict = {}
    for k, v in timestamp_dict.items():
        if v is not None:
            try:
                # Convert to float if it's not already a float
                filtered_dict[k] = float(v)
            except (TypeError, ValueError):
                logging.debug(f"Invalid timestamp value: {k}={v}, type={type(v)}")
                # Skip this invalid value
                continue

    # If we have no valid timestamps after filtering, return empty dict
    if not filtered_dict:
        logging.debug("No valid timestamps after filtering")
        return durations

    # Find potential start/end pairs based on naming convention
    keys = set(filtered_dict.keys())
    potential_pairs = _find_timestamp_pairs(keys)

    # Calculate durations for each pair
    for start_key, end_key in potential_pairs:
        start_time = filtered_dict.get(start_key)
        end_time = filtered_dict.get(end_key)

        duration_key = f"{start_key}_to_{end_key}"

        # Perform calculation ONLY if both values are valid numbers
        if isinstance(start_time, (int, float)) and isinstance(end_time, (int, float)):
            try:
                durations[duration_key] = end_time - start_time
            except TypeError:
                # This shouldn't happen with the isinstance check, but log if it does
                logging.warning(f"TypeError during duration calculation for {duration_key} despite type checks")
                # Don't add this key to durations
        else:
            # Log if one or both timestamps were missing or not numbers
            logging.debug(f"Cannot calculate duration for {duration_key}: Missing or invalid timestamps")
            # Don't add this key to durations

    # Also calculate sequential durations for timestamps that don't follow naming convention
    try:
        sorted_items = sorted(filtered_dict.items(), key=lambda x: x[1])

        # Calculate sequential durations
        for i in range(1, len(sorted_items)):
            prev_label, prev_time = sorted_items[i-1]
            curr_label, curr_time = sorted_items[i]

            # Skip if this is already covered by a named pair
            if f"{prev_label}_to_{curr_label}" in durations:
                continue

            # Ensure both timestamps are valid floats before subtraction
            if isinstance(prev_time, (int, float)) and isinstance(curr_time, (int, float)):
                try:
                    durations[f"{prev_label}_to_{curr_label}"] = float(curr_time) - float(prev_time)
                except (TypeError, ValueError):
                    # Skip this pair if calculation fails
                    continue

        # Calculate total duration from min to max timestamp
        valid_times = [t for t in filtered_dict.values() if isinstance(t, (int, float))]
        if len(valid_times) >= 2:
            first_ts = min(valid_times)
            last_ts = max(valid_times)
            durations["total_measured"] = last_ts - first_ts

    except (TypeError, ValueError) as e:
        logging.debug(f"Error calculating sequential durations: {e}")
        # Continue with whatever durations we've calculated so far

    return durations

# Performance statistics collector
class PerformanceStatsCollector:
    """
    Collects and analyzes performance statistics from timestamp dictionaries.
    """

    def __init__(self):
        """Initialize a new PerformanceStatsCollector."""
        self.stats = {}
        self.lock = threading.Lock()

    def add_measurement(self, durations: Dict[str, float]):
        """
        Add a set of durations to the collector.

        Args:
            durations: A dictionary of durations
        """
        # INSIDE PerformanceStatsCollector.add_measurement()
        _pt_logger.debug(f"Collector.add_measurement() called. PERFORMANCE_TRACKING_ENABLED = {PERFORMANCE_TRACKING_ENABLED}. Durations empty: {not durations}")

        # Early return if tracking is disabled
        if not PERFORMANCE_TRACKING_ENABLED:
            return

        # Early return if durations is empty or None
        if not durations:
            return

        with self.lock:
            for key, value in durations.items():
                # Skip invalid values
                if not isinstance(value, (int, float)):
                    continue

                # Only create new entries if performance tracking is enabled
                if PERFORMANCE_TRACKING_ENABLED:
                    if key not in self.stats:
                        self.stats[key] = {
                            "values": [],
                            "min": float('inf'),
                            "max": float('-inf'),
                            "sum": 0,
                            "count": 0
                        }

                    # Only store values if we're actively tracking performance
                    # This prevents memory leaks when tracking is temporarily enabled then disabled
                    self.stats[key]["values"].append(value)
                    self.stats[key]["min"] = min(self.stats[key]["min"], value)
                    self.stats[key]["max"] = max(self.stats[key]["max"], value)
                    self.stats[key]["sum"] += value
                    self.stats[key]["count"] += 1

    def get_stats(self) -> Dict[str, Dict[str, float]]:
        """
        Get statistics for all collected measurements.

        Returns:
            A dictionary mapping labels to statistics
        """
        result = {}
        with self.lock:
            for key, data in self.stats.items():
                if data["count"] > 0:
                    result[key] = {
                        "min": data["min"],
                        "max": data["max"],
                        "avg": data["sum"] / data["count"],
                        "count": data["count"],
                        "total": data["sum"]
                    }
        return result

    def reset(self):
        """Reset all collected statistics."""
        with self.lock:
            self.stats = {}

# Create a global stats collector
_stats_collector = PerformanceStatsCollector()

def get_stats_collector() -> PerformanceStatsCollector:
    """
    Get the global performance stats collector.

    Returns:
        The global PerformanceStatsCollector instance
    """
    return _stats_collector

def add_to_stats(timestamp_dict: Optional[Dict[str, float]]):
    """
    Calculate durations from a timestamp dictionary and add them to the stats collector.

    Args:
        timestamp_dict: A dictionary of timestamps
    """
    # Return early if performance tracking is disabled or timestamp_dict is None/empty
    if not PERFORMANCE_TRACKING_ENABLED or timestamp_dict is None or not timestamp_dict:
        return

    # Double-check that performance tracking is still enabled before calculating durations
    # This prevents unnecessary object creation if tracking was disabled between function calls
    if not PERFORMANCE_TRACKING_ENABLED:
        return

    durations = calculate_durations(timestamp_dict)

    # Triple-check that performance tracking is still enabled before adding to stats collector
    # This prevents unnecessary object creation if tracking was disabled during calculation
    if PERFORMANCE_TRACKING_ENABLED and durations:  # Only add measurements if we have durations to add
        _stats_collector.add_measurement(durations)

def get_performance_stats() -> Dict[str, Dict[str, float]]:
    """
    Get statistics for all collected performance measurements.

    Returns:
        A dictionary mapping labels to statistics
    """
    return _stats_collector.get_stats()

def reset_performance_stats():
    """Reset all collected performance statistics from the global collector."""
    _stats_collector.reset() # This already exists and should be fine
    _pt_logger.info("PerformanceTrackerReal: Cleared global PerformanceStatsCollector.")

def log_performance_durations(perf_timestamps: Optional[Dict[str, float]], context: str, threshold_ms: float = 10.0):
    """
    Log performance durations if they exceed a threshold.

    Args:
        perf_timestamps: Dictionary of timestamps
        context: A descriptive context string for the log message
        threshold_ms: Minimum duration in milliseconds to log (default: 10.0)
    """
    # Return early if performance tracking is disabled
    if not is_performance_tracking_enabled():
        return

    # Return early if perf_timestamps is None/empty
    if perf_timestamps is None or not perf_timestamps:
        return

    # Double-check that performance tracking is still enabled before proceeding
    # This prevents unnecessary object creation if tracking was disabled between function calls
    if not is_performance_tracking_enabled():
        return

    try:
        durations = calculate_durations(perf_timestamps)
        if not durations:  # Skip if no durations were calculated
            return

        # Triple-check that performance tracking is still enabled before proceeding further
        # This prevents unnecessary object creation if tracking was disabled during calculation
        if not is_performance_tracking_enabled():
            return

        # Get total duration if available, otherwise calculate from min/max timestamps
        total_duration_ms = durations.get('total_measured', 0) * 1000  # Convert to ms

        # Create logger if not already imported
        import logging
        logger = logging.getLogger("performance_timing")

        if total_duration_ms > threshold_ms:
            # Format durations for logging
            duration_strs = []
            for key, value in durations.items():
                if key != 'total_measured':  # Skip total in the details
                    duration_strs.append(f"{key}={value*1000:.2f}ms")

            details = ", ".join(duration_strs)
            logger.info(f"PERF_DUR [{context}]: Total={total_duration_ms:.2f}ms. Details: {details}")
        else:
            logger.debug(f"PERF_DUR [{context}]: Total={total_duration_ms:.2f}ms (below threshold).")

        # Add to stats collector for later analysis only if tracking is still enabled
        if is_performance_tracking_enabled():
            add_to_stats(perf_timestamps)

    except Exception as e:
        import logging
        logging.warning(f"PERF_DUR: Error logging performance for {context}: {e}")


def export_performance_stats_to_csv(filename: str) -> bool:
    """
    Export performance statistics to a CSV file.

    Args:
        filename: The name of the CSV file to create

    Returns:
        True if successful, False otherwise
    """
    import csv
    import os

    stats = get_performance_stats()
    if not stats:
        return False

    try:
        # Create directory if it doesn't exist
        directory = os.path.dirname(filename)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)

        with open(filename, 'w', newline='') as csvfile:
            fieldnames = ['metric', 'min_ms', 'max_ms', 'avg_ms', 'count', 'total_ms']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for key, data in sorted(stats.items()):
                writer.writerow({
                    'metric': key,
                    'min_ms': f"{data['min']*1000:.2f}",
                    'max_ms': f"{data['max']*1000:.2f}",
                    'avg_ms': f"{data['avg']*1000:.2f}",
                    'count': data['count'],
                    'total_ms': f"{data['total']*1000:.2f}"
                })
        return True
    except Exception as e:
        import logging
        logging.error(f"Error exporting performance stats to CSV: {e}")
        return False

# Enhanced functions that integrate with PerformanceBenchmarker
def capture_metric_with_benchmarker(metric_name: str, value: float, context: Optional[Dict[str, Any]] = None):
    """
    Capture a metric using the global PerformanceBenchmarker instance if available.

    Args:
        metric_name: Name of the metric (e.g., "queue_size", "processing_time_ms")
        value: The measured value
        context: Optional context dictionary (e.g., {"symbol": "AAPL", "service": "OCR"})
    """
    if not PERFORMANCE_TRACKING_ENABLED:
        return

    benchmarker = get_global_benchmarker()
    if benchmarker:
        try:
            benchmarker.capture_metric(metric_name, value, context)
        except Exception as e:
            import logging
            logging.warning(f"Error capturing metric '{metric_name}' with benchmarker: {e}")

def add_timestamp_with_capture(metric_name: str, perf_dict: Dict, key: str, context: Optional[Dict] = None):
    """
    Enhanced add_timestamp that also captures metrics with PerformanceBenchmarker.

    Args:
        metric_name: Base metric name for benchmarker (e.g., "ocr_processing")
        perf_dict: Performance dictionary to update
        key: Timestamp key to add
        context: Optional context for the metric
    """
    if not PERFORMANCE_TRACKING_ENABLED:
        return perf_dict

    # Standard timestamp tracking
    current_time = time.time()
    perf_dict[key] = current_time

    # Enhanced benchmarker integration
    benchmarker = get_global_benchmarker()
    if benchmarker and key.endswith('_end'):
        # Calculate duration if this is an end timestamp
        start_key = key.replace('_end', '_start')
        if start_key in perf_dict:
            duration_ms = (current_time - perf_dict[start_key]) * 1000
            capture_metric_with_benchmarker(f"{metric_name}_duration_ms", duration_ms, context)

    return perf_dict

def finalize_performance_scope(perf_dict: Optional[Dict[str, float]], scope_name: str, context: Optional[Dict] = None):
    """
    Finalize a performance measurement scope by calculating durations and capturing key metrics.

    Args:
        perf_dict: Performance dictionary with timestamps
        scope_name: Name of the performance scope (e.g., "ocr_conditioning", "trade_execution")
        context: Optional context for the metrics
    """
    if not PERFORMANCE_TRACKING_ENABLED or not perf_dict:
        return

    try:
        durations = calculate_durations(perf_dict)
        if not durations:
            return

        benchmarker = get_global_benchmarker()
        if benchmarker:
            # Capture total duration
            total_duration = durations.get('total_measured', 0) * 1000  # Convert to ms
            if total_duration > 0:
                capture_metric_with_benchmarker(f"{scope_name}_total_duration_ms", total_duration, context)

            # Capture key individual durations
            for duration_key, duration_value in durations.items():
                if duration_key != 'total_measured':
                    duration_ms = duration_value * 1000
                    metric_name = f"{scope_name}_{duration_key}_ms"
                    capture_metric_with_benchmarker(metric_name, duration_ms, context)

        # Also add to traditional stats collector
        add_to_stats(perf_dict)

    except Exception as e:
        import logging
        logging.warning(f"Error finalizing performance scope '{scope_name}': {e}")

# Queue monitoring utilities
def capture_queue_size(service_name: str, queue_size: int, context: Optional[Dict] = None):
    """
    Capture queue size metrics for monitoring bottlenecks.

    Args:
        service_name: Name of the service (e.g., "OCRDataConditioner", "EventBus")
        queue_size: Current queue size
        context: Optional context (e.g., {"queue_type": "input", "worker_id": "1"})
    """
    if not PERFORMANCE_TRACKING_ENABLED:
        return

    metric_name = f"{service_name.lower()}_queue_size"
    capture_metric_with_benchmarker(metric_name, float(queue_size), context)

def capture_throughput_metric(service_name: str, items_processed: int, time_window_seconds: float, context: Optional[Dict] = None):
    """
    Capture throughput metrics for performance monitoring.

    Args:
        service_name: Name of the service
        items_processed: Number of items processed in the time window
        time_window_seconds: Time window for the measurement
        context: Optional context
    """
    if not PERFORMANCE_TRACKING_ENABLED or time_window_seconds <= 0:
        return

    throughput = items_processed / time_window_seconds
    metric_name = f"{service_name.lower()}_throughput_items_per_sec"
    capture_metric_with_benchmarker(metric_name, throughput, context)

def capture_cpu_usage(service_name: str, cpu_percent: float, context: Optional[Dict] = None):
    """
    Capture CPU usage metrics for monitoring CPU-bound operations.

    Args:
        service_name: Name of the service
        cpu_percent: CPU usage percentage
        context: Optional context
    """
    if not PERFORMANCE_TRACKING_ENABLED:
        return

    metric_name = f"{service_name.lower()}_cpu_usage_percent"
    capture_metric_with_benchmarker(metric_name, cpu_percent, context)