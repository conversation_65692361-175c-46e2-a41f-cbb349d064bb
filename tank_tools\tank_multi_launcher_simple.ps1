# tank_multi_launcher_simple.ps1 - Simple launcher for multi-process monitoring
param(
    [string]$Mode = "monitor",
    [string]$ProcessPattern = "python",
    [int]$DemoMinutes = 5
)

Write-Host "🚀 TANK Multi-Process Monitoring Launcher" -ForegroundColor Green
Write-Host "=" * 60

function Test-PythonProcesses {
    param([string]$Pattern)
    
    $processes = Get-Process $Pattern -ErrorAction SilentlyContinue
    if ($processes.Count -eq 0) {
        Write-Host "❌ No processes found matching pattern '$Pattern'!" -ForegroundColor Red
        Write-Host ""
        Write-Host "Available processes:" -ForegroundColor Yellow
        Get-Process | Where-Object { 
            $_.ProcessName -like "*python*" -or 
            $_.ProcessName -like "*application*" -or 
            $_.ProcessName -like "*core*"
        } | Format-Table ProcessName, Id, WorkingSet -AutoSize
        return $false
    }
    return $true
}

switch ($Mode.ToLower()) {
    "monitor" {
        Write-Host "📊 Starting Multi-Process Real-Time Monitoring..." -ForegroundColor Cyan
        Write-Host "Process Pattern: $ProcessPattern" -ForegroundColor Yellow
        
        if (-not (Test-PythonProcesses $ProcessPattern)) {
            exit 1
        }
        
        Write-Host "✅ Found $((Get-Process $ProcessPattern).Count) processes" -ForegroundColor Green
        & .\tank_multi_process_monitor.ps1 -ProcessPattern $ProcessPattern -SampleIntervalSeconds 30
    }
    
    "leak" {
        Write-Host "🔍 Starting Multi-Process Leak Detection..." -ForegroundColor Cyan
        Write-Host "Process Pattern: $ProcessPattern" -ForegroundColor Yellow
        
        if (-not (Test-PythonProcesses $ProcessPattern)) {
            exit 1
        }
        
        Write-Host "✅ Found $((Get-Process $ProcessPattern).Count) processes" -ForegroundColor Green
        & .\tank_multi_leak_detector.ps1 -ProcessPattern $ProcessPattern -SampleIntervalSeconds 60
    }
    
    "demo" {
        Write-Host "🎮 Starting Demo Mode..." -ForegroundColor Cyan
        Write-Host "Demo Duration: $DemoMinutes minutes" -ForegroundColor Yellow
        
        if (-not (Test-PythonProcesses $ProcessPattern)) {
            exit 1
        }
        
        Write-Host "✅ Found $((Get-Process $ProcessPattern).Count) processes" -ForegroundColor Green
        Write-Host ""
        
        # Simple demo monitoring
        Write-Host "🔄 Running demo monitoring..." -ForegroundColor Cyan
        $endTime = (Get-Date).AddMinutes($DemoMinutes)
        $sampleCount = 0
        
        while ((Get-Date) -lt $endTime) {
            $sampleCount++
            $processes = Get-Process $ProcessPattern -ErrorAction SilentlyContinue
            
            if ($processes.Count -gt 0) {
                $totalMemoryMB = [math]::Round(($processes | Measure-Object WorkingSet64 -Sum).Sum / 1MB, 2)
                $avgMemoryMB = [math]::Round($totalMemoryMB / $processes.Count, 2)
                Write-Host "  Sample $sampleCount`: $($processes.Count) processes, $totalMemoryMB MB total, $avgMemoryMB MB avg" -ForegroundColor White
            }
            
            Start-Sleep 15
        }
        
        Write-Host ""
        Write-Host "🎮 Demo completed!" -ForegroundColor Green
        Write-Host "To run full monitoring:" -ForegroundColor Cyan
        Write-Host "  .\tank_multi_launcher_simple.ps1 -Mode monitor" -ForegroundColor Yellow
        Write-Host "  .\tank_multi_launcher_simple.ps1 -Mode leak" -ForegroundColor Yellow
    }
    
    "analyze" {
        Write-Host "📈 Analyzing Historical Data..." -ForegroundColor Cyan
        
        $csvFiles = Get-ChildItem "tank_multi_*.csv" -ErrorAction SilentlyContinue
        
        if ($csvFiles.Count -eq 0) {
            Write-Host "❌ No CSV data files found!" -ForegroundColor Red
            Write-Host "Run monitoring first to generate data." -ForegroundColor Yellow
            exit 1
        }
        
        foreach ($file in $csvFiles) {
            Write-Host "📊 Analyzing $($file.Name)..." -ForegroundColor Cyan
            try {
                $data = Import-Csv $file.FullName
                Write-Host "  ✅ $($data.Count) data points" -ForegroundColor Green
                
                if ($file.Name -like "*process_monitor*") {
                    $data | ForEach-Object { $_.TotalMemoryMB = [double]$_.TotalMemoryMB }
                    $memStats = $data | Measure-Object TotalMemoryMB -Average -Maximum -Minimum
                    Write-Host "    Avg Memory: $([math]::Round($memStats.Average, 2)) MB" -ForegroundColor White
                    Write-Host "    Peak Memory: $([math]::Round($memStats.Maximum, 2)) MB" -ForegroundColor White
                }
            } catch {
                Write-Host "  ❌ Error: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
    
    default {
        Write-Host "❌ Invalid mode: $Mode" -ForegroundColor Red
        Write-Host ""
        Write-Host "Available modes:" -ForegroundColor Yellow
        Write-Host "  monitor  - Real-time monitoring" -ForegroundColor White
        Write-Host "  leak     - Leak detection" -ForegroundColor White
        Write-Host "  demo     - Quick demo" -ForegroundColor White
        Write-Host "  analyze  - Analyze data" -ForegroundColor White
        Write-Host ""
        Write-Host "Examples:" -ForegroundColor Cyan
        Write-Host "  .\tank_multi_launcher_simple.ps1 -Mode monitor" -ForegroundColor Gray
        Write-Host "  .\tank_multi_launcher_simple.ps1 -Mode demo -DemoMinutes 3" -ForegroundColor Gray
    }
}
