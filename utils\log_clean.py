import os
import datetime
import sys
import re

# Add project root to path to find config.py
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
sys.path.insert(0, project_root)

try:
    from config import LOG_BASE_DIR
except ImportError:
    # Fallback if LOG_BASE_DIR is not in config
    LOG_BASE_DIR = os.path.join(project_root, "logs")

# Configuration
days_to_keep = 3  # Keep logs for the last 3 days
max_file_size_mb = 50  # Maximum file size in MB before trimming regardless of date

# Directories to exclude from processing
exclude_dirs = ["exports", "videos"]

# File patterns to exclude (using regex)
exclude_patterns = [
    r".*\.prof$",  # Profile files
    r".*\.bat$",   # Batch files
    r".*\.ps1$",   # PowerShell scripts
    r".*\.csv$",   # CSV files
    r".*\.py$",    # Python files
    r".*\.exe$",   # Executable files
    r"main_app\.log\.\d+$"  # Rotated log files (already handled by RotatingFileHandler)
]

# Function to check if a file should be excluded
def should_exclude(file_path):
    # Check if file matches any exclude pattern
    file_name = os.path.basename(file_path)
    for pattern in exclude_patterns:
        if re.match(pattern, file_name):
            return True
    return False

# Function to trim logs by date
def trim_log_file_by_date(file_path):
    try:
        with open(file_path, "r", encoding="utf-8", errors="replace") as f:
            lines = f.readlines()

        # Assuming log format: [YYYY-MM-DD HH:MM:SS] log message
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_to_keep)
        trimmed_lines = [line for line in lines if is_recent(line, cutoff_date)]

        # Only write back if we actually trimmed something
        if len(trimmed_lines) < len(lines):
            with open(file_path, "w", encoding="utf-8") as f:
                f.writelines(trimmed_lines)

            print(f"Trimmed {file_path} by date, kept {len(trimmed_lines)}/{len(lines)} lines.")
        else:
            print(f"No date trimming needed for {file_path}")

    except Exception as e:
        print(f"Error trimming {file_path} by date: {e}")

# Function to trim logs by size
def trim_log_file_by_size(file_path, max_size_mb=max_file_size_mb):
    try:
        # Get file size in MB
        file_size_mb = os.path.getsize(file_path) / (1024 * 1024)

        # If file is smaller than max size, no need to trim
        if file_size_mb <= max_size_mb:
            return

        # Calculate how much to trim (keep 75% of max size)
        target_size_mb = max_size_mb * 0.75
        target_size_bytes = target_size_mb * 1024 * 1024

        with open(file_path, "r", encoding="utf-8", errors="replace") as f:
            lines = f.readlines()

        # Calculate average line size
        total_bytes = sum(len(line.encode('utf-8')) for line in lines)
        avg_line_bytes = total_bytes / len(lines) if lines else 0

        # Calculate how many lines to keep
        if avg_line_bytes > 0:
            lines_to_keep = int(target_size_bytes / avg_line_bytes)
            # Keep the most recent lines
            trimmed_lines = lines[-lines_to_keep:] if lines_to_keep > 0 else []

            with open(file_path, "w", encoding="utf-8") as f:
                f.writelines(trimmed_lines)

            print(f"Trimmed {file_path} by size, kept {len(trimmed_lines)}/{len(lines)} lines. Size: {file_size_mb:.2f}MB -> ~{target_size_mb:.2f}MB")
        else:
            print(f"Cannot trim {file_path} by size: average line size is 0")

    except Exception as e:
        print(f"Error trimming {file_path} by size: {e}")

# Helper function to check if a log line is recent
def is_recent(line, cutoff_date):
    try:
        # Try different timestamp formats
        timestamp_formats = [
            # [YYYY-MM-DD HH:MM:SS]
            (r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]', "%Y-%m-%d %H:%M:%S"),
            # [HH:MM:SS]
            (r'\[(\d{2}:\d{2}:\d{2})\]', "%H:%M:%S"),
            # YYYY-MM-DD HH:MM:SS
            (r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', "%Y-%m-%d %H:%M:%S"),
            # MM/DD/YYYY HH:MM:SS
            (r'(\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2})', "%m/%d/%Y %H:%M:%S")
        ]

        for pattern, format_str in timestamp_formats:
            match = re.search(pattern, line)
            if match:
                timestamp_str = match.group(1)
                try:
                    log_date = datetime.datetime.strptime(timestamp_str, format_str)
                    # If format doesn't include date, assume current date
                    if format_str == "%H:%M:%S":
                        today = datetime.datetime.now().date()
                        log_date = datetime.datetime.combine(today, log_date.time())
                    return log_date >= cutoff_date
                except ValueError:
                    continue

        # If no timestamp found or couldn't parse, keep the line
        return True
    except:
        return True  # Keep line if format is unknown

# Main function to process all log files
def process_log_files():
    print(f"Starting log cleanup in {LOG_BASE_DIR}")
    print(f"Settings: keep logs for {days_to_keep} days, max file size: {max_file_size_mb}MB")

    # Get all files in the log directory and subdirectories
    for root, dirs, files in os.walk(LOG_BASE_DIR):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in exclude_dirs]

        for file in files:
            file_path = os.path.join(root, file)

            # Skip files that match exclude patterns
            if should_exclude(file_path):
                print(f"Skipping excluded file: {file_path}")
                continue

            # Process the file
            print(f"Processing: {file_path}")

            # First trim by size if file is too large
            trim_log_file_by_size(file_path)

            # Then trim by date
            trim_log_file_by_date(file_path)

# Run the main function
if __name__ == "__main__":
    process_log_files()
