# Golden Timestamp System Implementation

## Overview

The Golden Timestamp system provides universal data freshness validation across the entire TESTRADE system. Every piece of data that flows through Redis now carries its "birth certificate" timestamp, allowing consumers to detect and discard stale buffered data.

## Core Principle

Every Redis message now includes an `origin_timestamp_s` field in its metadata that indicates when the data was originally captured or created, not when it was published to Redis. This allows consumers to determine data freshness regardless of network delays, processing delays, or buffering.

## Implementation Components

### 1. Core Message Creation (`core/application_core.py`)

**Modified Method**: `_create_redis_message_json()`
- **New Parameter**: `origin_timestamp_s: Optional[float] = None`
- **Behavior**: If provided, uses the origin timestamp; otherwise defaults to `time.time()`
- **Impact**: All Redis messages now include the Golden Timestamp

```python
# Before
message = {
    "metadata": {
        "eventId": get_thread_safe_uuid(),
        "correlationId": correlation_id_val,
        "causationId": causation_id_val,
        "timestamp_ns": time.perf_counter_ns(),
        "eventType": event_type_str,
        "sourceComponent": source_component_name,
    },
    "payload": payload
}

# After
message = {
    "metadata": {
        "eventId": get_thread_safe_uuid(),
        "correlationId": correlation_id_val,
        "causationId": causation_id_val,
        "timestamp_ns": time.perf_counter_ns(),
        "origin_timestamp_s": origin_timestamp_s or time.time(),  # THE GOLDEN TIMESTAMP
        "eventType": event_type_str,
        "sourceComponent": source_component_name,
    },
    "payload": payload
}
```

### 2. OCR Data with Frame Timestamps

**Updated**: `_publish_raw_ocr_to_redis_specification()` in `core/application_core.py`
- **Change**: Now passes `ocr_data.frame_timestamp` as `origin_timestamp_s`
- **Impact**: OCR data uses actual frame capture time, not Redis publish time

```python
redis_message_json_str = self._create_redis_message_json(
    payload=ocr_payload_dict,
    event_type_str="TESTRADE_RAW_OCR_DATA",
    correlation_id_val=master_correlation_id,
    source_component_name="ApplicationCore.OCRPipeListener",
    causation_id_val=None,
    origin_timestamp_s=ocr_data.frame_timestamp  # Golden Timestamp: when frame was captured
)
```

### 3. Telemetry Data Support

**Updated**: `enqueue_telemetry()` in `core/application_core.py`
- **New Parameter**: `origin_timestamp_s: Optional[float] = None`
- **Payload**: Now includes the origin timestamp for downstream processing

**Updated**: `TelemetryStreamingService` in `modules/monitoring/telemetry_streaming_service.py`
- **Change**: Extracts `origin_timestamp_s` from telemetry payload and passes to `_create_redis_message_json`
- **Impact**: All telemetry data (orders, fills, positions, risk decisions) now carries Golden Timestamps

### 4. Data Freshness Validation Utility

**New File**: `utils/golden_timestamp_validator.py`
- **Purpose**: Centralized utility for checking data freshness
- **Key Functions**:
  - `is_data_stale()`: Check if data exceeds staleness threshold
  - `get_data_age_seconds()`: Calculate data age
  - `log_stale_data_rejection()`: Log when stale data is rejected

**Default Staleness Thresholds**:
- OCR data: 2.0 seconds
- Price data: 1.0 seconds  
- Position data: 5.0 seconds
- Order data: 3.0 seconds
- Risk data: 2.0 seconds
- Default: 3.0 seconds

## Usage Examples

### For Data Producers

```python
# OCR Service - Frame timestamp is automatically used
# No changes needed - handled in ApplicationCore

# Order/Position Services
app_core.enqueue_telemetry(
    source_service="OrderRepository",
    event_type="ORDER_FILL",
    data=fill_data,
    origin_timestamp_s=fill_received_time  # When fill actually occurred
)

# Risk Service
risk_decision_time = time.time()
# ... make risk decision ...
app_core.enqueue_telemetry(
    source_service="RiskService", 
    event_type="RISK_DECISION",
    data=decision_data,
    origin_timestamp_s=risk_decision_time  # When decision was made
)
```

### For Data Consumers (GUI)

```python
from utils.golden_timestamp_validator import is_data_stale, get_data_age_seconds

def handle_redis_message(message):
    event_type = message.get('metadata', {}).get('eventType')
    
    # Check if data is stale
    if is_data_stale(message, event_type=event_type):
        age = get_data_age_seconds(message)
        print(f"Rejecting stale {event_type} data (age: {age:.2f}s)")
        return  # Discard stale data
    
    # Process fresh data
    payload = message.get('payload', {})
    update_gui(payload)
```

## Benefits

1. **Universal Solution**: Works for all data types, not just OCR
2. **Accurate Freshness**: Based on actual data origin time, not transmission time
3. **Automatic Buffering Recovery**: GUI automatically rejects backlog when coming online
4. **Debugging**: Clear visibility into data age and staleness
5. **Configurable**: Different staleness thresholds per data type
6. **Backward Compatible**: Existing code continues to work

## System-Wide Impact

### Messages That Now Include Golden Timestamps:
- ✅ OCR frame data (uses frame capture timestamp)
- ✅ Order events (uses order creation/fill timestamp)  
- ✅ Position updates (uses position change timestamp)
- ✅ Risk decisions (uses decision timestamp)
- ✅ Price data (uses quote/trade timestamp)
- ✅ All telemetry data (uses event origin timestamp)

### Consumer Benefits:
- GUI components can reject stale data automatically
- Real-time displays show only fresh information
- System recovery is transparent to users
- Clear debugging when data flow issues occur

## Migration Notes

This implementation is backward compatible. Existing code will continue to work because:
- The new `origin_timestamp_s` parameter is optional
- If not provided, it defaults to current time
- Existing message consumers can ignore the new field
- New consumers can take advantage of freshness checking

## Testing

Use the Golden Timestamp validator to verify the implementation:

```python
# Check if bulletproof IPC recovery works
python check_mmap_buffer.py  # Check for buffered data
# Start babysitter service
# Verify stale data is rejected in GUI logs
```

The system is now bulletproof against stale data display during recovery scenarios.