#!/usr/bin/env python3
"""
Simple script to manage tesseract55.dll for OCR accelerator
"""

import os
import shutil
import sys

def copy_tesseract_dll():
    """Copy tesseract55.dll to root directory"""
    source = "ocr_accelerator/x64/Release/tesseract55.dll"
    target = "tesseract55.dll"
    
    if not os.path.exists(source):
        print(f"❌ Source not found: {source}")
        return False
        
    if os.path.exists(target):
        print(f"✅ {target} already exists in root")
        return True
        
    try:
        shutil.copy2(source, target)
        print(f"✅ Copied tesseract55.dll to root")
        return True
    except Exception as e:
        print(f"❌ Failed to copy: {e}")
        return False

def remove_tesseract_dll():
    """Remove tesseract55.dll from root directory"""
    target = "tesseract55.dll"
    
    if not os.path.exists(target):
        print(f"✅ {target} not in root (already clean)")
        return True
        
    try:
        os.remove(target)
        print(f"✅ Removed tesseract55.dll from root")
        return True
    except Exception as e:
        print(f"❌ Failed to remove: {e}")
        return False

def test_ocr_module():
    """Test if OCR accelerator module works"""
    build_dir = "ocr_accelerator/x64/Release"
    
    if not os.path.exists(build_dir):
        print(f"❌ Build directory not found: {build_dir}")
        return False
        
    original_cwd = os.getcwd()
    try:
        os.chdir(build_dir)
        import ocr_accelerator
        
        if hasattr(ocr_accelerator, 'test_function'):
            result = ocr_accelerator.test_function()
            print(f"✅ OCR module test: {result}")
            return True
        else:
            print("❌ test_function not found in module")
            return False
            
    except Exception as e:
        print(f"❌ OCR module test failed: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def main():
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python manage_tesseract_dll.py copy    # Copy tesseract55.dll to root")
        print("  python manage_tesseract_dll.py remove  # Remove tesseract55.dll from root") 
        print("  python manage_tesseract_dll.py test    # Test OCR module")
        return
        
    action = sys.argv[1].lower()
    
    if action == "copy":
        copy_tesseract_dll()
    elif action == "remove":
        remove_tesseract_dll()
    elif action == "test":
        # Copy DLL, test, then optionally remove
        print("=== Testing OCR Module ===")
        
        dll_was_present = os.path.exists("tesseract55.dll")
        
        if not dll_was_present:
            print("Copying tesseract55.dll for test...")
            if not copy_tesseract_dll():
                return
                
        success = test_ocr_module()
        
        if not dll_was_present:
            choice = input("\nRemove tesseract55.dll from root? (y/n): ").lower().strip()
            if choice == 'y':
                remove_tesseract_dll()
            else:
                print("Keeping tesseract55.dll in root")
                
        if success:
            print("\n🎯 OCR accelerator is working correctly!")
        else:
            print("\n❌ OCR accelerator has issues")
            
    else:
        print(f"Unknown action: {action}")

if __name__ == "__main__":
    main()