#!/usr/bin/env python3
"""
Clean solution for tesseract55.dll dependency management
"""

import os
import shutil
import contextlib
import atexit

class OCRDllManager:
    """Context manager to handle tesseract55.dll dependency cleanly"""
    
    def __init__(self):
        self.dll_name = "tesseract55.dll"
        self.build_dir = "ocr_accelerator/x64/Release"
        self.dll_source = os.path.join(self.build_dir, self.dll_name)
        self.dll_root = self.dll_name
        self.dll_copied = False
        self._module_cache = None
        
    def __enter__(self):
        """Copy tesseract55.dll to root temporarily"""
        if not os.path.exists(self.dll_source):
            raise FileNotFoundError(f"tesseract55.dll not found in {self.build_dir}")
            
        if not os.path.exists(self.dll_root):
            shutil.copy2(self.dll_source, self.dll_root)
            self.dll_copied = True
            print(f"Temporarily copied {self.dll_name} to root")
            
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Clean up tesseract55.dll from root"""
        if self.dll_copied and os.path.exists(self.dll_root):
            try:
                os.remove(self.dll_root)
                print(f"Cleaned up {self.dll_name} from root")
            except Exception as e:
                print(f"Warning: Could not remove {self.dll_name}: {e}")
                
    def get_ocr_module(self):
        """Get the OCR accelerator module with proper DLL handling"""
        if self._module_cache:
            return self._module_cache
            
        # Change to build directory for import
        build_path = os.path.abspath(self.build_dir)
        original_cwd = os.getcwd()
        
        try:
            os.chdir(build_path)
            import ocr_accelerator
            self._module_cache = ocr_accelerator
            return ocr_accelerator
        finally:
            os.chdir(original_cwd)

# Global instance for easy use
_dll_manager = None

def setup_ocr_accelerator():
    """Setup OCR accelerator with automatic DLL management"""
    global _dll_manager
    
    if _dll_manager is None:
        _dll_manager = OCRDllManager()
        _dll_manager.__enter__()
        
        # Register cleanup on exit
        atexit.register(lambda: _dll_manager.__exit__(None, None, None))
        
    return _dll_manager.get_ocr_module()

@contextlib.contextmanager
def temp_ocr_accelerator():
    """Context manager for temporary OCR accelerator usage"""
    with OCRDllManager() as manager:
        yield manager.get_ocr_module()

# Test functions
def test_ocr_accelerator():
    """Test the OCR accelerator with clean DLL management"""
    print("=== Testing OCR Accelerator with DLL Manager ===")
    
    try:
        with temp_ocr_accelerator() as ocr_module:
            print("✅ Module loaded successfully")
            
            # Test basic function
            if hasattr(ocr_module, 'test_function'):
                result = ocr_module.test_function()
                print(f"✅ test_function(): {result}")
            else:
                print("❌ test_function not found")
                
            # Check main function
            if hasattr(ocr_module, 'process_image_and_ocr'):
                print("✅ process_image_and_ocr function found")
            else:
                print("❌ process_image_and_ocr not found")
                
            return True
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ocr_accelerator()