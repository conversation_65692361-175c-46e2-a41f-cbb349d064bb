#!/usr/bin/env python3
"""Test script for C++ golden timestamp nanosecond function"""

import time
import sys
import os
from datetime import datetime

# Add module path
build_dir = r"C:\TESTRADE\ocr_accelerator\x64\Release"
if os.path.exists(build_dir):
    os.environ['PATH'] = build_dir + os.pathsep + os.environ.get('PATH', '')
    sys.path.insert(0, build_dir)

def test_golden_timestamp_ns():
    print("Testing C++ golden timestamp nanosecond function...")
    
    try:
        import ocr_accelerator
        
        # Test both functions exist
        has_seconds = hasattr(ocr_accelerator, 'get_golden_timestamp')
        has_nanoseconds = hasattr(ocr_accelerator, 'get_golden_timestamp_ns')
        
        print(f"✅ get_golden_timestamp: {'found' if has_seconds else 'NOT found'}")
        print(f"✅ get_golden_timestamp_ns: {'found' if has_nanoseconds else 'NOT found'}")
        
        if has_seconds and has_nanoseconds:
            print("\n🔍 CORRECTNESS TEST (Most Important):")
            
            # Get timestamps close together
            py_s = time.time()
            cpp_s = ocr_accelerator.get_golden_timestamp()
            py_ns = time.time_ns()  # ✅ FIXED: Use time.time_ns() not perf_counter_ns()
            cpp_ns = ocr_accelerator.get_golden_timestamp_ns()
            
            print(f"Python seconds:     {py_s:.9f}")
            print(f"C++ seconds:        {cpp_s:.9f}")
            print(f"Python nanoseconds: {py_ns}")
            print(f"C++ nanoseconds:    {cpp_ns}")
            
            # Check if they're in the same ballpark (Unix epoch)
            seconds_diff = abs(py_s - cpp_s)
            ns_diff = abs(py_ns - cpp_ns)
            
            print(f"\nDifferences:")
            print(f"Seconds diff:    {seconds_diff:.9f}s")
            print(f"Nanoseconds diff: {ns_diff}ns ({ns_diff/1e6:.3f}ms)")
            
            # Validate epochs are correct
            try:
                py_dt = datetime.fromtimestamp(py_s)
                cpp_dt = datetime.fromtimestamp(cpp_s)
                py_ns_dt = datetime.fromtimestamp(py_ns / 1e9)
                cpp_ns_dt = datetime.fromtimestamp(cpp_ns / 1e9)
                
                print(f"\nConverted to readable time:")
                print(f"Python seconds:     {py_dt}")
                print(f"C++ seconds:        {cpp_dt}")
                print(f"Python nanoseconds: {py_ns_dt}")
                print(f"C++ nanoseconds:    {cpp_ns_dt}")
                
                # Validation checks
                current_year = datetime.now().year
                
                if cpp_dt.year != current_year:
                    print(f"❌ C++ seconds timestamp is WRONG! Shows year {cpp_dt.year}, should be {current_year}")
                    print("   This indicates wrong epoch (probably boot time)")
                    return False
                    
                if cpp_ns_dt.year != current_year:
                    print(f"❌ C++ nanoseconds timestamp is WRONG! Shows year {cpp_ns_dt.year}, should be {current_year}")
                    print("   This indicates wrong epoch (probably boot time)")
                    return False
                
                if seconds_diff > 1.0:
                    print(f"❌ Timestamps differ by {seconds_diff:.3f} seconds - too much!")
                    return False
                    
                print("✅ All timestamps show correct current time!")
                
            except (ValueError, OSError) as e:
                print(f"❌ Cannot convert timestamps to readable time: {e}")
                print("   This usually means timestamps are invalid")
                return False
            
            print("\n🚀 PERFORMANCE TEST:")
            
            # ✅ FIXED: Compare like with like
            # C++ seconds vs Python seconds
            start = time.perf_counter()
            for i in range(10000):  # More iterations for better measurement
                cpp_ts_s = ocr_accelerator.get_golden_timestamp()
            cpp_s_time = time.perf_counter() - start
            
            start = time.perf_counter()
            for i in range(10000):
                py_ts_s = time.time()
            py_s_time = time.perf_counter() - start
            
            # C++ nanoseconds vs Python nanoseconds (both Unix epoch)
            start = time.perf_counter()
            for i in range(10000):
                cpp_ts_ns = ocr_accelerator.get_golden_timestamp_ns()
            cpp_ns_time = time.perf_counter() - start
            
            start = time.perf_counter()
            for i in range(10000):
                py_ts_ns = time.time_ns()  # ✅ FIXED: Use time.time_ns()
            py_ns_time = time.perf_counter() - start
            
            print(f"C++ seconds (10k calls):  {cpp_s_time:.6f}s vs Python: {py_s_time:.6f}s")
            if cpp_s_time > 0:
                print(f"  → C++ is {py_s_time/cpp_s_time:.1f}x {'faster' if py_s_time > cpp_s_time else 'slower'}")
            
            print(f"C++ nanosecs (10k calls): {cpp_ns_time:.6f}s vs Python: {py_ns_time:.6f}s")
            if cpp_ns_time > 0:
                print(f"  → C++ is {py_ns_time/cpp_ns_time:.1f}x {'faster' if py_ns_time > cpp_ns_time else 'slower'}")
            
            # Calculate calls per second
            if cpp_ns_time > 0 and py_ns_time > 0:
                cpp_calls_per_sec = 10000 / cpp_ns_time
                py_calls_per_sec = 10000 / py_ns_time
                print(f"\nThroughput:")
                print(f"C++ nanoseconds:  {cpp_calls_per_sec:,.0f} calls/sec")
                print(f"Python nanoseconds: {py_calls_per_sec:,.0f} calls/sec")
                
        else:
            print("❌ Missing functions!")
            available = [attr for attr in dir(ocr_accelerator) if not attr.startswith('_')]
            print("Available functions:", available)
            return False
            
        return True
            
    except ImportError as e:
        print(f"❌ Failed to import ocr_accelerator: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing golden timestamp: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_golden_timestamp_ns()
    sys.exit(0 if success else 1)