#!/usr/bin/env python3
"""
Direct C++ Timing Test

Tests if the C++ OCR accelerator module is returning timing data correctly.
"""

import sys
import os
import numpy as np
from PIL import ImageGrab
import time

# Add module path
build_dir = r"C:\TESTRADE\ocr_accelerator\x64\Release"
if os.path.exists(build_dir):
    os.environ['PATH'] = build_dir + os.pathsep + os.environ.get('PATH', '')
    sys.path.insert(0, build_dir)

def test_cpp_timing():
    print("🔬 DIRECT C++ TIMING TEST")
    print("=" * 50)
    
    try:
        import ocr_accelerator
        print("✅ C++ accelerator module imported successfully")
        
        # Take a screenshot to process
        print("📸 Capturing screenshot...")
        screenshot = ImageGrab.grab()
        frame = np.array(screenshot)
        
        if frame.ndim == 3 and frame.shape[2] == 3:
            # Convert RGB to BGR for OpenCV
            frame_bgr = frame[:, :, ::-1].copy()
            
            print(f"📊 Image size: {frame_bgr.shape}")
            print("🚀 Running C++ OCR with timing measurement...")
            
            # Call C++ function with test parameters
            result = ocr_accelerator.process_image_and_ocr(
                frame_bgr,
                upscale_factor=2.0,
                force_black_text_on_white=True,
                unsharp_strength=1.5,
                threshold_block_size=11,
                threshold_c=2,
                red_boost=1.0,
                green_boost=1.0,
                apply_text_mask_cleaning=True,
                text_mask_min_contour_area=50,
                text_mask_min_width=5,
                text_mask_min_height=5,
                enhance_small_symbols=True,
                symbol_max_height_for_enhancement_upscaled=20,
                period_comma_aspect_ratio_range_upscaled=(0.3, 1.5),
                period_comma_draw_radius_upscaled=3,
                hyphen_like_min_aspect_ratio_upscaled=2.0,
                hyphen_like_draw_min_height_upscaled=8
            )
            
            print("\n📊 C++ RESULT ANALYSIS:")
            print("-" * 30)
            print(f"Return type: {type(result)}")
            print(f"Keys returned: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            
            if isinstance(result, dict):
                # Check for timing data
                timing_keys = [k for k in result.keys() if '_ns' in k]
                print(f"\nTiming keys found: {timing_keys}")
                
                if timing_keys:
                    print("\n🕐 TIMING BREAKDOWN:")
                    for key in sorted(timing_keys):
                        value = result[key]
                        print(f"  {key}: {value}")
                    
                    # Calculate durations if we have the expected keys
                    expected_keys = ['t0_entry_ns', 't1_preproc_done_ns', 't2_contours_done_ns', 't3_tess_start_ns', 't4_tess_done_ns']
                    if all(k in result for k in expected_keys):
                        print("\n⏱️  PHASE DURATIONS:")
                        t0 = result['t0_entry_ns']
                        t1 = result['t1_preproc_done_ns']
                        t2 = result['t2_contours_done_ns']
                        t3 = result['t3_tess_start_ns']
                        t4 = result['t4_tess_done_ns']
                        
                        opencv_preproc_ms = (t1 - t0) / 1_000_000
                        opencv_contours_ms = (t2 - t1) / 1_000_000
                        tesseract_setup_ms = (t3 - t2) / 1_000_000
                        tesseract_ocr_ms = (t4 - t3) / 1_000_000
                        total_ms = (t4 - t0) / 1_000_000
                        
                        print(f"  OpenCV Preprocessing: {opencv_preproc_ms:.2f}ms")
                        print(f"  OpenCV Contours:     {opencv_contours_ms:.2f}ms")
                        print(f"  Tesseract Setup:     {tesseract_setup_ms:.2f}ms")
                        print(f"  Tesseract OCR:       {tesseract_ocr_ms:.2f}ms")
                        print(f"  Total C++ Time:      {total_ms:.2f}ms")
                        
                        # Performance breakdown
                        opencv_total = opencv_preproc_ms + opencv_contours_ms
                        tesseract_total = tesseract_setup_ms + tesseract_ocr_ms
                        
                        print(f"\n🎯 PERFORMANCE BREAKDOWN:")
                        print(f"  OpenCV:    {opencv_total:.2f}ms ({opencv_total/total_ms*100:.1f}%)")
                        print(f"  Tesseract: {tesseract_total:.2f}ms ({tesseract_total/total_ms*100:.1f}%)")
                        
                    else:
                        print(f"❌ Missing expected timing keys!")
                        print(f"   Expected: {expected_keys}")
                        print(f"   Found: {timing_keys}")
                else:
                    print("❌ No timing data returned from C++ module!")
                
                # Show other result data
                if 'text' in result:
                    text = result['text'].strip()
                    print(f"\n📝 OCR Text: '{text[:100]}{'...' if len(text) > 100 else ''}'")
                
                if 'confidence' in result:
                    print(f"🎯 Confidence: {result['confidence']:.1f}%")
            
            return True
            
        else:
            print(f"❌ Invalid image format: {frame.shape}")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import C++ accelerator: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_cpp_timing()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}")
    sys.exit(0 if success else 1)