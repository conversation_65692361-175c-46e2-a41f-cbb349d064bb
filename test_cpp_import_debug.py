#!/usr/bin/env python3
"""
Debug script to test C++ module import.
"""

import sys
import os

def test_cpp_import():
    """Debug C++ module import issues."""
    
    print("=== C++ Module Import Debug ===")
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print(f"Current working directory: {os.getcwd()}")
    
    # Add the Release directory to path
    release_dir = "/mnt/c/TESTRADE/ocr_accelerator/x64/Release"
    if release_dir not in sys.path:
        sys.path.insert(0, release_dir)
        print(f"Added to path: {release_dir}")
    
    # Check if .pyd file exists
    pyd_path = os.path.join(release_dir, "ocr_accelerator.pyd")
    print(f"PYD file exists: {os.path.exists(pyd_path)}")
    if os.path.exists(pyd_path):
        stat = os.stat(pyd_path)
        print(f"PYD file size: {stat.st_size} bytes")
    
    # Check DLL dependencies
    dll_files = [f for f in os.listdir(release_dir) if f.endswith('.dll')]
    print(f"DLL files in Release directory: {len(dll_files)}")
    for dll in sorted(dll_files):
        print(f"  {dll}")
    
    # Test various import methods
    print("\n=== Testing Import Methods ===")
    
    # Method 1: Standard import
    print("1. Standard import:")
    try:
        # Remove any existing module to force reload
        if 'ocr_accelerator' in sys.modules:
            del sys.modules['ocr_accelerator']
        
        import ocr_accelerator
        print("   SUCCESS: Module imported")
        print(f"   Module file: {getattr(ocr_accelerator, '__file__', 'No __file__')}")
        print(f"   Module attributes: {[attr for attr in dir(ocr_accelerator) if not attr.startswith('_')]}")
        print(f"   Has process_image_and_ocr: {hasattr(ocr_accelerator, 'process_image_and_ocr')}")
        
        # Try to call the function with dummy data
        if hasattr(ocr_accelerator, 'process_image_and_ocr'):
            import numpy as np
            test_image = np.ones((50, 100, 3), dtype=np.uint8) * 255
            print("   Testing function call...")
            try:
                result = ocr_accelerator.process_image_and_ocr(
                    test_image, 1.0, True, 1.0, 25, -2, 1.0, 1.0,
                    False, 100, 5, 5, False, 20, (0.5, 1.5), 3, 2.0, 3
                )
                print(f"   Function call SUCCESS: {type(result)}")
                if isinstance(result, dict):
                    print(f"   Result keys: {list(result.keys())}")
            except Exception as func_err:
                print(f"   Function call FAILED: {func_err}")
        
    except Exception as e:
        print(f"   FAILED: {e}")
        import traceback
        traceback.print_exc()
    
    # Method 2: Import from specific path
    print("\n2. Import from specific path:")
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("ocr_accelerator_direct", pyd_path)
        if spec and spec.loader:
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            print("   SUCCESS: Direct import worked")
            print(f"   Module attributes: {[attr for attr in dir(module) if not attr.startswith('_')]}")
        else:
            print("   FAILED: Could not create spec or loader")
    except Exception as e:
        print(f"   FAILED: {e}")

if __name__ == "__main__":
    test_cpp_import()