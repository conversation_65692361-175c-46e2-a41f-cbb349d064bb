"""
Function Tracer Interface

This module provides a unified interface for function tracing utilities.
It conditionally imports either the real function tracer or a dummy implementation
based on a global configuration setting.

Usage:
    from utils.function_tracer.interface import trace_function
    
    @trace_function('module_name')
    def my_function():
        pass
"""

import os
import logging
import importlib.util
from typing import Dict, List, Optional, Any, Union
from contextlib import contextmanager

# Set up logging
logger = logging.getLogger(__name__)

# Try to import global config to check if function tracing should be enabled
try:
    from utils.global_config import config as global_app_config
    # Check if the enable_trace_decorators setting exists
    _use_real_function_tracer = getattr(global_app_config, 'enable_trace_decorators', False)
except Exception as e:
    logger.error(f"Error importing global_config or accessing config: {e}")
    _use_real_function_tracer = False

# Check if we're in diagnostic mode
try:
    import sys
    main_module = sys.modules.get('__main__')
    if main_module and hasattr(main_module, 'FUNCTION_TRACING_FORCED_OFF'):
        logger.error("DIAGNOSTIC MODE DETECTED: Function tracing FORCED OFF")
        _use_real_function_tracer = False
except Exception as e:
    logger.error(f"Error checking for diagnostic mode: {e}")

# Log the decision
if _use_real_function_tracer:
    logger.info("Using REAL function tracer module")
else:
    logger.info("Using DUMMY function tracer module")

# Import the appropriate module based on the configuration
if _use_real_function_tracer:
    try:
        # Import the real function tracer
        from .function_tracer_impl import (
            trace_function,
            enable_function_tracing,
            is_function_tracing_enabled,
            cleanup_trace_context,
            setup_function_trace_logging,
            check_file_size,
            rotate_trace_file,
            init_trace_file
        )
        
        # Explicitly enable the real function tracer
        enable_function_tracing(True)
        logger.info("Real function_tracer module loaded and ENABLED")
    except ImportError as e:
        logger.error(f"Error importing real function tracer: {e}")
        # Fall back to dummy implementation
        _use_real_function_tracer = False
        
if not _use_real_function_tracer:
    try:
        # Import the dummy function tracer
        from .dummy_function_tracer import (
            trace_function,
            enable_function_tracing,
            is_function_tracing_enabled,
            cleanup_trace_context,
            setup_function_trace_logging,
            check_file_size,
            rotate_trace_file,
            init_trace_file
        )
        
        logger.info("DUMMY function_tracer module loaded. Real function tracing is OFF")
    except ImportError as e:
        logger.error(f"Error importing dummy function tracer: {e}")
        # Define minimal fallback implementations
        import functools
        
        def trace_function(module_name):
            """Minimal fallback implementation of trace_function."""
            def decorator(func):
                @functools.wraps(func)
                def wrapper(*args, **kwargs):
                    return func(*args, **kwargs)
                return wrapper
            return decorator
            
        def enable_function_tracing(enabled=True):
            """Minimal fallback implementation of enable_function_tracing."""
            return False
            
        def is_function_tracing_enabled():
            """Minimal fallback implementation of is_function_tracing_enabled."""
            return False
            
        def cleanup_trace_context():
            """Minimal fallback implementation of cleanup_trace_context."""
            pass
            
        def setup_function_trace_logging(module_name):
            """Minimal fallback implementation of setup_function_trace_logging."""
            return logging.getLogger("dummy_function_trace")
            
        def check_file_size(file_path):
            """Minimal fallback implementation of check_file_size."""
            return False
            
        def rotate_trace_file():
            """Minimal fallback implementation of rotate_trace_file."""
            pass
            
        def init_trace_file():
            """Minimal fallback implementation of init_trace_file."""
            pass
        
        logger.error("FALLBACK dummy function tracer functions defined")

# Add a global function to update the GlobalConfig with the function tracing setting
def update_global_config_with_function_tracing(enabled=True):
    """
    Update the global config with the function tracing setting.
    
    Args:
        enabled: True to enable function tracing, False to disable it
    """
    try:
        from utils.global_config import config, save_global_config
        
        # Create a new attribute if it doesn't exist
        if not hasattr(config, 'enable_trace_decorators'):
            config.enable_trace_decorators = enabled
        else:
            config.enable_trace_decorators = enabled
        
        # Save the updated config
        save_global_config(config)
        logger.info(f"Updated global config with enable_trace_decorators={enabled}")
        
        return True
    except Exception as e:
        logger.error(f"Error updating global config with function tracing setting: {e}")
        return False
