"""
Task NATIVE-ISOLATE2: Test with OpenCV (cv2) Import Prevented

This script runs a modified version of the application without the GUI,
without the Alpaca REST client, and without importing cv2 to determine
if OpenCV's native libraries are the source of the memory leak.

It will:
1. Patch sys.modules to prevent cv2 from being imported
2. Initialize the application components (except GUI, Alpaca REST client, and OpenCV)
3. Run for 15 minutes
4. Take a tracemalloc snapshot
5. Report memory usage

Usage:
    python scripts\native_isolate2_test_v2.py
"""

import os
import sys
import time
import logging
import tracemalloc
import datetime
import psutil
import threading
import importlib.util
from pathlib import Path
import builtins

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"native_isolate2_test_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

# Configure a file handler for the logs
file_handler = logging.FileHandler(log_file, mode="w", encoding="utf-8")
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter("[%(asctime)s] [%(levelname)-8s] [%(name)s] %(message)s")
file_handler.setFormatter(file_formatter)

# Configure a console handler for the logs
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter("[%(levelname)-8s] %(message)s")
console_handler.setFormatter(console_formatter)

# Get the root logger and add the handlers
root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG)
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

# Create a logger for this script
logger = logging.getLogger("native_isolate2_test")

# Constants
WAIT_DURATION_MINUTES = 15
SNAPSHOT_INTERVAL_MINUTES = 5

# Store the original import function
original_import = builtins.__import__

# Create a stub cv2 module
class StubCV2:
    """Stub implementation of cv2 module to prevent loading OpenCV's native libraries."""
    
    # Constants
    COLOR_BGR2GRAY = 6
    COLOR_RGB2BGR = 4
    COLOR_RGBA2BGR = 2
    COLOR_GRAY2BGR = 8
    COLOR_BGR2RGB = 4
    COLOR_GRAY2RGB = 8
    COLOR_BGR2HSV = 40
    COLOR_HSV2BGR = 54
    INTER_CUBIC = 2
    INTER_AREA = 3
    INTER_NEAREST = 0
    THRESH_BINARY = 0
    THRESH_OTSU = 8
    ADAPTIVE_THRESH_GAUSSIAN_C = 1
    RETR_CCOMP = 1
    RETR_EXTERNAL = 0
    CHAIN_APPROX_SIMPLE = 2
    FILLED = -1
    
    # VideoWriter constants
    VideoWriter_fourcc = lambda *args: 0
    
    def __init__(self):
        logger.info("NATIVE-ISOLATE2: Initialized stub cv2 module")
    
    def resize(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.resize")
        # Return the input image unchanged
        return args[0] if args else None
    
    def cvtColor(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.cvtColor")
        # Return the input image unchanged
        return args[0] if args else None
    
    def GaussianBlur(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.GaussianBlur")
        # Return the input image unchanged
        return args[0] if args else None
    
    def addWeighted(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.addWeighted")
        # Return the first input image unchanged
        return args[0] if args else None
    
    def adaptiveThreshold(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.adaptiveThreshold")
        # Return a binary version of the input image
        img = args[0] if args else None
        if img is not None:
            import numpy as np
            return np.ones_like(img) * 255
        return None
    
    def threshold(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.threshold")
        # Return a binary version of the input image
        img = args[0] if args else None
        if img is not None:
            import numpy as np
            return 0, np.ones_like(img) * 255
        return 0, None
    
    def findContours(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.findContours")
        # Return empty contours and hierarchy
        return [], None
    
    def boundingRect(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.boundingRect")
        # Return dummy bounding rectangle
        return 0, 0, 10, 10
    
    def contourArea(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.contourArea")
        # Return dummy area
        return 100
    
    def moments(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.moments")
        # Return dummy moments
        return {"m00": 1, "m10": 5, "m01": 5}
    
    def circle(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.circle")
        # Do nothing
        pass
    
    def rectangle(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.rectangle")
        # Do nothing
        pass
    
    def drawContours(self, *args, **kwargs):
        logger.debug("NATIVE-ISOLATE2: Called stub cv2.drawContours")
        # Do nothing
        pass
    
    class VideoWriter:
        def __init__(self, *args, **kwargs):
            logger.debug("NATIVE-ISOLATE2: Called stub cv2.VideoWriter.__init__")
        
        def isOpened(self):
            logger.debug("NATIVE-ISOLATE2: Called stub cv2.VideoWriter.isOpened")
            return True
        
        def write(self, *args, **kwargs):
            logger.debug("NATIVE-ISOLATE2: Called stub cv2.VideoWriter.write")
            pass
        
        def release(self):
            logger.debug("NATIVE-ISOLATE2: Called stub cv2.VideoWriter.release")
            pass

# Custom import function to prevent cv2 from being imported
def custom_import(name, globals=None, locals=None, fromlist=(), level=0):
    if name == 'cv2':
        logger.info(f"NATIVE-ISOLATE2: Prevented import of cv2")
        return sys.modules.get('cv2', StubCV2())
    return original_import(name, globals, locals, fromlist, level)

def log_memory_usage():
    """Log current memory usage and network I/O."""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    logger.info(f"Memory usage: RSS={memory_info.rss / (1024 * 1024):.2f} MB, VMS={memory_info.vms / (1024 * 1024):.2f} MB")
    
    # Log network I/O
    try:
        net_io_counters = psutil.net_io_counters()
        logger.info(f"Network I/O: Bytes sent={net_io_counters.bytes_sent / (1024):.2f} KB, Bytes received={net_io_counters.bytes_recv / (1024):.2f} KB")
    except Exception as e:
        logger.error(f"Failed to get network I/O counters: {e}")

def take_tracemalloc_snapshot(snapshot_dir):
    """Take a tracemalloc snapshot and save it to a file."""
    snapshot = tracemalloc.take_snapshot()
    snapshot_path = os.path.join(snapshot_dir, f"tracemalloc_snapshot_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pickle")
    snapshot.dump(snapshot_path)
    logger.info(f"Tracemalloc snapshot saved to: {snapshot_path}")
    
    # Log top 20 statistics
    logger.info("Top 20 memory allocations by size:")
    top_stats = snapshot.statistics('lineno')
    for i, stat in enumerate(top_stats[:20], 1):
        frame = stat.traceback[0]
        filename = os.path.basename(frame.filename)
        logger.info(f"#{i}: {filename}:{frame.lineno}: {stat.size / 1024:.1f} KiB")
        logger.info(f"    {frame.filename}:{frame.lineno}")

def monitor_memory_usage(stop_event, snapshot_dir):
    """Monitor memory usage at regular intervals."""
    start_time = time.time()
    last_snapshot_time = start_time
    
    # Initialize network I/O counters for rate calculation
    try:
        last_net_io = psutil.net_io_counters()
        last_net_io_time = time.time()
    except Exception as e:
        logger.error(f"Failed to initialize network I/O counters: {e}")
        last_net_io = None
        last_net_io_time = None
    
    while not stop_event.is_set():
        current_time = time.time()
        elapsed_minutes = (current_time - start_time) / 60
        
        # Log memory usage every minute
        log_memory_usage()
        
        # Calculate and log network I/O rate
        if last_net_io is not None:
            try:
                current_net_io = psutil.net_io_counters()
                time_diff = current_time - last_net_io_time
                
                # Calculate rates in KB/s
                sent_rate = (current_net_io.bytes_sent - last_net_io.bytes_sent) / (1024 * time_diff)
                recv_rate = (current_net_io.bytes_recv - last_net_io.bytes_recv) / (1024 * time_diff)
                
                logger.info(f"Network I/O Rate: Sent={sent_rate:.2f} KB/s, Received={recv_rate:.2f} KB/s")
                
                # Update for next iteration
                last_net_io = current_net_io
                last_net_io_time = current_time
            except Exception as e:
                logger.error(f"Failed to calculate network I/O rate: {e}")
        
        # Take a tracemalloc snapshot every SNAPSHOT_INTERVAL_MINUTES
        if (current_time - last_snapshot_time) / 60 >= SNAPSHOT_INTERVAL_MINUTES:
            take_tracemalloc_snapshot(snapshot_dir)
            last_snapshot_time = current_time
        
        # Sleep for 60 seconds
        time.sleep(60)

def main():
    """Main function to run the application without GUI, without Alpaca REST client, and without cv2."""
    logger.info("Starting native_isolate2_test_v2.py")
    logger.info(f"Logs will be saved to: {log_file}")
    
    # Create directory for tracemalloc snapshots
    snapshot_dir = os.path.join(log_dir, f"native_isolate2_test_snapshots_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}")
    os.makedirs(snapshot_dir, exist_ok=True)
    logger.info(f"Tracemalloc snapshots will be saved to: {snapshot_dir}")
    
    # Start tracemalloc
    tracemalloc.start()
    logger.info("Tracemalloc started for memory tracking")
    
    # Patch the import function to prevent cv2 from being imported
    builtins.__import__ = custom_import
    
    # Create a stub cv2 module and add it to sys.modules
    sys.modules['cv2'] = StubCV2()
    logger.info("NATIVE-ISOLATE2: Successfully patched import system to prevent cv2 from being imported")
    
    # Verify that the Lightspeed DLL folder has been moved
    try:
        from config import CPP_EXTENSION_DIR, CPP_DLL_PATH
        
        if os.path.exists(CPP_EXTENSION_DIR):
            logger.error(f"NATIVE-ISOLATE2: Lightspeed DLL folder still exists at {CPP_EXTENSION_DIR}")
            logger.error("NATIVE-ISOLATE2: Please move the folder out of the directory before running this test")
            return
        else:
            logger.info(f"NATIVE-ISOLATE2: Lightspeed DLL folder has been moved out of {CPP_EXTENSION_DIR}")
        
        if os.path.exists(CPP_DLL_PATH):
            logger.error(f"NATIVE-ISOLATE2: Lightspeed DLL still exists at {CPP_DLL_PATH}")
            logger.error("NATIVE-ISOLATE2: Please move the DLL out of the directory before running this test")
            return
        else:
            logger.info(f"NATIVE-ISOLATE2: Lightspeed DLL has been moved out of {CPP_DLL_PATH}")
    except Exception as e:
        logger.error(f"Failed to verify Lightspeed DLL folder: {e}")
    
    # Disable performance tracking
    try:
        from utils.performance_tracker import enable_performance_tracking
        enable_performance_tracking(False)
        logger.info("Performance tracking explicitly disabled")
    except ImportError:
        logger.warning("Could not import performance_tracker module")
    
    # Import necessary modules
    try:
        from main import setup_main_logging
        from utils.global_config import load_global_config, GlobalConfig
        from utils.symbol_loader import load_symbols_from_csv
        from modules.price_fetching.price_repository import PriceRepository
        from modules.price_fetching.price_fetching_service import PriceFetchingService
        from modules.risk_management.risk_service import RiskManagementService
        from modules.order_management.order_repository import OrderRepository
        from modules.trade_management.position_manager import PositionManager
        
        logger.info("Successfully imported all required modules")
    except ImportError as e:
        logger.error(f"Failed to import required modules: {e}")
        return
    
    # Set up logging
    setup_main_logging()
    logger.info("Set up main logging")
    
    # Load global config
    try:
        from utils.global_config import CONFIG_FILE_PATH
        config = load_global_config(CONFIG_FILE_PATH)
        logger.info("Loaded global config")
        
        # Ensure price fetching service and lightspeed broker are disabled
        config.disable_price_fetching_service = True
        config.disable_lightspeed_broker = True
        logger.info("Ensured price fetching service and lightspeed broker are disabled in config")
    except Exception as e:
        logger.error(f"Failed to load global config: {e}")
        return
    
    # Load symbols
    try:
        from config import TRADEABLE_SYMBOLS_CSV
        load_symbols_from_csv(TRADEABLE_SYMBOLS_CSV, fix_bom=True, force_reload=True)
        logger.info(f"Loaded symbols from {TRADEABLE_SYMBOLS_CSV}")
    except Exception as e:
        logger.error(f"Failed to load symbols: {e}")
        return
    
    # Initialize application components
    try:
        # Initialize OrderRepository
        order_repository = OrderRepository(broker_service=None)
        logger.info("Initialized OrderRepository")
        
        # Initialize PositionManager
        position_manager = PositionManager()
        logger.info("Initialized PositionManager")
        
        # Set position_manager in OrderRepository
        order_repository.set_position_manager(position_manager)
        logger.info("Set position_manager in OrderRepository")
        
        # Initialize PriceRepository without REST client
        price_repository = PriceRepository(
            risk_service=None,
            rest_client=None,  # Set to None for NATIVE-ISOLATE2 test
            config_service=config
        )
        logger.info("Initialized PriceRepository without REST client")
        
        # Initialize RiskManagementService
        risk_service = RiskManagementService(
            config_service=config,
            price_provider=price_repository,
            order_repository=order_repository,
            position_manager=position_manager,
            gui_logger_func=None
        )
        logger.info("Initialized RiskManagementService")
    except Exception as e:
        logger.error(f"Failed to initialize application components: {e}")
    
    # Log initial memory usage
    logger.info("Initial memory usage:")
    log_memory_usage()
    
    # Start memory monitoring thread
    stop_event = threading.Event()
    memory_monitor_thread = threading.Thread(
        target=monitor_memory_usage,
        args=(stop_event, snapshot_dir),
        daemon=True,
        name="MemoryMonitorThread"
    )
    memory_monitor_thread.start()
    logger.info("Started memory monitoring thread")
    
    # Wait for specified duration
    logger.info(f"Waiting for {WAIT_DURATION_MINUTES} minutes...")
    wait_end_time = time.time() + (WAIT_DURATION_MINUTES * 60)
    
    try:
        while time.time() < wait_end_time:
            remaining_minutes = int((wait_end_time - time.time()) / 60)
            remaining_seconds = int((wait_end_time - time.time()) % 60)
            logger.info(f"Remaining time: {remaining_minutes} minutes {remaining_seconds} seconds")
            time.sleep(60)  # Update every minute
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    
    # Stop memory monitoring thread
    stop_event.set()
    memory_monitor_thread.join(timeout=5)
    logger.info("Stopped memory monitoring thread")
    
    # Take final tracemalloc snapshot
    logger.info("Taking final tracemalloc snapshot...")
    take_tracemalloc_snapshot(snapshot_dir)
    
    # Log final memory usage
    logger.info("Final memory usage:")
    log_memory_usage()
    
    logger.info("Done.")
    logger.info(f"Logs saved to: {log_file}")
    logger.info(f"Tracemalloc snapshots saved to: {snapshot_dir}")

if __name__ == "__main__":
    main()
