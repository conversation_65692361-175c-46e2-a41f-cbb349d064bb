#!/usr/bin/env python3
"""
Minimal test to check C++ module status
"""

import os
import sys
import platform

# Only test the absolute minimum
if platform.system() == "Windows":
    print("=== Windows C++ Module Test ===")
    
    # Add module path
    module_path = os.path.abspath("ocr_accelerator/x64/Release")
    sys.path.insert(0, module_path)
    
    # Change directory for DLL loading
    original_cwd = os.getcwd()
    os.chdir(module_path)
    
    try:
        import ocr_accelerator
        print("✅ Import successful")
        
        # Check what's available
        items = [item for item in dir(ocr_accelerator) if not item.startswith('_')]
        print(f"Available items: {items}")
        
        # Check if functions exist
        for func in ['test_function', 'process_image_and_ocr']:
            exists = hasattr(ocr_accelerator, func)
            print(f"{func}: {'✅' if exists else '❌'}")
            
            if exists and func == 'test_function':
                try:
                    result = ocr_accelerator.test_function()
                    print(f"  test_function() = {result}")
                except Exception as e:
                    print(f"  Error calling test_function: {e}")
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        os.chdir(original_cwd)
else:
    print("This test requires Windows")