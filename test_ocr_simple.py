#!/usr/bin/env python3
"""Simple test to check if OCR services can be resolved"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.dependency_injection import DIContainer
from core.di_registration import register_all_services

# Create DI container
print("Creating DI container...")
container = DIContainer()

# Register all services
print("Registering services...")
register_all_services(container)
print("✅ Service registration complete")

# Test OCR services
print("\nTesting OCR services:")

# Test 1: IOCRProcessManager
try:
    from interfaces.ocr.services import IOCRProcessManager
    ocr_pm = container.resolve(IOCRProcessManager)
    print(f"✅ IOCRProcessManager: {type(ocr_pm).__name__}")
except Exception as e:
    print(f"❌ IOCRProcessManager: {e}")

# Test 2: IOCRDataConditioningService  
try:
    from interfaces.ocr.services import IOCRDataConditioningService
    ocr_dc = container.resolve(IOCRDataConditioningService)
    print(f"✅ IOCRDataConditioningService: {type(ocr_dc).__name__}")
except Exception as e:
    print(f"❌ IOCRDataConditioningService: {e}")

# Test 3: IOCRScalpingSignalOrchestratorService
try:
    from interfaces.ocr.services import IOCRScalpingSignalOrchestratorService
    ocr_orch = container.resolve(IOCRScalpingSignalOrchestratorService)
    print(f"✅ IOCRScalpingSignalOrchestratorService: {type(ocr_orch).__name__}")
except Exception as e:
    print(f"❌ IOCRScalpingSignalOrchestratorService: {e}")

print("\nTest complete!")