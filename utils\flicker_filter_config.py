"""
flicker_filter_config.py

Step 1: Define the user-configurable parameters for the flicker filter
so we can easily toggle or adjust them without rewriting code.

This file does NOT contain the actual flicker-check logic—just the config
schema (e.g., a dataclass) and possibly a load_config function if you
retrieve settings from JSON.
"""

import json
from dataclasses import dataclass
from typing import Optional

@dataclass
class FlickerFilterParams:
    """
    Holds all user-configurable settings for the flicker filter.
    Adjust as needed in your global config file or code.
    """

    enable_flicker_filter: bool = True
    """
    Master toggle to enable or disable flicker filtering entirely.
    If False, all new cost-basis values are accepted without checks.
    """

    known_add_shares: int = 10000
    """
    Hypothetical number of shares the trader might add at the new fill price.
    We assume the old cost basis is from old_shares, and we add 'known_add_shares'
    to get the new cost basis. 
    """

    time_sync_offset_sec: float = 0.0
    """
    If you want to offset your time-based lookups by a certain number of seconds,
    used in a function like get_time_synced_price(rolling_data, current_time - offset).
    Set to 0.0 if you don't need this.
    """

    accept_tolerance_cents: float = 5.0
    """
    Tolerance in cents. For example, 5.0 => ±$0.05 difference allowed between
    the computed Weighted Average cost basis and the OCR read.
    """

    recheck_count: int = 0
    """
    How many extra attempts to re-check the new cost basis if it fails the
    first time. e.g., 1 => total 2 attempts.
    If set to 0 => no retries (one-pass).
    """

    recheck_delay_sec: float = 0.2
    """
    How many seconds to sleep between re-check attempts if recheck_count > 0.
    Typically 0.1-0.3s is enough to get a new OCR frame.
    """

    price_check_mode: str = "synced_alpaca_price"
    """
    Which price to use for Weighted Avg feasibility:
      "trader_live_price"    => Use the trader's cost-basis + PnL if you have it
      "synced_alpaca_price" => A time-synced local feed price
      "alpaca_live_price"   => The direct subscription-based price
    """


# ---------------------------------------------------------------------
# Optional: A load function if you store these params in JSON
# ---------------------------------------------------------------------
def load_flicker_config(path: str) -> FlickerFilterParams:
    """
    Example load function if you have a flicker_filter section in a JSON file.
    """
    with open(path, "r") as f:
        data = json.load(f)

    # Suppose your JSON has a "flicker_filter" object with the relevant fields:
    flicker_data = data.get("flicker_filter", {})

    # Construct FlickerFilterParams from the dictionary,
    # using the same keys as the dataclass fields:
    return FlickerFilterParams(
        enable_flicker_filter=flicker_data.get("enable_flicker_filter", True),
        known_add_shares=flicker_data.get("known_add_shares", 10000),
        time_sync_offset_sec=flicker_data.get("time_sync_offset_sec", 0.0),
        accept_tolerance_cents=flicker_data.get("accept_tolerance_cents", 5.0),
        recheck_count=flicker_data.get("recheck_count", 0),
        recheck_delay_sec=flicker_data.get("recheck_delay_sec", 0.2),
        price_check_mode=flicker_data.get("price_check_mode", "synced_alpaca_price")
    )


"""
Usage example:

from utils.flicker_filter_config import load_flicker_config, FlickerFilterParams

# If you have a global config JSON with a "flicker_filter" section:
flicker_params = load_flicker_config("my_global_config.json")

# Or if you just want a default:
flicker_params = FlickerFilterParams()

print(flicker_params.accept_tolerance_cents)  # e.g. 5.0 => ±$0.05
"""