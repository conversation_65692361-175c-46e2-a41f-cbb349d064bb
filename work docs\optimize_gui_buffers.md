# Optimizing GUI Backend MMAP Buffer Sizes

## Current Situation
- GUI Backend uses same 7GB buffer configuration as Core
- GUI only sends small command messages (< 1KB each)
- Massive overkill for GUI's actual needs

## Solution: Configure Smaller Buffers for GUI

### Option 1: Override in GUI Backend Code
In `gui_backend.py`, when creating the BulletproofBabysitterIPCClient:

```python
# Define lightweight buffer sizes for GUI (in GB)
gui_socket_overrides = {
    'bulk': {'mmap_gb': 0.1},    # 100MB instead of 4GB
    'system': {'mmap_gb': 0.05},  # 50MB instead of 1GB  
    'trading': {'mmap_gb': 0.1}   # 100MB instead of 2GB
}

app_state.babysitter_ipc_client = BulletproofBabysitterIPCClient(
    zmq_context=zmq_context,
    ipc_config=app_state.config,
    logger_instance=logger,
    mission_control_notifier=mission_notifier,
    socket_config_overrides=gui_socket_overrides  # Add this parameter
)
```

### Option 2: Add GUI-Specific Config
In `utils/global_config.py`, add:

```python
# GUI-specific IPC buffer sizes (much smaller than Core)
IPC_MMAP_BULK_BUFFER_GB_GUI: float = 0.1    # 100MB for GUI
IPC_MMAP_SYSTEM_BUFFER_GB_GUI: float = 0.05  # 50MB for GUI
IPC_MMAP_TRADING_BUFFER_GB_GUI: float = 0.1  # 100MB for GUI
```

Then detect in GUI and use these values.

### Option 3: Environment-Based Config
Create separate config files:
- `config.json` - Standard config with 7GB buffers for Core
- `config_gui.json` - GUI config with 250MB total buffers

## Recommended Approach

**Option 1 is best** because:
1. No config file changes needed
2. GUI explicitly declares its lightweight needs
3. Clear in code that GUI uses smaller buffers
4. Still shares the same buffer files (just smaller allocation)

## Benefits of Smaller GUI Buffers
1. **Faster startup** - Less memory to map
2. **Lower memory usage** - 250MB vs 7GB
3. **Same functionality** - GUI doesn't need huge buffers
4. **Better resource allocation** - More memory for actual trading

## Important Note
Even with smaller buffer sizes, GUI will still:
- Share the same MMAP files as Core (due to our path fix)
- Be able to send all commands successfully
- Have sufficient buffer space (250MB is still huge for commands)

## Implementation Warning
If implementing Option 1, ensure the GUI process starts AFTER Core, otherwise it might create the MMAP files with smaller sizes and Core would fail to map the full size it expects.