# tank_gui.ps1 - TANK Tools GUI Interface
Add-Type -AssemblyName PresentationFramework
Add-Type -AssemblyName PresentationCore
Add-Type -AssemblyName WindowsBase

# XAML for the GUI
$xaml = @"
<Window xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="TANK Tools Control Center" Height="600" Width="800"
        WindowStartupLocation="CenterScreen" ResizeMode="CanResize">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="200"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2E3440" Padding="10">
            <StackPanel>
                <TextBlock Text="TANK Tools Control Center" FontSize="20" FontWeight="Bold"
                          Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="Real-time monitoring and analysis for TESTRADE ApplicationCore"
                          FontSize="12" Foreground="#D8DEE9" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- Main Tools Grid -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Quick Actions Row -->
                <TextBlock Grid.Row="0" Grid.ColumnSpan="3" Text="Quick Actions" FontSize="16" FontWeight="Bold"
                          Foreground="#5E81AC" Margin="0,0,0,10"/>

                <Button Grid.Row="1" Grid.Column="0" Name="btnQuickDemo" Content="Quick Demo (3 min)"
                       Height="50" Margin="5" Background="#A3BE8C" Foreground="White" FontWeight="Bold"/>
                <Button Grid.Row="1" Grid.Column="1" Name="btnMultiMonitor" Content="Multi-Process Monitor"
                       Height="50" Margin="5" Background="#5E81AC" Foreground="White" FontWeight="Bold"/>
                <Button Grid.Row="1" Grid.Column="2" Name="btnLeakDetect" Content="Leak Detection"
                       Height="50" Margin="5" Background="#BF616A" Foreground="White" FontWeight="Bold"/>

                <!-- Memory Monitoring Row -->
                <TextBlock Grid.Row="2" Grid.ColumnSpan="3" Text="Memory Monitoring" FontSize="14" FontWeight="Bold"
                          Foreground="#5E81AC" Margin="0,20,0,10"/>

                <Button Grid.Row="3" Grid.Column="0" Name="btnMemoryMonitor" Content="Memory Monitor"
                       Height="40" Margin="5" Background="#88C0D0"/>
                <Button Grid.Row="3" Grid.Column="1" Name="btnBulletproof" Content="Bulletproof Monitor"
                       Height="40" Margin="5" Background="#88C0D0"/>
                <Button Grid.Row="3" Grid.Column="2" Name="btnAnalyzer" Content="Data Analyzer"
                       Height="40" Margin="5" Background="#88C0D0"/>

                <!-- Baseline & Advanced Row -->
                <TextBlock Grid.Row="4" Grid.ColumnSpan="3" Text="Baseline & Advanced" FontSize="14" FontWeight="Bold"
                          Foreground="#5E81AC" Margin="0,20,0,10"/>

                <Button Grid.Row="5" Grid.Column="0" Name="btnBaseline" Content="Baseline Compare"
                       Height="40" Margin="5" Background="#EBCB8B"/>
                <Button Grid.Row="5" Grid.Column="1" Name="btnCreateBaseline" Content="Create Baseline"
                       Height="40" Margin="5" Background="#EBCB8B"/>
                <Button Grid.Row="5" Grid.Column="2" Name="btnProcessTest" Content="Process Test"
                       Height="40" Margin="5" Background="#D08770"/>
            </Grid>
        </ScrollViewer>
        
        <!-- Output/Feedback Area -->
        <Border Grid.Row="2" Background="#3B4252" BorderBrush="#4C566A" BorderThickness="1" Margin="10,0,10,10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <Border Grid.Row="0" Background="#2E3440" Padding="5">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="Output & Feedback" FontWeight="Bold" Foreground="White"/>
                        <Button Name="btnClearOutput" Content="Clear" Margin="10,0,0,0" Padding="5,2"
                               Background="#BF616A" Foreground="White" BorderThickness="0"/>
                    </StackPanel>
                </Border>
                
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="5">
                    <TextBlock Name="txtOutput" Text="Welcome to TANK Tools Control Center!&#x0A;Click any button to run monitoring tools..." 
                              Foreground="#D8DEE9" FontFamily="Consolas" FontSize="11" TextWrapping="Wrap"/>
                </ScrollViewer>
            </Grid>
        </Border>
    </Grid>
</Window>
"@

# Create the window
$reader = [System.Xml.XmlNodeReader]::new([xml]$xaml)
$window = [Windows.Markup.XamlReader]::Load($reader)

# Get controls
$btnQuickDemo = $window.FindName("btnQuickDemo")
$btnMultiMonitor = $window.FindName("btnMultiMonitor")
$btnLeakDetect = $window.FindName("btnLeakDetect")
$btnMemoryMonitor = $window.FindName("btnMemoryMonitor")
$btnBulletproof = $window.FindName("btnBulletproof")
$btnAnalyzer = $window.FindName("btnAnalyzer")
$btnBaseline = $window.FindName("btnBaseline")
$btnCreateBaseline = $window.FindName("btnCreateBaseline")
$btnProcessTest = $window.FindName("btnProcessTest")
$btnClearOutput = $window.FindName("btnClearOutput")
$txtOutput = $window.FindName("txtOutput")

# Function to add output with timestamp
function Add-Output {
    param([string]$Message, [string]$Color = "Info")

    $timestamp = Get-Date -Format "HH:mm:ss"
    $prefix = switch ($Color) {
        "Success" { "[OK]" }
        "Warning" { "[WARN]" }
        "Error" { "[ERROR]" }
        "Info" { "[INFO]" }
        default { "[LOG]" }
    }

    $newText = "$prefix [$timestamp] $Message`n" + $txtOutput.Text
    $txtOutput.Text = $newText

    # Auto-scroll to top
    $txtOutput.BringIntoView()
}

# Function to run PowerShell script with feedback
function Start-ToolScript {
    param([string]$ScriptName, [string]$Arguments = "", [string]$Description)
    
    Add-Output "Starting $Description..." "Info"
    
    try {
        # Check if script exists
        $scriptPath = Join-Path $PSScriptRoot $ScriptName
        if (-not (Test-Path $scriptPath)) {
            Add-Output "Script not found: $ScriptName" "Error"
            return
        }
        
        Add-Output "Launching: $ScriptName $Arguments" "Info"
        
        # Start the script in a new PowerShell window
        $processArgs = "-ExecutionPolicy Bypass -WindowStyle Normal -File `"$scriptPath`" $Arguments"
        Start-Process "powershell.exe" -ArgumentList $processArgs -WorkingDirectory $PSScriptRoot
        
        Add-Output "Successfully launched $Description" "Success"
        Add-Output "Check the new PowerShell window for output" "Info"
        
    } catch {
        Add-Output "Error launching $Description`: $($_.Exception.Message)" "Error"
    }
}

# Button event handlers
$btnQuickDemo.Add_Click({
    Start-ToolScript "tank_monitor_demo_simple.ps1" "-ProcessName python -DemoMinutes 3" "Quick Demo (3 minutes)"
})

$btnMultiMonitor.Add_Click({
    Start-ToolScript "tank_multi_launcher_simple.ps1" "-Mode monitor" "Multi-Process Monitor"
})

$btnLeakDetect.Add_Click({
    Start-ToolScript "tank_leak_quickstart.ps1" "-ProcessName python" "Leak Detection"
})

$btnMemoryMonitor.Add_Click({
    Start-ToolScript "tank_memory_monitor.ps1" "-ProcessName python" "Memory Monitor"
})

$btnBulletproof.Add_Click({
    Start-ToolScript "tank_bulletproof_monitor.ps1" "-ProcessName python" "Bulletproof Monitor"
})

$btnAnalyzer.Add_Click({
    Start-ToolScript "tank_monitor_analyzer.ps1" "" "Data Analyzer"
})

$btnBaseline.Add_Click({
    Start-ToolScript "tank_baseline_simple.ps1" "-ProcessName python -MonitorMinutes 10" "Baseline Comparison"
})

$btnCreateBaseline.Add_Click({
    Start-ToolScript "tank_baseline_launcher.ps1" "-Mode create-single -ProcessName python" "Create Baseline"
})

$btnProcessTest.Add_Click({
    Start-ToolScript "test_python_processes.ps1" "" "Process Detection Test"
})

$btnClearOutput.Add_Click({
    $txtOutput.Text = "Output cleared.`n"
})

# Window loaded event
$window.Add_Loaded({
    Add-Output "TANK Tools Control Center initialized" "Success"
    Add-Output "Ready to launch monitoring tools" "Info"
    
    # Check for Python processes
    try {
        $processes = Get-Process python -ErrorAction SilentlyContinue
        if ($processes.Count -gt 0) {
            $totalMemoryMB = [math]::Round(($processes | Measure-Object WorkingSet64 -Sum).Sum / 1MB, 2)
            Add-Output "Detected $($processes.Count) Python processes ($totalMemoryMB MB total)" "Success"
        } else {
            Add-Output "No Python processes detected - some tools may not work" "Warning"
        }
    } catch {
        Add-Output "Could not check for Python processes" "Warning"
    }
})

# Show the window
Add-Output "Launching TANK Tools GUI..." "Info"
$window.ShowDialog() | Out-Null
