# testrade_project_root/utils/redis_utils.py
import json
import time
import logging
from typing import Optional, Dict, Any
from utils.thread_safe_uuid import get_thread_safe_uuid

logger_helper = logging.getLogger(__name__) # If module level

def create_redis_message_json(
    payload: Dict[str, Any],
    event_type_str: str,
    source_component_name: str,
    correlation_id_val: Optional[str] = None,
    causation_id_val: Optional[str] = None,
    origin_timestamp_s: Optional[float] = None
) -> str:
    """
    Creates a standardized JSON string for a Redis Stream message with Golden Timestamp support.

    Args:
        payload: The actual event data dictionary.
        event_type_str: Specific event type string (e.g., "TESTRADE_RAW_OCR_DATA").
        source_component_name: Name of the TESTRADE component publishing this.
        correlation_id_val: The master correlation ID (OCR-originated if applicable).
                            For events not in an OCR flow (e.g., market data), this might be None or a temp ID.
        causation_id_val: Optional ID of the event that directly caused this one.
        origin_timestamp_s: Golden Timestamp - when the data originally occurred (optional).

    Returns:
        A JSON string representing the full Redis message.
    """
    if correlation_id_val is None and "ORDER" in event_type_str: # Be more specific for order-related events
        logger_helper.warning(f"Master correlationId is None for critical eventType '{event_type_str}' from '{source_component_name}'. This will break tracing. A new UUID will be used as fallback for metadata.correlationId.")
        # For critical events, ensure *something* is in correlationId if master is lost.
        # However, the primary goal is that master_correlation_id *should not* be None for these.
        # This fallback helps ensure the message has the field, but indicates a problem upstream.
        correlation_id_to_use = get_thread_safe_uuid() # Fallback, but signifies an issue
    else:
        correlation_id_to_use = correlation_id_val

    message_wrapper = {
        "metadata": {
            "eventId": get_thread_safe_uuid(), # Unique ID for this specific Redis message
            "correlationId": correlation_id_to_use,
            "causationId": causation_id_val,
            "timestamp_ns": time.perf_counter_ns(), # High-precision timestamp of this message creation
            "origin_timestamp_s": origin_timestamp_s or time.time(), # THE GOLDEN TIMESTAMP
            "epoch_timestamp_s": time.time(), # Wall-clock time for general reference
            "eventType": event_type_str,
            "sourceComponent": source_component_name
        },
        "payload": payload
    }
    try:
        return json.dumps(message_wrapper)
    except TypeError as e:
        logger_helper.error(f"Failed to serialize message to JSON for eventType '{event_type_str}': {e}. Payload keys: {list(payload.keys()) if isinstance(payload, dict) else 'N/A'}", exc_info=True)
        # Fallback: try to serialize a simpler error message
        error_payload = {
            "error_type": "SerializationError",
            "original_event_type": event_type_str,
            "original_correlation_id": correlation_id_val,
            "message": f"Failed to serialize original payload: {str(e)}"
        }
        error_wrapper = {
             "metadata": {
                "eventId": get_thread_safe_uuid(),
                "correlationId": correlation_id_val, # Try to preserve original correlationId
                "timestamp_ns": time.perf_counter_ns(),
                "epoch_timestamp_s": time.time(),
                "eventType": "TESTRADE_SERIALIZATION_ERROR", # Special event type for this error
                "sourceComponent": source_component_name
            },
            "payload": error_payload
        }
        return json.dumps(error_wrapper)
