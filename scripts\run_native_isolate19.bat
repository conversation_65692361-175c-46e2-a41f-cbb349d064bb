@echo off
echo ========================================================
echo Running NATIVE-ISOLATE19 Memory Leak Test
echo ========================================================
echo This test will run for 15 minutes to detect memory leaks
echo in the configuration loading system.
echo.
echo Test specifically checks if importing config.py and
echo utils.global_config.py and calling load_global_config()
echo causes memory leaks.
echo.
echo Started at: %date% %time%
echo ========================================================

mkdir "mem leak" 2>nul

python scripts/run_native_isolate19.py

echo ========================================================
echo Test completed at: %date% %time%
echo Results can be found in the 'mem leak' directory.
echo ========================================================
pause
