#include <pybind11/pybind11.h>
#include <pybind11/numpy.h>
#include <pybind11/stl.h>

namespace py = pybind11;

// Minimal test function without any external dependencies
py::dict test_function() {
    py::dict result;
    result["status"] = "success";
    result["message"] = "Minimal test function works";
    return result;
}

// Module definition
PYBIND11_MODULE(test_minimal, m) {
    m.doc() = "Minimal test module";
    m.def("test_function", &test_function, "Simple test function");
}