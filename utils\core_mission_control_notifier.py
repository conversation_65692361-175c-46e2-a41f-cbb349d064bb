#!/usr/bin/env python3
"""
CoreMissionControlNotifier Implementation

A concrete implementation of IMissionControlNotifier that:
1. Logs alerts locally for immediate visibility
2. Publishes alerts to <PERSON><PERSON> via BulletproofIPCClient for dashboard consumption
3. Integrates seamlessly with ApplicationCore's messaging infrastructure

This provides comprehensive monitoring and alerting for BulletproofIPCClient operations.
"""

import logging
import time
import json
from typing import Optional, Dict, Any, TYPE_CHECKING

# Import the interface - adjust path as needed based on your project structure
try:
    from core.bulletproof_ipc_client import IMissionControlNotifier
except ImportError:
    # Fallback interface definition if import fails
    class IMissionControlNotifier:
        """Mission Control Notifier Interface"""
        def notify_event(self, event_type: str, severity: str, message: str, details: Optional[Dict] = None) -> None:
            raise NotImplementedError

# Type checking imports to avoid circular dependencies
if TYPE_CHECKING:
    from application_core import ApplicationCore


class CoreMissionControlNotifier(IMissionControlNotifier):
    """
    Core Mission Control Notifier for TESTRADE ApplicationCore.
    
    Handles notifications from BulletproofIPCClient by:
    1. Logging alerts locally with appropriate severity levels
    2. Publishing alerts to <PERSON><PERSON> for dashboard/monitoring consumption
    3. Providing resilient error handling to prevent notification loops
    """
    
    def __init__(self, logger_instance: logging.Logger, telemetry_service: 'ITelemetryService', config_service: 'IConfigService'):
        """
        Initialize the Core Mission Control Notifier.
        
        Args:
            logger_instance: Logger for local alert logging
            telemetry_service: TelemetryService for Redis publishing
            config_service: ConfigService for accessing configuration
        """
        self.logger = logger_instance
        self._telemetry_service = telemetry_service
        self._config_service = config_service
        
        # Configuration for alert publishing
        self.alert_stream_name = getattr(
            config_service, 
            'redis_stream_ipc_alerts', 
            'testrade:alerts:ipc'
        )
        
        # Track alert publishing stats for monitoring
        self.alerts_logged = 0
        self.alerts_published = 0
        self.alerts_failed_publish = 0
        
        self.logger.info(f"CoreMissionControlNotifier initialized. Alert stream: {self.alert_stream_name}")
    
    
    def notify_event(self, event_type: str, severity: str, message: str, details: Optional[Dict] = None) -> None:
        """
        Handle a mission control notification event.
        
        Args:
            event_type: Type of event (e.g., "ZMQ_CHANNEL_CLOGGED")
            severity: Severity level ("CRITICAL", "ERROR", "WARNING", "INFO")
            message: Human-readable message
            details: Optional additional details dictionary
        """
        # 1. Log the alert locally for immediate visibility
        self._log_alert_locally(event_type, severity, message, details)
        
        # 2. Publish alert to Redis for dashboard consumption
        self._publish_alert_to_redis(event_type, severity, message, details)
    
    def _log_alert_locally(self, event_type: str, severity: str, message: str, details: Optional[Dict] = None) -> None:
        """Log alert locally with appropriate severity level."""
        log_entry = f"IPC_ALERT ({severity}) - Type: [{event_type}], Msg: [{message}]"
        if details:
            # Sanitize details for logging (avoid huge payloads)
            sanitized_details = self._sanitize_details_for_logging(details)
            log_entry += f", Details: {json.dumps(sanitized_details)}"
        
        # Log at appropriate level
        if severity == "CRITICAL":
            self.logger.critical(log_entry)
        elif severity == "ERROR":
            self.logger.error(log_entry)
        elif severity == "WARNING":
            self.logger.warning(log_entry)
        else:
            self.logger.info(log_entry)
        
        self.alerts_logged += 1
    
    def _publish_alert_to_redis(self, event_type: str, severity: str, message: str, details: Optional[Dict] = None) -> None:
        """Publish alert to Redis via TelemetryService."""
        if not self._telemetry_service:
            self.logger.warning(f"TelemetryService not available. Cannot publish alert '{event_type}' to Redis.")
            return
        
        try:
            # Create alert payload
            alert_payload = {
                "alert_timestamp": time.time(),
                "alert_type": event_type,
                "severity": severity,
                "message": message,
                "details": details or {},
                "source_component": "CoreMissionControlNotifier",
                "alert_id": f"{event_type}_{int(time.time() * 1000)}"  # Unique alert ID
            }
            
            # Extract correlation ID from details if provided
            alert_correlation_id = details.get("correlation_id_for_alert") if details else None
            
            # Publish via telemetry service
            success = self._telemetry_service.enqueue(
                source_component="CoreMissionControlNotifier",
                event_type="TESTRADE_IPC_ALERT",
                payload=alert_payload,
                stream_override=self.alert_stream_name
            )
            
            if success:
                self.alerts_published += 1
                self.logger.debug(f"Published IPC alert '{event_type}' to stream '{self.alert_stream_name}'")
            else:
                self.alerts_failed_publish += 1
                self.logger.warning(f"Failed to publish IPC alert '{event_type}' to Redis (TelemetryService.enqueue returned False)")
                
        except Exception as e:
            self.alerts_failed_publish += 1
            self.logger.error(f"Exception publishing alert '{event_type}' to Redis: {e}", exc_info=True)
    
    def _sanitize_details_for_logging(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize details dictionary for logging to avoid huge log entries."""
        sanitized = {}
        for key, value in details.items():
            if isinstance(value, str) and len(value) > 200:
                sanitized[key] = value[:200] + "...[truncated]"
            elif isinstance(value, (dict, list)) and len(str(value)) > 500:
                sanitized[key] = f"[{type(value).__name__} with {len(value)} items]"
            else:
                sanitized[key] = value
        return sanitized
    
    def get_notifier_stats(self) -> Dict[str, Any]:
        """Get statistics about alert handling."""
        return {
            "alerts_logged": self.alerts_logged,
            "alerts_published": self.alerts_published,
            "alerts_failed_publish": self.alerts_failed_publish,
            "alert_stream_name": self.alert_stream_name,
            "telemetry_service_available": self._telemetry_service is not None
        }
