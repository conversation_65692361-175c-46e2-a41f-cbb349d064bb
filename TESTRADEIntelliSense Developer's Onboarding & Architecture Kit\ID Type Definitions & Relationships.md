# ID Type Definitions & Relationships

## Purpose
This document defines all identifier types used throughout the TESTRADE system, their purposes, scopes, and relationships. Understanding these IDs is critical for proper event correlation, debugging, and IntelliSense analysis.

---

## ID Type Definitions

| ID Type | Purpose | Scope | Relationships | Generation |
|---------|---------|-------|---------------|------------|
| **Master Correlation ID** | Top-level ID for entire trading sessions or major operations | System-wide (core → Redis → consumers) | Parent to all other IDs in a session | `application_core.py` at session start |
| **Event ID** | Unique identifier for individual events | Internal to ApplicationCore (EventBus communication) | Child of Master Correlation ID | Via `get_thread_safe_uuid()` for each event |
| **Correlation ID (Redis)** | Trace events across system boundaries | Redis streams (core → babysitter → Redis → consumers) | Propagates Master Correlation ID to external systems | `_create_redis_message_json()` using Master Correlation ID |
| **Causation ID** | Track event chains (A → B → C) | EventBus handlers within ApplicationCore | Points to parent Event ID | Automatically set when events trigger new events |
| **Local Order ID** | TESTRADE's internal order identifier | Order management lifecycle (core services) | Maps to Broker Order ID | During order creation process |
| **Broker Order ID** | Broker-assigned order identifier | Broker communication layer | Maps to Local Order ID | By Lightspeed broker |
| **Ephemeral Correlation ID** | Short-lived broker operation tracking | Broker adapter internal operations | Ties broker requests to responses | By broker adapter for internal tracking |
| **LCM Trade ID** | Track multi-order trade lifecycles | Trade management services | Aggregates multiple Order IDs | During trade initialization |
| **Position UUID** | Unique position identifier | Position tracking services | References related Trade/Order IDs | When positions are opened |
| **Validation ID** | Track data validation pipelines | OCR/data validation services | References source Event ID | During validation initiation |
| **Broker Raw Message Correlation ID** | Correlate raw broker messages | Broker communication layer | Tied to Ephemeral Correlation ID | `lightspeed_broker.py` during message handling |

---

## Correlation System Flow

```mermaid
graph LR
    A[Master Correlation ID] --> B[Event ID]
    A --> C[Correlation ID Redis]
    B --> D[Causation ID]
    B --> E[Validation ID]
    B --> F[Local Order ID]
    F --> G[Broker Order ID]
    F --> H[LCM Trade ID]
    H --> I[Position UUID]
    G --> J[Ephemeral Correlation ID]
    J --> K[Broker Raw Message Correlation ID]
```

---

## Key Architectural Insights

### 1. **Hierarchy**
Master Correlation ID → Event ID → Domain-specific IDs (Order/Trade/Position)

### 2. **Boundary Crossing**
Only Correlation ID (Redis) propagates outside ApplicationCore per:
> **Section 3: The Two Event Buses**  
> "The External Bus (Redis)... is the ONLY way data leaves the TANK"

### 3. **Lifecycle Tracking**
Trade/Position IDs maintain relationships across:
- Order creation (Local Order ID → Broker Order ID)
- Trade assembly (Order IDs → LCM Trade ID)  
- Position management (Trade IDs → Position UUID)

### 4. **Broker-Specific IDs**
Ephemeral and Raw Message IDs exist solely within broker adapter scope

---

## Where to Verify

### 1. **ID Propagation**
Check `core/bulletproof_ipc_client.py` for Redis message wrapping:
```python
# core/bulletproof_ipc_client.py
def _create_redis_message_json(data):
    return {
        "correlationId": master_correlation_id,  # Propagated here
        "eventId": str(uuid.uuid4()),
        "payload": data
    }
```

### 2. **Event Chaining**
Inspect `core/event_bus.py` for causation handling:
```python
# core/event_bus.py
def publish(self, event):
    event.causation_id = last_event_id  # Set causation
    # ...
```

### 3. **Order Mapping**
Examine `services/order_manager.py` for ID translation:
```python
# services/order_manager.py
def create_order(self):
    local_id = generate_order_id()
    broker_id = self.broker_adapter.send_order(local_id)
    self.id_map[local_id] = broker_id  # ID mapping
```

---

## Critical Implementation Notes

### Master Correlation ID Generation
- **Location**: `application_core.py:1010`
- **Code**: `master_correlation_id = str(uuid.uuid4())`
- **Trigger**: Raw OCR image capture events
- **Propagation**: Flows through entire pipeline via Redis Correlation ID

### Event ID Threading
- **Safety**: Uses `get_thread_safe_uuid()` to prevent collision
- **Scope**: Internal EventBus communication only
- **Relationship**: Child of Master Correlation ID in same session

### Broker ID Mapping
- **Challenge**: Broker generates own IDs independent of TESTRADE
- **Solution**: Local Order ID ↔ Broker Order ID mapping in OrderRepository
- **Fallback**: Ephemeral Correlation ID for orphaned broker messages

---

## IntelliSense Correlation Requirements

For complete event tracing, IntelliSense needs:

1. **Master Correlation ID**: End-to-end session tracking
2. **Correlation ID (Redis)**: Cross-boundary event linking  
3. **Causation ID**: Event chain reconstruction
4. **Local/Broker Order ID mapping**: Order lifecycle correlation
5. **Timestamp correlation**: Temporal event sequencing when ID chains break

### Verification Checklist
- [ ] Master Correlation ID flows from OCR → Redis streams
- [ ] Event IDs link causation chains within ApplicationCore
- [ ] Correlation ID (Redis) preserves Master Correlation ID
- [ ] Order IDs map correctly: Local ↔ Broker
- [ ] Trade/Position IDs reference parent Order IDs
- [ ] Broker messages correlate via Ephemeral IDs

---

## Reference Files

For precise ID metadata schemas, check:
- `interfaces/trading/IOrderManager.py`
- `interfaces/core/IMessageMetadata.py`
- `core/events.py` - Event data structures
- `modules/order_management/order_data_types.py` - Order ID fields
- `utils/thread_safe_uuid.py` - UUID generation utilities