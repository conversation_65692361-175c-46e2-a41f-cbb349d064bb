#!/usr/bin/env python3
"""
<PERSON>ript to clean and rebuild the C++ OCR accelerator module
"""

import os
import sys
import subprocess
import platform

def run_command(cmd, cwd=None):
    """Run a command and return success status"""
    try:
        print(f"Running: {cmd}")
        if cwd:
            print(f"  in directory: {cwd}")
        
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        if result.stdout:
            print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("Command timed out!")
        return False
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def main():
    if platform.system() != "Windows":
        print("❌ This script must be run on Windows")
        return False
    
    # Find Visual Studio
    vs_paths = [
        r"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe",
        r"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\devenv.exe",
        r"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\devenv.exe",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\IDE\devenv.exe",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\devenv.exe",
    ]
    
    vs_exe = None
    for path in vs_paths:
        if os.path.exists(path):
            vs_exe = path
            break
    
    if not vs_exe:
        print("❌ Visual Studio not found")
        return False
    
    print(f"✅ Found Visual Studio: {vs_exe}")
    
    # Check if project exists
    project_dir = "ocr_accelerator"
    project_file = os.path.join(project_dir, "ocr_accelerator.vcxproj")
    
    if not os.path.exists(project_file):
        print(f"❌ Project file not found: {project_file}")
        return False
    
    print(f"✅ Found project: {project_file}")
    
    # Clean first
    print("\n=== CLEANING PROJECT ===")
    clean_cmd = f'"{vs_exe}" "{project_file}" /Clean "Release|x64"'
    if not run_command(clean_cmd):
        print("⚠️ Clean failed, continuing anyway...")
    
    # Build
    print("\n=== BUILDING PROJECT ===")
    build_cmd = f'"{vs_exe}" "{project_file}" /Build "Release|x64"'
    if not run_command(build_cmd):
        print("❌ Build failed!")
        return False
    
    # Check output
    pyd_file = os.path.join(project_dir, "x64", "Release", "ocr_accelerator.pyd")
    if os.path.exists(pyd_file):
        size = os.path.getsize(pyd_file)
        print(f"✅ Build successful! PYD file: {size:,} bytes")
        return True
    else:
        print("❌ Build completed but no PYD file found!")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nResult: {'SUCCESS' if success else 'FAILED'}")
    sys.exit(0 if success else 1)