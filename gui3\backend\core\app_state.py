"""
Application state management for GUI backend.
"""

import asyncio
import logging
import redis.asyncio as redis
from typing import Dict, Any, Optional, List
from ..utils.websocket_manager import WebSocketManager


class AppState:
    """
    Central application state following Horseshoe Architecture.
    This is a Consumer - only reads from <PERSON><PERSON>, never writes.
    """
    
    def __init__(self):
        self.logger = logging.getLogger("GUI.AppState")
        self.redis_client: Optional[redis.Redis] = None
        self.redis_connected = False
        self.ws_manager = WebSocketManager()
        self.message_router = None
        self.running = False
        
        # Redis configuration
        self.redis_config = {
            'host': 'localhost',
            'port': 6379,
            'decode_responses': True
        }
        
        # Streams to monitor (as a consumer)
        self.monitored_streams = [
            'testrade:raw-ocr-snapshots',
            'testrade:cleaned-ocr-snapshots',
            'testrade:position-updates',
            'testrade:account-updates',
            'testrade:order-events',
            'testrade:trade-lifecycle',
            'testrade:risk-events',
            'testrade:market-data',
            'testrade:maf-decisions',
            'testrade:gui-command-responses'  # For command responses
        ]
        
        # Command handler for request/response pattern
        from .command_handler import CommandHandler
        self.command_handler = CommandHandler(self)
        
    async def connect_redis(self) -> bool:
        """Connect to Redis."""
        try:
            self.redis_client = await redis.Redis(**self.redis_config)
            await self.redis_client.ping()
            self.redis_connected = True
            self.logger.info("Connected to Redis")
            return True
        except Exception as e:
            self.logger.error(f"Failed to connect to Redis: {e}")
            self.redis_connected = False
            return False
    
    async def start_redis_consumer(self):
        """
        Main Redis consumer loop.
        Reads from multiple streams and routes messages to handlers.
        """
        if not await self.connect_redis():
            self.logger.error("Cannot start Redis consumer without connection")
            return
        
        self.running = True
        last_ids = {stream: '$' for stream in self.monitored_streams}
        
        while self.running:
            try:
                # Read from multiple streams
                messages = await self.redis_client.xread(
                    streams=last_ids,
                    block=1000,  # Block for 1 second
                    count=10
                )
                
                # Process messages
                for stream_name, stream_messages in messages:
                    for message_id, data in stream_messages:
                        # Update last ID
                        last_ids[stream_name] = message_id
                        
                        # Route to appropriate handler
                        if self.message_router:
                            await self.message_router.route_message(
                                stream_name,
                                {'id': message_id, 'data': data}
                            )
                
            except asyncio.CancelledError:
                self.logger.info("Redis consumer cancelled")
                break
            except Exception as e:
                self.logger.error(f"Error in Redis consumer: {e}", exc_info=True)
                await asyncio.sleep(5)  # Wait before retrying
    
    async def send_command(self, command: str, data: Dict[str, Any]) -> Optional[str]:
        """
        Send a command to producers via command stream.
        Following Horseshoe pattern - consumers can only send commands.
        """
        if not self.redis_connected:
            self.logger.error("Cannot send command - Redis not connected")
            return None
        
        try:
            # Commands go to a dedicated command stream
            command_data = {
                'command': command,
                'data': data,
                'timestamp': asyncio.get_event_loop().time(),
                'source': 'gui_backend'
            }
            
            # Send to command stream
            message_id = await self.redis_client.xadd(
                'testrade:gui-commands',
                command_data
            )
            
            self.logger.info(f"Sent command {command} with ID {message_id}")
            return message_id
            
        except Exception as e:
            self.logger.error(f"Failed to send command: {e}")
            return None
    
    async def shutdown(self):
        """Clean shutdown of application state."""
        self.running = False
        
        if self.redis_client:
            await self.redis_client.close()
            
        self.logger.info("AppState shutdown complete")