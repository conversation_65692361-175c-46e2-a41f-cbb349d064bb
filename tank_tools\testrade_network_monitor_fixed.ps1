# testrade_network_monitor.ps1 - Monitor network output from TESTRADE
param(
    [string]$ProcessName = "python",
    [int]$IntervalSeconds = 5,
    [int]$MonitorMinutes = 60,
    [switch]$ShowDetails
)

# Track network statistics
class NetworkMonitor {
    [System.Collections.ArrayList]$Measurements
    [datetime]$StartTime
    [hashtable]$PreviousCounters
    
    NetworkMonitor() {
        $this.Measurements = New-Object System.Collections.ArrayList
        $this.StartTime = Get-Date
        $this.PreviousCounters = @{}
    }
}

# Function to find TESTRADE processes
function Get-TESTRADEProcesses {
    param([string]$ProcessName)
    
    try {
        $testradeProcesses = @()
        $processes = Get-Process $ProcessName -ErrorAction SilentlyContinue
        
        if ($processes) {
            foreach ($proc in $processes) {
                try {
                    $commandLine = (Get-CimInstance Win32_Process -Filter "ProcessId = $($proc.Id)" -ErrorAction SilentlyContinue).CommandLine
                    if ($commandLine -and ($commandLine -like "*testrade*" -or $commandLine -like "*ApplicationCore*" -or $commandLine -like "*run_headless_core*" -or $commandLine -like "*babysitter*")) {
                        $testradeProcesses += [PSCustomObject]@{
                            ProcessId = $proc.Id
                            ProcessName = $proc.ProcessName
                            CommandLine = $commandLine
                            WorkingSet = $proc.WorkingSet64
                        }
                    }
                } catch {
                    # Skip processes we can't access
                }
            }
        }
        return $testradeProcesses
    } catch {
        return @()
    }
}

# Function to get network connections for specific processes
function Get-ProcessNetworkConnections {
    param([array]$ProcessIds)
    
    try {
        $connections = @()
        $netstatOutput = netstat -ano 2>$null
        
        foreach ($line in $netstatOutput) {
            if ($line -match "^\s*(TCP|UDP)\s+(\S+)\s+(\S+)\s+(\S+)\s+(\d+)$") {
                $protocol = $matches[1]
                $localAddress = $matches[2]
                $remoteAddress = $matches[3]
                $state = $matches[4]
                $processId = [int]$matches[5]
                
                if ($processId -in $ProcessIds) {
                    $connections += [PSCustomObject]@{
                        Protocol = $protocol
                        LocalAddress = $localAddress
                        RemoteAddress = $remoteAddress
                        State = $state
                        ProcessId = $processId
                    }
                }
            }
        }
        return $connections
    } catch {
        return @()
    }
}

# Function to get network interface statistics
function Get-NetworkInterfaceStats {
    try {
        $interfaces = Get-Counter "\Network Interface(*)\Bytes Sent/sec", "\Network Interface(*)\Bytes Received/sec" -ErrorAction SilentlyContinue
        $stats = @{}
        
        foreach ($sample in $interfaces.CounterSamples) {
            $interfaceName = $sample.InstanceName
            if ($interfaceName -ne "_Total" -and $interfaceName -notlike "*Loopback*" -and $interfaceName -notlike "*isatap*") {
                if (-not $stats.ContainsKey($interfaceName)) {
                    $stats[$interfaceName] = @{ Sent = 0; Received = 0 }
                }
                
                if ($sample.Path -like "*Bytes Sent/sec*") {
                    $stats[$interfaceName].Sent = $sample.CookedValue
                } elseif ($sample.Path -like "*Bytes Received/sec*") {
                    $stats[$interfaceName].Received = $sample.CookedValue
                }
            }
        }
        return $stats
    } catch {
        return @{}
    }
}

# Function to analyze Redis/ZMQ ports
function Get-TESTRADENetworkActivity {
    param([array]$Connections)
    
    $redisConnections = $Connections | Where-Object { 
        $_.LocalAddress -like "*:6379*" -or $_.RemoteAddress -like "*:6379*" 
    }
    
    $zmqConnections = $Connections | Where-Object { 
        $_.LocalAddress -like "*:555*" -or $_.RemoteAddress -like "*:555*" -or
        $_.LocalAddress -like "*:556*" -or $_.RemoteAddress -like "*:556*"
    }
    
    $alpacaConnections = $Connections | Where-Object { 
        $_.RemoteAddress -like "*alpaca*" -or $_.RemoteAddress -like "*443*"
    }
    
    return [PSCustomObject]@{
        RedisConnections = $redisConnections.Count
        ZMQConnections = $zmqConnections.Count
        AlpacaConnections = $alpacaConnections.Count
        TotalConnections = $Connections.Count
        EstablishedConnections = ($Connections | Where-Object { $_.State -eq "ESTABLISHED" }).Count
        ListeningPorts = ($Connections | Where-Object { $_.State -eq "LISTENING" }).Count
    }
}

# Initialize monitor
$monitor = [NetworkMonitor]::new()
$endTime = (Get-Date).AddMinutes($MonitorMinutes)

Write-Host "🌐 TESTRADE Network Activity Monitor" -ForegroundColor Green
Write-Host "Process: $ProcessName (TESTRADE-related)" -ForegroundColor Cyan
Write-Host "Interval: $IntervalSeconds seconds" -ForegroundColor Cyan
Write-Host "Duration: $MonitorMinutes minutes" -ForegroundColor Cyan
Write-Host "=" * 80

while ((Get-Date) -lt $endTime) {
    try {
        # Find TESTRADE processes
        $testradeProcesses = Get-TESTRADEProcesses -ProcessName $ProcessName
        $processIds = $testradeProcesses | ForEach-Object { $_.ProcessId }
        
        # Get network connections
        $connections = @()
        if ($processIds.Count -gt 0) {
            $connections = Get-ProcessNetworkConnections -ProcessIds $processIds
        }
        
        # Get network interface stats
        $networkStats = Get-NetworkInterfaceStats
        $totalBytesSent = ($networkStats.Values | ForEach-Object { $_.Sent } | Measure-Object -Sum).Sum
        $totalBytesReceived = ($networkStats.Values | ForEach-Object { $_.Received } | Measure-Object -Sum).Sum
        
        # Analyze TESTRADE-specific network activity
        $testradeActivity = Get-TESTRADENetworkActivity -Connections $connections
        
        # Calculate rates
        $sentMBperSec = if ($totalBytesSent -gt 0) { [math]::Round($totalBytesSent / 1MB, 3) } else { 0 }
        $receivedMBperSec = if ($totalBytesReceived -gt 0) { [math]::Round($totalBytesReceived / 1MB, 3) } else { 0 }
        $sentKBperSec = if ($totalBytesSent -gt 0) { [math]::Round($totalBytesSent / 1KB, 1) } else { 0 }
        $receivedKBperSec = if ($totalBytesReceived -gt 0) { [math]::Round($totalBytesReceived / 1KB, 1) } else { 0 }
        
        # Store measurement
        $measurement = [PSCustomObject]@{
            Timestamp = Get-Date
            ElapsedMinutes = [math]::Round(((Get-Date) - $monitor.StartTime).TotalMinutes, 1)
            ProcessCount = $testradeProcesses.Count
            TotalConnections = $testradeActivity.TotalConnections
            EstablishedConnections = $testradeActivity.EstablishedConnections
            RedisConnections = $testradeActivity.RedisConnections
            ZMQConnections = $testradeActivity.ZMQConnections
            AlpacaConnections = $testradeActivity.AlpacaConnections
            SentMBperSec = $sentMBperSec
            ReceivedMBperSec = $receivedMBperSec
            SentKBperSec = $sentKBperSec
            ReceivedKBperSec = $receivedKBperSec
        }
        $null = $monitor.Measurements.Add($measurement)
        
        # Display current status
        Clear-Host
        Write-Host "🌐 TESTRADE Network Activity Monitor - Live Analysis" -ForegroundColor Green
        Write-Host "=" * 80
        Write-Host "Time: $($measurement.Timestamp.ToString('HH:mm:ss'))" -ForegroundColor Cyan
        Write-Host "Elapsed: $($measurement.ElapsedMinutes) minutes" -ForegroundColor Cyan
        Write-Host "TESTRADE Processes: $($measurement.ProcessCount)" -ForegroundColor $(if($measurement.ProcessCount -gt 0){'Green'}else{'Red'})
        Write-Host ""
        
        # Network activity summary
        Write-Host "📊 NETWORK ACTIVITY:" -ForegroundColor Yellow
        Write-Host "  Total Connections:      $($measurement.TotalConnections)" -ForegroundColor White
        Write-Host "  Established:            $($measurement.EstablishedConnections)" -ForegroundColor White
        Write-Host "  Redis Connections:      $($measurement.RedisConnections)" -ForegroundColor $(if($measurement.RedisConnections -gt 0){'Green'}else{'Gray'})
        Write-Host "  ZMQ Connections:        $($measurement.ZMQConnections)" -ForegroundColor $(if($measurement.ZMQConnections -gt 0){'Green'}else{'Gray'})
        Write-Host "  Alpaca Connections:     $($measurement.AlpacaConnections)" -ForegroundColor $(if($measurement.AlpacaConnections -gt 0){'Green'}else{'Gray'})
        Write-Host ""
        
        # Network throughput
        Write-Host "📈 NETWORK THROUGHPUT:" -ForegroundColor Yellow
        Write-Host "  Outbound:  $($measurement.SentMBperSec) MB/s ($($measurement.SentKBperSec) KB/s)" -ForegroundColor $(
            if ($measurement.SentMBperSec -gt 10) { "Red" }
            elseif ($measurement.SentMBperSec -gt 1) { "Yellow" }
            else { "Green" }
        )
        Write-Host "  Inbound:   $($measurement.ReceivedMBperSec) MB/s ($($measurement.ReceivedKBperSec) KB/s)" -ForegroundColor $(
            if ($measurement.ReceivedMBperSec -gt 10) { "Red" }
            elseif ($measurement.ReceivedMBperSec -gt 1) { "Yellow" }
            else { "Green" }
        )
        Write-Host ""
        
        # Show process details if requested
        if ($ShowDetails -and $testradeProcesses.Count -gt 0) {
            Write-Host "🔍 TESTRADE PROCESSES:" -ForegroundColor Yellow
            foreach ($proc in $testradeProcesses) {
                $memoryMB = [math]::Round($proc.WorkingSet / 1MB, 1)
                $procConnections = ($connections | Where-Object { $_.ProcessId -eq $proc.ProcessId }).Count
                Write-Host "  PID $($proc.ProcessId): $memoryMB MB, $procConnections connections" -ForegroundColor Gray
            }
            Write-Host ""
        }
        
        # Show connection details if requested
        if ($ShowDetails -and $connections.Count -gt 0) {
            Write-Host "🔗 ACTIVE CONNECTIONS (Top 10):" -ForegroundColor Yellow
            $connections | Select-Object -First 10 | ForEach-Object {
                Write-Host "  $($_.Protocol) $($_.LocalAddress) -> $($_.RemoteAddress) [$($_.State)]" -ForegroundColor Gray
            }
            Write-Host ""
        }
        
        # Running statistics
        if ($monitor.Measurements.Count -ge 3) {
            $recent = $monitor.Measurements | Select-Object -Last 5
            $avgSent = [math]::Round(($recent | Measure-Object SentMBperSec -Average).Average, 3)
            $avgReceived = [math]::Round(($recent | Measure-Object ReceivedMBperSec -Average).Average, 3)
            $avgConnections = [math]::Round(($recent | Measure-Object TotalConnections -Average).Average, 0)
            
            Write-Host "📊 RUNNING AVERAGES (last 5 intervals):" -ForegroundColor Yellow
            Write-Host "  Average Outbound:    $avgSent MB/s" -ForegroundColor White
            Write-Host "  Average Inbound:     $avgReceived MB/s" -ForegroundColor White
            Write-Host "  Average Connections: $avgConnections" -ForegroundColor White
            Write-Host ""
        }
        
        # Alerts
        if ($measurement.ProcessCount -eq 0) {
            Write-Host "⚠️  No TESTRADE processes detected" -ForegroundColor Yellow
        }
        
        if ($measurement.SentMBperSec -gt 50) {
            Write-Host "🚨 HIGH OUTBOUND TRAFFIC!" -ForegroundColor Red -BackgroundColor Yellow
        }
        
        if ($measurement.TotalConnections -gt 100) {
            Write-Host "⚠️  High connection count detected" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "Next measurement in $IntervalSeconds seconds... (Ctrl+C to stop)" -ForegroundColor Gray
        
        Start-Sleep $IntervalSeconds
        
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        Start-Sleep 5
    }
}

# Final summary
Write-Host ""
Write-Host "📋 NETWORK MONITORING SUMMARY:" -ForegroundColor Green
if ($monitor.Measurements.Count -gt 0) {
    $avgSent = ($monitor.Measurements | Measure-Object SentMBperSec -Average).Average
    $maxSent = ($monitor.Measurements | Measure-Object SentMBperSec -Maximum).Maximum
    $avgConnections = ($monitor.Measurements | Measure-Object TotalConnections -Average).Average
    $maxConnections = ($monitor.Measurements | Measure-Object TotalConnections -Maximum).Maximum
    
    Write-Host "Average Outbound: $([math]::Round($avgSent, 3)) MB/s" -ForegroundColor White
    Write-Host "Peak Outbound: $([math]::Round($maxSent, 3)) MB/s" -ForegroundColor White
    Write-Host "Average Connections: $([math]::Round($avgConnections, 0))" -ForegroundColor White
    Write-Host "Peak Connections: $maxConnections" -ForegroundColor White
}

Write-Host ""
Write-Host "Network monitoring completed successfully!" -ForegroundColor Green
