#pragma once
#if !defined(LS_LIGHTSPEEDTRADER_H)
#define LS_LIGHTSPEEDTRADER_H

// Copyright (c) 2001-2018 Lightspeed Financial, Inc. All rights reserved.

#define LSEXPORT extern "C" __declspec(dllexport)

// Functions of the following declaration are to be added to one of CPP files
// in the API client DLL project.  Though all are optional, at least
// LSInitInstance and LSExitInstance should appear in the DLL.
//
// LSEXPORT void LSInitInstance();
// LSEXPORT void LSExitInstance();
// LSEXPORT BOOL LSPreTranslateMessage(MSG *pMsg);
// LSEXPORT void LSEventLoopProc();

#include "L_Version.h"
#include "L_TimeUtil.h"
#include "L_Memory.h"
#include "L_MessageIds.h"
#include "L_Thread.h"
#include "L_Observer.h"
#include "L_Iterator.h"
#include "L_Sorter.h"
#include "L_DoubleUtil.h"
#include "L_Side.h"
#include "L_Symbols.h"
#include "L_Constants.h"
#include "L_TIF.h"
#include "L_Chart.h"
#include "L_BPType.h"
#include "L_OrderErrors.h"
#include "L_Execution.h"
#include "L_Order.h"
#include "L_Position.h"
#include "L_Account.h"
#include "L_Messages.h"
#include "L_FullQuote.h"
#include "L_Summary.h"
#include "L_Application.h"

#endif // !defined(LS_LIGHTSPEEDTRADER_H)

