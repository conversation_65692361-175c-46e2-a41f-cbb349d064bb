#!/usr/bin/env python3
"""
Test ApplicationCore startup in different modes
"""
import os
import sys
import logging
import time
import threading

# Add the TESTRADE root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.dependency_injection import DIContainer
from core.di_registration import register_all_services
from utils.testrade_modes import get_current_mode, TestradeMode
from interfaces.core.application import IApplicationCore as DI_ApplicationCore

# Configure logging to capture warnings
logging.basicConfig(
    level=logging.DEBUG,
    format='%(levelname)s:%(name)s:%(message)s'
)
logger = logging.getLogger(__name__)

# Track warnings
warnings_captured = []
original_warning = logging.Logger.warning

def capture_warning(self, msg, *args, **kwargs):
    """Capture warnings for analysis"""
    warnings_captured.append(f"{self.name}: {msg % args if args else msg}")
    original_warning(self, msg, *args, **kwargs)

# Monkey patch to capture warnings
logging.Logger.warning = capture_warning

def test_mode_startup(mode_name):
    """Test ApplicationCore startup in a specific mode"""
    global warnings_captured
    warnings_captured = []
    
    logger.info(f"\n{'='*60}")
    logger.info(f"Testing {mode_name} mode startup")
    logger.info(f"{'='*60}")
    
    os.environ['TESTRADE_MODE'] = mode_name
    
    try:
        # Create DI container and register services
        container = DIContainer()
        register_all_services(container)
        
        # Resolve ApplicationCore
        app_core = container.resolve(DI_ApplicationCore)
        logger.info(f"✓ ApplicationCore resolved successfully in {mode_name} mode")
        
        # Try to start it
        logger.info("Starting ApplicationCore...")
        app_core.start()
        
        # Give it a moment to initialize
        time.sleep(2)
        
        # Check if it's ready
        if hasattr(app_core, 'is_ready') and app_core.is_ready:
            logger.info(f"✓ ApplicationCore started and ready in {mode_name} mode")
        else:
            logger.info(f"✓ ApplicationCore started in {mode_name} mode")
            
        # Try to resolve some services that might cause warnings
        logger.info("\nTesting service resolutions...")
        
        # Test correlation logger
        from interfaces.logging.services import ICorrelationLogger
        try:
            correlation_logger = container.resolve(ICorrelationLogger)
            logger.info(f"✓ Correlation logger resolved: {type(correlation_logger).__name__}")
        except Exception as e:
            logger.error(f"✗ Failed to resolve correlation logger: {e}")
            
        # Test GUI Command Service (uses telemetry and IPC)
        try:
            gui_service = container.resolve('IGUICommandService')
            logger.info("✓ GUI Command Service resolved successfully")
        except Exception as e:
            logger.error(f"✗ Failed to resolve GUI Command Service: {e}")
            
        # Test System Health Service
        try:
            health_service = container.resolve('ISystemHealthMonitoringService')
            logger.info("✓ System Health Service resolved successfully")
        except Exception as e:
            logger.error(f"✗ Failed to resolve System Health Service: {e}")
            
        # Stop the application
        logger.info("\nStopping ApplicationCore...")
        if hasattr(app_core, 'stop'):
            app_core.stop()
            
    except Exception as e:
        logger.error(f"✗ Error during {mode_name} mode startup: {e}", exc_info=True)
        
    # Report warnings
    logger.info(f"\n{mode_name} Mode Warnings Summary:")
    logger.info(f"Total warnings: {len(warnings_captured)}")
    
    # Filter out expected warnings
    unexpected_warnings = []
    for warning in warnings_captured:
        # Filter out expected warnings
        if any(expected in warning for expected in [
            "EventBus not available",  # Expected in some services
            "Performance benchmarker not available",  # Optional dependency
            "ZMQ not available",  # Expected if ZMQ not installed
            "Mission control notifier not available",  # Expected without telemetry
            "OCRProcessManager not available",  # Optional dependency
            "ActiveSymbolsService returned no initial symbols",  # Expected without data
            "Alpaca REST client created",  # Info message
            "No tradeable symbols found in CSV",  # Expected without data
        ]):
            continue
            
        # Check for our target warnings - these should NOT appear with mode-aware DI
        if any(target in warning for target in [
            "Telemetry service required but not available",
            "Could not resolve IPC client",
            "IPC client required but not available",
            "ITelemetryService resolution FAILED",
            "IBulletproofBabysitterIPCClient resolution FAILED"
        ]):
            logger.error(f"✗ UNEXPECTED WARNING: {warning}")
            unexpected_warnings.append(warning)
        else:
            # Other warnings that might be of interest
            logger.debug(f"Other warning: {warning}")
            
    if unexpected_warnings:
        logger.error(f"✗ Found {len(unexpected_warnings)} unexpected warnings!")
        for warning in unexpected_warnings:
            logger.error(f"  - {warning}")
    else:
        logger.info("✓ No unexpected dependency resolution warnings!")
        
    return len(unexpected_warnings) == 0

def main():
    """Test all modes"""
    # Save original mode
    original_mode = os.environ.get('TESTRADE_MODE', '')
    
    results = {}
    
    try:
        # Test each mode
        for mode in ['TANK_SEALED', 'TANK_BUFFERED', 'LIVE']:
            results[mode] = test_mode_startup(mode)
            time.sleep(1)  # Brief pause between tests
            
    finally:
        # Restore original mode
        if original_mode:
            os.environ['TESTRADE_MODE'] = original_mode
        else:
            os.environ.pop('TESTRADE_MODE', None)
            
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("Mode-Aware DI Test Summary")
    logger.info(f"{'='*60}")
    
    all_passed = True
    for mode, passed in results.items():
        status = "✓ PASSED" if passed else "✗ FAILED"
        logger.info(f"{mode}: {status}")
        if not passed:
            all_passed = False
            
    if all_passed:
        logger.info("\n✓ All modes started without unexpected dependency warnings!")
    else:
        logger.error("\n✗ Some modes had unexpected warnings!")
        
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())