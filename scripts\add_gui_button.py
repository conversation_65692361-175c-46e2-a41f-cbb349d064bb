#!/usr/bin/env python3
"""
GUI Button Generator Script

This script automatically adds new buttons to the TESTRADE GUI with all the necessary
plumbing including HTML, CSS, JavaScript, and backend integration.

Usage:
    python scripts/add_gui_button.py --type config --name "Max Position Size" --config-key "maxPositionSize" --config-field "risk_max_position_size" --data-type int --default 2000
    python scripts/add_gui_button.py --type command --name "Toggle Recording" --command "toggle_recording_mode"
    python scripts/add_gui_button.py --type action --name "Export Data" --function "exportTradingData"
"""

import argparse
import json
import re
from pathlib import Path
from typing import Dict, Any, Optional


class GUIButtonGenerator:
    """Generates GUI buttons with complete integration."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.gui_html_path = self.project_root / "gui" / "portrait_trading_gui.html"
        self.gui_backend_path = self.project_root / "gui" / "gui_backend.py"
        self.command_service_path = self.project_root / "modules" / "gui_commands" / "gui_command_service.py"
        
    def generate_config_button(self, name: str, config_key: str, config_field: str, 
                             data_type: str, default_value: Any, section: str = "Configuration") -> Dict[str, str]:
        """Generate a configuration button that updates config values."""
        
        # Generate HTML form input
        input_type = "number" if data_type in ["int", "float"] else "text"
        html_input = f'''
                    <div class="form-group">
                        <label class="form-label">{name}</label>
                        <input type="{input_type}" class="form-input" value="{default_value}" id="{config_key}">
                    </div>'''
        
        # Generate backend handler
        backend_handler = f'''
            # Check for {config_key} format ({name.lower()} configuration)
            elif "{config_key}" in request:
                {config_key.lower()}_value = {data_type}(request.get("{config_key}", {default_value}))
                logger.info(f"GUI updating {name.lower()} to: {{config_key.lower()}_value}}")
                
                # Update the configuration
                try:
                    from utils.global_config import config, save_global_config
                    
                    # Update the in-memory config
                    config.{config_field} = {config_key.lower()}_value
                    
                    # Save to control.json
                    save_global_config(config)
                    
                    logger.info(f"✅ Successfully updated {config_field} to {{{config_key.lower()}_value}} and saved to control.json")
                    return {{"status": "success", "message": f"{name} updated to {{{config_key.lower()}_value}}"}}
                    
                except Exception as e:
                    logger.error(f"❌ Error updating {name.lower()}: {{e}}")
                    return {{"status": "error", "message": f"Failed to update {name.lower()}: {{e}}"}}'''
        
        # Generate JavaScript function (if needed for immediate UI updates)
        js_function = f'''
        function update{config_key.replace('_', '').title()}() {{
            const value = document.getElementById('{config_key}').value;
            updateConfig('{config_key}', value);
            addSystemMessage(`{name} updated to ${{value}}`, 'success');
        }}'''
        
        return {
            "html_input": html_input,
            "backend_handler": backend_handler,
            "js_function": js_function,
            "type": "config"
        }
    
    def generate_command_button(self, name: str, command: str, section: str = "System Controls", 
                              button_class: str = "btn") -> Dict[str, str]:
        """Generate a command button that sends commands to ApplicationCore."""
        
        function_name = f"{command.replace('_', '').lower()}"
        
        # Generate HTML button
        html_button = f'''
                        <button class="{button_class}" onclick="{function_name}()">{name}</button>'''
        
        # Generate JavaScript function
        js_function = f'''
        function {function_name}() {{
            sendCommand('{command}', {{}});
            addSystemMessage('{name} command sent', 'info');
        }}'''
        
        # Generate command service handler
        command_handler = f'''
    def _handle_{command.lower()}(self, parameters: Dict[str, Any]) -> CommandResult:
        """Handle {command.upper()} command."""
        try:
            # TODO: Implement {name.lower()} logic
            self.logger.info(f"{name} command executed with params: {{parameters}}")
            return CommandResult.success("{name} executed successfully")
        except Exception as e:
            return CommandResult.error(f"Error executing {name.lower()}: {{e}}")'''
        
        # Generate backend command mapping
        backend_mapping = f'        "{command.lower()}": "{command.upper()}",'
        
        return {
            "html_button": html_button,
            "js_function": js_function,
            "command_handler": command_handler,
            "backend_mapping": backend_mapping,
            "type": "command"
        }
    
    def generate_action_button(self, name: str, function_name: str, section: str = "Advanced Controls",
                             button_class: str = "btn") -> Dict[str, str]:
        """Generate an action button that performs local GUI actions."""
        
        # Generate HTML button
        html_button = f'''
                        <button class="{button_class}" onclick="{function_name}()">{name}</button>'''
        
        # Generate JavaScript function
        js_function = f'''
        function {function_name}() {{
            // TODO: Implement {name.lower()} logic
            addSystemMessage('{name} executed', 'success');
        }}'''
        
        return {
            "html_button": html_button,
            "js_function": js_function,
            "type": "action"
        }
    
    def add_to_gui_html(self, components: Dict[str, str], section: str):
        """Add components to the GUI HTML file."""
        if not self.gui_html_path.exists():
            print(f"❌ GUI HTML file not found: {self.gui_html_path}")
            return False
            
        content = self.gui_html_path.read_text()
        
        # Find the section to add to
        section_pattern = rf'<div class="control-section-title">{section}</div>'
        if section not in content:
            print(f"⚠️  Section '{section}' not found. Adding to Configuration section.")
            section = "Configuration"
            section_pattern = rf'<div class="control-section-title">{section}</div>'
        
        # Add HTML components
        if "html_input" in components:
            # Add form input before the last </div> of the section
            insertion_point = content.find('</div>', content.find(section_pattern))
            if insertion_point != -1:
                content = content[:insertion_point] + components["html_input"] + '\n\n                    ' + content[insertion_point:]
        
        if "html_button" in components:
            # Add button to btn-group
            btn_group_pattern = r'<div class="btn-group"[^>]*>'
            matches = list(re.finditer(btn_group_pattern, content))
            if matches:
                # Add to the last btn-group in the section
                last_match = matches[-1]
                insertion_point = content.find('</div>', last_match.end())
                if insertion_point != -1:
                    content = content[:insertion_point] + components["html_button"] + '\n                        ' + content[insertion_point:]
        
        # Add JavaScript function before the last </script>
        if "js_function" in components:
            script_end = content.rfind('</script>')
            if script_end != -1:
                content = content[:script_end] + '\n        ' + components["js_function"] + '\n\n        ' + content[script_end:]
        
        self.gui_html_path.write_text(content)
        print(f"✅ Updated GUI HTML: {self.gui_html_path}")
        return True
    
    def add_to_backend(self, components: Dict[str, str]):
        """Add components to the GUI backend."""
        if not self.gui_backend_path.exists():
            print(f"❌ GUI backend file not found: {self.gui_backend_path}")
            return False
            
        content = self.gui_backend_path.read_text()
        
        # Add backend handler to /config endpoint
        if "backend_handler" in components:
            # Find the config endpoint and add handler
            config_endpoint_pattern = r'# Handle empty requests\s*if not command_type:'
            match = re.search(config_endpoint_pattern, content)
            if match:
                insertion_point = match.start()
                content = content[:insertion_point] + components["backend_handler"] + '\n            \n            ' + content[insertion_point:]
        
        # Add command mapping
        if "backend_mapping" in components:
            command_map_pattern = r'command_map = \{[^}]*\}'
            match = re.search(command_map_pattern, content, re.DOTALL)
            if match:
                # Insert before the closing brace
                closing_brace = content.rfind('}', match.start(), match.end())
                content = content[:closing_brace] + '\n        ' + components["backend_mapping"] + '\n    ' + content[closing_brace:]
        
        self.gui_backend_path.write_text(content)
        print(f"✅ Updated GUI backend: {self.gui_backend_path}")
        return True
    
    def add_to_command_service(self, components: Dict[str, str]):
        """Add components to the GUI command service."""
        if not self.command_service_path.exists():
            print(f"❌ Command service file not found: {self.command_service_path}")
            return False
            
        content = self.command_service_path.read_text()
        
        # Add command handler
        if "command_handler" in components:
            # Add to the end of the class, before the last method
            last_method_pattern = r'def _handle_reset_to_default_config\(self[^}]*?return CommandResult\.info\([^)]*\)'
            match = re.search(last_method_pattern, content, re.DOTALL)
            if match:
                insertion_point = match.end()
                content = content[:insertion_point] + '\n    ' + components["command_handler"] + content[insertion_point:]
        
        self.command_service_path.write_text(content)
        print(f"✅ Updated command service: {self.command_service_path}")
        return True


def main():
    parser = argparse.ArgumentParser(description="Generate GUI buttons with complete integration")
    parser.add_argument("--type", choices=["config", "command", "action"], required=True,
                       help="Type of button to generate")
    parser.add_argument("--name", required=True, help="Display name of the button/control")
    parser.add_argument("--section", default="Configuration", help="GUI section to add to")
    
    # Config button specific
    parser.add_argument("--config-key", help="JavaScript config key (e.g., maxPositionSize)")
    parser.add_argument("--config-field", help="Backend config field (e.g., risk_max_position_size)")
    parser.add_argument("--data-type", choices=["int", "float", "str", "bool"], default="str")
    parser.add_argument("--default", help="Default value")
    
    # Command button specific  
    parser.add_argument("--command", help="Command name (e.g., toggle_recording_mode)")
    
    # Action button specific
    parser.add_argument("--function", help="JavaScript function name")
    
    # Button styling
    parser.add_argument("--button-class", default="btn", help="CSS class for button")
    
    args = parser.parse_args()
    
    generator = GUIButtonGenerator()
    
    if args.type == "config":
        if not all([args.config_key, args.config_field, args.default]):
            print("❌ Config buttons require --config-key, --config-field, and --default")
            return
            
        components = generator.generate_config_button(
            args.name, args.config_key, args.config_field, 
            args.data_type, args.default, args.section
        )
        
        generator.add_to_gui_html(components, args.section)
        generator.add_to_backend(components)
        
    elif args.type == "command":
        if not args.command:
            print("❌ Command buttons require --command")
            return
            
        components = generator.generate_command_button(
            args.name, args.command, args.section, args.button_class
        )
        
        generator.add_to_gui_html(components, args.section)
        generator.add_to_backend(components)
        generator.add_to_command_service(components)
        
    elif args.type == "action":
        if not args.function:
            print("❌ Action buttons require --function")
            return
            
        components = generator.generate_action_button(
            args.name, args.function, args.section, args.button_class
        )
        
        generator.add_to_gui_html(components, args.section)
    
    print(f"🎉 Successfully generated {args.type} button: {args.name}")


if __name__ == "__main__":
    main()
