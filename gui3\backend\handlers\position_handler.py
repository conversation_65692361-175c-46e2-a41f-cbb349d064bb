"""
Position and account message handlers for GUI backend.
"""

from typing import Dict, Any, Optional
from ..core.message_handler import MessageHandler


class PositionHandler(MessageHandler):
    """Handles position update messages."""
    
    async def handle(self, message_id: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process position updates."""
        
        payload = data.get('payload', {})
        
        return {
            'type': 'position_update',
            'message_id': message_id,
            'symbol': payload.get('symbol', ''),
            'quantity': payload.get('quantity', 0),
            'average_price': payload.get('average_price', 0),
            'realized_pnl': payload.get('realized_pnl_session', 0),
            'is_open': payload.get('is_open', False),
            'strategy': payload.get('strategy', 'unknown'),
            'timestamp': payload.get('last_update_timestamp', 0)
        }


class AccountHandler(MessageHandler):
    """Handles account update messages."""
    
    async def handle(self, message_id: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process account updates."""
        
        payload = data.get('payload', {})
        
        return {
            'type': 'account_update',
            'message_id': message_id,
            'account_id': payload.get('account_id', ''),
            'equity': payload.get('equity', 0),
            'buying_power': payload.get('buying_power', 0),
            'cash': payload.get('cash', 0),
            'realized_pnl_day': payload.get('realized_pnl_day', 0),
            'unrealized_pnl_day': payload.get('unrealized_pnl_day', 0),
            'timestamp': payload.get('last_update', 0)
        }


class TradeHandler(MessageHandler):
    """Handles trade lifecycle messages."""
    
    async def handle(self, message_id: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process trade lifecycle events."""
        
        event_type = data.get('event_type', '')
        payload = data.get('payload', {})
        
        if 'TRADE_OPENED' in event_type:
            return {
                'type': 'trade_opened',
                'message_id': message_id,
                'trade_id': payload.get('trade_id', ''),
                'symbol': payload.get('symbol', ''),
                'side': payload.get('side', ''),
                'quantity': payload.get('quantity', 0),
                'entry_price': payload.get('entry_price', 0),
                'timestamp': payload.get('timestamp', 0)
            }
        
        elif 'TRADE_CLOSED' in event_type:
            return {
                'type': 'trade_closed',
                'message_id': message_id,
                'trade_id': payload.get('trade_id', ''),
                'symbol': payload.get('symbol', ''),
                'realized_pnl': payload.get('realized_pnl', 0),
                'exit_price': payload.get('exit_price', 0),
                'timestamp': payload.get('timestamp', 0)
            }
        
        return None