# TESTRADE Market Data Troubleshooting Guide

## 🔍 Problem: Price and Quote Data Not Showing in GUI

This guide helps diagnose and fix issues where market data (prices, quotes, trades) is not appearing in the TESTRADE GUI.

## 📊 Market Data Pipeline Overview

```
Alpaca Feed → PriceRepository → ActiveSymbolsService Filter → FilteredMarketDataPublisher → BulletproofIPC → Redis → GUI
```

### Key Components:

1. **PriceRepository**: Receives market data from Alpaca
2. **ActiveSymbolsService**: Filters data to only relevant symbols
3. **FilteredMarketDataPublisher**: Publishes filtered data via IPC
4. **BulletproofIPCClient**: Handles reliable data transmission
5. **Redis Streams**: Transport layer to GUI
6. **GUI Backend**: Consumes data and displays in frontend

## 🚨 Common Issues & Solutions

### 1. **No Active Symbols (Most Common)**

**Symptoms:**
- All market data is filtered out
- Logs show `PR_DROP_TRADE/QUOTE: symbol not relevant`

**Root Cause:**
ActiveSymbolsService has no active symbols, so ALL market data is filtered out.

**Solutions:**
```python
# Add test symbols manually
from test_market_data_filter import add_test_symbols_to_active_service
add_test_symbols_to_active_service(['AAPL', 'TSLA', 'MSFT'])

# Or open a position/place an order to activate symbols naturally
```

### 2. **Market Data Streaming Disabled**

**Symptoms:**
- Configuration flag prevents data publishing
- Logs show streaming disabled messages

**Root Cause:**
`enable_internal_market_data_streaming` is missing or false in control.json

**Solution:**
```bash
# Run the fix script
python fix_market_data_config.py

# Or manually add to control.json:
"enable_internal_market_data_streaming": true
```

### 3. **TANK Mode Active (IPC Disabled)**

**Symptoms:**
- Logs show `TANK_MODE: Skipping IPC send`
- Data processed but not sent to Redis

**Root Cause:**
`ENABLE_IPC_DATA_DUMP` is false, activating TANK mode

**Solutions:**
```json
// Option 1: Disable TANK mode
"ENABLE_IPC_DATA_DUMP": true

// Option 2: Ensure Babysitter service is running for TANK mode
```

### 4. **FilteredMarketDataPublisher Not Initialized**

**Symptoms:**
- Logs show `FMP: Service not initialized`
- Publisher dropping all data

**Root Cause:**
FilteredMarketDataPublisher failed to initialize properly

**Solution:**
Check ApplicationCore initialization and DI container setup.

### 5. **BulletproofIPCClient Issues**

**Symptoms:**
- IPC client in offline mode
- ZMQ socket connection failures

**Root Cause:**
Network issues or Babysitter service not running

**Solutions:**
- Check Babysitter service status
- Verify ZMQ port availability
- Check network connectivity

## 🔧 Diagnostic Tools

### 1. **Comprehensive Diagnostics**
```bash
python test_market_data_filter.py
```
This runs a complete pipeline check and provides specific recommendations.

### 2. **Configuration Fix**
```bash
python fix_market_data_config.py
```
Automatically fixes common configuration issues.

### 3. **Manual Debug Commands**
```python
# Enable debug logging
from test_market_data_filter import enable_filter_debug_logging
enable_filter_debug_logging()

# Check active symbols
from test_market_data_filter import check_active_symbols
check_active_symbols()

# Add test symbols
from test_market_data_filter import add_test_symbols_to_active_service
add_test_symbols_to_active_service(['AAPL'])

# Run diagnostics
from test_market_data_filter import diagnose_market_data_pipeline
diagnose_market_data_pipeline()
```

## 📋 Step-by-Step Troubleshooting

### Step 1: Run Diagnostics
```bash
python test_market_data_filter.py
```

### Step 2: Fix Configuration Issues
```bash
python fix_market_data_config.py
```

### Step 3: Add Active Symbols
```python
from test_market_data_filter import add_test_symbols_to_active_service
add_test_symbols_to_active_service(['AAPL', 'TSLA'])
```

### Step 4: Enable Debug Logging
```python
from test_market_data_filter import enable_filter_debug_logging
enable_filter_debug_logging()
```

### Step 5: Monitor Data Flow
Watch logs for these key messages:
- `PR_PASS_TRADE/QUOTE: symbol is relevant` ✅ Good
- `PR_DROP_TRADE/QUOTE: symbol not relevant` ❌ Filtered out
- `FMP_PUBLISH_ENTRY: Publishing pre-filtered data` ✅ Publishing
- `FMP_SENT_BUFFERED_OK: Filtered data handed to BulletproofIPCClient` ✅ Sent

## 🎯 Key Configuration Settings

### Required in control.json:
```json
{
  "enable_internal_market_data_streaming": true,
  "ENABLE_IPC_DATA_DUMP": true,
  "FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API": true,
  "FEATURE_FLAG_ENABLE_OBSERVABILITY_PUBLISHER": true,
  "redis_stream_filtered_trades": "testrade:filtered:market-trades",
  "redis_stream_filtered_quotes": "testrade:filtered:market-quotes"
}
```

## 🚀 Quick Fixes Summary

1. **No data at all**: Run `python fix_market_data_config.py`
2. **Data filtered out**: Add symbols with `add_test_symbols_to_active_service()`
3. **TANK mode issues**: Set `ENABLE_IPC_DATA_DUMP: true` or start Babysitter
4. **Debug visibility**: Run `enable_filter_debug_logging()`
5. **Comprehensive check**: Run `python test_market_data_filter.py`

## 💡 Understanding the Filter

The ActiveSymbolsService filter is designed to reduce noise by only publishing market data for symbols that are:
- Currently held in positions (from PositionManager)
- Have pending orders (from OrderRepository)

This prevents the GUI from being overwhelmed with data for irrelevant symbols.

## 📞 Support

If issues persist after following this guide:
1. Check ApplicationCore logs for errors
2. Verify Alpaca feed connectivity
3. Ensure all services are properly initialized
4. Check Redis connectivity and streams
5. Verify GUI Backend is consuming data correctly

The diagnostic tools will provide specific error messages and recommendations for your particular setup.
