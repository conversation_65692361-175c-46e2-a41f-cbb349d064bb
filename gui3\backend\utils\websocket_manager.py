"""
WebSocket connection management for GUI backend.
"""

import logging
import json
from typing import List, Dict, Any
from fastapi import WebSocket


class WebSocketManager:
    """Manages WebSocket connections to GUI clients."""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.logger = logging.getLogger("GUI.WebSocketManager")
        
    async def connect(self, websocket: WebSocket) -> None:
        """Accept and track a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.append(websocket)
        self.logger.info(f"Client connected. Total connections: {len(self.active_connections)}")
        
        # Send welcome message
        await self.send_to_client(websocket, {
            'type': 'connection',
            'status': 'connected',
            'message': 'Welcome to TESTRADE GUI'
        })
    
    def disconnect(self, websocket: WebSocket) -> None:
        """Remove a disconnected WebSocket."""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        self.logger.info(f"Client disconnected. Remaining connections: {len(self.active_connections)}")
    
    async def send_to_client(self, websocket: WebSocket, data: Dict[str, Any]) -> None:
        """Send data to a specific client."""
        try:
            await websocket.send_json(data)
        except Exception as e:
            self.logger.error(f"Error sending to client: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, data: Dict[str, Any]) -> None:
        """Broadcast data to all connected clients."""
        if not self.active_connections:
            return
        
        # Convert to JSON once
        message = json.dumps(data)
        
        # Send to all clients
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                self.logger.debug(f"Failed to send to client: {e}")
                disconnected.append(connection)
        
        # Clean up disconnected clients
        for conn in disconnected:
            self.disconnect(conn)
    
    async def broadcast_high_priority(self, data: Dict[str, Any]) -> None:
        """Broadcast high-priority messages (errors, alerts, etc)."""
        data['priority'] = 'high'
        await self.broadcast(data)
    
    def get_connection_count(self) -> int:
        """Get number of active connections."""
        return len(self.active_connections)