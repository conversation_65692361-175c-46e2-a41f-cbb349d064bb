@echo off
REM ============================================================================
REM TESTRADE TANK MODE Debug Startup Script
REM Automatically starts with --show-consoles for debugging
REM ============================================================================

setlocal enabledelayedexpansion
cd /d C:\TESTRADE

echo.
echo ============================================================================
echo TESTRADE TANK MODE STARTUP (DEBUG MODE)
echo ============================================================================
echo.

REM Check if Python is available
echo STEP 1: Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found in PATH
    echo Please ensure Python is installed and available
    pause
    exit /b 1
)

REM Check if virtual environment exists
echo STEP 2: Checking virtual environment...
if not exist ".venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found at .venv\Scripts\python.exe
    echo Please run: python -m venv .venv
    echo Then: .venv\Scripts\pip install -r requirements.txt
    pause
    exit /b 1
)

REM Check if start_testrade.py exists
echo STEP 3: Checking startup script...
if not exist "start_testrade.py" (
    echo ERROR: start_testrade.py not found
    echo Please ensure you're running this from the TESTRADE directory
    pause
    exit /b 1
)

REM Check if utils/control.json exists
echo STEP 4: Checking configuration...
if not exist "utils\control.json" (
    echo ERROR: utils\control.json not found
    echo Please ensure the configuration file exists
    pause
    exit /b 1
)

REM Create required directories
echo STEP 5: Creating required directories...
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "data\babysitter_disk_buffer" mkdir data\babysitter_disk_buffer

echo.
echo STEP 6: Starting TESTRADE TANK MODE (DEBUG MODE)...
echo.
echo This will start all TESTRADE components with INDIVIDUAL CONSOLE WINDOWS:
echo   0. Redis connectivity check (WSL2 diagnostics)
echo   1. Babysitter Service (NEW CONSOLE)
echo   2. ApplicationCore (NEW CONSOLE)
echo   3. GUI Backend (NEW CONSOLE - uses gui/gui_backend.py)
echo   4. GUI Frontend (opens browser to http://localhost:8001/gui)
echo.
echo Each component will have its own console window for debugging.
echo Press Ctrl+C in THIS window to gracefully shutdown all components.
echo.

REM Activate virtual environment and run the Python script with debug flag
echo Activating virtual environment...
call .venv\Scripts\activate.bat

echo.
echo Starting Python startup script in DEBUG MODE...
echo (Each component will open in its own console window)
echo.

REM Run the Python script with --show-consoles flag
python start_testrade.py --show-consoles

REM Store exit code
set PYTHON_EXIT_CODE=%errorlevel%

echo.
echo ============================================================================
if %PYTHON_EXIT_CODE% neq 0 (
    echo DEBUG STARTUP FAILED (Exit Code: %PYTHON_EXIT_CODE%)
    echo.
    echo Check the individual console windows for detailed error information.
    echo Each component should have opened in its own window showing real-time output.
    echo.
    echo Common issues:
    echo   - Redis server not running in WSL2 on **************:6379
    echo   - WSL2 networking issues (Redis not accessible from Windows)
    echo   - Redis not configured to bind 0.0.0.0 (check WSL redis.conf)
    echo   - Missing Python dependencies
    echo   - Port conflicts (other TESTRADE instances running)
    echo   - Configuration errors in utils\control.json
) else (
    echo DEBUG STARTUP COMPLETED (Exit Code: %PYTHON_EXIT_CODE%)
    echo.
    echo If this was a clean shutdown via Ctrl+C, this is expected.
    echo Check the individual console windows to see component output.
    echo.
    echo Access points (if components started successfully):
    echo   - GUI: http://localhost:8001/gui (portrait_trading_gui.html)
    echo   - API: http://localhost:8001
    echo   - Bootstrap API: http://localhost:8001/api/v1/positions/current
    echo   - WebSocket: ws://localhost:8001/ws
)
echo ============================================================================
echo.

echo Press any key to close this window...
pause >nul
