<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TESTRADE GUI v2 - Simplified</title>
    <style>
        :root {
            --primary-color: #007bff;
            --primary-hover: #0056b3;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --dark-bg: #1a1a1a;
            --light-bg: #2d2d2d;
            --text-color: #ffffff;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-color);
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: var(--primary-color);
        }

        .section {
            background-color: var(--light-bg);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .quick-trade {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .trade-card {
            background-color: var(--dark-bg);
            padding: 15px;
            border-radius: 8px;
        }

        input[type="text"], input[type="number"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #444;
            border-radius: 4px;
            background-color: var(--dark-bg);
            color: var(--text-color);
        }

        #status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 4px;
            font-weight: bold;
        }

        #status.connected {
            background-color: var(--success-color);
        }

        #status.disconnected {
            background-color: var(--danger-color);
        }
    </style>
</head>
<body>
    <div id="status" class="disconnected">Disconnected</div>
    
    <div class="container">
        <h1>TESTRADE GUI v2</h1>
        
        <!-- Control Section -->
        <div class="section">
            <h2>System Control</h2>
            <div class="button-group">
                <!-- Look how simple it is to add buttons! -->
                <action-button action="start_trading" label="Start Trading"></action-button>
                <action-button action="stop_trading" label="Stop Trading"></action-button>
                <action-button action="refresh_positions" label="Refresh Positions"></action-button>
                <action-button action="refresh_account" label="Refresh Account"></action-button>
                <action-button action="refresh_orders" label="Refresh Orders"></action-button>
            </div>
        </div>

        <!-- Quick Trade Section -->
        <div class="section">
            <h2>Quick Trade</h2>
            <div class="quick-trade">
                <!-- AAPL Trade Card -->
                <div class="trade-card">
                    <h3>AAPL</h3>
                    <input type="number" id="aapl-qty" placeholder="Quantity" value="100">
                    <div class="button-group">
                        <action-button 
                            action="buy" 
                            label="Buy AAPL" 
                            symbol="AAPL" 
                            quantity="100"
                            order_type="MKT">
                        </action-button>
                        <action-button 
                            action="sell" 
                            label="Sell AAPL" 
                            symbol="AAPL" 
                            quantity="100"
                            order_type="MKT">
                        </action-button>
                    </div>
                </div>

                <!-- TSLA Trade Card -->
                <div class="trade-card">
                    <h3>TSLA</h3>
                    <input type="number" id="tsla-qty" placeholder="Quantity" value="50">
                    <div class="button-group">
                        <action-button 
                            action="buy" 
                            label="Buy TSLA" 
                            symbol="TSLA" 
                            quantity="50"
                            order_type="MKT">
                        </action-button>
                        <action-button 
                            action="sell" 
                            label="Sell TSLA" 
                            symbol="TSLA" 
                            quantity="50"
                            order_type="MKT">
                        </action-button>
                    </div>
                </div>

                <!-- Custom Symbol Card -->
                <div class="trade-card">
                    <h3>Custom Symbol</h3>
                    <input type="text" id="custom-symbol" placeholder="Symbol" value="SPY">
                    <input type="number" id="custom-qty" placeholder="Quantity" value="100">
                    <div class="button-group">
                        <action-button 
                            action="buy" 
                            label="Buy" 
                            symbol="SPY" 
                            quantity="100"
                            order_type="MKT">
                        </action-button>
                        <action-button 
                            action="sell" 
                            label="Sell" 
                            symbol="SPY" 
                            quantity="100"
                            order_type="MKT">
                        </action-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Display Section -->
        <div class="section">
            <h2>Live Data</h2>
            <div id="data-display">
                <p>Waiting for data...</p>
            </div>
        </div>
    </div>

    <!-- Import components and services -->
    <script type="module">
        import { ActionButton } from './frontend/components/ActionButton.js';
        import { CommandService } from './frontend/services/CommandService.js';

        // Initialize command service
        const commandService = CommandService.getInstance();

        // Update connection status
        const statusEl = document.getElementById('status');
        commandService.on('connected', () => {
            statusEl.textContent = 'Connected';
            statusEl.className = 'connected';
        });

        commandService.on('disconnected', () => {
            statusEl.textContent = 'Disconnected';
            statusEl.className = 'disconnected';
        });

        // Handle incoming messages
        const dataDisplay = document.getElementById('data-display');
        commandService.on('message', (message) => {
            // Display different message types
            const messageEl = document.createElement('div');
            messageEl.innerHTML = `
                <strong>${message.type}:</strong> 
                ${JSON.stringify(message, null, 2)}
            `;
            dataDisplay.appendChild(messageEl);
            
            // Keep only last 10 messages
            while (dataDisplay.children.length > 10) {
                dataDisplay.removeChild(dataDisplay.firstChild);
            }
        });

        // Dynamic quantity updates
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('input', (e) => {
                const symbol = e.target.id.split('-')[0].toUpperCase();
                const quantity = e.target.value;
                
                // Update all buttons for this symbol
                document.querySelectorAll(`action-button[symbol="${symbol}"]`).forEach(button => {
                    button.setAttribute('quantity', quantity);
                });
            });
        });

        // Custom symbol updates
        const customSymbolInput = document.getElementById('custom-symbol');
        const customQtyInput = document.getElementById('custom-qty');
        
        function updateCustomButtons() {
            const symbol = customSymbolInput.value.toUpperCase();
            const quantity = customQtyInput.value;
            
            const customCard = customSymbolInput.closest('.trade-card');
            customCard.querySelectorAll('action-button').forEach(button => {
                button.setAttribute('symbol', symbol);
                button.setAttribute('quantity', quantity);
            });
        }
        
        customSymbolInput.addEventListener('input', updateCustomButtons);
        customQtyInput.addEventListener('input', updateCustomButtons);

        // Listen for action events
        document.addEventListener('action-complete', (e) => {
            console.log('Action completed:', e.detail);
        });

        document.addEventListener('action-error', (e) => {
            console.error('Action error:', e.detail);
        });
    </script>
</body>
</html>