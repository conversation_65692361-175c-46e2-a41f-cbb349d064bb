# World-Class Metadata Architecture Guide

## Overview

TESTRADE has implemented a world-class metadata/payload separation pattern that provides clean separation of concerns, easier data extraction, and superior maintainability. This guide shows you how to implement this pattern correctly.

## 🎯 Core Architecture Principle

**Separate Tracing/Routing Data from Business Logic Data**

```
Event Structure:
├── metadata: Dict[str, Any]     # Tracing, routing, correlation data
└── payload fields: ...          # Business logic data
```

## 📋 Standard Metadata Block Structure

Every event should contain this standardized metadata block:

```python
metadata = {
    "eventId": "550e8400-e29b-41d4-a716-************",      # Unique ID for this event
    "correlationId": "master-correlation-id-from-origin",    # Master tracking ID
    "causationId": "parent-event-id-that-caused-this",       # Immediate parent event
    "timestamp_ns": **********890123456,                     # Nanosecond timestamp
    "epoch_timestamp_s": **********.890123,                  # Float epoch seconds
    "eventType": "TESTRADE_OCR_RESULT",                      # Event type identifier
    "sourceComponent": "OCRService"                          # Component that created event
}
```

## 🏗️ Implementation Pattern

### ✅ CORRECT: Clean Metadata/Payload Separation

```python
@dataclass
class WorldClassEventData:
    """
    World-class event structure with clean metadata/payload separation.
    
    This follows the standard TESTRADE event pattern:
    - metadata: All tracing, routing, and correlation data
    - payload fields: Business logic data
    """
    
    # --- METADATA BLOCK: Tracing, Routing, and Correlation Data ---
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # --- PAYLOAD: Business Logic Data ---
    
    # Core business results
    result_confidence: Optional[float] = None
    business_data: Dict[str, Any] = field(default_factory=dict)
    
    # High-precision timing (business timing, not tracing timing)
    T0_ProcessStart_ns: Optional[int] = None
    T1_ProcessComplete_ns: Optional[int] = None
    
    # Legacy compatibility fields (for transition period)
    legacy_event_id: Optional[str] = field(default=None, init=False)
    legacy_correlation_id: Optional[str] = field(default=None, init=False)

    def __post_init__(self):
        """Ensure backward compatibility and metadata integrity."""
        # Ensure metadata has required fields
        if not self.metadata.get('eventId'):
            from utils.thread_safe_uuid import get_thread_safe_uuid
            self.metadata['eventId'] = get_thread_safe_uuid()
        
        # Populate legacy fields for backward compatibility
        self.legacy_event_id = self.metadata.get('eventId')
        self.legacy_correlation_id = self.metadata.get('correlationId')
```

### ❌ AVOID: Mixed Metadata in Root Fields

```python
# DON'T DO THIS - mixing tracing and business data
@dataclass
class PoorEventData:
    event_id: str                    # Tracing data mixed with...
    correlation_id: str              # More tracing data...
    business_result: float           # Business data...
    confidence: float                # More business data...
    causation_id: str                # Back to tracing data
```

## 📊 Real Example: OCRParsedData

```python
@dataclass
class OCRParsedData:
    # --- METADATA BLOCK: All tracing, routing, and correlation data ---
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # --- PAYLOAD: Business Logic Data ---
    
    # Core OCR Results
    overall_confidence: Optional[float] = None
    snapshots: Dict[str, OCRSnapshot] = field(default_factory=dict)
    full_raw_text: str = ""
    
    # Clean T0-T3 High-Precision Milestone Timestamps
    T0_ImageIngress_ns: Optional[int] = None      # Image capture start
    T1_ImageProcessing_ns: Optional[int] = None   # Image processing complete
    T2_TesseractOutput_ns: Optional[int] = None   # Tesseract OCR complete
    T3_FormattedOutput_ns: Optional[int] = None   # Final output ready
    
    # Performance and Debug Data
    ocr_process_perf_timestamps: Optional[Dict[str, float]] = None
    processed_frame_base64: Optional[str] = None
    
    # Analysis Data
    roi_used_for_capture: Optional[List[int]] = field(default_factory=list)
    preprocessing_params_used: Optional[Dict[str, Any]] = field(default_factory=dict)
```

## 🔍 Data Extraction Examples

### Tracing and Correlation Data
```python
# All tracing data in one place - clean and organized
correlation_id = event_data.metadata['correlationId']
event_id = event_data.metadata['eventId'] 
causation_id = event_data.metadata['causationId']
source_component = event_data.metadata['sourceComponent']
timestamp_ns = event_data.metadata['timestamp_ns']

# Build causation chains
def build_causation_chain(events):
    chain = []
    current_event = find_final_event(events)
    while current_event:
        chain.append(current_event)
        parent_id = current_event.metadata.get('causationId')
        current_event = find_event_by_id(events, parent_id)
    return chain
```

### Business Logic Data
```python
# Business data clearly separated
confidence = ocr_data.overall_confidence
raw_text = ocr_data.full_raw_text
snapshots = ocr_data.snapshots

# High-precision timing analysis
image_capture_time = ocr_data.T0_ImageIngress_ns
processing_time = ocr_data.T1_ImageProcessing_ns
tesseract_time = ocr_data.T2_TesseractOutput_ns
output_ready_time = ocr_data.T3_FormattedOutput_ns

# Calculate precise latencies
total_latency_ms = (ocr_data.T3_FormattedOutput_ns - ocr_data.T0_ImageIngress_ns) / 1_000_000
ocr_latency_ms = (ocr_data.T2_TesseractOutput_ns - ocr_data.T1_ImageProcessing_ns) / 1_000_000
```

### IntelliSense Analysis Made Trivial
```python
# Find all events for a correlation ID
def find_correlation_chain(stream, correlation_id):
    return [event for event in stream if event.metadata['correlationId'] == correlation_id]

# Calculate end-to-end latencies across multiple events
def calculate_pipeline_latency(events):
    events.sort(key=lambda e: e.metadata['timestamp_ns'])
    start_time = events[0].metadata['timestamp_ns']
    end_time = events[-1].metadata['timestamp_ns'] 
    return (end_time - start_time) / 1_000_000  # ms

# Build complete causation trees
def build_causation_tree(events):
    tree = {}
    for event in events:
        event_id = event.metadata['eventId']
        causation_id = event.metadata.get('causationId')
        if causation_id not in tree:
            tree[causation_id] = []
        tree[causation_id].append(event_id)
    return tree
```

## 🎯 Creating Events with Metadata

### Standard Event Creation Pattern
```python
def create_child_event(parent_event_data, event_type, payload_data):
    """Create a child event with proper metadata inheritance."""
    
    # Extract parent correlation info
    parent_correlation_id = parent_event_data.metadata['correlationId']
    parent_event_id = parent_event_data.metadata['eventId']
    
    # Build new metadata block
    new_metadata = {
        "eventId": get_thread_safe_uuid(),           # Always new
        "correlationId": parent_correlation_id,     # Inherited
        "causationId": parent_event_id,             # Parent becomes cause
        "timestamp_ns": time.perf_counter_ns(),
        "epoch_timestamp_s": time.time(),
        "eventType": event_type,
        "sourceComponent": "YourComponent"
    }
    
    return YourEventData(
        metadata=new_metadata,
        # ... payload fields ...
    )
```

### Starting a New Correlation Chain
```python
def create_origin_event(event_type, payload_data):
    """Create the first event in a new correlation chain."""
    
    # Generate new correlation ID
    correlation_id = get_thread_safe_uuid()
    
    # Build origin metadata block
    origin_metadata = {
        "eventId": correlation_id,           # For origin, eventId = correlationId
        "correlationId": correlation_id,     # Master tracking ID
        "causationId": None,                 # No parent for origin event
        "timestamp_ns": time.perf_counter_ns(),
        "epoch_timestamp_s": time.time(),
        "eventType": event_type,
        "sourceComponent": "YourComponent"
    }
    
    return YourEventData(
        metadata=origin_metadata,
        # ... payload fields ...
    )
```

## 📈 Benefits of This Architecture

### 1. **Clean Separation of Concerns**
- Tracing data completely separated from business data
- No confusion about what field contains what type of information
- Easy to extend either metadata or payload without affecting the other

### 2. **Superior Data Extraction**
```python
# Before: Scattered and confusing
some_id = event.event_id or event.correlation_id or event.master_correlation_id

# After: Clean and obvious
correlation_id = event.metadata['correlationId']
```

### 3. **Easier IntelliSense Queries**
```python
# Find all events in a trading decision
correlation_events = [e for e in stream if e.metadata['correlationId'] == target_id]

# Calculate latencies
latencies = [(e.T3_OutputReady_ns - e.T0_StartTime_ns) for e in events]
```

### 4. **Future-Proof Design**
- Add new metadata fields without touching business logic
- Add new business fields without affecting tracing
- Easy to extend for new use cases

### 5. **Better Debugging and Analysis**
- All correlation data in predictable location
- Standard structure across all event types
- Clear audit trails through causation chains

## 🏢 Centralized Data Models

All shared data structures should live in `data_models/` for single source of truth:

```
data_models/
├── __init__.py          # Exports all data types
├── trading.py           # Trading-related data structures
├── pricing.py           # Market data structures  
├── enums.py            # Shared enumerations
└── ...                 # Other domain-specific models
```

### Import Pattern
```python
# ✅ CORRECT: Import from centralized location
from data_models import OCRParsedData, TradeSignal, OrderData

# ❌ AVOID: Local data type definitions
from .local_data_types import SomeDataClass
```

## 🔥 Migration Guide

### For Existing Events
1. **Create metadata dict** with all tracing fields
2. **Move business fields** to payload level  
3. **Add __post_init__** for backward compatibility
4. **Update creation code** to use metadata pattern

### For New Events
1. **Start with metadata block** as first field
2. **Add business fields** as payload
3. **Follow naming conventions** (T0, T1, T2, T3 for timing)
4. **Include to_dict() method** for serialization

## 🚀 Best Practices

1. **Always use `get_thread_safe_uuid()`** for ID generation
2. **Include all standard metadata fields** in every event
3. **Use high-precision timestamps** (nanoseconds) for timing
4. **Provide legacy compatibility** during transition periods
5. **Document the business meaning** of each payload field
6. **Keep metadata structure consistent** across all event types

## 📚 Related Documentation

- [ID Tracking System - Complete Guide.md](./ID%20Tracking%20System%20-%20Complete%20Guide.md)
- [Event Lifecycle & Data Flow Guide.md](./Event%20Lifecycle%20&%20Data%20Flow%20Guide.md)
- [Core Components & Public APIs - Reference.md](./Core%20Components%20&%20Public%20APIs%20-%20Reference.md)

---

**Remember**: This architecture represents world-class separation of concerns. Follow it religiously to maintain TESTRADE's clean, maintainable, and scalable codebase.