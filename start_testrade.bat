@echo off
REM ============================================================================
REM TESTRADE TANK MODE Startup Script (Fixed Version)
REM Runs the comprehensive Python startup script with proper environment
REM ============================================================================

setlocal enabledelayedexpansion
cd /d C:\TESTRADE

echo.
echo ============================================================================
echo TESTRADE TANK MODE STARTUP
echo ============================================================================
echo.

REM Check if Python is available
echo STEP 1: Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found in PATH
    echo Please ensure Python is installed and available
    pause
    exit /b 1
)

REM Check if virtual environment exists
echo STEP 2: Checking virtual environment...
if not exist ".venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found at .venv\Scripts\python.exe
    echo Please run: python -m venv .venv
    echo Then: .venv\Scripts\pip install -r requirements.txt
    pause
    exit /b 1
)

REM Check if start_testrade.py exists
echo STEP 3: Checking startup script...
if not exist "start_testrade.py" (
    echo ERROR: start_testrade.py not found
    echo Please ensure you're running this from the TESTRADE directory
    pause
    exit /b 1
)

REM Check if utils/control.json exists
echo STEP 4: Checking configuration...
if not exist "utils\control.json" (
    echo ERROR: utils\control.json not found
    echo Please ensure the configuration file exists
    pause
    exit /b 1
)

REM Create required directories
echo STEP 5: Creating required directories...
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "data\babysitter_disk_buffer" mkdir data\babysitter_disk_buffer

echo.
echo STEP 6: Starting TESTRADE TANK MODE...
echo.
echo This will start all TESTRADE components in the correct order:
echo   0. Redis connectivity check (WSL2 diagnostics)
echo   1. Babysitter Service
echo   2. ApplicationCore (Headless)
echo   3. GUI Backend
echo   4. GUI Frontend (optional)
echo.
echo The script will monitor all processes and provide health checks.
echo Press Ctrl+C to gracefully shutdown all components.
echo.

REM Ask user about console display mode
SET /P SHOW_CONSOLES_CHOICE="Show individual component consoles for debugging? (Y/N, default N): "
SET PYTHON_ARGS=
IF /I "%SHOW_CONSOLES_CHOICE%"=="Y" SET PYTHON_ARGS=--show-consoles

echo.

REM Activate virtual environment and run the Python script
echo Activating virtual environment...
call .venv\Scripts\activate.bat

echo.
echo Starting Python startup script...
echo (This window will stay open to show all output)
echo.

REM Run the Python script with optional arguments
python start_testrade.py %PYTHON_ARGS%

REM Store exit code
set PYTHON_EXIT_CODE=%errorlevel%

echo.
echo ============================================================================
if %PYTHON_EXIT_CODE% neq 0 (
    echo STARTUP FAILED (Exit Code: %PYTHON_EXIT_CODE%)
    echo.
    echo Check the logs directory for detailed error information:
    echo   - logs\babysitter_service.log
    echo   - logs\core.log
    echo   - logs\gui_backend.log
    echo   - logs\gui_frontend_*.log
    echo.
    echo Common issues:
    echo   - Redis server not running in WSL2 on **************:6379
    echo   - WSL2 networking issues (Redis not accessible from Windows)
    echo   - Redis not configured to bind 0.0.0.0 (check WSL redis.conf)
    echo   - Missing Python dependencies
    echo   - Port conflicts (other TESTRADE instances running)
    echo   - Configuration errors in utils\control.json
) else (
    echo STARTUP COMPLETED (Exit Code: %PYTHON_EXIT_CODE%)
    echo.
    echo If this was a clean shutdown via Ctrl+C, this is expected.
    echo If components were meant to stay running, check the logs.
    echo.
    echo Access points (if components started successfully):
    echo   - GUI: http://localhost:3000 (if frontend started)
    echo   - API: http://localhost:8001
    echo   - Direct GUI: file:///C:/TESTRADE/gui/portrait_trading_gui.html
)
echo ============================================================================
echo.

echo Press any key to close this window...
pause >nul