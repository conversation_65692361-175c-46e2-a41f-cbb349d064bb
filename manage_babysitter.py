import subprocess
import os
import sys
import time

# --- Configuration ---
BABYSITTER_SCRIPT = "core/babysitter_service.py"
PID_FILE = "babysitter.pid"
LOG_FILE = "babysitter_process.log"
# Use the Python executable from the currently active virtual environment
PYTHON_EXECUTABLE = sys.executable

def start():
    """Starts the Babysitter service as a fully independent background process."""
    if os.path.exists(PID_FILE):
        print(f"PID file '{PID_FILE}' exists. Babysitter may already be running.")
        return

    print(f"Starting Babysitter service... Output will be in '{LOG_FILE}'")
    
    try:
        # Open the log file that the new process will write to.
        log_handle = open(LOG_FILE, 'w')
    except IOError as e:
        print(f"FATAL: Could not open log file '{LOG_FILE}'. Error: {e}")
        return

    # **THE CRITICAL FLAGS FOR WINDOWS**
    # DETACHED_PROCESS: The new process does not inherit the console of the parent.
    # CREATE_NEW_PROCESS_GROUP: The new process is the root of a new process group.
    # This combination makes it truly independent.
    creation_flags = subprocess.DETACHED_PROCESS | subprocess.CREATE_NEW_PROCESS_GROUP
    
    # Launch the process
    process = subprocess.Popen(
        [PYTHON_EXECUTABLE, BABYSITTER_SCRIPT],
        stdout=log_handle,
        stderr=subprocess.STDOUT, # Redirect errors to the same log file
        creationflags=creation_flags,
        # On Windows, stdin, stdout, stderr must be handled.
        # We don't need stdin, and stdout/stderr go to our log file.
    )
    
    # Store the PID to manage the process later
    with open(PID_FILE, "w") as f:
        f.write(str(process.pid))
    
    print(f"Babysitter service launched successfully with PID: {process.pid}")

def stop():
    """Stops the Babysitter service using its PID."""
    if not os.path.exists(PID_FILE):
        print("Babysitter is not running (no PID file).")
        return

    with open(PID_FILE, "r") as f:
        pid = int(f.read())
    
    print(f"Stopping Babysitter process with PID: {pid}...")
    try:
        # Use taskkill to terminate the process group gracefully.
        subprocess.run(['taskkill', '/PID', str(pid), '/T', '/F'], check=True)
        print("Stop signal sent.")
    except Exception:
        print(f"Process with PID {pid} may have already stopped.")
    
    os.remove(PID_FILE)

def status():
    """Check if the Babysitter service is running."""
    if not os.path.exists(PID_FILE):
        print("Babysitter is not running (no PID file).")
        return
    
    with open(PID_FILE, "r") as f:
        pid = int(f.read())
    
    try:
        # Check if process is still running
        result = subprocess.run(['tasklist', '/FI', f'PID eq {pid}'], 
                              capture_output=True, text=True)
        if str(pid) in result.stdout:
            print(f"Babysitter is running with PID: {pid}")
        else:
            print(f"Babysitter PID {pid} not found. Cleaning up stale PID file.")
            os.remove(PID_FILE)
    except Exception as e:
        print(f"Error checking process status: {e}")

# --- Main command-line interface ---
if __name__ == "__main__":
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        if command == 'start':
            start()
        elif command == 'stop':
            stop()
        elif command == 'status':
            status()
        else:
            print("Usage: python manage_babysitter.py [start|stop|status]")
    else:
        print("Usage: python manage_babysitter.py [start|stop|status]")
        print("")
        print("Commands:")
        print("  start  - Start the Babysitter service in background")
        print("  stop   - Stop the Babysitter service")
        print("  status - Check if Babysitter is running")