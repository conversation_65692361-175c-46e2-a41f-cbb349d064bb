#!/usr/bin/env python3
"""
Simple test for Windows - copy this and run on Windows command prompt
"""

import os
import sys
import shutil

def main():
    print("=== Simple Windows OCR Test ===")
    
    # Step 1: Copy tesseract55.dll to root
    dll_source = "ocr_accelerator\\x64\\Release\\tesseract55.dll"
    dll_target = "tesseract55.dll"
    
    if os.path.exists(dll_source):
        if not os.path.exists(dll_target):
            shutil.copy2(dll_source, dll_target)
            print("Copied tesseract55.dll to root")
        else:
            print("tesseract55.dll already in root")
    else:
        print(f"ERROR: {dll_source} not found")
        return
    
    # Step 2: Import module
    build_dir = "ocr_accelerator\\x64\\Release"
    sys.path.insert(0, os.path.abspath(build_dir))
    
    original_dir = os.getcwd()
    os.chdir(build_dir)
    
    try:
        import ocr_accelerator
        print("✅ Module imported")
        
        # Check attributes
        attrs = [a for a in dir(ocr_accelerator) if not a.startswith('_')]
        print(f"Attributes: {attrs}")
        
        # Test function
        if hasattr(ocr_accelerator, 'test_function'):
            result = ocr_accelerator.test_function()
            print(f"test_function(): {result}")
        else:
            print("❌ No test_function")
            
        if hasattr(ocr_accelerator, 'process_image_and_ocr'):
            print("✅ process_image_and_ocr found")
        else:
            print("❌ No process_image_and_ocr")
    
    except Exception as e:
        print(f"Import failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        os.chdir(original_dir)
    
    # Cleanup
    cleanup = input("Remove tesseract55.dll from root? (y/n): ")
    if cleanup.lower() == 'y':
        try:
            os.remove(dll_target)
            print("Cleaned up tesseract55.dll")
        except:
            print("Could not remove tesseract55.dll")

if __name__ == "__main__":
    main()