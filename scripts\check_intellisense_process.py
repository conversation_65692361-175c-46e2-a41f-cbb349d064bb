#!/usr/bin/env python3
"""
IntelliSense Process Health Check

Verifies that IntelliSense process is running and healthy.
Checks logs for Redis connection and ACK behavior.
"""

import os
import psutil
import subprocess
import glob
from datetime import datetime, timedelta
from typing import List, Dict, Any

def find_intellisense_processes():
    """Find running IntelliSense processes."""
    intellisense_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'memory_info']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            name = proc.info['name'].lower()
            
            # Look for IntelliSense-related processes
            if any(keyword in cmdline.lower() for keyword in ['intellisense', 'intelligence']) or \
               any(keyword in name for keyword in ['intellisense', 'intelligence']):
                
                intellisense_processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'cmdline': cmdline,
                    'create_time': datetime.fromtimestamp(proc.info['create_time']),
                    'memory_mb': proc.info['memory_info'].rss / (1024 * 1024) if proc.info['memory_info'] else 0
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return intellisense_processes

def check_intellisense_logs():
    """Check IntelliSense logs for Redis activity and errors."""
    log_patterns = [
        "logs/intellisense*.log",
        "intellisense/logs/*.log", 
        "../intellisense/logs/*.log",
        "logs/*intellisense*.log",
        "*.log"  # Fallback - check all logs
    ]
    
    log_analysis = {
        "log_files_found": [],
        "recent_activity": False,
        "redis_connections": 0,
        "redis_errors": 0,
        "xack_calls": 0,
        "xreadgroup_calls": 0,
        "storage_writes": 0,
        "error_patterns": []
    }
    
    cutoff_time = datetime.now() - timedelta(minutes=10)  # Last 10 minutes
    
    for pattern in log_patterns:
        for log_file in glob.glob(pattern):
            if not os.path.isfile(log_file):
                continue
                
            try:
                mtime = datetime.fromtimestamp(os.path.getmtime(log_file))
                if mtime < cutoff_time:
                    continue  # Skip old log files
                
                log_analysis["log_files_found"].append({
                    "file": log_file,
                    "modified": mtime.strftime("%Y-%m-%d %H:%M:%S"),
                    "size": os.path.getsize(log_file)
                })
                
                # Analyze log content
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    
                    # Look at recent lines (last 1000)
                    recent_lines = lines[-1000:] if len(lines) > 1000 else lines
                    
                    for line in recent_lines:
                        line_lower = line.lower()
                        
                        # Check for recent activity (last 10 minutes)
                        if any(timestamp in line for timestamp in [
                            datetime.now().strftime("%Y-%m-%d %H:%M"),
                            (datetime.now() - timedelta(minutes=1)).strftime("%Y-%m-%d %H:%M"),
                            (datetime.now() - timedelta(minutes=2)).strftime("%Y-%m-%d %H:%M")
                        ]):
                            log_analysis["recent_activity"] = True
                        
                        # Count Redis operations
                        if "redis" in line_lower and "connect" in line_lower:
                            log_analysis["redis_connections"] += 1
                        if "xack" in line_lower:
                            log_analysis["xack_calls"] += 1
                        if "xreadgroup" in line_lower:
                            log_analysis["xreadgroup_calls"] += 1
                        if any(keyword in line_lower for keyword in ["wrote", "saved", "stored", "persisted"]):
                            log_analysis["storage_writes"] += 1
                        
                        # Look for errors
                        if any(keyword in line_lower for keyword in ["error", "exception", "failed", "timeout"]):
                            if "redis" in line_lower or "intellisense" in line_lower:
                                log_analysis["redis_errors"] += 1
                                if len(log_analysis["error_patterns"]) < 5:  # Keep first 5 errors
                                    log_analysis["error_patterns"].append(line.strip()[:200])
                        
            except Exception as e:
                log_analysis["error_patterns"].append(f"Error reading {log_file}: {e}")
    
    return log_analysis

def check_intellisense_config():
    """Check for IntelliSense configuration files."""
    config_patterns = [
        "intellisense/config/*.json",
        "intellisense/config/*.yaml", 
        "intellisense/config/*.yml",
        "config/intellisense*.json",
        "../intellisense/config/*"
    ]
    
    config_files = []
    for pattern in config_patterns:
        for config_file in glob.glob(pattern):
            if os.path.isfile(config_file):
                config_files.append({
                    "file": config_file,
                    "modified": datetime.fromtimestamp(os.path.getmtime(config_file)).strftime("%Y-%m-%d %H:%M:%S"),
                    "size": os.path.getsize(config_file)
                })
    
    return config_files

def main():
    """Main check function."""
    print("🧠 IntelliSense Process Health Check")
    print("=" * 50)
    
    # Check running processes
    print("\n🔍 Process Status:")
    processes = find_intellisense_processes()
    
    if processes:
        for proc in processes:
            uptime = datetime.now() - proc['create_time']
            print(f"✅ PID {proc['pid']}: {proc['name']}")
            print(f"   📅 Started: {proc['create_time'].strftime('%Y-%m-%d %H:%M:%S')} (uptime: {uptime})")
            print(f"   💾 Memory: {proc['memory_mb']:.1f} MB")
            print(f"   📋 Command: {proc['cmdline'][:100]}...")
    else:
        print("❌ No IntelliSense processes found!")
        print("   💡 IntelliSense may not be running or may have a different process name")
    
    # Check configuration
    print(f"\n⚙️  Configuration Files:")
    config_files = check_intellisense_config()
    if config_files:
        for config in config_files:
            print(f"✅ {config['file']} ({config['size']} bytes, modified: {config['modified']})")
    else:
        print("⚪ No IntelliSense config files found in common locations")
    
    # Check logs
    print(f"\n📋 Log Analysis (last 10 minutes):")
    log_analysis = check_intellisense_logs()
    
    if log_analysis["log_files_found"]:
        print(f"📄 Log files analyzed: {len(log_analysis['log_files_found'])}")
        for log_info in log_analysis["log_files_found"][:3]:  # Show first 3
            print(f"   📄 {log_info['file']} ({log_info['size']} bytes, {log_info['modified']})")
        
        print(f"\n📊 Activity Summary:")
        print(f"   🔄 Recent activity: {'✅ Yes' if log_analysis['recent_activity'] else '❌ No'}")
        print(f"   🔗 Redis connections: {log_analysis['redis_connections']}")
        print(f"   📥 XREADGROUP calls: {log_analysis['xreadgroup_calls']}")
        print(f"   ✅ XACK calls: {log_analysis['xack_calls']}")
        print(f"   💾 Storage writes: {log_analysis['storage_writes']}")
        print(f"   ❌ Redis errors: {log_analysis['redis_errors']}")
        
        if log_analysis["error_patterns"]:
            print(f"\n🚨 Recent Errors:")
            for error in log_analysis["error_patterns"][:3]:
                print(f"   ⚠️  {error}")
    else:
        print("⚪ No recent IntelliSense log files found")
    
    # Health assessment
    print(f"\n📋 HEALTH ASSESSMENT:")
    print("-" * 30)
    
    health_score = 0
    max_score = 5
    
    if processes:
        print("✅ IntelliSense process is running")
        health_score += 1
    else:
        print("❌ IntelliSense process not found")
    
    if log_analysis["recent_activity"]:
        print("✅ Recent log activity detected")
        health_score += 1
    else:
        print("❌ No recent log activity")
    
    if log_analysis["xreadgroup_calls"] > 0:
        print("✅ Redis XREADGROUP calls detected")
        health_score += 1
    else:
        print("❌ No Redis XREADGROUP calls found")
    
    if log_analysis["xack_calls"] > 0:
        print("✅ Redis XACK calls detected")
        health_score += 1
    else:
        print("❌ No Redis XACK calls found (CRITICAL!)")
    
    if log_analysis["redis_errors"] == 0:
        print("✅ No Redis errors in recent logs")
        health_score += 1
    else:
        print(f"❌ {log_analysis['redis_errors']} Redis errors found")
    
    print(f"\n🎯 Overall Health: {health_score}/{max_score}")
    
    if health_score < 3:
        print("🚨 CRITICAL: IntelliSense appears unhealthy!")
        print("   💡 Recommended actions:")
        print("   1. Check if IntelliSense is properly started")
        print("   2. Verify Redis connection settings")
        print("   3. Check IntelliSense logs for detailed errors")
        print("   4. Consider restarting IntelliSense")
    elif health_score < 5:
        print("⚠️  WARNING: IntelliSense may have issues")
        print("   💡 Monitor closely and check logs")
    else:
        print("✅ IntelliSense appears healthy!")

if __name__ == "__main__":
    main()
