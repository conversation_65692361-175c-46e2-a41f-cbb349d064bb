# launch_gui.ps1 - Quick launcher for TANK Tools GUI
Write-Host "Launching TANK Tools Control Center GUI..." -ForegroundColor Green

try {
    # Launch the GUI
    & .\tank_gui_simple.ps1
} catch {
    Write-Host "Error launching GUI: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Press any key to exit..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
