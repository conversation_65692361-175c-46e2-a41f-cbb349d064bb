"""
REST API router for GUI backend.
Provides endpoints for actions and queries.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import logging

router = APIRouter()
logger = logging.getLogger("GUI.API")


# Pydantic models for requests
class ActionRequest(BaseModel):
    """Generic action request model."""
    action: str
    data: Dict[str, Any]


class OrderRequest(BaseModel):
    """Order placement request."""
    symbol: str
    side: str  # BUY or SELL
    quantity: float
    order_type: str = "MKT"
    limit_price: Optional[float] = None


# API Endpoints
@router.post("/action")
async def execute_action(request: ActionRequest):
    """
    Execute a generic action by sending command to Redis.
    Following Horseshoe pattern - GUI only sends commands.
    """
    app_state = router.app.state.app_state
    
    # Send command
    command_id = await app_state.send_command(request.action, request.data)
    
    if command_id:
        return {
            "status": "success",
            "command_id": command_id,
            "message": f"Action '{request.action}' sent successfully"
        }
    else:
        raise HTTPException(status_code=500, detail="Failed to send command")


@router.post("/order")
async def place_order(request: OrderRequest):
    """Place a trading order."""
    app_state = router.app.state.app_state
    
    # Validate request
    if request.side not in ["BUY", "SELL"]:
        raise HTTPException(status_code=400, detail="Invalid side. Must be BUY or SELL")
    
    if request.quantity <= 0:
        raise HTTPException(status_code=400, detail="Quantity must be positive")
    
    # Send order command
    order_data = {
        "symbol": request.symbol,
        "side": request.side,
        "quantity": request.quantity,
        "order_type": request.order_type,
        "limit_price": request.limit_price
    }
    
    command_id = await app_state.send_command("place_order", order_data)
    
    if command_id:
        return {
            "status": "success",
            "command_id": command_id,
            "message": f"Order command sent for {request.quantity} shares of {request.symbol}"
        }
    else:
        raise HTTPException(status_code=500, detail="Failed to send order command")


@router.get("/streams")
async def get_monitored_streams():
    """Get list of monitored Redis streams."""
    app_state = router.app.state.app_state
    return {
        "streams": app_state.monitored_streams,
        "handlers": app_state.message_router.get_registered_streams() if app_state.message_router else []
    }


@router.get("/stats")
async def get_statistics():
    """Get handler statistics."""
    app_state = router.app.state.app_state
    
    if app_state.message_router:
        return app_state.message_router.get_all_stats()
    else:
        return {"error": "Message router not initialized"}


@router.post("/command/{command_name}")
async def send_command(command_name: str, data: Dict[str, Any] = {}):
    """Send a specific command to the trading system."""
    app_state = router.app.state.app_state
    
    # List of allowed commands
    allowed_commands = [
        "get_positions",
        "get_account",
        "get_orders",
        "cancel_order",
        "close_position",
        "start_trading",
        "stop_trading"
    ]
    
    if command_name not in allowed_commands:
        raise HTTPException(
            status_code=400,
            detail=f"Unknown command. Allowed: {', '.join(allowed_commands)}"
        )
    
    command_id = await app_state.send_command(command_name, data)
    
    if command_id:
        return {
            "status": "success",
            "command": command_name,
            "command_id": command_id
        }
    else:
        raise HTTPException(status_code=500, detail="Failed to send command")