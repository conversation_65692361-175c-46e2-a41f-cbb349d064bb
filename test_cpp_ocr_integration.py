#!/usr/bin/env python3
"""
Test script to verify C++ OCR accelerator integration in OCRService.
"""

import numpy as np
import cv2
import logging
from modules.ocr.ocr_service import OCRService, CPP_ACCELERATOR_AVAILABLE

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_cpp_accelerator_integration():
    """Test that the C++ accelerator is properly integrated."""
    
    print(f"C++ Accelerator Available: {CPP_ACCELERATOR_AVAILABLE}")
    
    if not CPP_ACCELERATOR_AVAILABLE:
        print("C++ accelerator not available, cannot test integration.")
        return False
    
    # Test direct C++ accelerator call
    try:
        import ocr_accelerator
        
        # Create a simple test image (white background with black text)
        test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255  # White background
        cv2.putText(test_image, "TEST 123", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        print("Testing C++ accelerator direct call...")
        result = ocr_accelerator.process_image_and_ocr(
            test_image,
            2.0,    # upscale_factor
            True,   # force_black_text_on_white
            1.0,    # unsharp_strength
            25,     # threshold_block_size
            -2,     # threshold_c
            1.0,    # red_boost
            1.0,    # green_boost
            True,   # apply_text_mask_cleaning
            15,     # text_mask_min_contour_area
            2,      # text_mask_min_width
            2,      # text_mask_min_height
            True,   # enhance_small_symbols
            16,     # symbol_max_height
            (0.5, 1.8),  # period_comma_ratio
            2,      # period_comma_radius
            1.8,    # hyphen_min_ratio
            2       # hyphen_min_height
        )
        
        extracted_text = result["text"].strip()
        confidence = result["confidence"]
        total_time_ms = (result["t4_tess_done_ns"] - result["t0_entry_ns"]) / 1_000_000
        
        print(f"C++ Accelerator Results:")
        print(f"  Text: '{extracted_text}'")
        print(f"  Confidence: {confidence:.1f}%")
        print(f"  Total Time: {total_time_ms:.1f}ms")
        print(f"  Contains expected text: {'TEST' in extracted_text}")
        
        return True
        
    except Exception as e:
        print(f"Error testing C++ accelerator: {e}")
        return False

def test_ocr_service_integration():
    """Test that OCRService properly uses the C++ accelerator."""
    
    try:
        # Create OCRService instance (without full initialization)
        ocr_service = OCRService()
        
        print("OCRService instantiated successfully with C++ accelerator integration")
        
        # Check that the accelerator flag is properly set
        print(f"OCRService has CPP_ACCELERATOR_AVAILABLE: {CPP_ACCELERATOR_AVAILABLE}")
        
        return True
        
    except Exception as e:
        print(f"Error testing OCRService integration: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("C++ OCR Accelerator Integration Test")
    print("=" * 60)
    
    # Test 1: Direct C++ accelerator
    print("\n1. Testing C++ accelerator direct call...")
    cpp_test_passed = test_cpp_accelerator_integration()
    
    # Test 2: OCRService integration
    print("\n2. Testing OCRService integration...")
    service_test_passed = test_ocr_service_integration()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY:")
    print(f"  C++ Accelerator Direct Test: {'PASSED' if cpp_test_passed else 'FAILED'}")
    print(f"  OCRService Integration Test: {'PASSED' if service_test_passed else 'FAILED'}")
    
    if cpp_test_passed and service_test_passed:
        print("\n✅ All tests PASSED! C++ OCR accelerator is properly integrated.")
    else:
        print("\n❌ Some tests FAILED. Check the error messages above.")
    print("=" * 60)