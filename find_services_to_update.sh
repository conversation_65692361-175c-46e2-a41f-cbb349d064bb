#!/bin/bash

echo "=== Services that need telemetry_service updates ==="
echo
grep -n "telemetry_service = container\.resolve(ITelemetryService)" core/di_registration.py | while IFS=: read -r line_num line_content; do
    # Get the factory name by looking backward from the line
    factory_name=$(grep -B15 "telemetry_service = container\.resolve(ITelemetryService)" core/di_registration.py | grep -E "def [a-z_]+_factory\(" | tail -1 | sed 's/.*def \([a-z_]*\)_factory.*/\1/')
    echo "Line $line_num: $factory_name factory"
done

echo
echo "=== Services that need ipc_client updates ==="
echo
grep -n "ipc_client = container\.resolve(IBulletproofBabysitterIPCClient)" core/di_registration.py | while IFS=: read -r line_num line_content; do
    # Get the factory name by looking backward from the line
    factory_name=$(grep -B15 "ipc_client = container\.resolve(IBulletproofBabysitterIPCClient)" core/di_registration.py | grep -E "def [a-z_]+_factory\(" | tail -1 | sed 's/.*def \([a-z_]*\)_factory.*/\1/')
    echo "Line $line_num: $factory_name factory"
done