#!/usr/bin/env python3
"""
Debug DLL dependencies for the C++ OCR accelerator module.
"""

import os
import sys
import platform
import subprocess

def check_dll_dependencies():
    """Check what DLLs are missing using Windows tools."""
    if platform.system() != "Windows":
        print("Not on Windows, cannot check DLL dependencies")
        return
        
    print("=== DLL Dependency Analysis ===")
    
    # Path to the .pyd file
    pyd_path = os.path.join("ocr_accelerator", "x64", "Release", "ocr_accelerator.pyd")
    
    if not os.path.exists(pyd_path):
        print(f"❌ PYD file not found: {pyd_path}")
        return
        
    print(f"✅ Analyzing: {os.path.abspath(pyd_path)}")
    
    try:
        # Use Windows 'dumpbin' tool if available (part of Visual Studio)
        result = subprocess.run([
            "dumpbin", "/dependents", pyd_path
        ], capture_output=True, text=True, shell=True)
        
        if result.returncode == 0:
            print("✅ DLL Dependencies (dumpbin):")
            print(result.stdout)
        else:
            print("⚠️  dumpbin not available, trying alternative method")
            
    except FileNotFoundError:
        print("⚠️  dumpbin not found in PATH")
    
    # Alternative: Try to load the module and see the specific error
    print("\n=== Attempting Module Load ===")
    
    # Set up the environment
    build_dir = os.path.abspath(os.path.join("ocr_accelerator", "x64", "Release"))
    
    if hasattr(os, 'add_dll_directory'):
        os.add_dll_directory(build_dir)
        
    if build_dir not in sys.path:
        sys.path.insert(0, build_dir)
    
    # Try to import and capture the exact error
    try:
        import ocr_accelerator
        print("✅ Module loaded successfully!")
    except ImportError as e:
        error_msg = str(e)
        print(f"❌ Import error: {error_msg}")
        
        # Try to identify the specific missing DLL
        if "DLL load failed" in error_msg:
            print("\n🔍 Checking for common missing DLLs:")
            
            common_dlls = [
                "msvcp140.dll",      # Visual C++ Redistributable
                "vcruntime140.dll",  # Visual C++ Runtime
                "vcruntime140_1.dll", # Visual C++ Runtime (newer)
                "api-ms-win-crt-runtime-l1-1-0.dll",  # Universal CRT
                "kernel32.dll",      # Windows Kernel
                "user32.dll",        # Windows User
                "ole32.dll",         # Windows OLE
                "oleaut32.dll",      # Windows OLE Automation
            ]
            
            for dll in common_dlls:
                try:
                    # Try to find the DLL in system directories
                    result = subprocess.run([
                        "where", dll
                    ], capture_output=True, text=True, shell=True)
                    
                    if result.returncode == 0:
                        print(f"  ✅ {dll} found in system")
                    else:
                        print(f"  ❌ {dll} NOT FOUND in system")
                        
                except Exception:
                    print(f"  ⚠️  Could not check {dll}")

def check_vcredist():
    """Check if Visual C++ Redistributable is installed."""
    print("\n=== Visual C++ Redistributable Check ===")
    
    try:
        # Check for Visual C++ Redistributable registry entries
        result = subprocess.run([
            "reg", "query", 
            "HKLM\\SOFTWARE\\Microsoft\\VisualStudio\\14.0\\VC\\Runtimes\\x64",
            "/v", "Version"
        ], capture_output=True, text=True, shell=True)
        
        if result.returncode == 0:
            print("✅ Visual C++ 2015-2022 Redistributable (x64) found")
            print(result.stdout)
        else:
            print("❌ Visual C++ 2015-2022 Redistributable (x64) NOT FOUND")
            print("📝 Install from: https://aka.ms/vs/17/release/vc_redist.x64.exe")
            
    except Exception as e:
        print(f"⚠️  Could not check Visual C++ Redistributable: {e}")

if __name__ == "__main__":
    check_dll_dependencies()
    check_vcredist()
    
    print("\n💡 Suggestions:")
    print("1. Install Visual C++ 2015-2022 Redistributable (x64)")
    print("2. Check if all required DLLs are in the build directory")
    print("3. Verify the .pyd file was compiled with the correct architecture (x64)")