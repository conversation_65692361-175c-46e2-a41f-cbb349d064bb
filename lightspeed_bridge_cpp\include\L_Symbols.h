#pragma once
#if !defined(LS_L_SYMBOLS_H)
#define LS_L_SYMBOLS_H

// Copyright (c) 2001-2018 Lightspeed Financial, Inc. All rights reserved.

#include "L_Application.h"

namespace LightspeedTrader
{

namespace L_SecFlag
{
	enum L_SecFlagType
	{
		ANY = -1,
		NONE = 0,
		EQUITY = 1,
		FUTURE = 2,
		OPTION = 4
	};
}

#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

static const char INDEX_PREFIX		= '.';
static const char FUTURES_PREFIX	= '/';
static const char OPTIONS_PREFIX	= '!';

inline bool L_IsEquity(char const *symbol)
{
	switch (*symbol)
	{
	case INDEX_PREFIX:
	case FUTURES_PREFIX:
	case OPTIONS_PREFIX:
		return false;
	}
	return true;
}

inline bool L_IsFuture(char const *symbol)
{
	return *symbol == FUTURES_PREFIX;
}

inline bool L_IsIndex(char const *symbol)
{
	return *symbol == INDEX_PREFIX;
}

#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

}

#endif // !defined(LS_L_SYMBOLS_H)
