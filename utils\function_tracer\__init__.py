"""
Function Tracer Package

This package provides utilities for tracing function execution throughout the application.
It includes both real and dummy implementations of function tracing.

The real implementation is used when function tracing is enabled, while the dummy
implementation is used when function tracing is disabled to minimize overhead.

Usage:
    # Import the interface module to get the appropriate implementation
    from utils.function_tracer.interface import trace_function
    
    # Or import the package and use the interface module
    from utils import function_tracer
    @function_tracer.trace_function('module_name')
    def my_function():
        pass
"""

# Import the interface module to make it available at the package level
from . import interface

# Re-export the interface functions for convenience
from .interface import (
    trace_function,
    enable_function_tracing,
    is_function_tracing_enabled,
    cleanup_trace_context,
    setup_function_trace_logging,
    check_file_size,
    rotate_trace_file,
    init_trace_file,
    update_global_config_with_function_tracing
)

# Log that the function_tracer package has been initialized
import logging
logger = logging.getLogger(__name__)
logger.info("function_tracer package initialized")
