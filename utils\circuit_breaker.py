# TESTRADE/utils/circuit_breaker.py
import time
import logging
from typing import Callable, Any

logger = logging.getLogger(__name__)

class CircuitBreakerOpenError(Exception):
    """Custom exception raised when the circuit breaker is open."""
    def __init__(self, message="Circuit breaker is open. Call rejected."):
        self.message = message
        super().__init__(self.message)

class ServiceCircuitBreaker:
    def __init__(self, failure_threshold: int = 3, recovery_timeout_seconds: int = 30, name: str = "UnnamedCircuit"):
        """
        Initializes the Circuit Breaker.

        Args:
            failure_threshold: Number of consecutive failures to open the circuit.
            recovery_timeout_seconds: Seconds to wait in OPEN state before transitioning to HALF_OPEN.
            name: A descriptive name for this circuit breaker instance (for logging).
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout_seconds = recovery_timeout_seconds
        self.name = name
        
        self._failure_count = 0
        self._last_failure_time: float = 0.0
        self._state = 'CLOSED'  # States: CLOSED, OPEN, HALF_OPEN
        
        logger.info(f"CircuitBreaker '{self.name}' initialized: Threshold={self.failure_threshold}, Timeout={self.recovery_timeout_seconds}s")

    @property
    def state(self):
        return self._state

    def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Executes the given function if the circuit is CLOSED or HALF_OPEN.
        If the circuit is OPEN, it raises CircuitBreakerOpenError or returns a fallback.
        """
        if self._state == 'OPEN':
            if (time.time() - self._last_failure_time) > self.recovery_timeout_seconds:
                self._state = 'HALF_OPEN'
                logger.warning(f"CircuitBreaker '{self.name}': State changed to HALF_OPEN. Recovery timeout elapsed.")
                # Reset failure count for the HALF_OPEN attempt
                # self._failure_count = 0 # Resetting here means first call in HALF_OPEN is like a fresh start
                                      # Let's not reset, so a failure in HALF_OPEN immediately re-trips to OPEN
            else:
                logger.warning(f"CircuitBreaker '{self.name}': Call rejected, circuit is OPEN. Time until HALF_OPEN: {self.recovery_timeout_seconds - (time.time() - self._last_failure_time):.1f}s")
                raise CircuitBreakerOpenError(f"CircuitBreaker '{self.name}' is OPEN.")
        
        # Attempt the call if CLOSED or HALF_OPEN
        try:
            result = func(*args, **kwargs)
            # If successful, especially in HALF_OPEN state, reset to CLOSED
            if self._state == 'HALF_OPEN':
                self._reset()
                logger.info(f"CircuitBreaker '{self.name}': Call successful in HALF_OPEN state. Circuit RESET to CLOSED.")
            # If it was already CLOSED and succeeded, _failure_count is already 0 or low.
            # We could also reset failure_count here on any success in CLOSED state if desired.
            # For now, let's only explicitly reset from HALF_OPEN success.
            # If it was CLOSED and successful, failure_count remains as is (likely 0).
            # If it was CLOSED and previously had some failures < threshold, a success should reset it.
            if self._state == 'CLOSED' and self._failure_count > 0:
                self._reset_failure_count() # Reset if it was CLOSED but had some prior failures

            return result
        except CircuitBreakerOpenError: # Re-raise if it was caught by nested breaker
            raise
        except Exception as e:
            self._record_failure()
            logger.error(f"CircuitBreaker '{self.name}': Call failed. Error: {e}. Failure count: {self._failure_count}/{self.failure_threshold}. State: {self._state}")
            raise # Re-raise the original exception after recording failure

    def _record_failure(self):
        self._failure_count += 1
        self._last_failure_time = time.time()
        if self._failure_count >= self.failure_threshold:
            if self._state != 'OPEN': # Avoid spamming "state changed to OPEN"
                self._state = 'OPEN'
                logger.critical(f"CircuitBreaker '{self.name}': Failure threshold reached. State changed to OPEN for {self.recovery_timeout_seconds}s.")

    def _reset(self):
        """Resets the circuit to CLOSED state and clears failure count."""
        self._state = 'CLOSED'
        self._failure_count = 0
        # self._last_failure_time = 0.0 # Not strictly necessary to reset last_failure_time
        logger.info(f"CircuitBreaker '{self.name}': RESET to CLOSED state.")

    def _reset_failure_count(self):
        """Resets only the failure count, typically on success in CLOSED state after prior non-tripping failures."""
        if self._failure_count > 0: # Only log if there was something to reset
            logger.info(f"CircuitBreaker '{self.name}': Failure count reset to 0 from {self._failure_count} (state was CLOSED).")
        self._failure_count = 0

    def force_open(self):
        """Manually forces the circuit to the OPEN state."""
        self._state = 'OPEN'
        self._last_failure_time = time.time() # Set failure time to start recovery timer
        self._failure_count = self.failure_threshold # Ensure it reflects threshold met
        logger.warning(f"CircuitBreaker '{self.name}': Manually FORCED OPEN.")

    def force_close(self):
        """Manually forces the circuit to the CLOSED state (resets it)."""
        self._reset()
        logger.warning(f"CircuitBreaker '{self.name}': Manually FORCED CLOSED (reset).")
