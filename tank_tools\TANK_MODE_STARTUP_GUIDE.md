# TESTRADE TANK MODE Startup Guide

## Overview

TANK MODE is TESTRADE's resilient architecture that provides:
- **Tank-like resilience**: Core operations continue even if Redis fails
- **Zero data loss**: Disk buffering with automatic replay
- **Clean separation**: Babysitter service handles all Redis operations
- **Health monitoring**: Comprehensive system health tracking

## Architecture

```
GUI Backend → BabysitterIPCClient → Babysitter → Redis
Core → BabysitterIPCClient → Babysitter → Redis
GUI Backend → Babysitter → ZMQ → Core/OCR (commands)
```

## Startup Sequence

### 1. Prerequisites

Ensure Redis is running:
```bash
# Check Redis status
redis-cli -h ************** ping
```

### 2. Start Components (in order)

#### Step 1: Start Babysitter Service
```bash
# Option A: Using startup script (recommended)
python start_babysitter_with_config.py

# Option B: Manual with environment variables
export BABYSITTER_REDIS_HOST=**************
export BABYSITTER_REDIS_PORT=6379
export BABYSITTER_REDIS_DB=0
python core/babysitter_service.py
```

#### Step 2: Start TESTRADE Core
```bash
python run_headless_core.py
```

#### Step 3: Start GUI Backend
```bash
cd gui
python gui_backend.py
```

#### Step 4: Start GUI Frontend
```bash
cd gui
npm start
# or
python -m http.server 3000
```

### 3. Verify TANK MODE Operation

#### Test Health Monitoring
```bash
python test_tank_mode.py --component core --duration 30
```

#### Test Command Flow
```bash
python test_tank_mode.py --component gui --duration 30
```

#### Test Full Integration
```bash
python test_tank_mode.py --component all --duration 120
```

## Configuration

### Key Configuration Files

1. **config/control.json**: Main configuration
   - IPC addresses for ZMQ communication
   - Redis connection details
   - Health monitoring settings

2. **Environment Variables** (for Babysitter):
   - `BABYSITTER_REDIS_HOST`: Redis server IP
   - `BABYSITTER_REDIS_PORT`: Redis server port
   - `BABYSITTER_DISK_BUFFER_PATH`: Disk buffer location

### Important Settings

```json
{
  "babysitter_ipc_address": "tcp://localhost:5555",
  "core_ipc_command_pull_address": "tcp://localhost:5556",
  "ocr_ipc_command_pull_address": "tcp://localhost:5557",
  "BABYSITTER_REDIS_HOST": "**************",
  "BABYSITTER_REDIS_PORT": 6379,
  "redis_stream_core_health": "testrade:health:core",
  "redis_stream_babysitter_health": "testrade:health:babysitter"
}
```

## Health Monitoring

### Health Streams

- **testrade:health:core**: Core component health
- **testrade:health:babysitter**: Babysitter service health  
- **testrade:health:redis_instance**: Redis instance health

### Health Check Commands

```bash
# Monitor Core health
redis-cli -h ************** XREAD STREAMS testrade:health:core $

# Monitor Babysitter health
redis-cli -h ************** XREAD STREAMS testrade:health:babysitter $

# Monitor all health streams
redis-cli -h ************** XREAD STREAMS testrade:health:core testrade:health:babysitter $ $
```

## Troubleshooting

### Common Issues

1. **Babysitter won't start**
   - Check Redis connectivity: `redis-cli -h ************** ping`
   - Verify port 5555 is available: `netstat -an | grep 5555`
   - Check logs: `tail -f logs/babysitter_service.log`

2. **Core can't connect to Babysitter**
   - Verify Babysitter is running: `ps aux | grep babysitter`
   - Check ZMQ ports 5556/5557 are available
   - Review Core logs for connection errors

3. **Commands not working**
   - Test GUI Backend: `curl -X POST http://localhost:8001/control/command -H "Content-Type: application/json" -d '{"command":"get_global_configuration","parameters":{}}'`
   - Check command flow: `python test_tank_mode.py --component gui`

### Log Locations

- **Core**: `logs/core.log`
- **Babysitter**: `logs/babysitter_service.log`
- **GUI Backend**: Console output
- **Disk Buffer**: `data/babysitter_disk_buffer/`

### Emergency Recovery

If Redis fails:
1. Core continues operating (TANK MODE)
2. Data is buffered to disk automatically
3. When Redis recovers, data is replayed automatically
4. No manual intervention required

## Performance Monitoring

### Key Metrics

- **Emergency buffer size**: Items waiting to be sent
- **Disk buffer files**: Number of pending replay files
- **Health check intervals**: Component responsiveness

### Monitoring Commands

```bash
# Check emergency buffer status
python -c "
import zmq
from core.bulletproof_ipc_client import BulletproofBabysitterIPCClient, PlaceholderMissionControlNotifier
from utils.global_config import load_global_config
import logging
config = load_global_config('config/control.json')
zmq_context = zmq.Context.instance()
mission_notifier = PlaceholderMissionControlNotifier(logging.getLogger('StatusCheck'))
client = BulletproofBabysitterIPCClient(zmq_context, config, logging.getLogger('BulletproofIPC'), mission_notifier)
print(f'Emergency buffer: {client.get_total_emergency_buffer_size()} items')
print(f'Healthy: {client.is_healthy()}')
client.close()
"
```

## Success Indicators

**TANK MODE is working correctly when:**
- All health streams show "healthy" status
- Commands execute successfully via GUI
- Emergency buffer size remains low (< 100)
- No errors in Babysitter logs
- Core continues operating during Redis outages

**TANK MODE provides maximum resilience for critical trading operations.**
