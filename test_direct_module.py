#!/usr/bin/env python3
"""
Direct test of the C++ module without complex path manipulation.
"""

import os
import sys

# Simple approach - add DLL path and import directly
dll_path = r"C:\TESTRADE\ocr_accelerator\x64\Release"
if hasattr(os, 'add_dll_directory'):
    os.add_dll_directory(dll_path)

# Add to PATH
current_path = os.environ.get('PATH', '')
os.environ['PATH'] = dll_path + os.pathsep + current_path

# Add to sys.path  
sys.path.insert(0, dll_path)

print("Testing C++ module import...")

try:
    import ocr_accelerator
    print(f"✅ Module imported: {ocr_accelerator}")
    print(f"Module file: {getattr(ocr_accelerator, '__file__', 'N/A')}")
    
    # Get all attributes
    attrs = dir(ocr_accelerator)
    print(f"All attributes: {attrs}")
    
    # Filter for functions (non-underscore, callable)
    functions = [attr for attr in attrs if not attr.startswith('_') and callable(getattr(ocr_accelerator, attr, None))]
    print(f"Functions found: {functions}")
    
    if functions:
        print("✅ SUCCESS: Functions are exported!")
        
        # Test test_function if available
        if 'test_function' in functions:
            try:
                result = ocr_accelerator.test_function()
                print(f"✅ test_function() works: {result}")
            except Exception as e:
                print(f"❌ test_function() failed: {e}")
    else:
        print("❌ FAILURE: No functions found")
        
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()