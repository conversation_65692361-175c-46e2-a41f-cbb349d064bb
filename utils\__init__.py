# utils/__init__.py

"""
Utility modules for the application.

This package contains various utility modules used throughout the application.
"""

# Import the performance tracker package
from . import performance_tracker

# Re-export the performance tracker functions for convenience
from .performance_tracker import (
    enable_performance_tracking,
    is_performance_tracking_enabled,
    create_timestamp_dict,
    add_timestamp,
    calculate_durations,
    add_to_stats,
    get_performance_stats,
    reset_performance_stats,
    reset_all_trackers,
    log_performance_durations,
    export_performance_stats_to_csv,
    get_tracker,
    track_performance,
    PerformanceTracker,
    DummyTracker,
    update_global_config_with_performance_tracking,
    # Enhanced benchmarker integration functions
    initialize_global_benchmarker,
    get_global_benchmarker,
    capture_metric_with_benchmarker,
    add_timestamp_with_capture,
    finalize_performance_scope,
    capture_queue_size,
    capture_throughput_metric,
    capture_cpu_usage
)

# Import the decorators package
from . import decorators

# Re-export the decorator functions for convenience
from .decorators.interface import (
    log_function_call,
    set_module_logging,
    enable_decorators,
    is_decorators_enabled,
    update_global_config_with_decorators
)

# Import the function_tracer package
from . import function_tracer

# Re-export the function_tracer functions for convenience
from .function_tracer.interface import (
    trace_function,
    enable_function_tracing,
    is_function_tracing_enabled,
    cleanup_trace_context,
    setup_function_trace_logging,
    update_global_config_with_function_tracing
)

# Log that the utils package has been initialized
import logging
logger = logging.getLogger(__name__)
logger.info("utils package initialized with performance_tracker, decorators, and function_tracer packages")