# interfaces/utility/services.py

from abc import ABC, abstractmethod
from typing import Optional

class ISystemHealthMonitoringService(ABC):
    """Interface for system health monitoring."""
    pass

class IGUICommandService(ABC):
    """Interface for GUI command service."""
    # TODO: Define proper interface methods when refactoring GUICommandService
    pass

class IROIService(ABC):
    """Interface for ROI service.""" 
    pass

class IPositionEnrichmentService(ABC):
    """Interface for position enrichment service."""
    pass

class ISymbolLoadingService(ABC):
    """Interface for symbol loading service."""
    
    @abstractmethod
    def load_symbols(self) -> None:
        """Load symbols."""
        pass
    
    @abstractmethod
    def get_symbols(self) -> list:
        """Get loaded symbols."""
        pass