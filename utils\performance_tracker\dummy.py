"""
Dummy Performance Tracker Implementation

This module provides no-op implementations of performance tracking utilities.
It is used when performance tracking is disabled to minimize memory usage.

All functions in this module are designed to be drop-in replacements for
the real implementations in the real.py module, but they do nothing and
allocate minimal memory.
"""

import time
from typing import Dict, List, Optional, Any, Union
from contextlib import contextmanager

# Global flag to enable/disable performance tracking (always False in dummy implementation)
PERFORMANCE_TRACKING_ENABLED = False

# Log that the dummy implementation is being used
import logging
_pt_logger = logging.getLogger("DummyPerformanceTracker")
_pt_logger.error("DUMMY_PERFORMANCE_TRACKER.PY LOADED. PERFORMANCE_TRACKING_ENABLED is always False.")

def enable_performance_tracking(enabled=True):
    """
    No-op implementation of enable_performance_tracking.
    Always returns False in the dummy implementation.

    Args:
        enabled: Ignored in the dummy implementation

    Returns:
        Always False
    """
    # Log the attempt to enable performance tracking
    _pt_logger.error(f"Attempt to enable performance tracking ignored in dummy implementation. Requested state: {enabled}")
    
    # Always return False in the dummy implementation
    return False

def is_performance_tracking_enabled():
    """
    No-op implementation of is_performance_tracking_enabled.
    Always returns False in the dummy implementation.

    Returns:
        Always False
    """
    return False

class DummyTracker:
    """A dummy tracker that does nothing."""

    def __init__(self, name: str = "dummy"):
        """Initialize a new DummyTracker."""
        self.name = name

    def mark(self, label: str) -> float:
        """Return current time without recording anything."""
        return time.time()

    def duration(self, start_label: str, end_label: str) -> Optional[float]:
        """Return None without calculating any duration."""
        return None

    def record_duration(self, label: str, duration: float) -> None:
        """Do nothing."""
        pass

    def get_stats(self, label: Optional[str] = None) -> Dict[str, Dict[str, float]]:
        """Return empty stats."""
        return {}

    def reset(self) -> None:
        """Do nothing."""
        pass

    def reset_timestamps(self) -> None:
        """Do nothing."""
        pass

    def to_dict(self) -> Dict[str, Any]:
        """Return empty dict."""
        return {"name": self.name, "timestamps": {}, "durations": {}}

    @contextmanager
    def track(self, label: str):
        """Do nothing context manager."""
        yield

# Use DummyTracker as PerformanceTracker in the dummy implementation
PerformanceTracker = DummyTracker

# Global dummy tracker instance
_dummy_tracker = DummyTracker()

def get_tracker(name: str = "default") -> DummyTracker:
    """
    No-op implementation of get_tracker.
    Always returns a DummyTracker instance.

    Args:
        name: Ignored in the dummy implementation

    Returns:
        A DummyTracker instance
    """
    return _dummy_tracker

def reset_all_trackers() -> None:
    """No-op implementation of reset_all_trackers."""
    pass

@contextmanager
def track_performance(label: str, tracker_name: str = "default"):
    """
    No-op implementation of track_performance.

    Args:
        label: Ignored in the dummy implementation
        tracker_name: Ignored in the dummy implementation
    """
    yield

def create_timestamp_dict() -> Optional[Dict[str, float]]:
    """
    No-op implementation of create_timestamp_dict.
    Always returns None in the dummy implementation.

    Returns:
        Always None
    """
    return None

def add_timestamp(timestamp_dict: Optional[Dict[str, float]], label: str) -> Optional[Dict[str, float]]:
    """
    No-op implementation of add_timestamp.
    Always returns the input timestamp_dict unchanged.

    Args:
        timestamp_dict: The timestamp dictionary to update
        label: Ignored in the dummy implementation

    Returns:
        The input timestamp_dict unchanged
    """
    return timestamp_dict

def calculate_durations(timestamp_dict: Optional[Dict[str, float]]) -> Dict[str, float]:
    """
    No-op implementation of calculate_durations.
    Always returns an empty dictionary.

    Args:
        timestamp_dict: Ignored in the dummy implementation

    Returns:
        An empty dictionary
    """
    return {}

class DummyStatsCollector:
    """A dummy stats collector that does nothing."""

    def add_measurement(self, durations: Dict[str, float]) -> None:
        """Do nothing."""
        pass

    def get_stats(self) -> Dict[str, Dict[str, float]]:
        """Return empty stats."""
        return {}

    def reset(self) -> None:
        """Do nothing."""
        pass

# Global dummy stats collector instance
_stats_collector = DummyStatsCollector()

def add_to_stats(timestamp_dict: Optional[Dict[str, float]]) -> None:
    """
    No-op implementation of add_to_stats.

    Args:
        timestamp_dict: Ignored in the dummy implementation
    """
    pass

def get_performance_stats() -> Dict[str, Dict[str, float]]:
    """
    No-op implementation of get_performance_stats.
    Always returns an empty dictionary.

    Returns:
        An empty dictionary
    """
    return {}

def reset_performance_stats() -> None:
    """No-op implementation of reset_performance_stats."""
    pass

def log_performance_durations(perf_timestamps: Optional[Dict[str, float]], context: str, threshold_ms: float = 10.0) -> None:
    """
    No-op implementation of log_performance_durations.

    Args:
        perf_timestamps: Ignored in the dummy implementation
        context: Ignored in the dummy implementation
        threshold_ms: Ignored in the dummy implementation
    """
    pass

def export_performance_stats_to_csv(filename: str) -> bool:
    """
    No-op implementation of export_performance_stats_to_csv.
    Always returns False in the dummy implementation.

    Args:
        filename: Ignored in the dummy implementation

    Returns:
        Always False
    """
    return False

# Enhanced benchmarker integration dummy functions
def initialize_global_benchmarker(instance):
    """No-op implementation of initialize_global_benchmarker."""
    pass

def get_global_benchmarker():
    """No-op implementation of get_global_benchmarker."""
    return None

def capture_metric_with_benchmarker(metric_name: str, value: float, context=None):
    """No-op implementation of capture_metric_with_benchmarker."""
    pass

def add_timestamp_with_capture(metric_name: str, perf_dict, key: str, context=None):
    """No-op implementation of add_timestamp_with_capture."""
    return perf_dict

def finalize_performance_scope(perf_dict, scope_name: str, context=None):
    """No-op implementation of finalize_performance_scope."""
    pass

def capture_queue_size(service_name: str, queue_size: int, context=None):
    """No-op implementation of capture_queue_size."""
    pass

def capture_throughput_metric(service_name: str, items_processed: int, time_window_seconds: float, context=None):
    """No-op implementation of capture_throughput_metric."""
    pass

def capture_cpu_usage(service_name: str, cpu_percent: float, context=None):
    """No-op implementation of capture_cpu_usage."""
    pass
