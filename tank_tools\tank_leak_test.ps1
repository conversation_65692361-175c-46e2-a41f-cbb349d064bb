# tank_leak_test.ps1 - Simple test version
param(
    [string]$ProcessName = "python",
    [int]$TestSeconds = 30
)

Write-Host "TANK Memory Leak Test" -ForegroundColor Green
Write-Host "Process: $ProcessName | Test Duration: $TestSeconds seconds" -ForegroundColor Cyan
Write-Host "=" * 60

$samples = @()
$startTime = Get-Date

try {
    $process = Get-Process $ProcessName -ErrorAction Stop | Select-Object -First 1
    Write-Host "Found process: $ProcessName (PID: $($process.Id))" -ForegroundColor Green
} catch {
    Write-Host "Process '$ProcessName' not found!" -ForegroundColor Red
    exit 1
}

$endTime = $startTime.AddSeconds($TestSeconds)

while ((Get-Date) -lt $endTime) {
    try {
        $process = Get-Process $ProcessName -ErrorAction Stop | Select-Object -First 1
        $timestamp = Get-Date
        
        $sample = [PSCustomObject]@{
            Timestamp = $timestamp
            MemoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
            HandleCount = $process.HandleCount
            ThreadCount = $process.Threads.Count
        }
        
        $samples += $sample
        
        Write-Host "[$($samples.Count)] $($timestamp.ToString('HH:mm:ss')) | Memory: $($sample.MemoryMB) MB | Handles: $($sample.HandleCount) | Threads: $($sample.ThreadCount)" -ForegroundColor White
        
        Start-Sleep 5
        
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        break
    }
}

Write-Host ""
Write-Host "TEST SUMMARY:" -ForegroundColor Yellow
Write-Host "Samples Collected: $($samples.Count)" -ForegroundColor White

if ($samples.Count -gt 1) {
    $memoryStats = $samples | Measure-Object MemoryMB -Average -Maximum -Minimum
    $memoryGrowth = $samples[-1].MemoryMB - $samples[0].MemoryMB
    
    Write-Host "Average Memory: $([math]::Round($memoryStats.Average, 2)) MB" -ForegroundColor White
    Write-Host "Peak Memory: $([math]::Round($memoryStats.Maximum, 2)) MB" -ForegroundColor White
    Write-Host "Memory Growth: $([math]::Round($memoryGrowth, 2)) MB" -ForegroundColor $(if($memoryGrowth -gt 10) { "Red" } elseif($memoryGrowth -gt 5) { "Yellow" } else { "Green" })
    
    if ($memoryGrowth -gt 10) {
        Write-Host "WARNING: Significant memory growth detected!" -ForegroundColor Red
    } elseif ($memoryGrowth -gt 5) {
        Write-Host "NOTICE: Moderate memory growth detected" -ForegroundColor Yellow
    } else {
        Write-Host "GOOD: Memory usage appears stable" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "Test completed successfully!" -ForegroundColor Green
