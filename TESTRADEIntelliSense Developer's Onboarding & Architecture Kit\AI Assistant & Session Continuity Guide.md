# AI Assistant & Session Continuity Guide for TESTRADE

## Purpose
This guide ensures AI assistants and new developers can quickly understand TESTRADE's complex architecture without "groundhog day" repetition. Read this FIRST before making any modifications.

## Critical System Rules - NEVER VIOLATE THESE

### 1. The TANK Pattern
- **Rule**: The core trading engine (TANK) must NEVER block or wait
- **Why**: Microsecond delays can cost thousands in trading
- **Implementation**: All external communication is one-way via IPC buffers

### 2. Redis is OUTPUT ONLY
- **Rule**: NEVER inject data via Redis streams
- **Why**: Redis streams are for external consumers (GUI, IntelliSense, MCP)
- **Correct Injection**: Use EventBus internally or GUI commands
- **Common Mistake**: Trying to inject cleaned OCR via Redis (won't work!)

### 3. NO GUESSWORK
- **Rule**: This is financial software - be 100% certain or don't do it
- **Why**: Bugs can cause real financial losses
- **Practice**: Read code, trace flows, verify assumptions

## Architecture Overview

### The "Horseshoe" Data Flow
```
TESTRADE Core (Right) → IPC Client → Babysitter (Top) → Redis → Consumers (Left)
                                           ↓
                                    (Redis Streams)
```

### Two Event Systems - DO NOT CONFUSE
1. **Internal EventBus** (in `event_bus.py`)
   - In-memory, in-process only
   - For components inside TESTRADE Core
   - Example: OCR → Orchestrator → MAF

2. **External Redis Streams** (via IPC)
   - System-wide broadcast
   - For GUI, IntelliSense, monitoring
   - One-way only (output from TANK)

### Key Data Flows

#### OCR Processing Flow
```
1. Image Capture (OCR Service)
   ↓ (Internal EventBus)
2. OCR Conditioning Service (child process)
   ↓ (Direct enqueue if flag=true, else EventBus)
3. Orchestrator (generates signals)
   ↓ (Internal EventBus)
4. MAF (filters signals) → Publishes decisions to Redis
   ↓ (Internal EventBus)
5. Risk Management
   ↓ (Internal EventBus)
6. Trade Executor → Orders to broker
```

#### Weekend Behavior
- Markets closed = No broker connection
- No real positions in Position Manager
- Lifecycle Manager rejects operations on non-existent positions
- Result: No signals generated, no MAF events

## Current Implementation Status

### MAF (Master Action Filter) - COMPLETE
- **Location**: `/modules/trade_management/master_action_filter_service.py`
- **Redis Stream**: `testrade:maf-decisions`
- **Features**:
  - Publishes suppression/override events
  - Proper correlation/causation ID tracking
  - Configurable settling periods
- **Testing**: Requires market hours and real positions

### ID Tracking System
- **eventId**: Unique per event
- **correlationId**: Follows entire trade flow
- **causationId**: Links to parent event
- **Documentation**: `ID Tracking System - Complete Guide.md`

### Stream Mappings
- **Config**: `utils/global_config.py` - defines stream names
- **Documentation**: 
  - `testrade_stream_mappings.json` - what exists
  - `intellisense_missing_data_mappings.json` - wishlist

## Testing Challenges & Solutions

### Weekend Testing Limitations
1. **Problem**: No market = no positions = no signals
2. **Attempted Solution**: Position injection via `DEBUG_SIMULATE_POSITION_UPDATE`
3. **Result**: Updates Position Manager temporarily but doesn't persist
4. **Real Solution**: Wait for market hours OR implement image-level replay

### Injection Points
1. **GUI Commands** (works)
   - Via `testrade:commands:from_gui`
   - Processed by ApplicationCore
   - Example: DEBUG_SIMULATE_POSITION_UPDATE

2. **Direct OCR Injection** (doesn't work)
   - Cleaned OCR via Redis is ignored
   - Must inject at image level (future work)

3. **Future: Image-Level Injection**
   - Similar to ROI implementation
   - Enables millisecond-precision replay
   - Full pipeline testing

## Common Pitfalls & Solutions

### Pitfall 1: Trying to Inject via Redis
**Wrong**: `redis.xadd('testrade:cleaned-ocr-snapshots', ...)`
**Right**: Use GUI commands or wait for image-level injection

### Pitfall 2: Expecting Consumer Groups
**Wrong**: Looking for Redis consumer groups
**Right**: TESTRADE uses direct publishing, not consumer groups

### Pitfall 3: Testing on Weekends
**Wrong**: Expecting signals without positions
**Right**: Understand market hours limitations

## Configuration Flags to Know

### OCR Processing
- `FEATURE_FLAG_USE_DIRECT_OCR_CONDITIONED_TO_ORCHESTRATOR`: true
  - When true: OCR conditioning directly enqueues to orchestrator
  - When false: Uses EventBus subscription

### IPC Settings
- `BABYSITTER_ENABLED`: Must be true for Redis publishing
- `ENABLE_IPC_DATA_DUMP`: Enables data flow to Redis

### MAF Settings (in control.json)
- `cost_basis_settling_duration_seconds`: ADD settling period
- `rPnL_settling_duration_seconds`: REDUCE settling period
- `*_override_threshold`: Thresholds to bypass settling

## Quick Debugging Commands

### Check System Health
```python
# Is TESTRADE running?
ps aux | grep application_core

# Check Redis streams
redis-cli -h **************
XLEN testrade:maf-decisions
XRANGE testrade:maf-decisions - + COUNT 5
```

### Monitor Events
```python
# Watch for MAF events
python tests/monitor_maf_live.py

# Check recent events via MCP
mcp__intellisense-trading__get_recent_trading_events --minutes_back 15
```

## Future Enhancements

### 1. MAF Bootstrap
- Add current MAF state query
- Useful for IntelliSense initial sync

### 2. Image-Level Injection
- Implement like ROI events
- Enable full replay testing
- Millisecond precision

### 3. Position Persistence
- Weekend testing mode
- Maintain simulated positions

## Session Handoff Checklist

When ending a work session, document:

1. **What Was Changed**
   - Files modified
   - Features added
   - Configs updated

2. **Current State**
   - What works
   - What doesn't
   - Any blockers

3. **Next Steps**
   - Immediate tasks
   - Testing needed
   - Open questions

## Remember

- TESTRADE is complex by necessity - it handles real money
- The TANK pattern ensures reliability under load
- Redis is for observation, not control
- When in doubt, trace the code path
- NO GUESSWORK - be certain or ask