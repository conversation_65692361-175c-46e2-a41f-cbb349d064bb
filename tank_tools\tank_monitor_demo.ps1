# tank_monitor_demo.ps1 - Quick demo of TANK monitoring
param(
    [string]$ProcessName = "python",
    [int]$DemoSeconds = 60
)

Write-Host "🚀 TANK Memory Monitor Demo" -ForegroundColor Green
Write-Host "Monitoring process: $ProcessName for $DemoSeconds seconds" -ForegroundColor Cyan
Write-Host "=" * 80

$startTime = Get-Date
$measurements = @()

for ($i = 1; $i -le ($DemoSeconds / 5); $i++) {
    try {
        $processes = Get-Process $ProcessName -ErrorAction Stop
        $mainProcess = $processes | Sort-Object WorkingSet64 -Descending | Select-Object -First 1
        
        $currentTime = Get-Date
        $elapsed = ($currentTime - $startTime).TotalSeconds
        
        $memoryMB = [math]::Round($mainProcess.WorkingSet64 / 1MB, 2)
        $memoryGB = [math]::Round($mainProcess.WorkingSet64 / 1GB, 3)
        
        $measurement = [PSCustomObject]@{
            Time = $currentTime
            ElapsedSec = [math]::Round($elapsed, 1)
            MemoryMB = $memoryMB
            MemoryGB = $memoryGB
            ProcessId = $mainProcess.Id
            Handles = $mainProcess.HandleCount
            Threads = $mainProcess.Threads.Count
        }
        $measurements += $measurement
        
        # Simple visual indicator
        $memoryBar = "█" * [math]::Min(50, [math]::Max(1, $memoryMB / 10))
        
        Write-Host "[$($i.ToString().PadLeft(2))] $($currentTime.ToString('HH:mm:ss')) | " -NoNewline -ForegroundColor Cyan
        Write-Host "$memoryMB MB " -NoNewline -ForegroundColor White
        Write-Host "[$memoryBar]" -ForegroundColor Green
        Write-Host "     PID: $($mainProcess.Id) | Handles: $($mainProcess.HandleCount) | Threads: $($mainProcess.Threads.Count)" -ForegroundColor Gray
        
        Start-Sleep 5
        
    } catch {
        Write-Host "Process '$ProcessName' not found" -ForegroundColor Red
        break
    }
}

Write-Host ""
Write-Host "📊 DEMO SUMMARY" -ForegroundColor Yellow
Write-Host "=" * 40

if ($measurements.Count -gt 0) {
    $avgMemory = ($measurements | Measure-Object MemoryMB -Average).Average
    $maxMemory = ($measurements | Measure-Object MemoryMB -Maximum).Maximum
    $minMemory = ($measurements | Measure-Object MemoryMB -Minimum).Minimum
    
    Write-Host "Measurements:    $($measurements.Count)" -ForegroundColor White
    Write-Host "Average Memory:  $([math]::Round($avgMemory, 2)) MB" -ForegroundColor White
    Write-Host "Peak Memory:     $([math]::Round($maxMemory, 2)) MB" -ForegroundColor White
    Write-Host "Min Memory:      $([math]::Round($minMemory, 2)) MB" -ForegroundColor White
    Write-Host "Memory Range:    $([math]::Round($maxMemory - $minMemory, 2)) MB" -ForegroundColor White
    
    Write-Host ""
    Write-Host "Demo completed successfully!" -ForegroundColor Green
    Write-Host "To run full monitoring, use:" -ForegroundColor Cyan
    Write-Host "  .\tank_memory_monitor.ps1 -ProcessName '$ProcessName'" -ForegroundColor Yellow
    Write-Host "  .\tank_bulletproof_monitor.ps1 -ProcessName '$ProcessName'" -ForegroundColor Yellow
} else {
    Write-Host "No measurements collected" -ForegroundColor Red
}
