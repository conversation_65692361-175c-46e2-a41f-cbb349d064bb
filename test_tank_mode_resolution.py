#!/usr/bin/env python3
"""
Test script for ServiceLifecycleManager Tank mode service resolution
"""

import os
import sys
import logging
from unittest.mock import Mock, MagicMock, patch

# Set up logging  
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_service_resolution_in_mode(mode_name):
    """Test service resolution in a specific Tank mode"""
    print(f"\n{'='*60}")
    print(f"Testing service resolution in {mode_name} mode")
    print(f"{'='*60}")
    
    # Set the environment variable
    os.environ['TESTRADE_MODE'] = mode_name
    
    # Clear any cached imports
    if 'utils.testrade_modes' in sys.modules:
        del sys.modules['utils.testrade_modes']
    if 'core.services.service_lifecycle_manager' in sys.modules:
        del sys.modules['core.services.service_lifecycle_manager']
    
    # Import after setting env var
    from utils.testrade_modes import get_current_mode, requires_ipc_services, requires_telemetry_service, requires_external_publishing
    from core.services.service_lifecycle_manager import ServiceLifecycleManager
    
    # Verify mode
    current_mode = get_current_mode()
    print(f"Mode verified: {current_mode.value}")
    print(f"  - Requires telemetry: {requires_telemetry_service()}")
    print(f"  - Requires IPC: {requires_ipc_services()}")
    print(f"  - Requires external publishing: {requires_external_publishing()}")
    
    # Create mocks
    mock_di_container = Mock()
    mock_config = Mock()
    mock_event_bus = Mock()
    
    # Track what was resolved
    resolved_services = []
    
    def mock_resolve(service_type):
        if hasattr(service_type, '__name__'):
            service_name = service_type.__name__
        else:
            service_name = str(service_type)
            
        resolved_services.append(service_name)
        
        # Return mock services
        mock_service = Mock()
        mock_service.start = Mock()
        mock_service.stop = Mock()
        mock_service.is_ready = True
        return mock_service
    
    mock_di_container.resolve = Mock(side_effect=mock_resolve)
    
    # Create ServiceLifecycleManager
    slm = ServiceLifecycleManager(
        di_container=mock_di_container,
        config_service=mock_config,
        event_bus=mock_event_bus
    )
    
    # Now trigger the resolution phase
    print("\nCalling _resolve_core_services()...")
    
    # Capture logs during resolution
    with patch('core.services.service_lifecycle_manager.logger') as mock_logger:
        try:
            slm._resolve_core_services()
        except Exception as e:
            print(f"Resolution error (expected): {e}")
    
    # Check what was logged
    print("\nLog messages during resolution:")
    for call in mock_logger.info.call_args_list:
        msg = call[0][0] if call[0] else ""
        if "Skipping" in msg or "resolved successfully" in msg or "MODE" in msg:
            print(f"  - {msg}")
    
    # Check resolution results
    print(f"\nServices resolved from DI container:")
    for service in resolved_services:
        print(f"  - {service}")
    
    print(f"\nService states after resolution:")
    print(f"  - telemetry_service: {'SET' if slm.telemetry_service else 'None'}")
    print(f"  - ipc_manager: {'SET' if slm.ipc_manager else 'None'}")
    print(f"  - market_data_publisher: {'SET' if slm.market_data_publisher else 'None'}")
    
    print(f"\nTier configurations:")
    print(f"  - TIER_1_SERVICES: {slm.TIER_1_SERVICES}")
    print(f"  - 'IIPCManager' in TIER_3_SERVICES: {'IIPCManager' in slm.TIER_3_SERVICES}")

def main():
    """Run tests for all modes"""
    
    # Test Tank mode utilities first
    print("Testing Tank mode utility functions...")
    from utils.testrade_modes import TestradeMode
    
    for mode in ["TANK_SEALED", "TANK_BUFFERED", "LIVE"]:
        test_service_resolution_in_mode(mode)
    
    print("\n" + "="*60)
    print("All tests complete!")
    print("="*60)

if __name__ == "__main__":
    main()