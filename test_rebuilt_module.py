#!/usr/bin/env python3
"""
Quick test of the rebuilt C++ OCR accelerator module.
This will verify that the pybind11 function exports are now working.
"""

import os
import sys

def test_rebuilt_module():
    """Test the rebuilt C++ module functions."""
    print("=== Testing Rebuilt C++ OCR Accelerator ===")
    
    # Set up module path
    cpp_module_path = "ocr_accelerator/x64/Release"
    abs_cpp_module_path = os.path.abspath(cpp_module_path)
    
    if not os.path.exists(abs_cpp_module_path):
        print(f"❌ Module directory not found: {abs_cpp_module_path}")
        return False
    
    # Add DLL directory and change to module path
    if hasattr(os, 'add_dll_directory'):
        try:
            os.add_dll_directory(abs_cpp_module_path)
        except Exception as e:
            print(f"⚠️ Could not add DLL directory: {e}")
    
    # Add to PATH
    current_path = os.environ.get('PATH', '')
    if abs_cpp_module_path not in current_path:
        os.environ['PATH'] = abs_cpp_module_path + os.pathsep + current_path
    
    # Change working directory for import
    original_cwd = os.getcwd()
    try:
        os.chdir(abs_cpp_module_path)
        
        # Import the module
        print("🔄 Importing ocr_accelerator module...")
        import ocr_accelerator
        print("✅ Module imported successfully!")
        
        # Check available functions
        functions = [attr for attr in dir(ocr_accelerator) if not attr.startswith('_') and callable(getattr(ocr_accelerator, attr))]
        print(f"📋 Available functions: {functions}")
        
        if not functions:
            print("❌ FAILED: No functions found in module")
            return False
        
        # Test test_function
        if 'test_function' in functions:
            print("🧪 Testing test_function()...")
            try:
                result = ocr_accelerator.test_function()
                print(f"✅ test_function() result: {result}")
            except Exception as e:
                print(f"❌ test_function() failed: {e}")
                return False
        else:
            print("❌ test_function not found")
            return False
        
        # Check process_image_and_ocr
        if 'process_image_and_ocr' in functions:
            print("✅ process_image_and_ocr function found!")
            print("🎉 SUCCESS: Both functions are properly exported!")
            return True
        else:
            print("❌ process_image_and_ocr function not found")
            return False
            
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        # Restore original working directory
        os.chdir(original_cwd)

if __name__ == "__main__":
    success = test_rebuilt_module()
    if success:
        print("\n🎉 REBUILD SUCCESS: C++ module functions are now properly exported!")
        print("💡 You can now run the full test: python test_cpp_ocr_accelerator.py")
    else:
        print("\n❌ REBUILD FAILED: Functions still not exported properly")
        print("💡 Check if the Visual Studio project was built with the fixed settings")