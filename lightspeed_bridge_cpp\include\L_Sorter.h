#pragma once
#if !defined(LS_L_MARKETSORTER_H)
#define LS_L_MARKETSORTER_H

// Copyright (c) 2001-2018 Lightspeed Financial, Inc. All rights reserved.

#include "L_Application.h"
#include "L_Observer.h"

namespace LightspeedTrader
{

class L_Sort;
class L_Summary;
class L_SortOperator;

class L_SortIndexIteratorBase
{
public:
	virtual L_SortIndexIteratorBase *L_CopyBase() const = 0;
	virtual void L_Destroy() = 0;
	virtual bool L_IsEqual(L_SortIndexIteratorBase const *) const = 0;
	virtual void L_Increment() = 0;
	virtual void L_Decrement() = 0;
	virtual L_Observer *L_ConstDeref() const = 0;
	virtual void const *L_DirectData() const = 0;
};

template<typename sortElement>
class C_SortIndexIterator : public L_SortIndexIteratorBase
{
public:
#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
	typedef std::bidirectional_iterator_tag l_iterator_category;
	typedef ptrdiff_t l_distance_type;
	typedef size_t l_size_type;
#endif
	typedef sortElement *l_value_type;
	typedef l_value_type *l_pointer;
	typedef l_value_type l_reference;
	typedef l_value_type const *l_const_pointer;
	typedef l_value_type l_const_reference;
	typedef C_SortIndexIterator l_this_iterator;

	l_this_iterator *L_Copy() const
	{
		return static_cast<l_this_iterator *>(L_CopyBase());
	}
};

class L_Sort : public L_Observable
{
protected:
	virtual void L_AddElement(size_t param = 0) = 0;
	virtual void L_UpdateElement(L_Observer const *ob) = 0;
	virtual void L_UpdateSort(L_SortOperator const *sortOp, long elementCount, long startingRank) = 0;
	virtual void L_UpdateSortRange(long elementCount, long startingRank) = 0;
	virtual L_SortOperator const *L_CurrentSortOperator() const = 0;
	virtual L_SortIndexIteratorBase const *L_Begin() const = 0;
	virtual L_SortIndexIteratorBase const *L_End() const = 0;
};

class L_SortOperator
{
public:
	virtual bool ToKeep(L_Observer *element) const = 0;
	virtual bool Compare(L_Observer *lhs, L_Observer *rhs) const = 0;

	L_Buffer *l_Buffer;
};

class L_SymbolFilterStatic
{
public:
	virtual bool ToKeep(char const *symbol) const = 0;

	L_Buffer *l_Buffer;
};


#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

template<typename sortElement>
class C_SortOperator : public L_SortOperator
{
public:
	typedef sortElement sort_element_type;
	typedef typename sortElement::param_type param_type;

	typedef L_SortOperator const alloc_interface_type;
	enum : long { id = L_BufferType::LibSortOperator };
};

template<typename sortElement>
class C_LibSortOperator : public C_SortOperator<sortElement>
{
public:
	virtual bool ToKeep(L_Observer *element) const
	{
		return true;
	}
	virtual bool Compare(L_Observer *lhs, L_Observer *rhs) const
	{
		sortElement *l(static_cast<sortElement *>(lhs));
		sortElement *r(static_cast<sortElement *>(rhs));

		return *l < *r;
	}
};

template<typename sortElement, typename toKeepFuncType, typename compareFuncType>
class C_FunctionSpecifiedSortOp : public C_SortOperator<sortElement>
{
public:
	typedef L_SortOperator const alloc_interface_type;
	enum : long { id = L_BufferType::LibSortOperator };

	C_FunctionSpecifiedSortOp(toKeepFuncType toKeep, compareFuncType compare)
		: toKeep(toKeep)
		, compare(compare)
	{
	}
	C_FunctionSpecifiedSortOp(C_FunctionSpecifiedSortOp const &rhs)
		: toKeep(rhs.toKeep)
		, compare(rhs.compare)
	{
	}
	C_FunctionSpecifiedSortOp &operator=(C_FunctionSpecifiedSortOp const &rhs)
	{
		toKeep = rhs.toKeep;
		compare = rhs.compare;
		return *this;
	}
	virtual bool ToKeep(L_Observer *element) const
	{
		sortElement *el(static_cast<sortElement *>(element));
		return toKeep(*el);
	}
	virtual bool Compare(L_Observer *lhs, L_Observer *rhs) const
	{
		sortElement *l(static_cast<sortElement *>(lhs));
		sortElement *r(static_cast<sortElement *>(rhs));

		return compare(*l, *r);
	}
	toKeepFuncType toKeep;
	compareFuncType compare;
};

template<typename F>
class C_SymbolFilterStatic : public L_SymbolFilterStatic
{
public:
	enum : long { id = L_BufferType::LibSymbolFilter };
	typedef L_SymbolFilterStatic const alloc_interface_type;

	C_SymbolFilterStatic(F toKeepInit)
		: toKeep(toKeepInit)
	{
	}
	C_SymbolFilterStatic(C_SymbolFilterStatic const &rhs)
		: toKeep(rhs.toKeep)
	{
	}
	virtual bool ToKeep(char const *symbol) const
	{
		return toKeep(symbol);
	}
	F toKeep;
};

template<typename sortElement>
class C_Sort : public L_Sort
{
public:
	typedef sortElement sort_element_type;
	typedef typename sortElement::param_type param_type;
	typedef C_WrappedConstIterator<C_SortIndexIterator<sortElement> > iterator;

#if !(defined(_MSC_VER) && _MSC_VER < 1600)
	static_assert(std::is_pod<typename sortElement::param_type>::value && sizeof(typename sortElement::param_type) == sizeof(void *), "C_Sort: The parameter type, sortElement::param_type, must be POD and sizeof(void *).");
#endif

	class C_MsgUpdate : public L_MsgSortUpdate
	{
	public:
		C_Sort const *C_SourceSort() const
		{
			return static_cast<C_Sort const *>(L_SourceSort());
		}
		C_SortIndexIterator<sortElement> const *C_AddsBegin() const
		{
			return static_cast<C_SortIndexIterator<sortElement> const *>(L_AddsBegin());
		}
		C_SortIndexIterator<sortElement> const *C_AddsEnd() const
		{
			return static_cast<C_SortIndexIterator<sortElement> const *>(L_AddsEnd());
		}
		C_SortIndexIterator<sortElement> const *C_RemovesBegin() const
		{
			return static_cast<C_SortIndexIterator<sortElement> const *>(L_RemovesBegin());
		}
		C_SortIndexIterator<sortElement> const *C_RemovesEnd() const
		{
			return static_cast<C_SortIndexIterator<sortElement> const *>(L_RemovesEnd());
		}

		iterator adds_begin() const
		{
			return iterator(C_AddsBegin());
		}
		iterator adds_end() const
		{
			return iterator(C_AddsEnd());
		}
		iterator removes_begin() const
		{
			return iterator(C_RemovesBegin());
		}
		iterator removes_end() const
		{
			return iterator(C_RemovesEnd());
		}
	};

	void C_AddElement(param_type param)
	{
		L_AddElement(*reinterpret_cast<size_t *>(reinterpret_cast<size_t>(&param)));
	}
	void C_UpdateElement(sortElement const *ob)
	{
		L_UpdateElement(ob);
	}
	void C_UpdateSort(C_SortOperator<sortElement> const *sortOp, long elementCount, long startingRank)
	{
		L_UpdateSort(sortOp, elementCount, startingRank);
	}
	template<typename sortOpPtr>
	void C_UpdateSort(sortOpPtr sortOp, long elementCount, long startingRank)
	{
		C_UpdateSort(get_pointer(sortOp), elementCount, startingRank);
	}
	template<typename toKeepFuncType, typename compareFuncType>
	void C_UpdateSort(long elementCount, long startingRank, compareFuncType compare, toKeepFuncType toKeep)
	{
		L_UpdateSort(C_Alloc<C_FunctionSpecifiedSortOp<sortElement, toKeepFuncType, compareFuncType> >(toKeep, compare).get(), elementCount, startingRank);
	}
	void C_UpdateSortRange(long elementCount, long startingRank)
	{
		L_UpdateSortRange(elementCount, startingRank);
	}
	C_SortOperator<sortElement> const *C_CurrentSortOperator() const
	{
		return static_cast<C_SortOperator<sortElement> const *>(L_CurrentSortOperator());
	}

	template<typename filterFunc>
	void C_AddMarketSummaryElements(filterFunc filt)
	{
#if !(defined(_MSC_VER) && _MSC_VER < 1600)
		static_assert(std::is_same<L_Summary const *, typename sortElement::param_type>::value, "C_AddMarketSummaryElements: The parameter type, sortElement::param_type, must be L_Summary const *.");
#else
		L_Summary const **test = reinterpret_cast<sortElement::param_type *>(0);
#endif
		C_Ptr<C_SymbolFilterStatic<filterFunc> > pFilt(C_Alloc<C_SymbolFilterStatic<filterFunc> >(filt));
		L_AddMarketSummaryElements(this, pFilt.get());
	}

	void C_AddMarketSummaryElements()
	{
		L_AddMarketSummaryElements(this, 0);
	}

	C_SortIndexIterator<sortElement> const *C_Begin() const
	{
		return static_cast<C_SortIndexIterator<sortElement> const *>(L_Begin());
	}
	C_SortIndexIterator<sortElement> const *C_End() const
	{
		return static_cast<C_SortIndexIterator<sortElement> const *>(L_End());
	}
	iterator begin() const
	{
		return iterator(C_Begin());
	}
	iterator end() const
	{
		return iterator(C_End());
	}

	virtual void L_Attach(L_Observer *observer) const
	{
		L_Observable::L_Attach(observer);
	}
	virtual void L_Detach(L_Observer *observer) const
	{
		L_Observable::L_Detach(observer);
	}
};

#pragma push_macro("new")
#undef new
template<typename sortElement>
void C_IndexConstructor(void *mem, L_Sort *sort, size_t param)
{
	new(reinterpret_cast<sortElement *>(mem)) sortElement(static_cast<C_Sort<sortElement> *>(sort), *reinterpret_cast<typename sortElement::param_type *>(&param));
}
#pragma pop_macro("new")

template<typename sortElement, typename sortOpPtr>
C_Sort<sortElement> *C_CreateSort(sortOpPtr pSortOp, long elementCount, long startingRank, L_Strand *strand = 0)
{
#if !(defined(_MSC_VER) && _MSC_VER < 1600)
	static_assert(std::is_base_of<L_Observer, sortElement>::value, "C_CreateSort: The sortElement type doesn't extend L_Observer.");
#endif
	C_SortOperator<sortElement> *op(get_pointer(pSortOp));
	return static_cast<C_Sort<sortElement> *>(L_CreateSort(op, elementCount, startingRank, strand, sizeof(sortElement), __alignof(sortElement), size_t(static_cast<L_Observer *>(static_cast<sortElement *>(0))), C_IndexConstructor<sortElement>, C_Destructor<sortElement>));
}

template<typename sortElement, typename toKeepFuncType, typename compareFuncType>
C_Sort<sortElement> *C_CreateSort(long elementCount, long startingRank, compareFuncType compare, toKeepFuncType toKeep, L_Strand *strand = 0)
{
#if !(defined(_MSC_VER) && _MSC_VER < 1600)
	static_assert(std::is_base_of<L_Observer, sortElement>::value, "C_CreateSort: The sortElement type doesn't extend L_Observer.");
#endif
	return static_cast<C_Sort<sortElement> *>(L_CreateSort(C_Alloc<C_FunctionSpecifiedSortOp<sortElement, toKeepFuncType, compareFuncType> >(toKeep, compare).get(), elementCount, startingRank, strand, sizeof(sortElement), __alignof(sortElement), size_t(static_cast<L_Observer *>(static_cast<sortElement *>(0))), C_IndexConstructor<sortElement>, C_Destructor<sortElement>));
}

template<typename sortElement>
C_Sort<sortElement> *C_CreateSort(long elementCount, long startingRank, L_Strand *strand = 0)
{
#if !(defined(_MSC_VER) && _MSC_VER < 1600)
	static_assert(std::is_base_of<L_Observer, sortElement>::value, "C_CreateSort: The sortElement type doesn't extend L_Observer.");
#endif
	return static_cast<C_Sort<sortElement> *>(L_CreateSort(
					C_Alloc<C_LibSortOperator<sortElement> >().get(),
					elementCount,
					startingRank,
					strand,
					sizeof(sortElement),
					__alignof(sortElement),
					size_t(static_cast<L_Observer *>(static_cast<sortElement *>(0))),
					C_IndexConstructor<sortElement>,
					C_Destructor<sortElement>
				));
}

template<typename sortPtr>
void C_DestroySort(sortPtr sort)
{
	L_DestroySort(get_pointer(sort));
}

#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)


}

#endif // !defined(LS_L_MARKETSORTER_H)

