# C:/TESTRADE/config.py
import os

# --- Core Project Paths ---
# Get the absolute path of the directory containing this config file (C:\TESTRADE)
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# --- Source Code Paths ---
MODULES_DIR = os.path.join(PROJECT_ROOT, "modules")
UTILS_DIR = os.path.join(PROJECT_ROOT, "utils")
GUI_DIR = os.path.join(PROJECT_ROOT, "gui")
SERVICES_DIR = os.path.join(PROJECT_ROOT, "services")
INTERFACES_DIR = os.path.join(PROJECT_ROOT, "interfaces")
CPP_EXTENSION_DIR = os.path.join(PROJECT_ROOT, "lightspeed_bridge_cpp")

# --- Runtime Data Paths ---
LOG_BASE_DIR = os.path.join(PROJECT_ROOT, "logs") # Base for all logs/runtime data
VIDEO_DIR = os.path.join(LOG_BASE_DIR, "videos")
EXPORT_DIR = os.path.join(LOG_BASE_DIR, "exports")

# --- Static Data Paths ---
DATA_DIR = os.path.join(PROJECT_ROOT, "data")
TRADEABLE_SYMBOLS_CSV = os.path.join(DATA_DIR, "tradeable_symbols.csv")

# --- OCR Settings ---
roi_coordinates = [64, 159, 681, 296] # Default ROI [x1, y1, x2, y2] - MATCHES control.json ROI_COORDINATES

# --- Executable Paths ---
TESSERACT_CMD = r'C:\Program Files\Tesseract-OCR\tesseract.exe' # Updated path
CPP_DLL_PATH = os.path.join(CPP_EXTENSION_DIR, "x64", "Release", "LightspeedBridge.dll") # Path to the built DLL

# --- Ensure Runtime Directories Exist ---
os.makedirs(LOG_BASE_DIR, exist_ok=True)
os.makedirs(VIDEO_DIR, exist_ok=True)
os.makedirs(EXPORT_DIR, exist_ok=True)
