#!/usr/bin/env python3
"""
TESTRADE Multi-Mode Startup Script
==================================
Provides three execution modes:
1. broadcast - Full system with Redis, GUI, and Babysitter
2. headless-buffered - Headless with IPC buffering via Babysitter
3. headless-nobcast - Headless without IPC broadcasting

Usage:
    python start_testrade_modes.py [mode]
    
Where mode is one of: broadcast, headless-buffered, headless-nobcast
Default mode: broadcast
"""

import subprocess
import sys
import os
import json
import time
import signal
import psutil
from pathlib import Path
from typing import Optional, List

# Configuration paths
CONFIG_FILE = "utils/control.json"
CONFIG_BACKUP = "utils/control_backup.json"

# Process tracking
started_processes: List[subprocess.Popen] = []

def print_banner(mode: str):
    """Print startup banner."""
    print("=" * 60)
    print(f"🚀 TESTRADE Startup - Mode: {mode.upper()}")
    print("=" * 60)

def backup_config():
    """Backup current configuration."""
    try:
        import shutil
        shutil.copy2(CONFIG_FILE, CONFIG_BACKUP)
        print(f"✅ Configuration backed up to {CONFIG_BACKUP}")
    except Exception as e:
        print(f"⚠️  Warning: Could not backup config: {e}")

def restore_config():
    """Restore configuration from backup."""
    try:
        import shutil
        if os.path.exists(CONFIG_BACKUP):
            shutil.copy2(CONFIG_BACKUP, CONFIG_FILE)
            print(f"✅ Configuration restored from {CONFIG_BACKUP}")
    except Exception as e:
        print(f"⚠️  Warning: Could not restore config: {e}")

def update_config(updates: dict):
    """Update configuration file with given values."""
    try:
        # Load current config
        with open(CONFIG_FILE, 'r') as f:
            config = json.load(f)
        
        # Apply updates
        config.update(updates)
        
        # Save updated config
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Configuration updated: {list(updates.keys())}")
        return True
    except Exception as e:
        print(f"❌ Failed to update config: {e}")
        return False

def start_process(command: List[str], name: str, show_output: bool = False) -> Optional[subprocess.Popen]:
    """Start a process and track it."""
    try:
        print(f"🔄 Starting {name}...")
        
        kwargs = {
            'cwd': Path.cwd(),
            'creationflags': subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
        }
        
        if not show_output:
            kwargs['stdout'] = subprocess.DEVNULL
            kwargs['stderr'] = subprocess.DEVNULL
        
        proc = subprocess.Popen(command, **kwargs)
        started_processes.append(proc)
        
        print(f"✅ {name} started (PID: {proc.pid})")
        return proc
        
    except Exception as e:
        print(f"❌ Failed to start {name}: {e}")
        return None

def check_babysitter_running():
    """Check if Babysitter is already running and optionally start it."""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and 'babysitter_service.py' in ' '.join(proc.info['cmdline']):
                print(f"✅ Babysitter already running (PID: {proc.info['pid']})")
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    return False

def cleanup(signum=None, frame=None):
    """Clean up all started processes."""
    print("\n🛑 Shutting down TESTRADE...")
    
    for proc in reversed(started_processes):
        try:
            if proc.poll() is None:  # Process is still running
                print(f"  🔄 Stopping process {proc.pid}...")
                proc.terminate()
                try:
                    proc.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    proc.kill()
                    proc.wait()
        except Exception as e:
            print(f"  ⚠️  Error stopping process: {e}")
    
    # Restore original configuration
    restore_config()
    
    print("✅ Shutdown complete")
    if signum is not None:
        sys.exit(0)

def mode_broadcast():
    """Full broadcast mode with Redis, GUI, and Babysitter."""
    print("📡 Broadcast Mode: Full system with Redis, GUI, and Babysitter")
    
    # Ensure Babysitter is enabled and IPC publishing is on
    config_updates = {
        "BABYSITTER_ENABLED": True,
        "ENABLE_IPC_DATA_DUMP": True,
        "ENABLE_EMERGENCY_GUI": True
    }
    
    if not update_config(config_updates):
        return 1
    
    # Check/Start Babysitter if needed
    if not check_babysitter_running():
        babysitter_proc = start_process(
            [sys.executable, "core/babysitter_service.py"],
            "Babysitter Service"
        )
        if not babysitter_proc:
            return 1
        time.sleep(3)  # Give Babysitter time to start
    
    # Start Core
    core_proc = start_process(
        [sys.executable, "run_headless_core.py"],
        "TESTRADE Core",
        show_output=True
    )
    if not core_proc:
        return 1
    
    time.sleep(5)  # Give Core time to initialize
    
    # Start GUI Backend
    gui_proc = start_process(
        [sys.executable, "gui/gui_backend.py"],
        "GUI Backend"
    )
    if gui_proc:
        print("\n📊 GUI Access:")
        print("  🌐 Backend API: http://localhost:8001")
        print("  🖥️  Frontend: file:///C:/TESTRADE/gui/portrait_trading_gui.html")
    
    return 0

def mode_headless_buffered():
    """Headless mode with IPC buffering via Babysitter."""
    print("💾 Headless Buffered Mode: Core + Babysitter (no GUI)")
    
    # Enable Babysitter and IPC publishing, disable GUI
    config_updates = {
        "BABYSITTER_ENABLED": True,
        "ENABLE_IPC_DATA_DUMP": True,
        "ENABLE_EMERGENCY_GUI": False
    }
    
    if not update_config(config_updates):
        return 1
    
    # Check/Start Babysitter if needed
    if not check_babysitter_running():
        babysitter_proc = start_process(
            [sys.executable, "core/babysitter_service.py"],
            "Babysitter Service"
        )
        if not babysitter_proc:
            return 1
        time.sleep(3)  # Give Babysitter time to start
    
    # Start Core only
    core_proc = start_process(
        [sys.executable, "run_headless_core.py"],
        "TESTRADE Core (Headless + Buffered)",
        show_output=True
    )
    if not core_proc:
        return 1
    
    print("\n💡 IPC data is being buffered by Babysitter")
    print("📡 Redis streams are being populated")
    
    return 0

def mode_headless_nobcast():
    """Headless mode without IPC broadcasting."""
    print("🔇 Headless No-Broadcast Mode: Core only (no IPC)")
    
    # Disable Babysitter and IPC publishing
    config_updates = {
        "BABYSITTER_ENABLED": False,
        "ENABLE_IPC_DATA_DUMP": False,
        "ENABLE_EMERGENCY_GUI": False
    }
    
    if not update_config(config_updates):
        return 1
    
    # Start Core only
    core_proc = start_process(
        [sys.executable, "run_headless_core.py"],
        "TESTRADE Core (Headless No-Broadcast)",
        show_output=True
    )
    if not core_proc:
        return 1
    
    print("\n⚡ Running in minimal mode - no IPC broadcasting")
    print("🔕 No data is being sent to Redis")
    
    return 0

def main():
    """Main entry point."""
    # Parse command line arguments
    mode = sys.argv[1].lower() if len(sys.argv) > 1 else "broadcast"
    
    if mode not in ["broadcast", "headless-buffered", "headless-nobcast"]:
        print(f"❌ Invalid mode: {mode}")
        print("Usage: python start_testrade_modes.py [broadcast|headless-buffered|headless-nobcast]")
        return 1
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, cleanup)
    signal.signal(signal.SIGTERM, cleanup)
    if hasattr(signal, 'SIGBREAK'):
        signal.signal(signal.SIGBREAK, cleanup)
    
    # Backup current configuration
    backup_config()
    
    # Print startup banner
    print_banner(mode)
    
    # Execute the appropriate mode
    try:
        if mode == "broadcast":
            result = mode_broadcast()
        elif mode == "headless-buffered":
            result = mode_headless_buffered()
        elif mode == "headless-nobcast":
            result = mode_headless_nobcast()
        
        if result == 0:
            print("\n✅ TESTRADE started successfully!")
            print("⏹️  Press Ctrl+C to stop")
            
            # Keep running until interrupted
            while True:
                time.sleep(1)
                # Check if any critical process has died
                for proc in started_processes:
                    if proc.poll() is not None:
                        print(f"\n⚠️  Process {proc.pid} stopped unexpectedly!")
                        cleanup()
                        return 1
        else:
            cleanup()
            return result
            
    except KeyboardInterrupt:
        pass  # cleanup will be called by signal handler
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        cleanup()
        return 1

if __name__ == "__main__":
    sys.exit(main())