import logging

class OCRFilter(logging.Filter):
    """
    A filter that removes OCR-related debug messages from the log output.
    """
    def filter(self, record):
        # Skip debug messages containing OCR-related keywords
        if record.levelno == logging.DEBUG:
            message = record.getMessage().lower()
            ocr_keywords = [
                "ocr_raw_time", 
                "ocr_processed_time", 
                "ocr_time", 
                "timestamp_ocr",
                "raw_ocr_text",
                "processed_ocr_text",
                "extra fields",
                "metadata"
            ]
            
            # Skip messages containing OCR keywords
            for keyword in ocr_keywords:
                if keyword in message:
                    return False
                    
        # Allow all other messages
        return True
