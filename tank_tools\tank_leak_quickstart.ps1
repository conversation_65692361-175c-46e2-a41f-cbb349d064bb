# tank_leak_quickstart.ps1 - Quick leak detection setup and launcher
param(
    [string]$Mode = "detect",  # detect, analyze, demo
    [string]$ProcessName = "ApplicationCore",
    [int]$DemoMinutes = 10
)

Write-Host "🚀 TANK Memory Leak Detection Quick Start" -ForegroundColor Green
Write-Host "=" * 60

switch ($Mode.ToLower()) {
    "detect" {
        Write-Host "🔍 Starting Memory Leak Detection..." -ForegroundColor Cyan
        Write-Host "Process: $ProcessName" -ForegroundColor Yellow
        Write-Host "Press Ctrl+C to stop monitoring" -ForegroundColor Gray
        Write-Host ""
        
        # Check if process exists
        try {
            $process = Get-Process $ProcessName -ErrorAction Stop
            Write-Host "✅ Found process: $ProcessName (PID: $($process.Id))" -ForegroundColor Green
        } catch {
            Write-Host "❌ Process '$ProcessName' not found!" -ForegroundColor Red
            Write-Host "Available processes:" -ForegroundColor Yellow
            Get-Process | Where-Object { $_.ProcessName -like "*python*" -or $_.ProcessName -like "*application*" -or $_.ProcessName -like "*core*" } | 
                Format-Table ProcessName, Id, WorkingSet -AutoSize
            exit 1
        }
        
        Write-Host "Starting advanced leak detector..." -ForegroundColor Cyan
        & .\tank_leak_detector.ps1 -ProcessName $ProcessName -SampleIntervalSeconds 30 -EnableAlerts -EnableCSVLogging
    }
    
    "analyze" {
        Write-Host "📊 Starting Historical Analysis..." -ForegroundColor Cyan
        
        if (-not (Test-Path "tank_leak_analysis.csv")) {
            Write-Host "❌ No historical data found!" -ForegroundColor Red
            Write-Host "Run leak detection first: .\tank_leak_quickstart.ps1 -Mode detect" -ForegroundColor Yellow
            exit 1
        }
        
        Write-Host "✅ Found historical data file" -ForegroundColor Green
        Write-Host "Generating comprehensive analysis..." -ForegroundColor Cyan
        & .\tank_leak_analyzer.ps1 -GenerateReport -AnalysisHours 24
        
        Write-Host ""
        Write-Host "📄 Opening analysis report..." -ForegroundColor Cyan
        if (Test-Path "tank_leak_report.html") {
            Start-Process "tank_leak_report.html"
        }
    }
    
    "demo" {
        Write-Host "🎮 Starting Demo Mode..." -ForegroundColor Cyan
        Write-Host "Demo Duration: $DemoMinutes minutes" -ForegroundColor Yellow
        Write-Host "This will run leak detection with fast sampling for demonstration" -ForegroundColor Gray
        Write-Host ""
        
        # Check if process exists
        try {
            $process = Get-Process $ProcessName -ErrorAction Stop
            Write-Host "✅ Found process: $ProcessName (PID: $($process.Id))" -ForegroundColor Green
        } catch {
            Write-Host "❌ Process '$ProcessName' not found!" -ForegroundColor Red
            Write-Host "Available processes:" -ForegroundColor Yellow
            Get-Process | Where-Object { $_.ProcessName -like "*python*" -or $_.ProcessName -like "*application*" -or $_.ProcessName -like "*core*" } | 
                Format-Table ProcessName, Id, WorkingSet -AutoSize
            exit 1
        }
        
        Write-Host "Starting demo leak detector (fast sampling)..." -ForegroundColor Cyan
        
        # Create a demo script that runs for limited time
        $demoScript = @"
# Demo leak detection - auto-stop after $DemoMinutes minutes
`$startTime = Get-Date
`$endTime = `$startTime.AddMinutes($DemoMinutes)

Write-Host "Demo will run until: `$(`$endTime.ToString('HH:mm:ss'))" -ForegroundColor Yellow
Write-Host ""

# Start the leak detector with fast sampling
`$job = Start-Job -ScriptBlock {
    param(`$ProcessName)
    & .\tank_leak_detector.ps1 -ProcessName `$ProcessName -SampleIntervalSeconds 15 -AnalysisWindowMinutes 10 -EnableAlerts:`$false
} -ArgumentList "$ProcessName"

# Monitor until demo time expires
while ((Get-Date) -lt `$endTime) {
    Start-Sleep 30
    if (`$job.State -ne "Running") {
        Write-Host "Demo stopped early" -ForegroundColor Yellow
        break
    }
}

# Stop the job
Stop-Job `$job
Remove-Job `$job

Write-Host ""
Write-Host "🎮 Demo completed!" -ForegroundColor Green
Write-Host "Check tank_leak_analysis.csv for collected data" -ForegroundColor Cyan
Write-Host "Run analysis: .\tank_leak_quickstart.ps1 -Mode analyze" -ForegroundColor Yellow
"@
        
        $demoScript | Out-File "temp_demo.ps1" -Encoding UTF8
        & .\temp_demo.ps1
        Remove-Item "temp_demo.ps1" -Force
    }
    
    default {
        Write-Host "❌ Invalid mode: $Mode" -ForegroundColor Red
        Write-Host ""
        Write-Host "Available modes:" -ForegroundColor Yellow
        Write-Host "  detect  - Start real-time leak detection" -ForegroundColor White
        Write-Host "  analyze - Analyze historical data and generate report" -ForegroundColor White
        Write-Host "  demo    - Run demonstration mode (limited time)" -ForegroundColor White
        Write-Host ""
        Write-Host "Examples:" -ForegroundColor Cyan
        Write-Host "  .\tank_leak_quickstart.ps1 -Mode detect" -ForegroundColor Gray
        Write-Host "  .\tank_leak_quickstart.ps1 -Mode analyze" -ForegroundColor Gray
        Write-Host "  .\tank_leak_quickstart.ps1 -Mode demo -DemoMinutes 5" -ForegroundColor Gray
        exit 1
    }
}
