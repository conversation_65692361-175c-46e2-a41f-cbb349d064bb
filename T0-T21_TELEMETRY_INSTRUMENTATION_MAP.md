# T0-T21 Telemetry Instrumentation Map

## Overview
This document provides a comprehensive map of all T0-T21 timing milestones implemented in the TESTRADE system, showing how timing data flows through the telemetry system to IntelliSense.

## Telemetry Streams
All timing milestones are published to specific instrumentation streams:
- **OCR Capture**: `testrade:instrumentation:ocr-capture`
- **OCR Conditioning**: `testrade:instrumentation:ocr-conditioning`
- **Orchestration**: `testrade:instrumentation:orchestration`
- **Risk Assessment**: `testrade:instrumentation:risk-assessment`
- **Trade Executor**: `testrade:instrumentation:trade-executor`
- **Broker Gateway**: `testrade:instrumentation:broker-gateway`
- **Order Repository**: `testrade:instrumentation:order-repository`
- **Finish Line**: `testrade:instrumentation:finish-line`

## Milestone Definitions

### T0-T3: OCR Capture Pipeline
**Service**: `OCRService` (modules/ocr/ocr_service.py)
**Event Type**: `OCR_CAPTURE_MILESTONES`
**Stream**: `testrade:instrumentation:ocr-capture`

| Milestone | Description | Capture Point |
|-----------|-------------|---------------|
| **T0_ImageIngress_ns** | Image capture start | `time.perf_counter_ns()` at frame grab |
| **T1_ImageProcessing_ns** | Image preprocessing complete | After OpenCV preprocessing |
| **T2_TesseractOutput_ns** | Tesseract OCR complete | After pytesseract.image_to_string() |
| **T3_FormattedOutput_ns** | Final OCR output ready | Before observer notification |

**Timing Data Included**:
- `T0_to_T1_ms`: Image capture to preprocessing
- `T1_to_T2_ms`: Preprocessing to OCR
- `T2_to_T3_ms`: OCR to formatting
- `T0_to_T3_total_ms`: Total OCR pipeline

### T4-T6: OCR Conditioning Pipeline
**Service**: `PythonOCRDataConditioner` (modules/ocr/python_ocr_data_conditioner.py)
**Event Type**: `OCR_CONDITIONING_MILESTONES`
**Stream**: `testrade:instrumentation:ocr-conditioning`

| Milestone | Description | Capture Point |
|-----------|-------------|---------------|
| **T4_ConditioningStart_ns** | Conditioning pipeline start | Entry to `condition_ocr_data()` |
| **T5_ValidationComplete_ns** | Validation and filtering complete | After all validation checks |
| **T6_ConditioningComplete_ns** | Final conditioning output ready | Before returning result |

**Timing Data Included**:
- `T4_to_T5_validation_ms`: Start to validation complete
- `T5_to_T6_conditioning_ms`: Validation to final output
- `T4_to_T6_total_ms`: Total conditioning time
- `T0_to_T6_total_ms`: End-to-end OCR pipeline (if T0 available)

### T7-T12: Signal Orchestration Pipeline
**Service**: `OCRScalpingSignalOrchestratorService` (modules/trade_management/ocr_scalping_signal_orchestrator_service.py)
**Event Type**: `ORCHESTRATION_MILESTONES`
**Stream**: `testrade:instrumentation:orchestration`

| Milestone | Description | Capture Point |
|-----------|-------------|---------------|
| **t7_orchestration_start_ns** | Orchestration pipeline start | Entry to signal processing |
| **t8_interpretation_end_ns** | Signal interpretation complete | After signal generation |
| **t9_maf_filtering_start_ns** | MAF filtering start | Entry to MasterActionFilter |
| **t10_maf_decision_ready_ns** | MAF decision ready | MAF filter complete |
| **t11_order_request_ready_ns** | Order request constructed | OrderRequestData created |
| **t12_order_request_published_ns** | Order published to event bus | After event bus publish |

**Timing Data Included**:
- `T7_to_T8_ms`: Orchestration start to interpretation
- `T8_to_T11_ms`: Interpretation to order construction
- `T9_to_T10_ms`: MAF filtering duration
- `T11_to_T12_ms`: Order construction to publish
- `T7_to_T12_total_ms`: Total orchestration time

### T13-T15: Risk Assessment Pipeline
**Service**: `RiskManagementService` (modules/risk_management/risk_service.py)
**Event Types**: 
- `RISK_ASSESSMENT_MILESTONES` (for approved orders)
- `RISK_REJECTION_MILESTONES` (for rejected orders)
**Stream**: `testrade:instrumentation:risk-assessment`

| Milestone | Description | Capture Point |
|-----------|-------------|---------------|
| **T13_RiskCheckStart_ns** | Risk check pipeline start | Entry to `handle_order_request()` |
| **T14_RiskCheckEnd_ns** | Risk check pipeline complete | After all risk checks |
| **T15_RiskEventPublished_ns** | Risk assessment event published | After event bus publish |

**Timing Data Included**:
- `T13_to_T14_assessment_ms`: Risk check duration
- `T14_to_T15_publish_ms`: Assessment to publish
- `T13_to_T15_total_ms`: Total risk pipeline
- `assessment_duration_ns`: Raw duration in nanoseconds

### T16-T17: Trade Execution Pipeline
**Service**: `TradeExecutor` (modules/trade_management/trade_executor.py)
**Event Type**: `BROKER_SUBMISSION_MILESTONES`
**Stream**: `testrade:instrumentation:trade-executor`

| Milestone | Description | Capture Point |
|-----------|-------------|---------------|
| **T16_ExecutionStart_ns** | Trade execution pipeline start | Entry to `_route_execution()` |
| **T17_BrokerSubmission_ns** | Broker submission complete | After broker.submit_order() |

**Timing Data Included**:
- `execution_duration_ns`: T17 - T16 in nanoseconds
- `execution_duration_ms`: T17 - T16 in milliseconds
- `submission_successful`: Boolean success indicator

### T18-T19: Broker Communication
**Service**: `LightspeedBroker` (modules/broker_bridge/lightspeed_broker.py)
**Event Type**: (Published through broker gateway telemetry)
**Stream**: `testrade:instrumentation:broker-gateway`

| Milestone | Description | Capture Point |
|-----------|-------------|---------------|
| **T18_BrokerSend_ns** | Broker message send time | Before client.send_message_no_read() |
| **T19_BrokerMessageReceived_ns** | Broker response received | Entry to `_handle_order_status_update()` |

**Timing Data Included**:
- `T18_to_T19_latency_ms`: Broker round-trip time
- Message type and status information

### T20-T21: Order Repository Updates
**Service**: `OrderRepository` (modules/order_management/order_repository.py)

#### T20: Order Status Update
**Event Type**: (Published through order repository telemetry)
**Stream**: `testrade:instrumentation:order-repository`

| Milestone | Description | Capture Point |
|-----------|-------------|---------------|
| **T20_RepositoryUpdate_start_ns** | Repository update start | Entry to `handle_order_status_update()` |
| **T20_RepositoryUpdate_end_ns** | Repository update complete | After state machine update |

**Timing Data Included**:
- `T20_duration_ns`: Update duration in nanoseconds
- `T20_duration_ms`: Update duration in milliseconds

#### T21: Fill Update (FINISH LINE)
**Event Type**: (Published through finish line telemetry)
**Stream**: `testrade:instrumentation:finish-line`

| Milestone | Description | Capture Point |
|-----------|-------------|---------------|
| **T21_RepositoryFillUpdate_start_ns** | Fill update start | Entry to `handle_order_filled()` |
| **T21_RepositoryFillUpdate_end_ns** | Fill update complete | After fill processing |

**Timing Data Included**:
- `T21_duration_ns`: Fill update duration in nanoseconds
- `T21_duration_ms`: Fill update duration in milliseconds
- `T0_to_T21_total_ms`: **Complete end-to-end latency from image capture to fill**
- `milestone`: "T21_FINISH_LINE"
- `pipeline_complete`: true

## End-to-End Pipeline Flow

```
T0 (Image Capture) 
  ↓
T1 (Image Processing)
  ↓
T2 (Tesseract OCR)
  ↓
T3 (Formatted Output)
  ↓
T4 (Conditioning Start)
  ↓
T5 (Validation Complete)
  ↓
T6 (Conditioning Complete)
  ↓
T7 (Orchestration Start)
  ↓
T8 (Interpretation End)
  ↓
T9 (MAF Filter Start) → T10 (MAF Decision)
  ↓
T11 (Order Request Ready)
  ↓
T12 (Order Published)
  ↓
T13 (Risk Check Start)
  ↓
T14 (Risk Check End)
  ↓
T15 (Risk Event Published)
  ↓
T16 (Execution Start)
  ↓
T17 (Broker Submission)
  ↓
T18 (Broker Send) → T19 (Broker Response)
  ↓
T20 (Repository Update)
  ↓
T21 (Fill - FINISH LINE)
```

## IntelliSense Integration

All timing data flows to IntelliSense through the telemetry system:

1. **Real-time Monitoring**: Each service publishes milestone events with nanosecond precision timestamps
2. **Correlation Tracking**: All events include correlation_id for end-to-end tracing
3. **Latency Analysis**: Calculated latencies between milestones for performance monitoring
4. **Pipeline Health**: Complete visibility into each stage of the trading pipeline

## Key Metrics

- **T0 to T3**: OCR capture latency (target: <50ms)
- **T4 to T6**: Conditioning latency (target: <10ms)
- **T7 to T12**: Orchestration latency (target: <20ms)
- **T13 to T15**: Risk assessment latency (target: <5ms)
- **T16 to T17**: Execution latency (target: <10ms)
- **T18 to T19**: Broker round-trip (network dependent)
- **T0 to T21**: Complete photon-to-fill latency (target: <250ms)

## Usage in IntelliSense

IntelliSense can query these instrumentation streams to:
- Monitor real-time pipeline performance
- Detect bottlenecks and anomalies
- Generate latency histograms and percentiles
- Track performance trends over time
- Correlate timing with trading outcomes