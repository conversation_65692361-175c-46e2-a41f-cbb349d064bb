#!/usr/bin/env python3
"""
Test script to fetch YIBO price via Alpaca REST API
"""
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.global_config import GlobalConfig
import alpaca_trade_api as tradeapi

def test_yibo_price():
    """Test fetching YIBO price via Alpaca REST API"""
    
    # Load credentials
    config = GlobalConfig()
    api_key = config.ALPACA_API_KEY
    api_secret = config.ALPACA_API_SECRET
    
    print(f"Testing YIBO price fetch via Alpaca REST API...")
    print(f"API Key: {api_key[:8]}...")
    
    try:
        # Create Alpaca REST client
        api = tradeapi.REST(
            api_key,
            api_secret,
            base_url='https://paper-api.alpaca.markets'  # Paper trading endpoint
        )
        
        symbol = "YIBO"
        print(f"📊 Fetching latest price for {symbol}...")
        
        # Get latest trade with timing
        try:
            import time
            start_time = time.time()
            latest_trade = api.get_latest_trade(symbol)
            trade_time = time.time() - start_time
            print(f"✅ Latest trade for {symbol} (fetched in {trade_time*1000:.1f}ms):")
            print(f"   Price: ${latest_trade.price:.4f}")
            print(f"   Size: {latest_trade.size}")
            print(f"   Timestamp: {latest_trade.timestamp}")
            print(f"   Exchange: {latest_trade.exchange}")
        except Exception as e:
            print(f"❌ Error fetching latest trade: {e}")
        
        # Get latest quote with timing
        try:
            start_time = time.time()
            latest_quote = api.get_latest_quote(symbol)
            quote_time = time.time() - start_time
            print(f"✅ Latest quote for {symbol} (fetched in {quote_time*1000:.1f}ms):")
            print(f"   Bid: ${latest_quote.bid_price:.4f} (size: {latest_quote.bid_size})")
            print(f"   Ask: ${latest_quote.ask_price:.4f} (size: {latest_quote.ask_size})")
            print(f"   Timestamp: {latest_quote.timestamp}")
        except Exception as e:
            print(f"❌ Error fetching latest quote: {e}")
        
        # Get snapshot (combines trade and quote data) with timing
        try:
            start_time = time.time()
            snapshot = api.get_snapshot(symbol)
            snapshot_time = time.time() - start_time
            print(f"✅ Snapshot for {symbol} (fetched in {snapshot_time*1000:.1f}ms):")
            if snapshot.latest_trade:
                print(f"   Last Trade: ${snapshot.latest_trade.price:.4f}")
            if snapshot.latest_quote:
                print(f"   Bid/Ask: ${snapshot.latest_quote.bid_price:.4f}/${snapshot.latest_quote.ask_price:.4f}")
            if snapshot.daily_bar:
                print(f"   Daily: Open=${snapshot.daily_bar.open:.4f}, High=${snapshot.daily_bar.high:.4f}, Low=${snapshot.daily_bar.low:.4f}, Close=${snapshot.daily_bar.close:.4f}")
        except Exception as e:
            print(f"❌ Error fetching snapshot: {e}")
        
        print(f"✅ YIBO price fetch test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_yibo_price()
    sys.exit(0 if success else 1)