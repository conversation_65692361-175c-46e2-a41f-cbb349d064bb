# tank_leak_detector.ps1 - Advanced leak detection with statistical analysis
param(
    [string]$ProcessName = "ApplicationCore",
    [int]$SampleIntervalSeconds = 60,
    [int]$AlertThresholdMB = 50,
    [int]$AnalysisWindowMinutes = 30,
    [string]$LogFile = "tank_leak_analysis.csv",
    [switch]$EnableAlerts = $true,
    [switch]$EnableCSVLogging = $true
)

class MemoryAnalyzer {
    [System.Collections.ArrayList]$Samples
    [datetime]$StartTime
    [string]$ProcessName
    [int]$AnalysisWindowMinutes
    
    MemoryAnalyzer([string]$processName, [int]$windowMinutes) {
        $this.Samples = New-Object System.Collections.ArrayList
        $this.StartTime = Get-Date
        $this.ProcessName = $processName
        $this.AnalysisWindowMinutes = $windowMinutes
    }
    
    [void]AddSample([PSCustomObject]$sample) {
        $this.Samples.Add($sample) | Out-Null
        
        # Keep only samples within analysis window
        $cutoffTime = (Get-Date).AddMinutes(-$this.AnalysisWindowMinutes)
        $this.Samples = $this.Samples | Where-Object { $_.Timestamp -gt $cutoffTime }
    }
    
    [PSCustomObject]AnalyzeTrend() {
        if ($this.Samples.Count -lt 5) {
            return [PSCustomObject]@{
                Status = "INSUFFICIENT_DATA"
                TrendType = "UNKNOWN"
                GrowthRate = 0
                Confidence = 0
                SampleCount = $this.Samples.Count
            }
        }
        
        # Linear regression for trend analysis
        $x = 1..$this.Samples.Count
        $y = $this.Samples.MemoryMB
        
        $n = $this.Samples.Count
        $sumX = ($x | Measure-Object -Sum).Sum
        $sumY = ($y | Measure-Object -Sum).Sum
        $sumXY = 0; for($i=0; $i -lt $n; $i++) { $sumXY += $x[$i] * $y[$i] }
        $sumX2 = ($x | ForEach-Object { $_ * $_ } | Measure-Object -Sum).Sum
        
        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX)
        $intercept = ($sumY - $slope * $sumX) / $n
        
        # Calculate R-squared for confidence
        $yMean = ($y | Measure-Object -Average).Average
        $ssReg = 0; $ssTot = 0
        for($i=0; $i -lt $n; $i++) {
            $predicted = $slope * ($i + 1) + $intercept
            $ssReg += ($predicted - $yMean) * ($predicted - $yMean)
            $ssTot += ($y[$i] - $yMean) * ($y[$i] - $yMean)
        }
        $rSquared = if ($ssTot -ne 0) { $ssReg / $ssTot } else { 0 }
        
        # Enhanced trend classification
        $trendType = if ($slope -gt 10) { "CRITICAL_LEAK" }
                    elseif ($slope -gt 5) { "LEAK_DETECTED" }
                    elseif ($slope -gt 2) { "MODERATE_GROWTH" }
                    elseif ($slope -gt 0.5) { "SLOW_GROWTH" }
                    elseif ($slope -gt -0.5) { "STABLE" }
                    else { "DECREASING" }
        
        # Calculate additional statistics
        $memoryStats = $y | Measure-Object -Average -Maximum -Minimum -StandardDeviation
        $volatility = if ($memoryStats.Average -gt 0) { 
            ($memoryStats.StandardDeviation / $memoryStats.Average) * 100 
        } else { 0 }
        
        return [PSCustomObject]@{
            Status = "ANALYZED"
            TrendType = $trendType
            GrowthRate = [math]::Round($slope, 3)
            Confidence = [math]::Round($rSquared * 100, 1)
            SampleCount = $n
            ProjectedMemoryIn1Hour = [math]::Round($y[-1] + $slope * 60, 1)
            ProjectedMemoryIn4Hours = [math]::Round($y[-1] + $slope * 240, 1)
            AverageMemory = [math]::Round($memoryStats.Average, 2)
            MaxMemory = [math]::Round($memoryStats.Maximum, 2)
            MinMemory = [math]::Round($memoryStats.Minimum, 2)
            Volatility = [math]::Round($volatility, 1)
            TimeSpanMinutes = [math]::Round(((Get-Date) - $this.Samples[0].Timestamp).TotalMinutes, 1)
        }
    }
    
    [PSCustomObject]DetectMemoryLeaks() {
        $analysis = $this.AnalyzeTrend()
        
        $leakDetected = $false
        $severity = "NONE"
        $recommendations = @()
        $actions = @()
        
        if ($analysis.TrendType -eq "CRITICAL_LEAK") {
            $leakDetected = $true
            $severity = "CRITICAL"
            $recommendations += "CRITICAL memory leak detected with $($analysis.GrowthRate) MB/interval growth rate"
            $recommendations += "Projected memory in 1 hour: $($analysis.ProjectedMemoryIn1Hour) MB"
            $recommendations += "Projected memory in 4 hours: $($analysis.ProjectedMemoryIn4Hours) MB"
            $actions += "IMMEDIATE ACTION REQUIRED: Consider restarting TANK"
            $actions += "Monitor system resources and check for memory-intensive operations"
        }
        elseif ($analysis.TrendType -eq "LEAK_DETECTED") {
            $leakDetected = $true
            $severity = "HIGH"
            $recommendations += "Memory leak detected with $($analysis.GrowthRate) MB/interval growth rate"
            $recommendations += "Projected memory in 1 hour: $($analysis.ProjectedMemoryIn1Hour) MB"
            $actions += "Plan for TANK restart within next few hours"
            $actions += "Investigate recent code changes or data processing patterns"
        }
        elseif ($analysis.TrendType -eq "MODERATE_GROWTH") {
            $severity = "MEDIUM"
            $recommendations += "Moderate memory growth detected - monitor closely"
            $recommendations += "Growth rate: $($analysis.GrowthRate) MB/interval"
            $actions += "Continue monitoring for trend confirmation"
        }
        elseif ($analysis.TrendType -eq "SLOW_GROWTH") {
            $severity = "LOW"
            $recommendations += "Slow memory growth detected - normal for active trading"
            $actions += "Continue routine monitoring"
        }
        elseif ($analysis.TrendType -eq "STABLE") {
            $recommendations += "Memory usage is stable - excellent system health"
            $actions += "No action needed - system operating normally"
        }
        elseif ($analysis.TrendType -eq "DECREASING") {
            $recommendations += "Memory usage decreasing - possible cleanup or reduced activity"
            $actions += "Monitor for normal operational patterns"
        }
        
        # Add volatility analysis
        if ($analysis.Volatility -gt 20) {
            $recommendations += "High memory volatility ($($analysis.Volatility)%) detected"
            $actions += "Check for irregular processing patterns or memory fragmentation"
        }
        
        return [PSCustomObject]@{
            LeakDetected = $leakDetected
            Severity = $severity
            Analysis = $analysis
            Recommendations = $recommendations
            Actions = $actions
            Timestamp = Get-Date
        }
    }
}

# Initialize analyzer
$analyzer = [MemoryAnalyzer]::new($ProcessName, $AnalysisWindowMinutes)
$alertCount = 0
$startTime = Get-Date

# Create CSV header if logging enabled
if ($EnableCSVLogging) {
    "Timestamp,MemoryMB,MemoryGB,HandleCount,ThreadCount,PageFaultsMB,TrendType,GrowthRate,Confidence,Severity,LeakDetected" | Out-File $LogFile
}

Write-Host "🔍 TANK Advanced Memory Leak Detector Started" -ForegroundColor Green
Write-Host "Process: $ProcessName | Sample Interval: $SampleIntervalSeconds seconds" -ForegroundColor Cyan
Write-Host "Alert Threshold: $AlertThresholdMB MB | Analysis Window: $AnalysisWindowMinutes minutes" -ForegroundColor Yellow
Write-Host "CSV Logging: $EnableCSVLogging | Alerts: $EnableAlerts" -ForegroundColor Magenta
Write-Host "=" * 100

while ($true) {
    try {
        $process = Get-Process $ProcessName -ErrorAction Stop
        $timestamp = Get-Date
        
        # Collect detailed sample
        $sample = [PSCustomObject]@{
            Timestamp = $timestamp
            MemoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
            MemoryGB = [math]::Round($process.WorkingSet64 / 1GB, 3)
            CPUTime = $process.TotalProcessorTime.TotalMilliseconds
            HandleCount = $process.HandleCount
            ThreadCount = $process.Threads.Count
            PageFaults = [math]::Round($process.PagedMemorySize64 / 1MB, 2)
        }
        
        $analyzer.AddSample($sample)
        
        # Perform leak analysis
        $leakAnalysis = $analyzer.DetectMemoryLeaks()
        
        # CSV Logging
        if ($EnableCSVLogging) {
            "$($timestamp.ToString('yyyy-MM-dd HH:mm:ss')),$($sample.MemoryMB),$($sample.MemoryGB),$($sample.HandleCount),$($sample.ThreadCount),$($sample.PageFaults),$($leakAnalysis.Analysis.TrendType),$($leakAnalysis.Analysis.GrowthRate),$($leakAnalysis.Analysis.Confidence),$($leakAnalysis.Severity),$($leakAnalysis.LeakDetected)" | Out-File $LogFile -Append
        }
        
        # Display results
        Clear-Host
        Write-Host "🔍 TANK Advanced Memory Leak Detector - Analysis Report" -ForegroundColor Green
        Write-Host "=" * 100
        Write-Host "Current Time: $($timestamp.ToString('HH:mm:ss')) | Runtime: $([math]::Round(((Get-Date) - $startTime).TotalMinutes, 1)) minutes" -ForegroundColor Cyan
        Write-Host "Sample Count: $($analyzer.Samples.Count) | Analysis Window: $AnalysisWindowMinutes minutes" -ForegroundColor Cyan
        Write-Host ""
        
        # Current metrics
        Write-Host "📊 CURRENT METRICS:" -ForegroundColor Yellow
        Write-Host "  Memory Usage: $($sample.MemoryMB) MB ($($sample.MemoryGB) GB)" -ForegroundColor White
        Write-Host "  Handle Count: $($sample.HandleCount)" -ForegroundColor White
        Write-Host "  Thread Count: $($sample.ThreadCount)" -ForegroundColor White
        Write-Host "  Page Faults:  $($sample.PageFaults) MB" -ForegroundColor White
        Write-Host ""
        
        # Statistical analysis
        if ($leakAnalysis.Analysis.Status -eq "ANALYZED") {
            Write-Host "📈 STATISTICAL ANALYSIS:" -ForegroundColor Yellow
            Write-Host "  Trend Type:   $($leakAnalysis.Analysis.TrendType)" -ForegroundColor $(
                switch ($leakAnalysis.Analysis.TrendType) {
                    "CRITICAL_LEAK" { "Red" }
                    "LEAK_DETECTED" { "Red" }
                    "MODERATE_GROWTH" { "Yellow" }
                    "SLOW_GROWTH" { "Yellow" }
                    "STABLE" { "Green" }
                    "DECREASING" { "Cyan" }
                    default { "White" }
                }
            )
            Write-Host "  Growth Rate:  $($leakAnalysis.Analysis.GrowthRate) MB per interval" -ForegroundColor White
            Write-Host "  Confidence:   $($leakAnalysis.Analysis.Confidence)%" -ForegroundColor White
            Write-Host "  Volatility:   $($leakAnalysis.Analysis.Volatility)%" -ForegroundColor White
            Write-Host "  Time Span:    $($leakAnalysis.Analysis.TimeSpanMinutes) minutes" -ForegroundColor White
            Write-Host ""
            
            Write-Host "📊 MEMORY STATISTICS:" -ForegroundColor Yellow
            Write-Host "  Average:      $($leakAnalysis.Analysis.AverageMemory) MB" -ForegroundColor White
            Write-Host "  Maximum:      $($leakAnalysis.Analysis.MaxMemory) MB" -ForegroundColor White
            Write-Host "  Minimum:      $($leakAnalysis.Analysis.MinMemory) MB" -ForegroundColor White
            Write-Host "  Range:        $([math]::Round($leakAnalysis.Analysis.MaxMemory - $leakAnalysis.Analysis.MinMemory, 2)) MB" -ForegroundColor White
            Write-Host ""
            
            if ($leakAnalysis.Analysis.ProjectedMemoryIn1Hour) {
                Write-Host "🔮 PROJECTIONS:" -ForegroundColor Yellow
                Write-Host "  In 1 Hour:    $($leakAnalysis.Analysis.ProjectedMemoryIn1Hour) MB" -ForegroundColor Cyan
                Write-Host "  In 4 Hours:   $($leakAnalysis.Analysis.ProjectedMemoryIn4Hours) MB" -ForegroundColor Cyan
                Write-Host ""
            }
        }
        
        # Leak detection results
        if ($leakAnalysis.LeakDetected) {
            $alertCount++
            Write-Host "🚨 MEMORY LEAK DETECTED! (Alert #$alertCount)" -ForegroundColor Red -BackgroundColor Yellow
            Write-Host "  Severity: $($leakAnalysis.Severity)" -ForegroundColor Red
            Write-Host ""
            
            Write-Host "📋 ANALYSIS:" -ForegroundColor Yellow
            $leakAnalysis.Recommendations | ForEach-Object {
                Write-Host "  • $_" -ForegroundColor White
            }
            Write-Host ""
            
            Write-Host "⚡ RECOMMENDED ACTIONS:" -ForegroundColor Red
            $leakAnalysis.Actions | ForEach-Object {
                Write-Host "  • $_" -ForegroundColor Yellow
            }
            
            # Send Windows notification if enabled
            if ($EnableAlerts) {
                try {
                    Add-Type -AssemblyName System.Windows.Forms
                    $notification = New-Object System.Windows.Forms.NotifyIcon
                    $notification.Icon = [System.Drawing.SystemIcons]::Warning
                    $notification.BalloonTipTitle = "TANK Memory Leak Detected"
                    $notification.BalloonTipText = "Severity: $($leakAnalysis.Severity) | Growth: $($leakAnalysis.Analysis.GrowthRate) MB/interval"
                    $notification.Visible = $true
                    $notification.ShowBalloonTip(5000)
                    $notification.Dispose()
                } catch {
                    Write-Host "  (Alert notification failed)" -ForegroundColor Gray
                }
            }
            
        } else {
            Write-Host "✅ NO MEMORY LEAK DETECTED" -ForegroundColor Green
            if ($leakAnalysis.Recommendations.Count -gt 0) {
                Write-Host ""
                Write-Host "📋 SYSTEM STATUS:" -ForegroundColor Yellow
                $leakAnalysis.Recommendations | ForEach-Object {
                    Write-Host "  • $_" -ForegroundColor Gray
                }
            }
        }
        
        Write-Host ""
        Write-Host "Next analysis in $SampleIntervalSeconds seconds... (Ctrl+C to stop)" -ForegroundColor Gray
        if ($EnableCSVLogging) {
            Write-Host "Data logged to: $LogFile" -ForegroundColor Gray
        }
        
        Start-Sleep $SampleIntervalSeconds
        
    } catch {
        Write-Host "❌ Error monitoring process: $($_.Exception.Message)" -ForegroundColor Red
        Start-Sleep 10
    }
}
