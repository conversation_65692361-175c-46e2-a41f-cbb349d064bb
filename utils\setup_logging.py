"""
Utility module for setting up logging configuration.
"""

import logging

def setup_debug_logging():
    """
    Set up debug logging for key modules to help with debugging.
    Call this function early in the application startup to enable detailed logging.
    """
    # Configure price fetching module logging
    price_fetching_logger = logging.getLogger("modules.price_fetching.price_fetching_service")
    price_fetching_logger.setLevel(logging.DEBUG)
    
    # Configure websockets library logging (very verbose)
    websockets_logger = logging.getLogger("websockets")
    websockets_logger.setLevel(logging.DEBUG)
    
    # Configure price repository logging
    price_repo_logger = logging.getLogger("modules.price_fetching.price_repository")
    price_repo_logger.setLevel(logging.DEBUG)
    
    # Configure root logger if not already configured
    root_logger = logging.getLogger()
    if not root_logger.handlers:
        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        
        # Add handler to root logger
        root_logger.addHandler(console_handler)
        root_logger.setLevel(logging.INFO)
    
    logging.info("Debug logging configured for WebSocket and price fetching modules")

def disable_debug_logging():
    """
    Disable debug logging for modules that were set to DEBUG level.
    Call this function when detailed logging is no longer needed.
    """
    # Reset price fetching module logging
    price_fetching_logger = logging.getLogger("modules.price_fetching.price_fetching_service")
    price_fetching_logger.setLevel(logging.INFO)
    
    # Reset websockets library logging
    websockets_logger = logging.getLogger("websockets")
    websockets_logger.setLevel(logging.WARNING)
    
    # Reset price repository logging
    price_repo_logger = logging.getLogger("modules.price_fetching.price_repository")
    price_repo_logger.setLevel(logging.INFO)
    
    logging.info("Debug logging disabled for WebSocket and price fetching modules")
