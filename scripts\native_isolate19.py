import time
import logging
import tkinter as tk
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import App class
from gui.app import App

# Configure logging
logging.basicConfig(level=logging.ERROR, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# Global reference to hold the app instance for potential cleanup
app_instance_ref = None

def main():
    global app_instance_ref
    logger.error("NATIVE_ISOLATE19: Starting. Loading config, then App instance...")
    try:
        # Create a hidden Tkinter root
        temp_root = tk.Tk()
        temp_root.withdraw()  # Keep it hidden, don't mainloop

        # Ensure App.__init__ respects flags to keep services idle/disabled
        logger.error("NATIVE_ISOLATE19: Instantiating App...")
        app_instance_ref = App(temp_root)  # This will run App.__init__
        logger.error("NATIVE_ISOLATE19: App instantiated.")

        # Verify SystemMonitorThread started if App init normally does
        if hasattr(app_instance_ref, '_monitoring_thread') and app_instance_ref._monitoring_thread.is_alive():
            logger.error("NATIVE_ISOLATE19: App.SystemMonitorThread is alive.")
        else:
            logger.error("NATIVE_ISOLATE19: App.SystemMonitorThread DID NOT START or is not alive.")

    except Exception as e:
        logger.error(f"NATIVE_ISOLATE19: Error during setup: {e}", exc_info=True)

    logger.error("NATIVE_ISOLATE19: Sleeping for 15 minutes.")
    end_time = time.time() + 15 * 60

    # Log initial memory usage
    import psutil
    process = psutil.Process()
    initial_rss = process.memory_info().rss
    logger.error(f"NATIVE_ISOLATE19: Initial RSS: {initial_rss / (1024 * 1024):.2f} MB")

    # Log memory usage every minute
    last_log_time = time.time()
    log_interval = 60  # seconds

    while time.time() < end_time:
        try:
            # Process Tkinter events minimally if needed for App init without full mainloop
            if 'temp_root' in locals() and temp_root.winfo_exists():
                temp_root.update_idletasks()
                temp_root.update()

            # Log memory usage every minute
            current_time = time.time()
            if current_time - last_log_time >= log_interval:
                current_rss = process.memory_info().rss
                elapsed_minutes = (current_time - end_time + 15 * 60) / 60
                logger.error(f"NATIVE_ISOLATE19: [{elapsed_minutes:.1f} min] RSS: {current_rss / (1024 * 1024):.2f} MB, Growth: {(current_rss - initial_rss) / (1024 * 1024):.2f} MB")
                last_log_time = current_time

            time.sleep(0.1)  # Shorter sleep to allow some event processing
        except KeyboardInterrupt:
            logger.error("NATIVE_ISOLATE19: Test interrupted by user.")
            break
        except tk.TclError:
            logger.error("NATIVE_ISOLATE19: Tkinter root destroyed.")
            break  # If root is destroyed
        except Exception as e:
            logger.error(f"NATIVE_ISOLATE19: Error during sleep: {e}", exc_info=True)

    # Log final memory usage
    final_rss = process.memory_info().rss
    logger.error(f"NATIVE_ISOLATE19: Final RSS: {final_rss / (1024 * 1024):.2f} MB, Total Growth: {(final_rss - initial_rss) / (1024 * 1024):.2f} MB")
    logger.error("NATIVE_ISOLATE19: Finished sleep.")

if __name__ == "__main__":
    logger.error("NATIVE_ISOLATE19: __main__ block entered.")
    main()
    if app_instance_ref and hasattr(app_instance_ref, 'on_closing'):
        logger.error("NATIVE_ISOLATE19: Calling app_instance_ref.on_closing()")
        try:
            app_instance_ref.on_closing()
        except Exception as e_close:
            logger.error(f"NATIVE_ISOLATE19: Error in on_closing: {e_close}")
    logger.error("NATIVE_ISOLATE19: __main__ block finished.")
