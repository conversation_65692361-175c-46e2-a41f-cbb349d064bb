# MMAP Buffer Duplication Fix

## The Problem
We discovered **14.2GB of duplicate MMAP buffers** created by GUI Backend and ApplicationCore using different paths:
- ApplicationCore: `/mnt/c/TESTRADE/data/ipc_mmap_buffers/` (7.1GB)
- GUI Backend: `/mnt/c/TESTRADE/gui/data/ipc_mmap_buffers/` (7.1GB)

This happened because:
1. The MMAP path was configured as relative: `"data/ipc_mmap_buffers"`
2. ApplicationCore runs from `/mnt/c/TESTRADE/`
3. GUI Backend runs from `/mnt/c/TESTRADE/gui/`
4. Both created their own 7GB buffer sets

## The Fix Applied
Updated `BulletproofBabysitterIPCClient` to:
1. Detect the project root directory dynamically
2. Always resolve MMAP paths relative to project root, not current working directory
3. Add debug logging to show path resolution

## Cleanup Steps

### 1. Stop All Services
```bash
# Stop all TESTRADE processes
taskkill /F /IM python.exe
```

### 2. Remove Duplicate Buffers
```bash
# Remove the duplicate GUI buffers (save 7.1GB)
rm -rf /mnt/c/TESTRADE/gui/data/ipc_mmap_buffers/
```

### 3. Verify Single Buffer Location
After restarting, both ApplicationCore and GUI Backend should use:
- `/mnt/c/TESTRADE/data/ipc_mmap_buffers/`

### 4. Monitor Logs
Check logs for the new path resolution debug info:
```
MMAP Path Resolution Debug:
  - Current working directory: [will vary]
  - Project root detected: /mnt/c/TESTRADE
  - Configured path: data/ipc_mmap_buffers
  - Resolved absolute path: /mnt/c/TESTRADE/data/ipc_mmap_buffers
```

## Benefits
1. **Save 7.1GB disk space**
2. **Shared buffers** - Both processes use the same memory-mapped files
3. **Better performance** - OS can cache one set of buffers instead of two
4. **Correct architecture** - No message isolation between Core and GUI

## Future Considerations
1. Consider if GUI Backend really needs 7GB buffers (it mainly sends small commands)
2. Could use smaller buffers for GUI (e.g., 100MB for system channel)
3. Document that all processes must share the same MMAP buffer location