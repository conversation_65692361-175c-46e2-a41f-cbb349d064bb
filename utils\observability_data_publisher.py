# TESTRADE/utils/observability_data_publisher.py
import os
import json
import time
import logging
import threading
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class ObservabilityDataPublisher:
    def __init__(self, 
                 log_dir: str = "data/observability_logs", 
                 config_service: Optional[Any] = None): # Optional GlobalConfig for future use
        """
        Initializes the ObservabilityDataPublisher.

        Args:
            log_dir: Directory to store the NDJSON log files.
            config_service: Optional GlobalConfig instance for future settings.
        """
        self.log_dir = Path(log_dir)
        self.config_service = config_service # Store for potential future use
        self._file_handles: Dict[str, Any] = {}
        self._lock = threading.RLock() # Lock for accessing file_handles dict and writing

        try:
            self.log_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"ObservabilityDataPublisher initialized. Log directory: {self.log_dir.resolve()}")
        except Exception as e:
            logger.error(f"Failed to create observability log directory {self.log_dir}: {e}", exc_info=True)
            # Continue, but publishing will likely fail.

        # Define base filenames
        self._log_files_config = {
            "raw_ocr": "raw_ocr.ndjson",
            "cleaned_ocr": "cleaned_ocr.ndjson",
            "market_data_trades": "market_data_trades.ndjson",
            "market_data_quotes": "market_data_quotes.ndjson",
            # Add more streams here as needed (e.g., positions, orders)
        }
        
        # Pre-open files or open on first write (opening on first write is often safer for startup)
        # For now, we'll open on first write in _write_to_log

    def _get_file_handle(self, stream_type_key: str):
        """Gets or creates a file handle for a given stream type. Thread-safe."""
        with self._lock:
            if stream_type_key not in self._file_handles:
                if stream_type_key not in self._log_files_config:
                    logger.error(f"Unknown stream type key for observability: {stream_type_key}")
                    return None
                
                file_path = self.log_dir / self._log_files_config[stream_type_key]
                try:
                    # Open in append mode, create if not exists
                    # Using line buffering (bufsize=1) for quicker flushes to disk
                    self._file_handles[stream_type_key] = file_path.open('a', buffering=1, encoding='utf-8')
                    logger.info(f"Opened observability log file for '{stream_type_key}': {file_path}")
                except Exception as e:
                    logger.error(f"Failed to open log file {file_path} for '{stream_type_key}': {e}", exc_info=True)
                    return None
            return self._file_handles.get(stream_type_key)

    def _write_to_log(self, stream_type_key: str, data_dict: Dict):
        """Writes a data dictionary as a JSON line to the specified log file."""
        if not isinstance(data_dict, dict):
            logger.warning(f"Observability: Data for stream '{stream_type_key}' is not a dict, cannot publish: {type(data_dict)}")
            return

        # Add a common timestamp if not already present, for consistent NDJSON entries
        if 'event_ts' not in data_dict: # Use a distinct timestamp key for the publisher
             data_dict['event_ts'] = time.time()

        fh = self._get_file_handle(stream_type_key)
        if fh:
            try:
                with self._lock: # Ensure write and flush are atomic for this handle
                    json_line = json.dumps(data_dict)
                    fh.write(json_line + "\n")
                    # fh.flush() # Line buffering should handle this, but explicit flush can be added if issues
            except Exception as e:
                logger.error(f"Failed to write to observability log '{stream_type_key}': {e}", exc_info=True)
                # Consider closing and trying to reopen the handle if write fails persistently
                # self.close_log_file(stream_type_key) 

    def publish_raw_ocr(self, raw_ocr_data_dict: Dict):
        """Publishes raw OCR data to its dedicated log file."""
        self._write_to_log("raw_ocr", raw_ocr_data_dict)

    def publish_cleaned_ocr(self, cleaned_ocr_snapshots_dict: Dict[str, Dict[str, Any]]):
        """
        Publishes cleaned OCR snapshots.
        Each symbol's snapshot will be a separate line in the NDJSON file.
        """
        # The input is Dict[symbol, snapshot_dict]. We want to log each snapshot.
        # For NDJSON, it's better if each line is a self-contained record.
        # So, we can augment each snapshot with its symbol and a common timestamp.
        publish_time = time.time()
        for symbol, snapshot_data in cleaned_ocr_snapshots_dict.items():
            # Create a new dict for each line to include the symbol and a consistent timestamp
            log_entry = {
                'event_ts': publish_time, # Publisher's timestamp for this batch
                'symbol': symbol,
                **snapshot_data # Unpack the snapshot data
            }
            self._write_to_log("cleaned_ocr", log_entry)
    
    def publish_market_data(self, data_type: str, market_data_dict: Dict):
        """
        Publishes market data (trade or quote) to its log file.
        Args:
            data_type: "trade" or "quote"
            market_data_dict: Dictionary representation of the trade/quote data.
        """
        if data_type == "trade":
            self._write_to_log("market_data_trades", market_data_dict)
        elif data_type == "quote":
            self._write_to_log("market_data_quotes", market_data_dict)
        else:
            logger.warning(f"Observability: Unknown market data type '{data_type}' for publishing.")

    def close_log_file(self, stream_type_key: str):
        """Closes a specific log file handle."""
        with self._lock:
            fh = self._file_handles.pop(stream_type_key, None)
            if fh:
                try:
                    fh.close()
                    logger.info(f"Closed observability log file for '{stream_type_key}'.")
                except Exception as e:
                    logger.error(f"Error closing log file for '{stream_type_key}': {e}", exc_info=True)

    def shutdown(self):
        """Closes all open log file handles."""
        logger.info("ObservabilityDataPublisher shutting down and closing log files...")
        with self._lock:
            for key in list(self._file_handles.keys()): # Iterate over a copy of keys
                fh = self._file_handles.pop(key)
                if fh:
                    try:
                        fh.close()
                    except Exception as e:
                        logger.error(f"Error closing log file for '{key}' during shutdown: {e}", exc_info=True)
        logger.info("ObservabilityDataPublisher shutdown complete.")