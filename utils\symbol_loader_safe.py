#!/usr/bin/env python3
"""
Safe symbol loader with absolute path handling and enhanced error recovery.
This is an alternative implementation to address environment-specific hanging issues.
"""

import os
import sys
import csv
import re
import logging
from pathlib import Path
from typing import Set, Tuple, Optional

# Global symbol sets
tradeable_symbols: Set[str] = set()
non_tradeable_symbols: Set[str] = set()

# Tracking variables
_symbols_loaded = False
_last_loaded_path: Optional[str] = None
_last_loaded_timestamp = 0

# Set up logging with minimal configuration to avoid deadlocks
logger = logging.getLogger(__name__)

def get_project_paths() -> Tuple[Path, Path]:
    """
    Calculate absolute paths to project directories.
    
    Returns:
        Tuple of (base_dir, data_dir)
    """
    # Calculate absolute path to data directory
    current_file = Path(__file__).resolve()
    base_dir = current_file.parent.parent  # Go up from utils/ to project root
    data_dir = base_dir / 'data'
    
    return base_dir, data_dir

def load_symbols_safe(filename: str = 'tradeable_symbols.csv') -> bool:
    """
    Load symbols with absolute path safety and enhanced error handling.
    
    Args:
        filename: Name of the CSV file in the data directory
        
    Returns:
        True if loading succeeded, False otherwise
    """
    global tradeable_symbols, non_tradeable_symbols, _symbols_loaded
    global _last_loaded_path, _last_loaded_timestamp
    
    try:
        # Get absolute paths
        base_dir, data_dir = get_project_paths()
        filepath = data_dir / filename
        
        print(f"Loading symbols from: {filepath}")
        
        # Check if file exists
        if not filepath.exists():
            logger.error(f"Symbol file missing: {filepath}")
            print(f"ERROR: Symbol file missing: {filepath}")
            return False
        
        # Check file size
        file_size = filepath.stat().st_size
        if file_size == 0:
            logger.error(f"Symbol file is empty: {filepath}")
            print(f"ERROR: Symbol file is empty: {filepath}")
            return False
        
        print(f"File exists, size: {file_size} bytes")
        
        # Clear existing sets
        tradeable_symbols.clear()
        non_tradeable_symbols.clear()
        
        # Load symbols with enhanced error handling
        with open(filepath, 'r', encoding='utf-8-sig') as csvfile:
            # Auto-detect CSV dialect
            try:
                sample = csvfile.read(1024)
                csvfile.seek(0)
                dialect = csv.Sniffer().sniff(sample)
                print(f"Detected CSV dialect: delimiter='{dialect.delimiter}'")
            except Exception as e:
                print(f"CSV dialect detection failed: {e}, using default")
                dialect = csv.excel
            
            # Read CSV
            reader = csv.DictReader(csvfile, dialect=dialect)
            headers = [h.strip() for h in reader.fieldnames] if reader.fieldnames else []
            print(f"CSV headers: {headers}")
            
            # Case-insensitive header mapping
            header_map = {col.lower(): col for col in headers}
            
            if 'symbol' not in header_map:
                logger.error("Missing 'Symbol' column in CSV")
                print("ERROR: Missing 'Symbol' column in CSV")
                return False
            
            if 'tradeable' not in header_map:
                logger.error("Missing 'TRADEABLE' column in CSV")
                print("ERROR: Missing 'TRADEABLE' column in CSV")
                return False
            
            symbol_col = header_map['symbol']
            tradeable_col = header_map['tradeable']
            
            # Process rows
            row_count = 0
            tradeable_count = 0
            non_tradeable_count = 0
            
            for row_idx, row in enumerate(reader, 1):
                row_count += 1
                
                # Get symbol
                raw_symbol = row.get(symbol_col, '').strip().upper()
                if not raw_symbol:
                    continue
                
                # Clean symbol (remove non-alphanumeric characters)
                symbol = re.sub(r'[^A-Z0-9]', '', raw_symbol)
                if not symbol:
                    continue
                
                # Get tradeable status
                raw_value = str(row.get(tradeable_col, '')).strip().lower()
                is_tradeable = raw_value in {'y', 'yes', 'true', '1'}
                
                # Add to appropriate set
                if is_tradeable:
                    tradeable_symbols.add(symbol)
                    tradeable_count += 1
                else:
                    non_tradeable_symbols.add(symbol)
                    non_tradeable_count += 1
                
                # Log first few symbols for verification
                if row_idx <= 5:
                    print(f"Row {row_idx}: {symbol} -> {'Tradeable' if is_tradeable else 'Non-tradeable'}")
        
        # Update tracking variables
        _symbols_loaded = True
        _last_loaded_path = str(filepath)
        _last_loaded_timestamp = filepath.stat().st_mtime
        
        print(f"Successfully loaded {tradeable_count} tradeable and {non_tradeable_count} non-tradeable symbols")
        logger.info(f"Loaded {tradeable_count} tradeable and {non_tradeable_count} non-tradeable symbols")
        
        return True
        
    except Exception as e:
        logger.exception(f"Failed to load symbols: {e}")
        print(f"ERROR: Failed to load symbols: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_symbol_counts() -> Tuple[int, int]:
    """
    Get current symbol counts.
    
    Returns:
        Tuple of (tradeable_count, non_tradeable_count)
    """
    return len(tradeable_symbols), len(non_tradeable_symbols)

def is_symbol_tradeable(symbol: str) -> Optional[bool]:
    """
    Check if a symbol is tradeable.
    
    Args:
        symbol: Symbol to check
        
    Returns:
        True if tradeable, False if non-tradeable, None if unknown
    """
    symbol = symbol.upper().strip()
    
    if symbol in tradeable_symbols:
        return True
    elif symbol in non_tradeable_symbols:
        return False
    else:
        return None

def ensure_symbols_loaded() -> bool:
    """
    Ensure symbols are loaded, loading them if necessary.
    
    Returns:
        True if symbols are available, False otherwise
    """
    if not _symbols_loaded:
        return load_symbols_safe()
    return True

def main():
    """Test function for the safe symbol loader."""
    print("Testing Safe Symbol Loader")
    print("=" * 40)
    
    # Test path calculation
    base_dir, data_dir = get_project_paths()
    print(f"Base directory: {base_dir}")
    print(f"Data directory: {data_dir}")
    
    # Test symbol loading
    success = load_symbols_safe()
    
    if success:
        tradeable_count, non_tradeable_count = get_symbol_counts()
        print(f"\n✅ Loading successful!")
        print(f"Tradeable symbols: {tradeable_count}")
        print(f"Non-tradeable symbols: {non_tradeable_count}")
        
        # Test a few symbols
        test_symbols = ['AAPL', 'MSFT', 'GOOGL', 'INVALID']
        for symbol in test_symbols:
            status = is_symbol_tradeable(symbol)
            if status is True:
                print(f"{symbol}: Tradeable")
            elif status is False:
                print(f"{symbol}: Non-tradeable")
            else:
                print(f"{symbol}: Unknown")
    else:
        print("\n❌ Loading failed!")

if __name__ == "__main__":
    main()
