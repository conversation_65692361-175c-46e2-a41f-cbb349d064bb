# 🐙 GitHub MCP Integration Plan

## Quick Implementation (5 min)

### 1. Add GitHub Tool to MCP Server
```python
# Add to intellisense/mcp/server.py _get_system_tools()
Tool(
    name="github_commit_and_push",
    description="Commit changes and push to GitHub repository",
    inputSchema={
        "type": "object",
        "properties": {
            "commit_message": {"type": "string"},
            "files": {"type": "array", "items": {"type": "string"}},
            "branch": {"type": "string", "default": "main"},
            "push": {"type": "boolean", "default": true}
        },
        "required": ["commit_message"]
    }
)
```

### 2. Add Handler in _handle_tool_call()
```python
elif name == "github_commit_and_push":
    result = await self._handle_github_operations(arguments)
    return result
```

### 3. GitHub Handler Method
```python
async def _handle_github_operations(self, args):
    import subprocess
    import os
    
    os.chdir("/mnt/c/testrade")
    
    # Add files
    files = args.get("files", ["."])
    for file in files:
        subprocess.run(["git", "add", file])
    
    # Commit
    commit_msg = args["commit_message"]
    result = subprocess.run(["git", "commit", "-m", commit_msg], 
                          capture_output=True, text=True)
    
    # Push if requested
    if args.get("push", True):
        push_result = subprocess.run(["git", "push"], 
                                   capture_output=True, text=True)
        
    return {
        "committed": result.returncode == 0,
        "pushed": push_result.returncode == 0 if args.get("push") else False,
        "message": f"Committed: {commit_msg}"
    }
```

## Usage
```bash
# Via MCP
curl -X POST http://localhost:8002/mcp/github_commit \
  -d '{"commit_message": "Add complete MCP integration with GitHub tools", "files": ["intellisense/mcp/"]}'

# Via VS Code Agent
mcpClient.callTool("github_commit_and_push", {
  "commit_message": "Implement MasterActionFilter pipeline", 
  "files": ["modules/trade_management/"]
})
```

## Next Session Enhancement
- Add branch management
- Pull request creation  
- Status checks
- Diff analysis