@echo off
echo === Clean Rebuild of C++ OCR Accelerator ===

cd /d "C:\TESTRADE\ocr_accelerator"

echo Step 1: Clean existing build files...
if exist "x64\Release\ocr_accelerator.pyd" del "x64\Release\ocr_accelerator.pyd"
if exist "x64\Release\ocr_accelerator.lib" del "x64\Release\ocr_accelerator.lib"
if exist "x64\Release\ocr_accelerator.exp" del "x64\Release\ocr_accelerator.exp"
if exist "x64\Release\ocr_accelerator.obj" del "x64\Release\ocr_accelerator.obj"

echo Step 2: Find MSBuild...
set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
if not exist %MSBUILD_PATH% (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
)
if not exist %MSBUILD_PATH% (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
)

if not exist %MSBUILD_PATH% (
    echo ERROR: MSBuild not found
    pause
    exit /b 1
)

echo Found MSBuild: %MSBUILD_PATH%

echo Step 3: Clean build...
%MSBUILD_PATH% ocr_accelerator.vcxproj /p:Configuration=Release /p:Platform=x64 /t:Clean

echo Step 4: Rebuild...
%MSBUILD_PATH% ocr_accelerator.vcxproj /p:Configuration=Release /p:Platform=x64 /t:Rebuild

echo Step 5: Check output...
if exist "x64\Release\ocr_accelerator.pyd" (
    echo SUCCESS: PYD file created
    dir "x64\Release\ocr_accelerator.pyd"
) else (
    echo ERROR: PYD file not created
    pause
    exit /b 1
)

echo Step 6: Test import...
cd /d "C:\TESTRADE"
python minimal_cpp_test.py

pause