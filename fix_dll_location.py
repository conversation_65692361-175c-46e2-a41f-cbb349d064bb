#!/usr/bin/env python3
"""
Fix DLL location issue by copying only the required DLLs back to root.
"""

import os
import shutil

def copy_required_dlls():
    """Copy only the DLLs that the C++ module actually needs to the root."""
    print("=== Fixing DLL Location Issue ===")
    
    build_dir = "ocr_accelerator/x64/Release"
    
    if not os.path.exists(build_dir):
        print(f"❌ Build directory not found: {build_dir}")
        return False
    
    # Only the DLLs that the C++ module directly depends on
    required_dlls = [
        "tesseract55.dll",        # Core Tesseract
        "leptonica-1.85.0.dll",   # Image processing for Tesseract
    ]
    
    print("Copying critical DLLs to root directory...")
    
    copied_count = 0
    for dll in required_dlls:
        src_path = os.path.join(build_dir, dll)
        dst_path = dll
        
        if os.path.exists(src_path):
            try:
                shutil.copy2(src_path, dst_path)
                print(f"✅ Copied: {dll}")
                copied_count += 1
            except Exception as e:
                print(f"❌ Failed to copy {dll}: {e}")
        else:
            print(f"⚪ Not found: {dll}")
    
    print(f"\nCopied {copied_count} critical DLLs to root directory")
    return copied_count > 0

def test_import_after_fix():
    """Test if the module imports after copying DLLs."""
    print("\n=== Testing Import After Fix ===")
    
    try:
        import sys
        
        # Simple import test
        build_path = os.path.abspath("ocr_accelerator/x64/Release")
        if build_path not in sys.path:
            sys.path.insert(0, build_path)
        
        import ocr_accelerator
        print("✅ Import successful!")
        
        # Test basic function
        if hasattr(ocr_accelerator, 'test_function'):
            result = ocr_accelerator.test_function()
            print(f"✅ test_function(): {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import still failed: {e}")
        return False

if __name__ == "__main__":
    print("TESTRADE DLL Location Fix")
    print("=" * 40)
    
    # Copy required DLLs
    fix_applied = copy_required_dlls()
    
    if fix_applied:
        # Test import
        success = test_import_after_fix()
        
        if success:
            print("\n🎉 DLL fix successful! Module now imports correctly.")
            print("💡 The C++ module needed core DLLs in the application root directory.")
        else:
            print("\n❌ Module still fails to import after DLL fix.")
            print("💡 May need additional DLLs or different approach.")
    else:
        print("\n❌ Could not apply DLL fix.")
    
    print("\n📝 Note: Only critical Tesseract DLLs were copied to minimize clutter.")