# OCR Display Correlation & Streaming Architecture Issue

**Date:** 2025-01-13  
**Reporter:** Augment Agent  
**Severity:** Medium - UI/UX Issue  
**Impact:** Confusing OCR display behavior, architectural inconsistency  

## Executive Summary

The TESTRADE OCR display system exhibits two fundamental architectural issues:

1. **Missing Correlation Between Raw and Processed OCR**: Raw OCR frames and their corresponding processed results are not correlated via UUIDs, leading to out-of-sync displays
2. **Redis Used as Container vs Stream**: The system treats Redis streams as persistent containers rather than flowing data streams, causing stale data to persist in the GUI

## Current Behavior (Problematic)

### Observed Issue
When raw OCR text doesn't match tradeline regex patterns:
- **Raw OCR window** shows current frame text (correct)
- **Processed OCR window** shows last valid tradeline data (incorrect - should be empty)
- **No correlation** between raw and processed displays
- **Confusing user experience** - displays appear out of sync

### Root Cause Analysis

#### 1. Missing UUID Correlation
**Current Flow:**
```
Raw OCR Frame → UUID: abc123 → GUI: {"type": "raw_ocr", "content": "..."}
Processed OCR → UUID: abc123 → GUI: {"type": "processed_ocr", "snapshots": {...}}
```

**Problem:** GUI messages don't include correlation IDs, so frontend can't tie them together.

#### 2. Conditional Message Publishing
**Location:** `modules/ocr/ocr_data_conditioning_service.py:146`
```python
if cleaned_data_snapshots:  # Only publishes when snapshots exist
    # Publish to Redis...
```

**Problem:** When raw OCR doesn't match tradeline patterns, NO processed_ocr message is sent, so GUI retains stale data.

#### 3. Redis as Container vs Stream
**Current Architecture:** Redis streams accumulate data that gets consumed but GUI state persists
**Expected Architecture:** Data flows through Redis, gets consumed, GUI clears after processing

## Technical Deep Dive

### Current Data Flow
```
1. OCR Process → Raw OCR (UUID: abc123) → ApplicationCore → Redis → GUI Backend → WebSocket
2. Conditioning Service → Processes Raw OCR → IF valid tradeline → Redis → GUI Backend → WebSocket
3. Frontend → Receives raw_ocr → Updates raw display
4. Frontend → Receives processed_ocr (maybe) → Updates processed display
5. Frontend → NO correlation between messages → Displays drift apart
```

### Code Locations

#### OCR Data Generation
- **File:** `modules/ocr/ocr_process_main.py:261`
- **UUID Creation:** `event_id=internal_ocr_pkg.get("ocr_event_id", str(uuid.uuid4()))`

#### Conditioning Service
- **File:** `modules/ocr/ocr_data_conditioning_service.py:146`
- **Conditional Publishing:** `if cleaned_data_snapshots:`
- **Correlation Preservation:** Lines 187, 203 - `origin_correlation_id=master_correlation_id`

#### GUI Backend
- **File:** `gui/gui_backend.py:167, 190`
- **Missing Correlation:** Messages don't include `event_id`
- **Raw OCR:** `{"type": "raw_ocr", "content": ocr_text, "confidence": confidence}`
- **Processed OCR:** `{"type": "processed_ocr", "snapshots": snapshots}`

#### Frontend
- **File:** `gui/portrait_trading_gui.html:1399`
- **No Correlation Logic:** `updateProcessedOcrTable()` just updates display
- **No Timeout/Cleanup:** Stale data persists indefinitely

## Proposed Solution Architecture

### 1. Add Correlation IDs to GUI Messages
```javascript
// Raw OCR Message
{
    "type": "raw_ocr",
    "event_id": "abc123",
    "content": "Long YIBO 1000 @ 15.25",
    "confidence": 85.2,
    "timestamp": "2025-01-13T10:30:45.123Z"
}

// Processed OCR Message (correlated)
{
    "type": "processed_ocr", 
    "event_id": "abc123",
    "snapshots": {"YIBO": {...}},
    "symbols_detected": ["YIBO"]
}

// No Match Message (new)
{
    "type": "processed_ocr",
    "event_id": "abc123", 
    "snapshots": {},
    "symbols_detected": [],
    "no_tradeline_match": true
}
```

### 2. Frontend Correlation State Management
```javascript
// Pending frame correlation
const pendingFrames = new Map();

// Raw OCR handler
function handleRawOCR(data) {
    pendingFrames.set(data.event_id, {
        raw: data,
        processed: null,
        timestamp: Date.now()
    });
    updateRawOcrDisplay(data);
    
    // Clear processed display immediately
    clearProcessedOcrDisplay();
}

// Processed OCR handler  
function handleProcessedOCR(data) {
    const frame = pendingFrames.get(data.event_id);
    if (frame) {
        frame.processed = data;
        updateProcessedOcrDisplay(data);
        pendingFrames.delete(data.event_id); // Clean up
    }
}

// Timeout cleanup (every 2 seconds)
setInterval(() => {
    const now = Date.now();
    for (const [eventId, frame] of pendingFrames) {
        if (now - frame.timestamp > 2000) {
            pendingFrames.delete(eventId);
            // Clear processed display if no match received
            if (!frame.processed) {
                clearProcessedOcrDisplay();
            }
        }
    }
}, 2000);
```

### 3. Always-Send Architecture
Modify conditioning service to always send a message:
```python
# Always create and send cleaned event, even if empty
cleaned_event_data_payload = CleanedOCRSnapshotEventData(
    frame_timestamp=getattr(ocr_parsed_data, 'frame_timestamp', 0.0),
    snapshots=cleaned_data_snapshots,  # Could be empty {}
    original_ocr_event_id=master_correlation_id,
    raw_ocr_confidence=getattr(ocr_parsed_data, 'overall_confidence', None),
    origin_correlation_id=master_correlation_id
)
# Always publish, regardless of snapshots content
self._publish_cleaned_ocr_to_redis(cleaned_event_data_payload)
```

## Implementation Impact Assessment

### Files Requiring Changes
1. **GUI Backend** (`gui/gui_backend.py`)
   - Add `event_id` to WebSocket messages
   - Extract correlation IDs from Redis payloads

2. **Frontend** (`gui/portrait_trading_gui.html`) 
   - Implement correlation state management
   - Add timeout/cleanup logic
   - Modify display update functions

3. **Conditioning Service** (`modules/ocr/ocr_data_conditioning_service.py`)
   - Remove conditional publishing
   - Always send processed_ocr messages

### Risk Assessment
- **High Risk:** Core real-time trading data flow
- **Medium Risk:** WebSocket message structure changes
- **Low Risk:** Frontend state management (isolated)

### Testing Requirements
- **Unit Tests:** Correlation logic, timeout cleanup
- **Integration Tests:** End-to-end OCR flow with correlation
- **Performance Tests:** Memory usage with correlation state
- **User Acceptance:** OCR display behavior validation

## Recommendations

### Phase 1: Investigation & Design
1. **SME Review** of proposed architecture
2. **Prototype** correlation logic in isolated environment
3. **Performance analysis** of state management overhead

### Phase 2: Implementation
1. **Backend changes** (GUI backend message correlation)
2. **Frontend changes** (correlation state management)  
3. **Service changes** (always-send architecture)

### Phase 3: Validation
1. **Comprehensive testing** in development environment
2. **Performance validation** under load
3. **User acceptance testing** with real OCR scenarios

## Alternative Solutions

### Option A: Simple Timeout Approach
- Clear processed OCR display after 2 seconds of no updates
- **Pros:** Minimal changes, low risk
- **Cons:** Doesn't solve correlation issue, arbitrary timeout

### Option B: Timestamp-Based Correlation  
- Use frame timestamps instead of UUIDs for correlation
- **Pros:** Leverages existing timestamps
- **Cons:** Less precise, potential race conditions

### Option C: Status Quo with Documentation
- Document current behavior as "expected"
- **Pros:** No development risk
- **Cons:** Poor user experience continues

## Conclusion

The correlation architecture issue represents a fundamental design gap that affects user experience and system consistency. While not critical to trading functionality, it creates confusion and violates the principle of Redis as a flowing stream rather than persistent storage.

**Recommendation:** Proceed with SME review and phased implementation of the correlation solution, prioritizing the always-send architecture as the lowest-risk first step.
