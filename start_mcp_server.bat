@echo off
echo Starting IntelliSense MCP Server...
echo.

REM Set PYTHONPATH to include the project root
set PYTHONPATH=%~dp0

REM Run the MCP server
echo Running MCP server with 18 trading tools...
echo Connected to Redis at **************:6379
echo Loading 592 IntelliSense modules...
echo.
python intellisense\mcp\server_runner.py

if errorlevel 1 (
    echo.
    echo Error: Failed to start MCP server
    echo Please ensure MCP 1.10.1 is installed: pip install mcp==1.10.1
    pause
)