#!/usr/bin/env python3
"""
Emergency GUI Setup Script
Creates directory structure and files for TANK Emergency GUI
"""

import os
import sys
from pathlib import Path

def create_directory_structure():
    """Create necessary directories"""
    directories = [
        'emergency_gui',
        'emergency_gui/templates',
        'emergency_gui/static'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")

def create_requirements_file():
    """Create requirements.txt for Emergency GUI"""
    requirements = """flask==2.3.3
flask-socketio==5.3.6
pyzmq==25.1.1
requests==2.31.0
python-socketio==5.8.0
eventlet==0.33.3
"""
    
    with open('emergency_gui/requirements.txt', 'w', encoding='utf-8') as f:
        f.write(requirements)
    print("✅ Created requirements.txt")

def create_run_script():
    """Create convenient run script"""
    run_script = """#!/usr/bin/env python3
'''
Quick start script for TANK Emergency GUI
'''

import subprocess
import sys
import os

def main():
    print("🚀 Starting TANK Emergency GUI...")
    
    # Check if we're in the right directory
    if not os.path.exists('emergency_gui_server.py'):
        print("❌ Error: emergency_gui_server.py not found")
        print("   Make sure you're in the emergency_gui directory")
        sys.exit(1)
    
    # Check if requirements are installed
    try:
        import flask
        import flask_socketio
        import zmq
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("   Run: pip install -r requirements.txt")
        sys.exit(1)
    
    # Start the server
    try:
        subprocess.run([sys.executable, 'emergency_gui_server.py'])
    except KeyboardInterrupt:
        print("\\n👋 Emergency GUI stopped")

if __name__ == '__main__':
    main()
"""
    
    with open('emergency_gui/run_gui.py', 'w', encoding='utf-8') as f:
        f.write(run_script)
    
    # Make it executable on Unix systems
    if os.name != 'nt':
        os.chmod('emergency_gui/run_gui.py', 0o755)
    
    print("✅ Created run_gui.py")

def create_config_template():
    """Create configuration template"""
    config = """{
    "emergency_gui": {
        "server_port": 8766,
        "server_host": "localhost",
        "tank_status_port": 5561,
        "tank_command_port": 5560,
        "tank_http_port": 9999,
        "auto_refresh_interval": 5,
        "alert_sound_enabled": true,
        "keyboard_shortcuts_enabled": true
    },
    "tank_integration": {
        "enable_status_publisher": true,
        "enable_command_receiver": true,
        "enable_local_http_api": true,
        "status_broadcast_interval": 5,
        "command_timeout": 30
    }
}
"""
    
    with open('emergency_gui/config.json', 'w', encoding='utf-8') as f:
        f.write(config)
    print("✅ Created config.json")

def create_readme():
    """Create README file"""
    readme = """# TANK Emergency GUI

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start the Emergency GUI:**
   ```bash
   python run_gui.py
   ```
   Or directly:
   ```bash
   python emergency_gui_server.py
   ```

3. **Open in browser:**
   http://localhost:8766

## Features

- **Real-time status monitoring** of TANK Core and IPC channels
- **Buffer level visualization** with color-coded warnings
- **Emergency controls** for force close and OCR management
- **ROI adjustment** interface
- **Critical alerts** with visual and audio notifications
- **Keyboard shortcuts** for quick access

## Keyboard Shortcuts

- `Ctrl+Shift+F` - Force Close All Positions
- `Ctrl+Shift+S` - Stop OCR Process
- `Ctrl+Shift+T` - Start OCR Process  
- `Ctrl+Shift+R` - Refresh Status

## Configuration

Edit `config.json` to customize:
- Server ports
- Refresh intervals
- Alert settings
- TANK integration options

## Troubleshooting

**GUI shows "DISCONNECTED":**
- Ensure TANK ApplicationCore is running
- Check that ports 5560, 5561, 9999 are available
- Verify TANK has Emergency GUI integration enabled

**Commands not working:**
- Check TANK logs for command receiver errors
- Ensure proper integration with ApplicationCore
- Verify command methods are implemented

**No status updates:**
- Check TANK status publisher is running
- Verify IPC buffer stats are available
- Look for ZMQ connection errors

## Architecture

```
Browser ←→ Emergency GUI Server ←→ TANK ApplicationCore
          (WebSocket)              (ZMQ + HTTP)
```

- **Status Updates**: TANK → ZMQ PUB → GUI → WebSocket → Browser
- **Commands**: Browser → HTTP → GUI → ZMQ PUSH → TANK
- **Initial Status**: Browser → HTTP → TANK HTTP API

## Security

- Runs on localhost only
- No external network dependencies
- Command confirmations required
- Local ZMQ/HTTP communications only
"""
    
    with open('emergency_gui/README.md', 'w', encoding='utf-8') as f:
        f.write(readme)
    print("✅ Created README.md")

def copy_existing_files():
    """Copy existing emergency GUI files if they exist"""
    files_to_copy = [
        ('gui/emergency_gui_server.py', 'emergency_gui/emergency_gui_server.py'),
        ('gui/emergency_gui_html.html', 'emergency_gui/templates/emergency_dashboard.html')
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            import shutil
            shutil.copy2(src, dst)
            print(f"✅ Copied {src} → {dst}")
        else:
            print(f"⚠️  {src} not found - will need to be copied manually")

def print_completion_message():
    """Print setup completion message"""
    message = """
╔══════════════════════════════════════════════════════════════╗
║                 SETUP COMPLETE! 🎉                          ║
╠══════════════════════════════════════════════════════════════╣
║                                                              ║
║  Emergency GUI files created in: emergency_gui/             ║
║                                                              ║
║  Next steps:                                                 ║
║  1. cd emergency_gui                                         ║
║  2. pip install -r requirements.txt                         ║
║  3. Integrate with TANK (see agent instructions)            ║
║  4. python run_gui.py                                       ║
║                                                              ║
║  Access GUI at: http://localhost:8766                       ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝

📁 Files created:
├── emergency_gui/
│   ├── requirements.txt          (Dependencies)
│   ├── config.json              (Configuration)
│   ├── run_gui.py               (Quick start script)
│   ├── README.md                (Documentation)
│   └── templates/               (HTML templates - copy from artifacts)

🔧 Copy these files from the artifacts:
1. Copy emergency_gui_server.py to emergency_gui/
2. Copy emergency_dashboard.html to emergency_gui/templates/

📋 Agent Instructions:
See the "Instructions for Agent" artifact for TANK integration steps.

⚠️  Important: The Emergency GUI requires integration with your 
   ApplicationCore. Follow the agent instructions to add the 
   required ZMQ publishers, command receivers, and HTTP API.
"""
    print(message)

def main():
    """Main setup function"""
    print("🔧 Setting up TANK Emergency GUI...")
    
    try:
        create_directory_structure()
        create_requirements_file()
        create_run_script()
        create_config_template()
        create_readme()
        copy_existing_files()
        print_completion_message()
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
