# modules/order_management/order_data_types.py

from dataclasses import dataclass, field, replace
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any # Removed List, added Set for linked_ephemeral_ids if preferred later
from enum import Enum

# Import all enums from consolidated data_models 
from data_models import OrderS<PERSON>, OrderOrigin, OrderLSAgentStatus, TradeActionType, OrderEventType

# Note: OrderLSAgentStatus is imported from core.enums to maintain type consistency

# --- Trade Action Type Enum ---
# Imported from modules.trade_management.interfaces

@dataclass(slots=True, frozen=True)
class FillRecord:
    # Core fill data
    shares_filled: float
    fill_price: float
    fill_time: float  # Epoch timestamp of the fill execution

    # Optional/additional data often available
    commission: float = 0.0
    side: Optional[OrderSide] = None # Side of this specific fill leg
    liquidity: Optional[str] = None  # e.g., "A" (Added), "R" (Removed)
    fill_id_broker: Optional[str] = None # Broker's unique ID for this fill

    fill_time_est_str: Optional[str] = None # Formatted EST string, e.g., "HH:MM:SS.mmm"

    meta: Dict[str, Any] = field(default_factory=dict)

@dataclass(slots=True, frozen=True)
class StatusHistoryEntry:
    timestamp: float # Epoch timestamp when this status was recorded by our system
    status: OrderLSAgentStatus # The OrderLSAgentStatus Enum member
    reason: Optional[str] = None
    broker_timestamp: Optional[float] = None # Optional: Timestamp from broker for this status event

@dataclass(slots=True, frozen=True)
class Order:
    # --- Required fields (no defaults) ---
    local_id: int
    symbol: str
    side: OrderSide
    event_type: OrderEventType
    requested_shares: float
    timestamp_requested: float

    # --- Optional fields (with defaults) ---
    version: int = 0
    ls_order_id: Optional[int] = None
    ephemeral_corr_id: Optional[int] = None
    linked_ephemeral_ids: Tuple[int, ...] = field(default_factory=tuple)
    action_type: Optional[TradeActionType] = None
    master_correlation_id: Optional[str] = None  # <<<< NEW FIELD for Master Correlation ID >>>>
    requested_lmt_price: Optional[float] = None
    order_type: str = "LMT" # Could be an Enum: e.g., OrderInstructionType(LMT, MKT)
    requested_cost: float = 0.0
    real_time_price_at_request: Optional[float] = None
    trader_price_at_request: Optional[float] = None
    cancel_request_time: Optional[float] = field(default=None, compare=False)
    timestamp_ocr: Optional[float] = None
    timestamp_ocr_processed: Optional[float] = None
    timestamp_to_bridge_sent: Optional[float] = None # When Python BrokerBridge sent to C++
    timestamp_bridge_ack: Optional[float] = None      # When C++ ack'd receipt from Python BrokerBridge
    timestamp_ls_api_sent_ok: Optional[float] = None  # Placeholder: If L_MsgOrderRequested reports OK
    timestamp_broker_final_status: Optional[float] = None # Time of first terminal status
    timestamp_broker: Optional[float] = None # When broker first acknowledges/links the order
    timestamp_sent: Optional[float] = None   # When order was sent to broker by our system (can be same as timestamp_broker initially)
    timestamp_broker_ack: Optional[float] = None # Specifically for when broker acknowledges (distinct from first link if needed)

    # --- Execution State & Details ---
    ls_status: OrderLSAgentStatus = OrderLSAgentStatus.PENDING_SUBMISSION
    fills: Tuple[FillRecord, ...] = field(default_factory=tuple)
    filled_quantity: float = 0.0
    leftover_shares: float = 0.0 # Default to 0.0, should be set explicitly by the creator

    # --- Descriptive & Relational ---
    parent_trade_id: Optional[int] = None
    comment: Optional[str] = None
    rejection_reason: Optional[str] = None
    cancel_reason: Optional[str] = None
    ocr_confidence: Optional[float] = None

    # --- History & Extensibility ---
    status_history: Tuple[StatusHistoryEntry, ...] = field(default_factory=tuple)
    perf_timestamps: Dict[str, float] = field(default_factory=dict)
    meta: Dict[str, Any] = field(default_factory=dict)

    # --- NEW: Professional order tracking fields ---
    client_id: str = field(default="TESTRADE")  # Who created this order
    tags: Dict[str, Any] = field(default_factory=dict)  # Flexible metadata
    origin: OrderOrigin = field(default=OrderOrigin.PYTHON)  # Where this order originated from

    # --- Helper for creating new instances with updates ---
    def with_update(self, **changes) -> 'Order':
        if 'version' not in changes:
            changes['version'] = self.version + 1
        return replace(self, **changes)
    
    # --- Backward compatibility helper ---
    @property
    def is_broker_originated(self) -> bool:
        """Check if this order originated from the broker (for backward compatibility)."""
        # Use origin field if available, otherwise fall back to event_type check
        if hasattr(self, 'origin'):
            return self.origin == OrderOrigin.BROKER
        # Fallback for orders created before origin field was added
        return self.event_type == OrderEventType.MANUAL_BROKER_ORDER
