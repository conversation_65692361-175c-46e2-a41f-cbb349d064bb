"""
Script to capture logs from the SymbolLoaderInternal logger.
This script runs the application for a short time and then exits,
capturing all logs from the SymbolLoaderInternal logger.
"""

import os
import sys
import time
import logging
import subprocess
import datetime

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"symbol_loader_logs_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

# Configure a file handler for the logs
file_handler = logging.FileHandler(log_file, mode="w", encoding="utf-8")
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter("[%(asctime)s] [%(levelname)-8s] [%(name)s] %(message)s")
file_handler.setFormatter(file_formatter)

# Configure a console handler for the logs
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter("[%(levelname)-8s] %(message)s")
console_handler.setFormatter(console_formatter)

# Get the root logger and add the handlers
root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG)
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

# Create a logger for this script
logger = logging.getLogger("capture_symbol_loader_logs")

def main():
    """Main function to run the application and capture logs."""
    logger.info("Starting capture_symbol_loader_logs.py")
    logger.info(f"Logs will be saved to: {log_file}")
    
    # Run the application
    logger.info("Running the application...")
    
    # Use subprocess to run the application
    process = subprocess.Popen(
        ["python", "main.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=1,
        universal_newlines=True,
    )
    
    # Wait for a short time to let the application start and load symbols
    logger.info("Waiting for 30 seconds to let the application start and load symbols...")
    time.sleep(30)
    
    # Kill the process
    logger.info("Killing the application process...")
    process.terminate()
    
    # Wait for the process to terminate
    try:
        process.wait(timeout=5)
        logger.info("Application process terminated successfully.")
    except subprocess.TimeoutExpired:
        logger.warning("Application process did not terminate within timeout. Killing forcefully...")
        process.kill()
        process.wait()
        logger.info("Application process killed forcefully.")
    
    # Get the output from the process
    stdout, stderr = process.communicate()
    
    # Log the output
    logger.info("Application stdout:")
    for line in stdout.splitlines():
        logger.info(f"  {line}")
    
    logger.info("Application stderr:")
    for line in stderr.splitlines():
        logger.info(f"  {line}")
    
    # Extract the SymbolLoaderInternal logs from the main_app.log file
    logger.info("Extracting SymbolLoaderInternal logs from main_app.log...")
    main_app_log = os.path.join(log_dir, "main_app.log")
    
    if os.path.exists(main_app_log):
        with open(main_app_log, "r", encoding="utf-8") as f:
            main_app_log_content = f.read()
        
        # Extract lines containing "SymbolLoaderInternal"
        symbol_loader_logs = [line for line in main_app_log_content.splitlines() if "SymbolLoaderInternal" in line]
        
        # Write the extracted logs to the log file
        logger.info(f"Found {len(symbol_loader_logs)} SymbolLoaderInternal log entries.")
        logger.info("SymbolLoaderInternal logs:")
        for line in symbol_loader_logs:
            logger.info(f"  {line}")
    else:
        logger.error(f"Main app log file not found: {main_app_log}")
    
    logger.info("Done.")
    logger.info(f"Logs saved to: {log_file}")

if __name__ == "__main__":
    main()
