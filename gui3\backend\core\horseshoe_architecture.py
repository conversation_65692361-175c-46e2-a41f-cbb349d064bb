"""
TESTRAD<PERSON> HORSESHOE ARCHITECTURE
================================

CRITICAL DESIGN PRINCIPLE: The Horseshoe Pattern MUST NEVER BE VIOLATED

The TESTRADE system follows a strict horseshoe architecture pattern where <PERSON><PERSON> acts as
the central message bus, with clear separation between consumers and producers:

                    CONSUMERS (Left Side)              PRODUCERS (Right Side)
                    ====================              =====================
                           |                                    |
                           |                                    |
    GUI Backend  <---------|                                    |---------> Signal Generators
    MCP Servers  <---------|          R E D I S                 |---------> OCR Process
    IntelliSense <---------|       (Message Bus)                |---------> Trading Engine
    Monitoring   <---------|                                    |---------> Broker Bridge
                           |                                    |---------> Risk Service
                           |                                    |---------> Order Management
                           |                                    |
                    ====================              =====================
                       READ/CONSUME                         WRITE/PRODUCE

RULES:
-----
1. CONSUMERS (Left Side) - ONLY READ from Redis streams
   - GUI Backend: Displays data, sends commands
   - MCP Servers: Query data on demand
   - IntelliSense: Analyzes and records data
   - Monitoring: Observes system health

2. PRODUCERS (Right Side) - ONLY WRITE to Redis streams
   - Signal Generators: Create trading signals
   - OCR Process: Produces market data snapshots
   - Trading Engine: Executes trades
   - Broker Bridge: Reports fills and positions
   - Risk Service: Validates and controls trading
   - Order Management: Tracks order lifecycle

3. COMMUNICATION FLOW:
   - Consumers NEVER directly call Producers
   - Producers NEVER directly call Consumers
   - All communication goes through Redis streams
   - Commands from Consumers use dedicated command streams
   - Responses from Producers use dedicated response streams

4. REDIS STREAMS:
   - Each stream has a clear owner (producer)
   - Streams are named with clear purpose (e.g., testrade:ocr-parsed)
   - No circular dependencies between streams

5. BENEFITS:
   - Complete decoupling between components
   - Easy to add new consumers without affecting producers
   - Easy to scale individual components
   - Clear data flow and debugging
   - Resilient to component failures

VIOLATIONS TO AVOID:
-------------------
- DO NOT have GUI directly call trading services
- DO NOT have producers read from consumer streams
- DO NOT create bidirectional dependencies
- DO NOT bypass Redis for "performance" reasons

This architecture ensures clean separation of concerns and maintains
system integrity even as complexity grows.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import logging


class HorseshoeConsumer(ABC):
    """
    Base class for all components on the consumer (left) side of the horseshoe.
    Consumers ONLY READ from Redis streams and send commands.
    """
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"Consumer.{name}")
        self.logger.info(f"Initializing Horseshoe Consumer: {name}")
    
    @abstractmethod
    async def consume_stream(self, stream_name: str, message: Dict[str, Any]) -> None:
        """Process a message from a Redis stream."""
        pass
    
    async def send_command(self, command_stream: str, command_data: Dict[str, Any]) -> Optional[str]:
        """Send a command to producers via command stream."""
        # Commands are the only way consumers can trigger producer actions
        pass


class HorseshoeProducer(ABC):
    """
    Base class for all components on the producer (right) side of the horseshoe.
    Producers ONLY WRITE to Redis streams and respond to commands.
    """
    
    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"Producer.{name}")
        self.logger.info(f"Initializing Horseshoe Producer: {name}")
    
    @abstractmethod
    async def produce_event(self, stream_name: str, event_data: Dict[str, Any]) -> None:
        """Publish an event to a Redis stream."""
        pass
    
    async def handle_command(self, command: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Handle a command from consumers."""
        # Producers respond to commands by producing events
        pass


class HorseshoeValidator:
    """
    Validates that components follow the horseshoe architecture.
    Used during development and testing to catch violations.
    """
    
    @staticmethod
    def validate_consumer(component: HorseshoeConsumer) -> bool:
        """Ensure consumer only has read methods."""
        # Check that consumer doesn't have direct producer calls
        return True
    
    @staticmethod
    def validate_producer(component: HorseshoeProducer) -> bool:
        """Ensure producer only has write methods."""
        # Check that producer doesn't read from consumer streams
        return True
    
    @staticmethod
    def validate_stream_ownership(stream_name: str, owner_type: str) -> bool:
        """Ensure streams are owned by correct side of horseshoe."""
        consumer_prefixes = ['gui:', 'mcp:', 'intellisense:']
        producer_prefixes = ['testrade:', 'ocr:', 'broker:', 'risk:', 'order:']
        
        if owner_type == 'consumer':
            return any(stream_name.startswith(p) for p in consumer_prefixes)
        elif owner_type == 'producer':
            return any(stream_name.startswith(p) for p in producer_prefixes)
        return False