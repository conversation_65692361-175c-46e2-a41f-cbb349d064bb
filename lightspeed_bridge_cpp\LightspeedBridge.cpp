////////////////////////////////////////////////////////////////////////////////
// LightspeedBridge.cpp : Bridges ephemeral correlation, adds worker thread,
//                        handles LULD/Halt/Resume, specific rejection reasons,
//                        and formats EST timestamps.
//
// Build as a DLL. Place in Lightspeed's extension folder.
////////////////////////////////////////////////////////////////////////////////

#include "pch.h" // Use if using precompiled headers

// Standard & Windows Headers
#include <afxwin.h>
#include <windows.h> // For time zone conversion, SYSTEMTIME, FILETIME
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "Ws2_32.lib")

#include <string>
#include <vector>
#include <deque>
#include <list>
#include <unordered_map>
#include <mutex>
#include <thread>
#include <atomic>
#include <condition_variable>
#include <chrono>
#include <cmath>
#include <cstring> // For memcpy/sprintf_s
#include <fstream>
#include <algorithm>
#include <stdexcept> // For error handling if needed
#include <memory>    // For unique_ptr/shared_ptr if needed (used in GetEstTimeZoneInfoForYear helper implicitly)
#include <limits>    // For numeric limits if checking error values
#include <ctime>     // For time_t
#include <sstream>   // For string formatting / thread IDs

// Lightspeed Trader API Headers
#include <L_Observer.h>
#include <L_Messages.h>
#include <L_Order.h>
#include <L_Account.h>
#include <L_Execution.h>
#include <L_Position.h>
#include <L_Application.h>
#include <L_OrderErrors.h> // Crucial for L_OrderResult codes and potentially L_OrderError struct
#include <L_Summary.h>     // May be implicitly used by L_Account or explicitly for LULD


// JSON Library (ensure nlohmann/json.hpp is in your include path)
#include "json.hpp"
using json = nlohmann::json;

#ifndef LSEXPORT
#define LSEXPORT __declspec(dllexport)
#endif

using namespace LightspeedTrader;

//--------------------------------------------------------------------------
// Log Levels and Configuration
//--------------------------------------------------------------------------
enum class LogLevel { LL_ERROR, LL_WARNING, LL_INFO, LL_DEBUG };
static std::atomic<LogLevel> g_minLogLevel = LogLevel::LL_DEBUG;
static const bool g_flushLogsFrequently = false;

//--------------------------------------------------------------------------
// Forward Declarations
//--------------------------------------------------------------------------
static json HandleInboundCommand(const json& jreq, SOCKET client);
static json HandlePlaceOrder(const json& jreq);
static json HandleCancelOrder(const json& jreq);
static json HandleGetAccountData(const json& jreq);
static json HandleGetPositionsDetailed(const json& jreq);
static json HandleGetOrdersDetailed(const json& jreq);
static json HandleGetPosition(const json& jreq);
static json HandleGetAllPositions(const json& jreq);

static void BroadcastAllPositions();
static void BroadcastJson(const json& msg);
// static void SendCrossOwnError(long ls_order_id, const std::string& reason); // Commented out as likely redundant

// Logging Functions (Enhanced)
static std::string GetLocalTimePrefix();
static void LogMessage(LogLevel level, const std::string& message, std::ofstream& fileStream, const char* filePrefix = "");
static void DebugPrint(LogLevel level, const std::string& s);
static void EphemeralLog(LogLevel level, const std::string& s);
static void LogReply(SOCKET clientSock, const std::string& msg);

// String Building Helper Macro (to avoid C2110 string concatenation errors)
#define BUILD_STRING(stream_ops) \
    [&]() { \
        std::stringstream ss; \
        ss stream_ops; \
        return ss.str(); \
    }()

// Alternative macro name for consistency with generated code
#define BUILD_STRING_OIM(stream_ops) BUILD_STRING(stream_ops)

// Time Zone Helper
static bool GetEstTimeZoneInfoForYear(WORD year, TIME_ZONE_INFORMATION& tzi);

// Networking Helpers
static void SetNonBlocking(SOCKET s);
static bool SendAllNonBlocking(SOCKET s, const char* buf, int len);

// Worker Thread Function
static void InboundWorkerThread();


//--------------------------------------------------------------------------
// Global Data & State
//--------------------------------------------------------------------------
static L_Account* g_account = nullptr;
class MyAccountObserver;
static MyAccountObserver* g_observerInstance = nullptr;

// Old map for ephemeral to local Python ID (used by old g_orderQueues bridging logic)
// static std::unordered_map<long, int>  g_ephemeralToLocal; // Replaced by g_cppEphemeralToPythonLocalId

// OrderKey struct (used only as a value in g_pendingOrderKeys for rejection context)
struct OrderKey {
    std::string symbol;
    char side;
    long quantity;
    double limitPrice;

    // operator== can be removed if OrderKey is only a value in g_pendingOrderKeys
    // and not compared directly elsewhere.
    /*
    bool operator==(const OrderKey& o) const {
        return symbol == o.symbol && side == o.side && quantity == o.quantity &&
            std::fabs(limitPrice - o.limitPrice) < 1e-9;
    }
    */
};

// static std::unordered_map<OrderKey, std::list<long>, OrderKeyHash> g_orderQueues; // Removed

// This map is kept for retrieving order details (symbol, side, qty, price) if L_MsgOrderRequested results in an immediate rejection.
// It helps provide more context in the rejection message back to Python.
// Key: cpp_ephemeral_corr (long, C++ generated)
// Value: OrderKey struct (contains symbol, side, quantity, limitPrice)
static std::unordered_map<long, OrderKey> g_pendingOrderKeys;


// --- New C++ Data Structures from lightspeed_upgrade.txt ---
// Map 1: Links C++ ephemeral ID (used for L_SendOrderBasic call) to the Python local_copy_id.
// Key: cpp_ephemeral_corr (long, C++ generated)
// Value: python_local_copy_id (int)
static std::unordered_map<long, int> g_cppEphemeralToPythonLocalId;

// Map for correlation_id tracking (mirroring local_copy_id pattern)
// Key: cpp_ephemeral_corr (long, C++ generated)
// Value: correlation_id (string, from Python)
static std::unordered_map<long, std::string> g_cppEphemeralToCorrelationId;

// Map 2: Links Lightspeed's initial L_Order1ReferenceId to C++ ephemeralCorrForThisCall.
// Key: lsOrder1RefId (long, from L_MsgOrderRequested)
// Value: cpp_ephemeral_corr (long, C++ generated)
static std::unordered_map<long, long> g_lsRefIdToCppEphemeral;

// Map 3: Links Lightspeed's canonical L_Order::L_OrderId() back to C++ ephemeralCorrForThisCall.
// Key: lsOrderId_canonical (long, from L_MsgOrderChange/L_Order object)
// Value: cpp_ephemeral_corr (long, C++ generated)
static std::unordered_map<long, long> g_lsCanonicalOrderIdToCppEphemeral;

static std::mutex g_mapMutex;
static std::atomic<long> g_ephemeralGen{ 1 };
// static std::atomic<int>  g_autoLocalId(10000); // Not used if local_copy_id comes from client

static std::atomic<bool> g_running(true);
static SOCKET g_listenSock = INVALID_SOCKET;
static std::thread g_listenThread;
static std::mutex g_clientsMutex;
static std::vector<SOCKET> g_clients;

struct InboundCommand { std::string rawLine; SOCKET client; };
static std::deque<InboundCommand> g_highPriorityQueue;
static std::mutex g_highPriMutex;
static std::deque<InboundCommand> g_lowPriorityQueue;
static std::mutex g_lowPriMutex;
static std::condition_variable g_inboundCV;
static std::mutex g_inboundCVMutex;
static std::thread g_inboundWorker;
static std::atomic<bool> g_inboundWorkerRunning{ false };
static std::chrono::steady_clock::time_point g_lastMapLogTime;

static std::mutex g_logMutex;
static std::ofstream g_debugFile("C:\\testrade\\logs\\LightspeedDebug.log", std::ios::app);
static std::ofstream g_replyFile("C:\\testrade\\logs\\LightspeedReplies.txt", std::ios::app);
static std::ofstream g_ephemeralFile("C:\\testrade\\logs\\EphemeralDebug.log", std::ios::app);

static const std::unordered_map<long, std::string> g_orderResultReasons = {
    {L_OrderResult::ORDER_SENT_OK,                  "Order Sent OK"},
    {L_OrderResult::SOES_ORDER_DISABLED,            "SOES Order Disabled"},
    {L_OrderResult::MM_IN_EXCLUSION_LIST,           "MM In Exclusion List"},
    {L_OrderResult::ZERO_SHARES_ORDERED,            "Zero Shares Ordered"},
    {L_OrderResult::EXECUTIONS_DISABLED,            "Executions Disabled"},
    {L_OrderResult::BUYING_POWER_EXCEEDED,          "Buying Power Exceeded"},
    {L_OrderResult::SHORT_SELL_VIOLATION,           "Short Sell Violation"},
    {L_OrderResult::STOCK_NOT_SHORTABLE,            "Stock Not Shortable"},
    {L_OrderResult::EXECUTOR_NOT_CONNECTED,         "Executor Not Connected"},
    {L_OrderResult::MAXORDERSHARES_EXCEEDED,        "Max Order Shares Exceeded"},
    {L_OrderResult::WAIT_CONSTRAINT_VIOLATION,      "Wait Constraint Violation"},
    {L_OrderResult::STOCK_HALTED,                   "Stock Halted"},
    {L_OrderResult::MKXT_BOOK_OR_KILL,              "Market Maker Book Or Kill"},
    {L_OrderResult::SMALL_CAPS_NOT_SOESABLE,        "Small Caps Not SOESable"},
    {L_OrderResult::OWN_CROSSING,                   "Own Crossing"},
    {L_OrderResult::CANNOT_TRADE_SYMBOL,            "Cannot Trade Symbol"},
    {L_OrderResult::MARKET_HALTED,                  "Market Halted"},
    {L_OrderResult::FUTURES_MARGINABILITY_UNKNOWN,  "Futures Marginability Unknown"},
    {L_OrderResult::TRADINGMONITOR_BLOCKED_ORDER,   "Trading Monitor Blocked Order"},
    {L_OrderResult::DECLINED_AT_CONFIRM_BY_USER,    "Declined At Confirm By User"},
    {L_OrderResult::ROUTING_BLOCKED_ORDER,          "Routing Blocked Order"},
    {L_OrderResult::OTHER_REJECTION,                "Other Rejection"},
    {L_OrderResult::EXECUTOR_NOT_LOGGED_IN,         "Executor Not Logged In"},
    {L_OrderResult::UNINITIALIZED_SUMMARY,          "Uninitialized Summary"},
    {L_OrderResult::INVALID_SUMMARY,                "Invalid Summary"},
    {L_OrderResult::INVALID_ORDER_TYPE,             "Invalid Order Type"},
    {L_OrderResult::DUPLICATE_ORDER_REJECT,         "Duplicate Order Reject"},
    {L_OrderResult::INSUFFICIENT_SHARES,            "Insufficient Shares"},
    {L_OrderResult::NO_SIMUL_OPEN_CLOSE,            "No Simul Open Close"}
};

static std::string GetOrderResultReason(long resultCode) {
    auto it = g_orderResultReasons.find(resultCode);
    if (it != g_orderResultReasons.end()) {
        return it->second;
    }
    DebugPrint(LogLevel::LL_WARNING, "Encountered unknown L_OrderResult code: " + std::to_string(resultCode));
    return "Unknown Rejection Code (" + std::to_string(resultCode) + ")";
}

//--------------------------------------------------------------------------
// Logging Implementation (Enhanced)
//--------------------------------------------------------------------------
static std::string GetLocalTimePrefix() {
    SYSTEMTIME st;
    GetLocalTime(&st);
    char timeBuf[64];
    sprintf_s(timeBuf, sizeof(timeBuf),
        "[%04d-%02d-%02d %02d:%02d:%02d.%03d]",
        st.wYear, st.wMonth, st.wDay,
        st.wHour, st.wMinute, st.wSecond, st.wMilliseconds);
    return std::string(timeBuf);
}

static void LogMessage(LogLevel level, const std::string& message, std::ofstream& fileStream, const char* filePrefix) {
    if (level >= g_minLogLevel.load(std::memory_order_relaxed)) {
        std::stringstream ss_tid;
        ss_tid << std::this_thread::get_id();
        std::string levelStr;
        switch (level) {
        case LogLevel::LL_ERROR:   levelStr = "[ERROR]  "; break;
        case LogLevel::LL_WARNING: levelStr = "[WARNING]"; break;
        case LogLevel::LL_INFO:    levelStr = "[INFO]   "; break;
        case LogLevel::LL_DEBUG:   levelStr = "[DEBUG]  "; break;
        default:                levelStr = "[UNKNOWN]"; break;
        }

        std::lock_guard<std::mutex> lock(g_logMutex);
        if (fileStream.is_open()) { // Check if file stream is open
            fileStream << GetLocalTimePrefix()
                << "[Thd:" << ss_tid.str() << "]"
                << levelStr
                << (filePrefix ? filePrefix : "")
                << message << std::endl;
            if (g_flushLogsFrequently) {
                fileStream.flush();
            }
        }
    }
}

static void DebugPrint(LogLevel level, const std::string& s) {
    LogMessage(level, s, g_debugFile, nullptr);
}

static void EphemeralLog(LogLevel level, const std::string& s) {
    LogMessage(level, s, g_ephemeralFile, nullptr);
}

static void LogReply(SOCKET clientSock, const std::string& msg) {
    std::lock_guard<std::mutex> lock(g_logMutex);
    if (g_replyFile.is_open()) { // Check if file stream is open
        g_replyFile << GetLocalTimePrefix()
            << "[ToSock:" << clientSock << "] "
            << msg; // msg from worker already includes newline
        if (g_flushLogsFrequently) {
            g_replyFile.flush();
        }
    }
}

//--------------------------------------------------------------------------
// Time Zone Helper Implementation
//--------------------------------------------------------------------------
// This is the version of GetEstTimeZoneInfoForYear from your originally pasted code.
bool GetEstTimeZoneInfoForYear(WORD year, TIME_ZONE_INFORMATION& tzi) {
    ZeroMemory(&tzi, sizeof(tzi));
    const wchar_t* tzKeyName = L"Eastern Standard Time"; // Standard key name
    const wchar_t* dynDstRegPath = L"SYSTEM\\CurrentControlSet\\Control\\TimeZoneInformation\\Dynamic DST";
    HKEY hKeyDynDst = nullptr;
    LONG result = RegOpenKeyExW(HKEY_LOCAL_MACHINE, dynDstRegPath, 0, KEY_READ, &hKeyDynDst);

    if (result == ERROR_SUCCESS) {
        DWORD dwYear = year;
        DWORD dwDataSize = sizeof(tzi);
        result = RegQueryValueExW(hKeyDynDst, std::to_wstring(dwYear).c_str(), nullptr, nullptr, reinterpret_cast<LPBYTE>(&tzi), &dwDataSize);
        RegCloseKey(hKeyDynDst);

        if (result == ERROR_SUCCESS && dwDataSize == sizeof(TIME_ZONE_INFORMATION)) {
            DebugPrint(LogLevel::LL_DEBUG, "[GetEstTimeZoneInfoForYear] Successfully read dynamic DST info for year " + std::to_string(year));
            return true;
        }
        else {
            DebugPrint(LogLevel::LL_WARNING, BUILD_STRING(<< "[GetEstTimeZoneInfoForYear] Failed to read/validate dynamic DST for year " << year << ". RegQueryValueExW Error: " << result << ". Falling back."));
        }
    }
    else {
        DebugPrint(LogLevel::LL_WARNING, "[GetEstTimeZoneInfoForYear] Failed to open Dynamic DST reg key. RegOpenKeyExW Error: " + std::to_string(result) + ". Falling back.");
    }

    DebugPrint(LogLevel::LL_DEBUG, "[GetEstTimeZoneInfoForYear] Falling back to GetTimeZoneInformation for year " + std::to_string(year));
    DWORD tzId = GetTimeZoneInformation(&tzi);
    if (tzId == TIME_ZONE_ID_INVALID) {
        DebugPrint(LogLevel::LL_ERROR, "[GetEstTimeZoneInfoForYear] GetTimeZoneInformation failed. Error: " + std::to_string(GetLastError()));
        return false;
    }
    return true;
}

//--------------------------------------------------------------------------
// Networking Helpers
//--------------------------------------------------------------------------
static void SetNonBlocking(SOCKET s) {
    u_long mode = 1;
    ioctlsocket(s, FIONBIO, &mode);
}

static bool SendAllNonBlocking(SOCKET s, const char* buf, int len) {
    const int kMaxRetries = 100;
    int totalSent = 0;
    int tries = 0;
    while (totalSent < len) {
        int ret = send(s, buf + totalSent, len - totalSent, 0);
        if (ret == SOCKET_ERROR) {
            int err = WSAGetLastError();
            if (err == WSAEWOULDBLOCK) {
                if (++tries > kMaxRetries) {
                    DebugPrint(LogLevel::LL_WARNING, "SendAllNonBlocking => too many WSAEWOULDBLOCK tries for socket " + std::to_string(s));
                    return false;
                }
                Sleep(10);
                continue;
            }
            else {
                DebugPrint(LogLevel::LL_ERROR, BUILD_STRING(<< "SendAllNonBlocking => socket error: " << err << " on socket " << s));
                return false;
            }
        }
        else if (ret == 0) {
            DebugPrint(LogLevel::LL_INFO, "SendAllNonBlocking => ret=0 => remote closed socket " + std::to_string(s));
            return false;
        }
        else {
            totalSent += ret;
            tries = 0;
        }
    }
    return true;
}

//--------------------------------------------------------------------------
// Inbound Worker Thread Implementation
//--------------------------------------------------------------------------
static void InboundWorkerThread() {
    DebugPrint(LogLevel::LL_INFO, "InboundWorker => Starting");
    while (g_inboundWorkerRunning.load(std::memory_order_relaxed))
    {
        InboundCommand cmdToProcess;
        bool haveWork = false;

        {
            std::lock_guard<std::mutex> lockHP(g_highPriMutex);
            if (!g_highPriorityQueue.empty()) {
                cmdToProcess = std::move(g_highPriorityQueue.front());
                g_highPriorityQueue.pop_front();
                haveWork = true;
            }
        }

        if (!haveWork) {
            std::lock_guard<std::mutex> lockLP(g_lowPriMutex);
            if (!g_lowPriorityQueue.empty()) {
                cmdToProcess = std::move(g_lowPriorityQueue.front());
                g_lowPriorityQueue.pop_front();
                haveWork = true;
            }
        }

        if (!haveWork) {
            // ---- START REVISED ADDITION FOR MAP LOGGING ----
            auto now_time = std::chrono::steady_clock::now();
            if (g_lastMapLogTime.time_since_epoch().count() == 0 || (now_time - g_lastMapLogTime > std::chrono::minutes(5)) ) { // Log every 5 minutes or if never logged
                std::stringstream map_log_ss;
                {
                    std::lock_guard<std::mutex> lock(g_mapMutex);
                    map_log_ss << "Map Sizes: cppEphToPyLocal=" << g_cppEphemeralToPythonLocalId.size()
                               << ", cppEphToCorrelation=" << g_cppEphemeralToCorrelationId.size()
                               << ", lsRefToCppEph=" << g_lsRefIdToCppEphemeral.size()
                               << ", lsCanonToCppEph=" << g_lsCanonicalOrderIdToCppEphemeral.size()
                               << ", pendingOrderKeys=" << g_pendingOrderKeys.size();
                }
                DebugPrint(LogLevel::LL_INFO, map_log_ss.str());
                g_lastMapLogTime = now_time;
            }
            // ---- END REVISED ADDITION FOR MAP LOGGING ----

            std::unique_lock<std::mutex> lockCV(g_inboundCVMutex);
            g_inboundCV.wait_for(lockCV, std::chrono::milliseconds(200), [&] {
                if (!g_inboundWorkerRunning.load(std::memory_order_relaxed)) return true;
                std::lock_guard<std::mutex> lockHP(g_highPriMutex);
                if (!g_highPriorityQueue.empty()) return true;
                std::lock_guard<std::mutex> lockLP(g_lowPriMutex);
                return !g_lowPriorityQueue.empty();
                });
            if (!g_inboundWorkerRunning.load(std::memory_order_relaxed)) break;
            continue;
        }

        DebugPrint(LogLevel::LL_INFO, "InboundWorker => Handling: " + cmdToProcess.rawLine + " from socket " + std::to_string(cmdToProcess.client));
        json jresp;
        long reqId = -1L;
        try {
            json jreq = json::parse(cmdToProcess.rawLine);
            reqId = jreq.value("req_id", -1L);
            jresp = HandleInboundCommand(jreq, cmdToProcess.client);
        }
        catch (json::parse_error& e) {
            DebugPrint(LogLevel::LL_ERROR, "InboundWorker => JSON parse error: " + std::string(e.what()) + " for line: " + cmdToProcess.rawLine);
            jresp = { {"ok", false}, {"error", "JSON parse error: " + std::string(e.what())} };
        }
        catch (const std::exception& e) {
            DebugPrint(LogLevel::LL_ERROR, "InboundWorker => Standard exception: " + std::string(e.what()) + " processing line: " + cmdToProcess.rawLine);
            jresp = { {"ok", false}, {"error", "Processing error: " + std::string(e.what())} };
        }
        catch (...) {
            DebugPrint(LogLevel::LL_ERROR, "InboundWorker => Unknown exception processing line: " + cmdToProcess.rawLine);
            jresp = { {"ok", false}, {"error", "Unknown processing error"} };
        }

        if (!jresp.empty()) {
            if (reqId != -1L && !jresp.contains("req_id")) {
                jresp["req_id"] = reqId;
            }
            std::string out_str;
            try {
                out_str = jresp.dump() + "\n";
            }
            catch (const json::type_error& e) {
                DebugPrint(LogLevel::LL_ERROR, "InboundWorker => JSON dump error for response: " + std::string(e.what()));
                json error_resp = { {"ok", false}, {"error", "Internal error generating response."} };
                if (reqId != -1L) error_resp["req_id"] = reqId;
                out_str = error_resp.dump() + "\n";
            }

            LogReply(cmdToProcess.client, out_str);
            if (!SendAllNonBlocking(cmdToProcess.client, out_str.c_str(), (int)out_str.length())) {
                // Error already logged by SendAllNonBlocking
            }
        }
    }
    DebugPrint(LogLevel::LL_INFO, "InboundWorker => Exiting gracefully.");
}

//--------------------------------------------------------------------------
// DLL Entry Points & Observer Implementation
//--------------------------------------------------------------------------
extern "C"
{
    class MyAccountObserver : public L_Observer {
    public:
        MyAccountObserver(L_Account* acct) : m_acct(acct) {
            if (m_acct) {
                m_acct->L_Attach(this);
                DebugPrint(LogLevel::LL_INFO, "MyAccountObserver => Attached to L_Account.");
            }
            else {
                DebugPrint(LogLevel::LL_ERROR, "MyAccountObserver => ERROR: L_Account is null on construction!");
            }
        }

        ~MyAccountObserver() {
            if (m_acct) {
                m_acct->L_Detach(this);
                DebugPrint(LogLevel::LL_INFO, "MyAccountObserver => Detached from L_Account.");
            }
            m_acct = nullptr;
        }

        MyAccountObserver(const MyAccountObserver&) = delete;
        MyAccountObserver& operator=(const MyAccountObserver&) = delete;
        MyAccountObserver(MyAccountObserver&&) = delete;
        MyAccountObserver& operator=(MyAccountObserver&&) = delete;

        void HandleMessage(const L_Message* msg) override {
            AFX_MANAGE_STATE(AfxGetStaticModuleState());

            if (!m_acct) {
                DebugPrint(LogLevel::LL_ERROR, "MyAccountObserver::HandleMessage => ERROR: m_acct is null!");
                return;
            }
            if (!msg) {
                DebugPrint(LogLevel::LL_WARNING, "MyAccountObserver::HandleMessage => Received null message pointer.");
                return;
            }
            DebugPrint(LogLevel::LL_DEBUG, "HandleMessage => Received Msg Type: " + std::to_string(msg->L_Type()));

            switch (msg->L_Type()) {
            case L_MsgOrderRequested::id:
            {
                auto req = static_cast<const L_MsgOrderRequested*>(msg);
                long ephemeralCorrFromLS_Actually = req->L_CorrelationId(); // This should now be the value LS assigned (e.g., 9)
                long resultCode = req->L_Result();
                long lsOrder1RefId = req->L_Order1ReferenceId();
                std::string reasonString = GetOrderResultReason(resultCode);

                EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "=== L_MSG_ORDER_REQUESTED ===")); // Changed to LL_DEBUG
                EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "L_MsgOrderRequested: Received for ActualCppEphemeralCorrFromLS=" << ephemeralCorrFromLS_Actually // Log the actual value
                                                                << ", LS_Order1ReferenceId=" << lsOrder1RefId
                                                                << ", L_Result=" << resultCode << " (" << reasonString << ")"));

                int python_local_id = -1;
                std::string correlation_id = "";
                OrderKey rejectedKey;
                bool keyDetailsFound = false;

                {
                    std::lock_guard<std::mutex> lock(g_mapMutex);
                    // Now use ephemeralCorrFromLS_Actually to find python_local_id
                    auto itPyLocal = g_cppEphemeralToPythonLocalId.find(ephemeralCorrFromLS_Actually);
                    if (itPyLocal != g_cppEphemeralToPythonLocalId.end()) {
                        python_local_id = itPyLocal->second;
                        EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "L_MsgOrderRequested: Found PythonLocalId=" << python_local_id << " for CppEph=" << ephemeralCorrFromLS_Actually));
                    } else {
                        EphemeralLog(LogLevel::LL_ERROR, "L_MsgOrderRequested: CRITICAL - Could not find python_local_id for CppEph=" + std::to_string(ephemeralCorrFromLS_Actually) + ". This breaks primary link!");
                    }
                    
                    // Find correlation_id (mirroring local_copy_id pattern)
                    auto itCorr = g_cppEphemeralToCorrelationId.find(ephemeralCorrFromLS_Actually);
                    if (itCorr != g_cppEphemeralToCorrelationId.end()) {
                        correlation_id = itCorr->second;
                        EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "L_MsgOrderRequested: Found correlation_id=" << correlation_id << " for CppEph=" << ephemeralCorrFromLS_Actually));
                    } else {
                        EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "L_MsgOrderRequested: No correlation_id found for CppEph=" << ephemeralCorrFromLS_Actually));
                    }

                    auto itKey = g_pendingOrderKeys.find(ephemeralCorrFromLS_Actually);
                    if (itKey != g_pendingOrderKeys.end()) {
                        rejectedKey = itKey->second;
                        keyDetailsFound = true;
                    }
                }

                if (resultCode != L_OrderResult::ORDER_SENT_OK) {
                    json rejectionJson = {
                        {"event", "order_status"},
                        {"ls_order_id", -1L},
                        {"ephemeral_corr", ephemeralCorrFromLS_Actually},
                        {"local_copy_id", python_local_id},
                        {"status", "Rejected"},
                        {"reason", reasonString}
                    };
                    if (keyDetailsFound) {
                        rejectionJson["symbol"] = rejectedKey.symbol;
                        rejectionJson["side"] = (rejectedKey.side == L_Side::BUY) ? "BUY" : "SELL"; // Ensure L_Side::BUY is char
                        rejectionJson["requested_shares"] = rejectedKey.quantity;
                    }
                    // Add correlation_id if available (mirroring local_copy_id pattern)
                    if (!correlation_id.empty()) {
                        rejectionJson["correlation_id"] = correlation_id;
                    }
                    EphemeralLog(LogLevel::LL_ERROR, BUILD_STRING_OIM(<< "L_MsgOrderRequested: REJECTED. PythonLocalId=" << python_local_id // Changed to LL_ERROR for rejections
                                                                    << ", CppEph=" << ephemeralCorrFromLS_Actually << ", Reason=" << reasonString));
                    BroadcastJson(rejectionJson);

                    if (ephemeralCorrFromLS_Actually > 0) {
                        std::lock_guard<std::mutex> lock(g_mapMutex);
                        g_cppEphemeralToPythonLocalId.erase(ephemeralCorrFromLS_Actually);
                        g_cppEphemeralToCorrelationId.erase(ephemeralCorrFromLS_Actually); // Clean correlation_id map too
                        g_pendingOrderKeys.erase(ephemeralCorrFromLS_Actually);
                        EphemeralLog(LogLevel::LL_DEBUG, "L_MsgOrderRequested: REJECTED. Cleaned maps for CppEph=" + std::to_string(ephemeralCorrFromLS_Actually));
                    }
                } else { // ORDER_SENT_OK
                    if (ephemeralCorrFromLS_Actually > 0 && lsOrder1RefId > 0) {
                        std::lock_guard<std::mutex> lock(g_mapMutex);
                        g_lsRefIdToCppEphemeral[lsOrder1RefId] = ephemeralCorrFromLS_Actually; // Link LS_Ref_ID to the ID LS uses
                        g_pendingOrderKeys.erase(ephemeralCorrFromLS_Actually);
                        EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "L_MsgOrderRequested: ORDER_SENT_OK. Stored map g_lsRefIdToCppEphemeral[" << lsOrder1RefId
                                                                    << "] = " << ephemeralCorrFromLS_Actually
                                                                    << " (for PythonLocalId=" << python_local_id << "). g_pendingOrderKeys cleaned."));
                    } else {
                        EphemeralLog(LogLevel::LL_ERROR, BUILD_STRING_OIM(<< "L_MsgOrderRequested: ORDER_SENT_OK but lsOrder1RefId ("<<lsOrder1RefId<<") or CppEph ("<<ephemeralCorrFromLS_Actually
                                                                    <<") invalid. Link missed. PythonLocalId was " << python_local_id));
                         if(lsOrder1RefId <= 0 && ephemeralCorrFromLS_Actually > 0) {
                             std::lock_guard<std::mutex> lock(g_mapMutex);
                             g_pendingOrderKeys.erase(ephemeralCorrFromLS_Actually);
                             EphemeralLog(LogLevel::LL_WARNING, "L_MsgOrderRequested: ORDER_SENT_OK but bad lsOrder1RefId. Cleaned g_pendingOrderKeys for CppEph=" + std::to_string(ephemeralCorrFromLS_Actually));
                         }
                    }
                }
            }
            break;

            case L_MsgOrderChange::id:
            {
                auto oc = static_cast<const L_MsgOrderChange*>(msg);
                if (!oc) {
                    DebugPrint(LogLevel::LL_ERROR, "HandleMessage => L_MsgOrderChange cast failed.");
                    EphemeralLog(LogLevel::LL_ERROR, "L_MsgOrderChange: Cast to L_MsgOrderChange failed!"); // Use LogLevel
                    return;
                }

                long lsCanonicalId = oc->L_OrderId(); // Renamed for clarity from lsOrderId_canonical
                long category = oc->L_Category();
                auto CategoryToString = [](long c)->std::string {
                    switch (c) {
                    case L_OrderChange::Create:          return "Submitted"; // Lightspeed's "Create" often means "Submitted to exchange"
                    case L_OrderChange::Receive:         return "Accepted";  // Lightspeed's "Receive" often means "Accepted by exchange"
                    case L_OrderChange::Exec:            return "PartiallyFilled"; // Base status, refined below
                    case L_OrderChange::CancelCreate:    return "CancelPending";
                    case L_OrderChange::CancelRejection: return "CancelRejected";
                    case L_OrderChange::Cancel:          return "Cancelled";
                    case L_OrderChange::Rejection:       return "Rejected";
                    case L_OrderChange::Kill:            return "Killed";
                    default:                             return "Unknown(" + std::to_string(c) + ")";
                    }
                };
                std::string status_str = CategoryToString(category); // Use a different name from 'status' in json

                std::stringstream eph_debug_stream;
                eph_debug_stream << "=== L_MSG_ORDER_CHANGE START === LSCanonicalId=" << lsCanonicalId
                                 << ", Category=" << category << "(" << status_str << ")" // Use status_str
                                 << ", L_MsgOrderChange::L_ReferenceId()=" << oc->L_ReferenceId() << ". ";

                L_Order* od = m_acct->L_FindOrderByOrderId(lsCanonicalId);
                long lsRefId_from_LOrder_object = 0;
                if (od) {
                    lsRefId_from_LOrder_object = od->L_ReferenceId();
                    eph_debug_stream << "L_Order object FOUND. L_Order::L_ReferenceId()=" << lsRefId_from_LOrder_object << ". ";
                } else {
                    eph_debug_stream << "L_Order object NOT FOUND for LSCanonicalId=" << lsCanonicalId << ". ";
                }

                int python_local_id = -1;
                std::string correlation_id = "";
                long original_cpp_ephemeral = -1L;
                long ref_id_that_led_to_link = 0;

                {
                    std::lock_guard<std::mutex> lock(g_mapMutex);

                    auto it_canon_to_eph = g_lsCanonicalOrderIdToCppEphemeral.find(lsCanonicalId);
                    if (it_canon_to_eph != g_lsCanonicalOrderIdToCppEphemeral.end()) {
                        original_cpp_ephemeral = it_canon_to_eph->second;
                        eph_debug_stream << "PathA_SUCCESS: Found CppEph=" << original_cpp_ephemeral << ". ";
                    } else {
                        eph_debug_stream << "PathA_FAILED. ";
                        if (od && lsRefId_from_LOrder_object > 0) {
                            auto it_ref_obj_to_eph = g_lsRefIdToCppEphemeral.find(lsRefId_from_LOrder_object);
                            if (it_ref_obj_to_eph != g_lsRefIdToCppEphemeral.end()) {
                                original_cpp_ephemeral = it_ref_obj_to_eph->second;
                                g_lsCanonicalOrderIdToCppEphemeral[lsCanonicalId] = original_cpp_ephemeral;
                                ref_id_that_led_to_link = lsRefId_from_LOrder_object;
                                eph_debug_stream << "PathB_SUCCESS: L_Order::L_RefId=" << lsRefId_from_LOrder_object
                                                 << " -> CppEph=" << original_cpp_ephemeral
                                                 << ". Linked LSCanonicalId=" << lsCanonicalId << ". ";
                            } else {
                                eph_debug_stream << "PathB_FAILED: L_Order::L_RefId=" << lsRefId_from_LOrder_object
                                                 << " not in g_lsRefIdToCppEphemeral (size=" << g_lsRefIdToCppEphemeral.size() << "). ";
                            }
                        } else {
                             eph_debug_stream << "PathB_SKIPPED (no 'od' or invalid lsRefId_from_LOrder_object). ";
                        }

                        if (original_cpp_ephemeral == -1L && oc->L_ReferenceId() > 0) {
                            auto it_msg_ref_to_eph = g_lsRefIdToCppEphemeral.find(oc->L_ReferenceId());
                            if (it_msg_ref_to_eph != g_lsRefIdToCppEphemeral.end()) {
                                original_cpp_ephemeral = it_msg_ref_to_eph->second;
                                g_lsCanonicalOrderIdToCppEphemeral[lsCanonicalId] = original_cpp_ephemeral;
                                ref_id_that_led_to_link = oc->L_ReferenceId();
                                eph_debug_stream << "PathC_SUCCESS: L_MsgOrderChange::L_RefId=" << oc->L_ReferenceId()
                                                 << " -> CppEph=" << original_cpp_ephemeral
                                                 << ". Linked LSCanonicalId=" << lsCanonicalId << ". ";
                            } else {
                                eph_debug_stream << "PathC_FAILED: L_MsgOrderChange::L_RefId=" << oc->L_ReferenceId()
                                                 << " not in g_lsRefIdToCppEphemeral (size=" << g_lsRefIdToCppEphemeral.size() << "). ";
                            }
                        } else if (original_cpp_ephemeral == -1L) {
                            eph_debug_stream << "PathC_SKIPPED (L_MsgOrderChange::L_RefId not > 0). ";
                        }
                    }

                    if (original_cpp_ephemeral != -1L) {
                        auto it_eph_to_py = g_cppEphemeralToPythonLocalId.find(original_cpp_ephemeral);
                        if (it_eph_to_py != g_cppEphemeralToPythonLocalId.end()) {
                            python_local_id = it_eph_to_py->second;
                            eph_debug_stream << "PythonLookup_SUCCESS: PyLocalId=" << python_local_id << " from CppEph=" << original_cpp_ephemeral << ". ";
                        } else {
                            eph_debug_stream << "PythonLookup_FAILED_CRITICAL: CppEph=" << original_cpp_ephemeral
                                         << " NOT IN g_cppEphemeralToPythonLocalId (size="
                                         << g_cppEphemeralToPythonLocalId.size() << ")! PyLocalId will be -1. ";
                        }
                        
                        // Lookup correlation_id (mirroring local_copy_id pattern)
                        auto it_eph_to_corr = g_cppEphemeralToCorrelationId.find(original_cpp_ephemeral);
                        if (it_eph_to_corr != g_cppEphemeralToCorrelationId.end()) {
                            correlation_id = it_eph_to_corr->second;
                            eph_debug_stream << "CorrelationLookup_SUCCESS: correlation_id=" << correlation_id << " from CppEph=" << original_cpp_ephemeral << ". ";
                        } else {
                            eph_debug_stream << "CorrelationLookup_FAILED: CppEph=" << original_cpp_ephemeral << " NOT IN g_cppEphemeralToCorrelationId. ";
                        }
                    } else {
                        eph_debug_stream << "PythonLookup_SKIPPED: original_cpp_ephemeral is -1. PyLocalId will be -1. ";
                    }
                }

                EphemeralLog(LogLevel::LL_DEBUG, eph_debug_stream.str()); // Use LL_DEBUG or LL_INFO for this trace normally

                std::string sym_json_val = (od && od->L_Symbol()) ? od->L_Symbol() : ""; // Moved declaration up
                char sideChar_json_val = (od ? od->L_TradeSide() : ' '); // Moved declaration up
                std::string sideStr_json_val = (sideChar_json_val == L_Side::BUY) ? "BUY" : ((sideChar_json_val == L_Side::SELL) ? "SELL" : "UNKNOWN"); // Moved declaration up
                long originalShares_json_val = (od ? od->L_OriginalShares() : 0); // Moved declaration up
                long executedShares_json_val = (od ? od->L_ExecutedShares() : 0); // Moved declaration up

                json jresp = {
                    {"event",            "order_status"},
                    {"event_type",       "order_status"},
                    {"ls_order_id",      lsCanonicalId},
                    {"ephemeral_corr",   original_cpp_ephemeral},
                    {"local_copy_id",    python_local_id},
                    {"status",           status_str}, // Use status_str from CategoryToString
                    {"symbol",           sym_json_val},
                    {"side",             sideStr_json_val},
                    {"requested_shares", originalShares_json_val}
                };
                
                // Add correlation_id if available (mirroring local_copy_id pattern)
                if (!correlation_id.empty()) {
                    jresp["correlation_id"] = correlation_id;
                }

                // ---- IMPORTANT: Refine status for Fills BEFORE using it in isTerminalOrderState ----
                if (category == L_OrderChange::Exec) {
                    const L_Execution* ex = oc->L_Exec();
                    if (ex) {
                        if (executedShares_json_val == originalShares_json_val && originalShares_json_val > 0) {
                            status_str = "Filled";
                        } else if (executedShares_json_val > 0) {
                            status_str = "PartiallyFilled";
                        }
                        jresp["status"] = status_str; // Update status in JSON

                        jresp["fill_shares"] = std::labs(ex->L_Shares());
                        jresp["fill_price"] = ex->L_AveragePrice();
                        jresp["liquidity"] = std::string(1, ex->L_Liquidity());

                        std::string execSymbolForRawUpdate;
                        long execTimeMillisForRawUpdate = 0;
                        time_t execTimeSecUTCForRawUpdate = 0;

                        if (od && od->L_Symbol()) {
                            execSymbolForRawUpdate = od->L_Symbol();
                        }
                        execTimeMillisForRawUpdate = ex->L_ExecTimeMillis();
                        execTimeSecUTCForRawUpdate = ex->L_ExecTime();

                        // ============================================================================
                        // EST TIMESTAMP FORMATTING (Existing logic - untouched)
                        // ============================================================================
                        long fillMsSinceMidnightEST = ex->L_ExecTimeMillis();
                        time_t execTimeSecUTC = ex->L_ExecTime();
                        std::string fill_time_est_str = "--:--:--.---";
                        if (execTimeSecUTC > 0 && fillMsSinceMidnightEST >= 0) {
                            try {
                                // ... (existing time conversion logic) ...
                                ULARGE_INTEGER uliUTC;
                                uliUTC.QuadPart = ((ULONGLONG)execTimeSecUTC * 10000000ULL) + 116444736000000000ULL;
                                FILETIME ftUTC;
                                ftUTC.dwLowDateTime = uliUTC.LowPart;
                                ftUTC.dwHighDateTime = uliUTC.HighPart;

                                SYSTEMTIME stUTC;
                                if (!FileTimeToSystemTime(&ftUTC, &stUTC)) {
                                    throw std::runtime_error("FileTimeToSystemTime failed for initial UTC conversion");
                                }

                                TIME_ZONE_INFORMATION tziEST;
                                if (!GetEstTimeZoneInfoForYear(stUTC.wYear, tziEST)) {
                                    throw std::runtime_error("GetEstTimeZoneInfoForYear failed for year " + std::to_string(stUTC.wYear));
                                }

                                SYSTEMTIME stExecEST;
                                if (!SystemTimeToTzSpecificLocalTime(&tziEST, &stUTC, &stExecEST)) {
                                    throw std::runtime_error("SystemTimeToTzSpecificLocalTime (Exec day EST) failed. Error: " + std::to_string(GetLastError()));
                                }

                                SYSTEMTIME stMidnightEST = stExecEST;
                                stMidnightEST.wHour = 0; stMidnightEST.wMinute = 0; stMidnightEST.wSecond = 0; stMidnightEST.wMilliseconds = 0;

                                SYSTEMTIME stMidnightUTC;
                                if (!TzSpecificLocalTimeToSystemTime(&tziEST, &stMidnightEST, &stMidnightUTC)) {
                                    DebugPrint(LogLevel::LL_ERROR, BUILD_STRING(<< "[TimeFormat] TzSpecificLocalTimeToSystemTime (Midnight EST to UTC) failed. Error: " << GetLastError() << ". Symbol: " << sym_json_val));
                                    throw std::runtime_error("TzSpecificLocalTimeToSystemTime (Midnight EST to UTC) failed.");
                                }

                                FILETIME ftMidnightUTC;
                                if (!SystemTimeToFileTime(&stMidnightUTC, &ftMidnightUTC)) {
                                    throw std::runtime_error("SystemTimeToFileTime (Midnight UTC) failed. Error: " + std::to_string(GetLastError()));
                                }

                                ULARGE_INTEGER uliMidnightUTC_val;
                                uliMidnightUTC_val.LowPart = ftMidnightUTC.dwLowDateTime; uliMidnightUTC_val.HighPart = ftMidnightUTC.dwHighDateTime;
                                ULARGE_INTEGER uliFinalExecUTC;
                                uliFinalExecUTC.QuadPart = uliMidnightUTC_val.QuadPart + ((ULONGLONG)fillMsSinceMidnightEST * 10000ULL);

                                FILETIME ftFinalExecUTC;
                                ftFinalExecUTC.dwLowDateTime = uliFinalExecUTC.LowPart; ftFinalExecUTC.dwHighDateTime = uliFinalExecUTC.HighPart;
                                SYSTEMTIME stFinalExecUTC;
                                if (!FileTimeToSystemTime(&ftFinalExecUTC, &stFinalExecUTC)) {
                                    throw std::runtime_error("FileTimeToSystemTime (Final UTC) failed. Error: " + std::to_string(GetLastError()));
                                }
                                SYSTEMTIME stFinalExecEST;
                                if (!SystemTimeToTzSpecificLocalTime(&tziEST, &stFinalExecUTC, &stFinalExecEST)) {
                                    throw std::runtime_error("SystemTimeToTzSpecificLocalTime (Final EST) failed. Error: " + std::to_string(GetLastError()));
                                }
                                char buffer[64];
                                sprintf_s(buffer, sizeof(buffer), "%02d:%02d:%02d.%03d",
                                    stFinalExecEST.wHour, stFinalExecEST.wMinute,
                                    stFinalExecEST.wSecond, stFinalExecEST.wMilliseconds);
                                fill_time_est_str = buffer;

                            } catch (const std::exception& e) {
                                DebugPrint(LogLevel::LL_ERROR, "[TimeFormatError] Exception during EST conversion: " + std::string(e.what()) + ". Symbol: " + sym_json_val);
                                fill_time_est_str = "ERR_CONV";
                            } catch (...) {
                                DebugPrint(LogLevel::LL_ERROR, "[TimeFormatError] Unknown exception during EST conversion. Symbol: " + sym_json_val);
                                fill_time_est_str = "ERR_UNK";
                            }
                        }
                        // ============================================================================
                        // END OF EST TIMESTAMP FORMATTING (Original Logic)
                        // ============================================================================
                        jresp["fill_time_est"] = fill_time_est_str;
                        jresp["broker_exec_time_utc_sec"] = execTimeSecUTCForRawUpdate;
                        jresp["broker_exec_time_ms_est_midnight"] = execTimeMillisForRawUpdate;
                        // ... (add other fill details to jresp as before) ...
                    }
                } else if (category == L_OrderChange::Rejection) {
                    jresp["reason"] = "Rejected (Post-Submission)"; // Example, refine as needed
                    // status_str is already "Rejected"
                } // ... other status refinements if needed ...
                // ---- END OF STATUS REFINEMENT ----


                EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "L_MsgOrderChange: FINAL JSON to Python: " << jresp.dump())); // Use LL_DEBUG
                BroadcastJson(jresp);

                // Corrected redefinition: 'isTerminalOrderState' was defined twice in my previous snippet.
                // Now, use the refined 'status_str' for this check.
                bool currentOrderIsTerminal = (status_str == "Filled" || status_str == "Killed" ||
                                             status_str == "Cancelled" || status_str == "Rejected");

                if (currentOrderIsTerminal && lsCanonicalId > 0) { // Use currentOrderIsTerminal
                    std::lock_guard<std::mutex> lock(g_mapMutex);
                    EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "L_MsgOrderChange: Terminal State Cleanup for LSCanonicalId=" << lsCanonicalId // Use LL_DEBUG
                                                                     << ", linked CppEph=" << original_cpp_ephemeral
                                                                     << ", linked LSRefId=" << ref_id_that_led_to_link));
                    if (original_cpp_ephemeral != -1L) {
                        g_cppEphemeralToPythonLocalId.erase(original_cpp_ephemeral);
                        g_cppEphemeralToCorrelationId.erase(original_cpp_ephemeral); // Clean correlation_id map too
                    }
                    if (ref_id_that_led_to_link > 0) {
                        g_lsRefIdToCppEphemeral.erase(ref_id_that_led_to_link);
                    }
                    g_lsCanonicalOrderIdToCppEphemeral.erase(lsCanonicalId);
                    EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "L_MsgOrderChange: Terminal cleanup complete.")); // Use LL_DEBUG
                }
                EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "=== L_MSG_ORDER_CHANGE END ===")); // Use LL_DEBUG

                if (category == L_OrderChange::Exec) {
                    // BroadcastAllPositions(); and raw_share_update
                    DebugPrint(LogLevel::LL_INFO, "HandleMessage(L_MsgOrderChange) => Execution occurred for lsCanonicalId=" + std::to_string(lsCanonicalId) +
                        ". Triggering BroadcastAllPositions.");
                    BroadcastAllPositions();

                    if (!sym_json_val.empty() && g_account) {
                        L_Position* pos = g_account->L_FindPosition(sym_json_val.c_str());
                        if (pos) {
                            long newTotalShares = pos->L_Shares();
                            json rawShareUpdateMsg = {
                                {"event", "raw_share_update"},
                                {"symbol", sym_json_val},
                                {"new_total_shares_at_broker", newTotalShares}
                            };
                            DebugPrint(LogLevel::LL_INFO, "[BROADCASTING RAW SHARE UPDATE] " + rawShareUpdateMsg.dump());
                            BroadcastJson(rawShareUpdateMsg);
                        } else {
                            DebugPrint(LogLevel::LL_WARNING, "HandleMessage(L_MsgOrderChange) => Could not find position for symbol '" + sym_json_val + "' after execution to send raw_share_update.");
                        }
                    }
                }
            }
            break;

            case L_MsgAccountChange::id: {
                DebugPrint(LogLevel::LL_INFO, "HandleMessage => Received L_MsgAccountChange");
                json jr_acct_summary = { // Changed variable name to avoid conflict if any
                    {"event", "account_update"}, {"event_type", "account_update"},
                    {"closed_pl", m_acct->L_ClosedPL()}, {"open_pl", m_acct->L_OpenPL()},
                    {"net_pl", m_acct->L_NetPL()}, {"buying_power", m_acct->L_BuyingPower()},
                    {"bp_in_use", m_acct->L_BuyingPowerInUse()}, {"base_bp", m_acct->L_BaseBuyingPower()},
                    {"equity", m_acct->L_Equity()}, {"running_balance", m_acct->L_RunningBalance()},
                    {"long_value", m_acct->L_LongValue()}, {"short_value", m_acct->L_ShortValue()},
                    {"net_dollar_value", m_acct->L_NetDollarValue()}
                };
                DebugPrint(LogLevel::LL_DEBUG, "[BROADCASTING ACC_SUMMARY_UPDATE] " + jr_acct_summary.dump());
                BroadcastJson(jr_acct_summary);

                // ALSO, broadcast all positions as their P&L might have changed
                DebugPrint(LogLevel::LL_INFO, "HandleMessage(L_MsgAccountChange) => Account data changed. Triggering BroadcastAllPositions for P&L updates.");
                BroadcastAllPositions();
            } break;

            case L_MsgLULD::id: {
                auto luld_msg = static_cast<const L_MsgLULD*>(msg);
                if (!luld_msg) { DebugPrint(LogLevel::LL_ERROR, "[L_MsgLULD] Cast failed."); break; }
                const char* symbolPtr = luld_msg->L_Symbol();
                if (!symbolPtr || symbolPtr[0] == '\0') { DebugPrint(LogLevel::LL_ERROR, "[L_MsgLULD] Null/empty symbol."); break; }

                json status_payload = {
                    {"event", "trading_status"}, {"symbol", std::string(symbolPtr)}, {"status", "LULD_Update"},
                    {"luld_indicator", std::string(1, luld_msg->L_Indicator())},
                    {"luld_high", luld_msg->L_High()}, {"luld_low", luld_msg->L_Low()},
                    {"timestamp", time(nullptr)} // System time of DLL processing
                };
                DebugPrint(LogLevel::LL_INFO, "[L_MsgLULD] " + status_payload.dump(2));
                BroadcastJson(status_payload);
            } break;

            case L_MsgStockHalted::id: {
                auto halt_msg = static_cast<const L_MsgStockHalted*>(msg);
                if (!halt_msg) { DebugPrint(LogLevel::LL_ERROR, "[L_MsgStockHalted] Cast failed."); break; }
                const char* symbolPtr = halt_msg->L_Symbol();
                if (!symbolPtr || symbolPtr[0] == '\0') { DebugPrint(LogLevel::LL_ERROR, "[L_MsgStockHalted] Null/empty symbol."); break; }

                json halt_payload = {
                    {"event", "trading_status"}, {"symbol", std::string(symbolPtr)}, {"status", "Halted"},
                    {"reason", halt_msg->L_Reason() ? std::string(halt_msg->L_Reason()) : "No reason provided"},
                    {"timestamp", halt_msg->L_Time()}
                };
                DebugPrint(LogLevel::LL_INFO, "[L_MsgStockHalted] " + halt_payload.dump(2));
                BroadcastJson(halt_payload);
            } break;

            case L_MsgStockResumed::id: {
                auto resume_msg = static_cast<const L_MsgStockResumed*>(msg);
                if (!resume_msg) { DebugPrint(LogLevel::LL_ERROR, "[L_MsgStockResumed] Cast failed."); break; }
                const char* symbolPtr = resume_msg->L_Symbol();
                if (!symbolPtr || symbolPtr[0] == '\0') { DebugPrint(LogLevel::LL_ERROR, "[L_MsgStockResumed] Null/empty symbol."); break; }

                json resume_payload = {
                   {"event", "trading_status"}, {"symbol", std::string(symbolPtr)}, {"status", "Resumed"},
                   {"timestamp", resume_msg->L_Time()}
                };
                DebugPrint(LogLevel::LL_INFO, "[L_MsgStockResumed] " + resume_payload.dump(2));
                BroadcastJson(resume_payload);
            } break;

            default:
                DebugPrint(LogLevel::LL_DEBUG, "HandleMessage => Unhandled message type: " + std::to_string(msg->L_Type()));
                break;
            }
        }

    private:
        L_Account* m_acct;
    };


    LSEXPORT void LSInitInstance() {
        AFX_MANAGE_STATE(AfxGetStaticModuleState());
        DebugPrint(LogLevel::LL_INFO, "LSInitInstance => Initializing...");

        g_account = L_GetAccount();
        if (!g_account) {
            DebugPrint(LogLevel::LL_ERROR, "LSInitInstance => ERROR: Failed to get L_Account!");
            L_LogAppMessage("LightspeedExtension Error: Failed to get L_Account!");
            return;
        }
        DebugPrint(LogLevel::LL_INFO, "LSInitInstance => Got L_Account.");

        static MyAccountObserver staticObserverInstance(g_account);
        g_observerInstance = &staticObserverInstance;

        if (!g_observerInstance) { // Should not happen if staticObserverInstance construction succeeded
            DebugPrint(LogLevel::LL_ERROR, "LSInitInstance => Failed to assign observer instance!");
            return;
        }

        L_SubscribeToMarketStatus(g_observerInstance);
        DebugPrint(LogLevel::LL_INFO, "LSInitInstance => Subscribed observer to Market Status.");

        g_inboundWorkerRunning.store(true, std::memory_order_release);
        g_inboundWorker = std::thread(InboundWorkerThread);
        DebugPrint(LogLevel::LL_INFO, "LSInitInstance => Inbound worker thread started.");

        g_running.store(true, std::memory_order_release);
        g_listenThread = std::thread([]() {
            DebugPrint(LogLevel::LL_INFO, "ListenThread => Starting...");
            WSADATA wsa;
            if (WSAStartup(MAKEWORD(2, 2), &wsa) != 0) {
                DebugPrint(LogLevel::LL_ERROR, "ListenThread => WSAStartup failed: " + std::to_string(WSAGetLastError()));
                return;
            }

            g_listenSock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
            if (g_listenSock == INVALID_SOCKET) {
                DebugPrint(LogLevel::LL_ERROR, "ListenThread => Socket creation failed: " + std::to_string(WSAGetLastError()));
                WSACleanup();
                return;
            }

            char reuseOpt = 1;
            setsockopt(g_listenSock, SOL_SOCKET, SO_REUSEADDR, &reuseOpt, sizeof(reuseOpt));

            sockaddr_in sin; ZeroMemory(&sin, sizeof(sin));
            sin.sin_family = AF_INET;
            inet_pton(AF_INET, "127.0.0.1", &sin.sin_addr);
            sin.sin_port = htons(9999);

            if (bind(g_listenSock, (sockaddr*)&sin, sizeof(sin)) == SOCKET_ERROR) {
                DebugPrint(LogLevel::LL_ERROR, "ListenThread => Bind failed on 127.0.0.1:9999. Error: " + std::to_string(WSAGetLastError()));
                closesocket(g_listenSock); WSACleanup(); return;
            }
            DebugPrint(LogLevel::LL_INFO, "ListenThread => Socket bound to 127.0.0.1:9999.");

            if (listen(g_listenSock, SOMAXCONN) == SOCKET_ERROR) {
                DebugPrint(LogLevel::LL_ERROR, "ListenThread => Listen failed: " + std::to_string(WSAGetLastError()));
                closesocket(g_listenSock); WSACleanup(); return;
            }
            DebugPrint(LogLevel::LL_INFO, "ListenThread => Listening for incoming connections...");

            while (g_running.load(std::memory_order_relaxed)) {
                fd_set readfds; FD_ZERO(&readfds);
                if (g_listenSock != INVALID_SOCKET) FD_SET(g_listenSock, &readfds); // Check if still valid before FD_SET
                else break; // Stop if socket became invalid

                timeval tv; tv.tv_sec = 1; tv.tv_usec = 0;
                int activity = select(0, &readfds, nullptr, nullptr, &tv); // First param ignored on Windows for FD_SETSIZE limit

                if (!g_running.load(std::memory_order_relaxed)) break; // Check after select returns

                if (activity == SOCKET_ERROR) {
                    DebugPrint(LogLevel::LL_ERROR, "ListenThread => Select failed: " + std::to_string(WSAGetLastError()));
                    Sleep(100); // Prevent tight loop on persistent select error
                    continue;
                }

                if (activity > 0 && g_listenSock != INVALID_SOCKET && FD_ISSET(g_listenSock, &readfds)) {
                    sockaddr_in client_addr; int client_addr_len = sizeof(client_addr);
                    SOCKET clientSock = accept(g_listenSock, (sockaddr*)&client_addr, &client_addr_len);

                    if (clientSock == INVALID_SOCKET) {
                        if (g_running.load(std::memory_order_relaxed)) { // Log only if not shutting down
                            DebugPrint(LogLevel::LL_WARNING, "ListenThread => Accept failed: " + std::to_string(WSAGetLastError()));
                        }
                        continue;
                    }
                    if (!g_running.load(std::memory_order_relaxed)) { // Check again, could have been signaled
                        closesocket(clientSock);
                        break;
                    }

                    char clientIP[INET_ADDRSTRLEN];
                    inet_ntop(AF_INET, &client_addr.sin_addr, clientIP, INET_ADDRSTRLEN);
                    DebugPrint(LogLevel::LL_INFO, "ListenThread => Accepted connection from " + std::string(clientIP) + " on socket " + std::to_string(clientSock));
                    SetNonBlocking(clientSock);
                    { std::lock_guard<std::mutex> lock(g_clientsMutex); g_clients.push_back(clientSock); }

                    std::thread([clientSock, clientIPStr = std::string(clientIP)]() {
                        DebugPrint(LogLevel::LL_INFO, "ClientThread [" + clientIPStr + ":" + std::to_string(clientSock) + "] => Started.");
                        char buf[4096]; std::string partialLine;

                        while (g_running.load(std::memory_order_relaxed)) {
                            fd_set clientReadfds; FD_ZERO(&clientReadfds); FD_SET(clientSock, &clientReadfds);
                            timeval clientTv; clientTv.tv_sec = 0; clientTv.tv_usec = 500000; // 0.5s timeout
                            int clientActivity = select(0, &clientReadfds, nullptr, nullptr, &clientTv);

                            if (!g_running.load(std::memory_order_relaxed)) break;

                            if (clientActivity == SOCKET_ERROR) {
                                DebugPrint(LogLevel::LL_ERROR, BUILD_STRING(<< "ClientThread [" << clientIPStr << ":" << clientSock << "] => Select error: " << WSAGetLastError()));
                                break;
                            }
                            if (clientActivity == 0) { continue; }

                            if (FD_ISSET(clientSock, &clientReadfds)) {
                                int bytesReceived = recv(clientSock, buf, sizeof(buf) - 1, 0);
                                if (bytesReceived == SOCKET_ERROR) {
                                    int err = WSAGetLastError();
                                    if (err != WSAEWOULDBLOCK) {
                                        DebugPrint(LogLevel::LL_ERROR, BUILD_STRING(<< "ClientThread [" << clientIPStr << ":" << clientSock << "] => Recv error: " << err));
                                        break;
                                    }
                                }
                                else if (bytesReceived == 0) {
                                    DebugPrint(LogLevel::LL_INFO, "ClientThread [" + clientIPStr + ":" + std::to_string(clientSock) + "] => Connection closed by peer.");
                                    break;
                                }
                                else {
                                    partialLine.append(buf, bytesReceived);
                                    size_t newlinePos;
                                    while ((newlinePos = partialLine.find('\n')) != std::string::npos) {
                                        std::string completeLine = partialLine.substr(0, newlinePos);
                                        partialLine.erase(0, newlinePos + 1);
                                        completeLine.erase(0, completeLine.find_first_not_of(" \t\r\n"));
                                        completeLine.erase(completeLine.find_last_not_of(" \t\r\n") + 1);

                                        if (!completeLine.empty()) {
                                            bool isHighPri = (completeLine.find("\"cmd\":\"place_order\"") != std::string::npos ||
                                                completeLine.find("\"cmd\":\"cancel_order\"") != std::string::npos);

                                            InboundCommand inbound{ std::move(completeLine), clientSock };
                                            if (isHighPri) {
                                                std::lock_guard<std::mutex> lockHP(g_highPriMutex);
                                                g_highPriorityQueue.push_back(std::move(inbound));
                                            }
                                            else {
                                                std::lock_guard<std::mutex> lockLP(g_lowPriMutex);
                                                g_lowPriorityQueue.push_back(std::move(inbound));
                                            }
                                            g_inboundCV.notify_one();
                                        }
                                    }
                                }
                            }
                        }
                        DebugPrint(LogLevel::LL_INFO, "ClientThread [" + clientIPStr + ":" + std::to_string(clientSock) + "] => Exiting loop.");
                        {
                            std::lock_guard<std::mutex> lock(g_clientsMutex);
                            g_clients.erase(std::remove(g_clients.begin(), g_clients.end(), clientSock), g_clients.end());
                        }
                        shutdown(clientSock, SD_BOTH);
                        closesocket(clientSock);
                        DebugPrint(LogLevel::LL_INFO, "ClientThread [" + clientIPStr + ":" + std::to_string(clientSock) + "] => Socket closed. Thread ending.");
                        }).detach();
                }
            }
            DebugPrint(LogLevel::LL_INFO, "ListenThread => Exiting loop.");
            if (g_listenSock != INVALID_SOCKET) {
                closesocket(g_listenSock);
                g_listenSock = INVALID_SOCKET;
            }
            WSACleanup();
            DebugPrint(LogLevel::LL_INFO, "ListenThread => Listener socket closed. WSA cleaned up. Thread ending.");
            });
        DebugPrint(LogLevel::LL_INFO, "LSInitInstance => Initialization complete.");
    }


    LSEXPORT void LSExitInstance() {
        AFX_MANAGE_STATE(AfxGetStaticModuleState());
        DebugPrint(LogLevel::LL_INFO, "LSExitInstance => Shutting down...");

        g_running.store(false, std::memory_order_release);
        g_inboundWorkerRunning.store(false, std::memory_order_release);

        // Signal listener thread to stop and unblock accept/select
        if (g_listenSock != INVALID_SOCKET) {
            // Closing the socket from another thread is one way to unblock accept/select
            // For select, just having g_running false should be enough if it checks g_running after select returns.
            // For accept, closing the socket is more direct.
            SOCKET tempSock = g_listenSock; // Avoid race if g_listenSock is set to INVALID_SOCKET by listener thread
            g_listenSock = INVALID_SOCKET; // Mark it as intended to be closed
            if (tempSock != INVALID_SOCKET) closesocket(tempSock); // This will make accept/select fail in listener
        }
        g_inboundCV.notify_all();

        if (g_listenThread.joinable()) {
            g_listenThread.join();
            DebugPrint(LogLevel::LL_INFO, "LSExitInstance => Listener thread joined.");
        }
        if (g_inboundWorker.joinable()) {
            g_inboundWorker.join();
            DebugPrint(LogLevel::LL_INFO, "LSExitInstance => Inbound worker thread joined.");
        }

        {
            std::lock_guard<std::mutex> lock(g_clientsMutex);
            DebugPrint(LogLevel::LL_INFO, "LSExitInstance => Closing " + std::to_string(g_clients.size()) + " remaining client sockets...");
            for (SOCKET c : g_clients) {
                shutdown(c, SD_BOTH);
                closesocket(c);
            }
            g_clients.clear();
        }

        if (g_observerInstance) {
            L_UnsubscribeFromMarketStatus(g_observerInstance);
        }
        g_observerInstance = nullptr;
        g_account = nullptr;

        DebugPrint(LogLevel::LL_INFO, "LSExitInstance => Closing log files.");
        {
            std::lock_guard<std::mutex> lock(g_logMutex);
            if (g_debugFile.is_open()) { g_debugFile.flush(); g_debugFile.close(); }
            if (g_replyFile.is_open()) { g_replyFile.flush(); g_replyFile.close(); }
            if (g_ephemeralFile.is_open()) { g_ephemeralFile.flush(); g_ephemeralFile.close(); }
        }
        OutputDebugStringA("LightspeedExtension: LSExitInstance complete.\n");
    }

    LSEXPORT BOOL LSPreTranslateMessage(MSG* pMsg) {
        AFX_MANAGE_STATE(AfxGetStaticModuleState());
        return FALSE;
    }
}


//--------------------------------------------------------------------------
// Implementation of handle_inbound commands
//--------------------------------------------------------------------------
static json HandleInboundCommand(const json& jreq, SOCKET /*client*/) { // client param not used here
    if (!jreq.is_object() || !jreq.contains("cmd") || !jreq.at("cmd").is_string()) {
        DebugPrint(LogLevel::LL_WARNING, "HandleInboundCommand => Invalid request format.");
        return { {"ok", false}, {"error", "Invalid command format or missing/invalid 'cmd' field"} };
    }
    std::string cmd = jreq.at("cmd").get<std::string>();
    DebugPrint(LogLevel::LL_INFO, "HandleInboundCommand => Processing command: '" + cmd + "'");

    if (cmd == "ping") {
        long reqId = jreq.value("req_id", -1L);
        return { {"ok", true}, {"reply", "pong"}, {"event", "ping_response"}, {"req_id", reqId} };
    }
    if (cmd == "place_order") return HandlePlaceOrder(jreq);
    if (cmd == "cancel_order") return HandleCancelOrder(jreq);
    if (cmd == "get_account_data") return HandleGetAccountData(jreq);
    if (cmd == "get_positions_detailed") return HandleGetPositionsDetailed(jreq);
    if (cmd == "get_orders_detailed") return HandleGetOrdersDetailed(jreq);
    if (cmd == "get_position") return HandleGetPosition(jreq);
    if (cmd == "get_all_positions") return HandleGetAllPositions(jreq);

    DebugPrint(LogLevel::LL_WARNING, "HandleInboundCommand => Unknown command received: '" + cmd + "'");
    return { {"ok", false}, {"error", "Unknown command: " + cmd} };
}

static json HandlePlaceOrder(const json& jreq) {
    long reqId = jreq.value("req_id", -1L);
    if (!g_account) {
        DebugPrint(LogLevel::LL_ERROR, "HandlePlaceOrder => Error: g_account is null.");
        return { {"ok", false}, {"req_id", reqId}, {"error", "Lightspeed account not available"} };
    }

    std::string sym, act, otyp, correlation_id; long qty; double limP = 0.0; bool outsideRth; int local_copy_id;
    try {
        sym = jreq.at("symbol").get<std::string>();
        act = jreq.at("action").get<std::string>();
        qty = jreq.at("quantity").get<long>();
        otyp = jreq.at("orderType").get<std::string>();
        if (otyp == "LMT") limP = jreq.at("limitPrice").get<double>();
        outsideRth = jreq.value("outsideRth", false);
        local_copy_id = jreq.value("local_copy_id", -1);
        correlation_id = jreq.value("correlation_id", "");

        if (local_copy_id == -1) {
            DebugPrint(LogLevel::LL_WARNING, "HandlePlaceOrder => 'local_copy_id' is -1 or missing from Python request.");
            // Continue processing - this will be logged but order will still be placed
        }

        if (sym.empty()) throw std::runtime_error("Symbol cannot be empty");
        if (qty <= 0) throw std::runtime_error("Quantity must be positive");
        if (act != "BUY" && act != "SELL") throw std::runtime_error("Invalid action (must be BUY or SELL)");
        if (otyp != "LMT" && otyp != "MKT") throw std::runtime_error("Invalid orderType (must be LMT or MKT)");
        if (otyp == "LMT" && limP <= 0.0) throw std::runtime_error("Limit price must be positive for LMT orders");

    }
    catch (const json::out_of_range& e) { // Specific for missing fields
        DebugPrint(LogLevel::LL_WARNING, "HandlePlaceOrder => Missing parameter: " + std::string(e.what()));
        return { {"ok", false}, {"req_id", reqId}, {"error", "Missing parameter: " + std::string(e.what())} };
    }
    catch (const json::type_error& e) { // Specific for wrong type
        DebugPrint(LogLevel::LL_WARNING, "HandlePlaceOrder => Parameter type error: " + std::string(e.what()));
        return { {"ok", false}, {"req_id", reqId}, {"error", "Parameter type error: " + std::string(e.what())} };
    }
    catch (const std::runtime_error& e) { // For our custom validation messages
        DebugPrint(LogLevel::LL_WARNING, "HandlePlaceOrder => Invalid parameter: " + std::string(e.what()));
        return { {"ok", false}, {"req_id", reqId}, {"error", "Invalid parameter: " + std::string(e.what())} };
    }
    catch (const std::exception& e) { // Generic catch-all
        DebugPrint(LogLevel::LL_WARNING, "HandlePlaceOrder => Invalid parameters: " + std::string(e.what()));
        return { {"ok", false}, {"req_id", reqId}, {"error", "Invalid parameters: " + std::string(e.what())} };
    }

    long lsOrderType = (otyp == "LMT" ? L_OrderType::LIMIT : L_OrderType::MARKET);
    double priceToSend = (otyp == "LMT") ? (std::floor(limP * 100.0 + 0.5) / 100.0) : 0.0;

    long originally_generated_cpp_ephemeral = g_ephemeralGen.fetch_add(1, std::memory_order_relaxed);
    char side_char_for_key_actual = (act == "BUY" ? L_Side::BUY : L_Side::SELL);
    OrderKey key_for_pending{ sym, side_char_for_key_actual, qty, priceToSend };

    EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "=== PLACE_ORDER_ATTEMPT ==="));
    EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "HandlePlaceOrder: PythonSentLocalId=" << local_copy_id
                                                    << ", OriginallyGeneratedCppEphemeral=" << originally_generated_cpp_ephemeral // Log the one we generated
                                                    << ", Symbol=" << sym << ", Action=" << act << ", Qty=" << qty));

    long correlationIdFromLightspeed = originally_generated_cpp_ephemeral; // Initialize with our ID
                                                                        // This will be passed by address and potentially modified by L_SendOrderBasic

    // Call L_SendOrderBasic, passing the ADDRESS of correlationIdFromLightspeed
    g_account->L_SendOrderBasic(sym.c_str(), lsOrderType, side_char_for_key_actual, static_cast<unsigned long>(qty),
        L_PriceBase::abs, priceToSend,
        "NSDQ", L_TIF::DAY, outsideRth, 0, 0.0, nullptr,
        &correlationIdFromLightspeed, nullptr); // Pass address

    // AFTER the call, correlationIdFromLightspeed now holds the ID Lightspeed will use in L_MsgOrderRequested
    EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "HandlePlaceOrder: L_SendOrderBasic called. ID used/returned by LS API (correlationIdFromLightspeed) = " << correlationIdFromLightspeed));

    { // Mutex scope for map operations
        std::lock_guard<std::mutex> lock(g_mapMutex);
        if (local_copy_id != -1) {
            // Store the link using the ID Lightspeed will actually send back in L_MsgOrderRequested
            g_cppEphemeralToPythonLocalId[correlationIdFromLightspeed] = local_copy_id;
            EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "HandlePlaceOrder: Stored map g_cppEphemeralToPythonLocalId[" << correlationIdFromLightspeed // Key is now from LS
                                                            << "] = " << local_copy_id));
        } else {
             EphemeralLog(LogLevel::LL_ERROR, BUILD_STRING_OIM(<< "HandlePlaceOrder: Python did not send a valid local_copy_id (" << local_copy_id
                                                        << ") for CorrIdFromLS=" << correlationIdFromLightspeed << ". Linkage to Python ID will fail for this order initially."));
        }
        
        // Store correlation_id if provided (mirroring local_copy_id pattern)
        if (!correlation_id.empty()) {
            g_cppEphemeralToCorrelationId[correlationIdFromLightspeed] = correlation_id;
            EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "HandlePlaceOrder: Stored correlation_id mapping g_cppEphemeralToCorrelationId[" << correlationIdFromLightspeed 
                                                            << "] = " << correlation_id));
        } else {
            EphemeralLog(LogLevel::LL_DEBUG, BUILD_STRING_OIM(<< "HandlePlaceOrder: No correlation_id provided from Python for CorrIdFromLS=" << correlationIdFromLightspeed));
        }
        // g_pendingOrderKeys should ideally also use correlationIdFromLightspeed if that's what L_MsgOrderRequested will contain
        // Or, keep g_pendingOrderKeys with originally_generated_cpp_ephemeral and adjust L_MsgOrderRequested logic for immediate rejections if needed
        // For now, let's assume L_MsgOrderRequested will use correlationIdFromLightspeed for its L_CorrelationId()
        g_pendingOrderKeys[correlationIdFromLightspeed] = key_for_pending;
    }

    // AFTER L_SendOrderBasic, correlationIdFromLightspeed holds the value LS API will use.
    // Make sure the JSON returned to Python uses this value for "ephemeral_corr".
    return {
        {"ok", true},
        {"req_id", reqId}, // This is Python's original request ID for the command
        {"event", "order_status"},
        {"status", "SentToLightspeed"}, // Status from DLL
        {"ephemeral_corr", correlationIdFromLightspeed}, // <--- ENSURE THIS USES THE VALUE *AFTER* L_SendOrderBasic
        {"local_copy_id", local_copy_id} // Python's order ID
    };
}

static json HandleCancelOrder(const json& jreq) {
    long reqId = jreq.value("req_id", -1L);
    if (!g_account) return { {"ok", false}, {"req_id", reqId}, {"error", "Lightspeed account not available"} };
    long ls_order_id_val; // Use _val to avoid conflict
    try {
        ls_order_id_val = jreq.at("ls_order_id").get<long>();
        if (ls_order_id_val <= 0) throw std::runtime_error("ls_order_id must be positive.");
    }
    catch (const std::exception& e) {
        return { {"ok", false}, {"req_id", reqId}, {"error", "Invalid ls_order_id: " + std::string(e.what())} };
    }

    L_Order* od = g_account->L_FindOrderByOrderId(ls_order_id_val);
    if (!od) {
        DebugPrint(LogLevel::LL_WARNING, "HandleCancelOrder => Order not found or inactive for ls_order_id=" + std::to_string(ls_order_id_val));
        return { {"ok", false}, {"req_id", reqId}, {"error", "Order not found or inactive: " + std::to_string(ls_order_id_val)} };
    }

    g_account->L_CancelOrder(od);
    EphemeralLog(LogLevel::LL_INFO, "CancelOrder Sent: ls_order_id=" + std::to_string(ls_order_id_val));
    return { {"ok", true}, {"req_id", reqId}, {"event", "order_status"}, {"status", "CancelRequestSent"}, {"ls_order_id", ls_order_id_val} };
}

static json HandleGetAccountData(const json& jreq) {
    long reqId = jreq.value("req_id", -1L);
    if (!g_account) return { {"ok", false}, {"req_id", reqId}, {"error", "Lightspeed account not available"} };
    return {
        {"ok", true}, {"req_id", reqId},
        {"account_data", {
            {"closed_pl", g_account->L_ClosedPL()}, {"open_pl", g_account->L_OpenPL()},
            {"net_pl", g_account->L_NetPL()}, {"buying_power", g_account->L_BuyingPower()},
            {"bp_in_use", g_account->L_BuyingPowerInUse()}, {"base_bp", g_account->L_BaseBuyingPower()},
            {"equity", g_account->L_Equity()}, {"running_balance", g_account->L_RunningBalance()},
            {"long_value", g_account->L_LongValue()}, {"short_value", g_account->L_ShortValue()},
            {"net_dollar_value", g_account->L_NetDollarValue()}
        }}
    };
}

static json HandleGetPositionsDetailed(const json& jreq) {
    long reqId = jreq.value("req_id", -1L);
    if (!g_account) return { {"ok", false}, {"req_id", reqId}, {"error", "Lightspeed account not available"} };
    json positionsArray = json::array();
    try {
        for (auto it = g_account->positions_begin(); it != g_account->positions_end(); ++it) {
            L_Position* p = *it; if (!p) continue;
            const char* symPtr = p->L_Symbol(); std::string sym_str = (symPtr && symPtr[0] != '\0') ? symPtr : "UNKNOWN";
            json fillsArr = json::array();
            try { // Inner try-catch for executions of a single position
                for (auto fx_it = p->executions_begin(); fx_it != p->executions_end(); ++fx_it) {
                    const L_Execution& ex = *fx_it;
                    fillsArr.push_back({
                        {"fill_shares", std::labs(ex.L_Shares())}, {"fill_price", ex.L_AveragePrice()},
                        {"fill_time_ms_since_midnight", ex.L_ExecTimeMillis()},
                        {"liquidity", std::string(1, ex.L_Liquidity())}
                        });
                }
            }
            catch (const std::exception& e_exec) {
                DebugPrint(LogLevel::LL_WARNING, "HandleGetPositionsDetailed => Exception iterating executions for " + sym_str + ": " + e_exec.what());
            }
            positionsArray.push_back({
                {"symbol", sym_str}, {"shares", p->L_Shares()}, {"avg_price", p->L_AveragePrice()},
                {"realized_pnl", p->L_ClosedPL()}, {"unrealized_pnl", p->L_OpenPL()},
                {"total_pnl", p->L_ClosedPL() + p->L_OpenPL()}, {"fills", fillsArr}
                });
        }
    }
    catch (const std::exception& e_pos) {
        DebugPrint(LogLevel::LL_ERROR, "HandleGetPositionsDetailed => Exception iterating positions: " + std::string(e_pos.what()));
        return { {"ok", false}, {"req_id", reqId}, {"error", "Exception fetching positions data: " + std::string(e_pos.what())} };
    }
    return { {"ok", true}, {"req_id", reqId}, {"event", "get_positions_response"}, {"positions", positionsArray} };
}

static json HandleGetOrdersDetailed(const json& jreq) {
    long reqId = jreq.value("req_id", -1L);
    if (!g_account) return { {"ok", false}, {"req_id", reqId}, {"error", "Lightspeed account not available"} };
    json ordersArray = json::array();
    try {
        for (auto it = g_account->orders_begin(); it != g_account->orders_end(); ++it) {
            L_Order* od = *it; if (!od) continue;
            long lsOrdId = od->L_OrderId(); // This is lsOrderId_canonical
            long ref = od->L_ReferenceId(); // This is lsRefId_from_LOrder_object
            const char* symPtr = od->L_Symbol(); std::string sym_str = (symPtr && symPtr[0] != '\0') ? symPtr : "UNKNOWN";
            char sideChar = od->L_TradeSide(); std::string side_str = (sideChar == L_Side::BUY) ? "BUY" : "SELL";

            int python_local_id_val = -1;
            long original_cpp_ephemeral_val = -1;

            { // Mutex scope for ID resolution
                std::lock_guard<std::mutex> lock(g_mapMutex);
                // Try to find original C++ ephemeral using the canonical LS Order ID
                auto it_canon_to_eph = g_lsCanonicalOrderIdToCppEphemeral.find(lsOrdId);
                if (it_canon_to_eph != g_lsCanonicalOrderIdToCppEphemeral.end()) {
                    original_cpp_ephemeral_val = it_canon_to_eph->second;
                    auto it_eph_to_py = g_cppEphemeralToPythonLocalId.find(original_cpp_ephemeral_val);
                    if (it_eph_to_py != g_cppEphemeralToPythonLocalId.end()) {
                        python_local_id_val = it_eph_to_py->second;
                    }
                }
                // If not found via canonical ID, try via reference ID (lsRefId_from_LOrder_object)
                // This path might be relevant for orders still in L_MsgOrderRequested state or very early L_MsgOrderChange
                else if (ref > 0 && original_cpp_ephemeral_val == -1) { // Check if not already found
                    auto it_ref_to_eph = g_lsRefIdToCppEphemeral.find(ref);
                    if (it_ref_to_eph != g_lsRefIdToCppEphemeral.end()) {
                        original_cpp_ephemeral_val = it_ref_to_eph->second;
                        auto it_eph_to_py = g_cppEphemeralToPythonLocalId.find(original_cpp_ephemeral_val);
                        if (it_eph_to_py != g_cppEphemeralToPythonLocalId.end()) {
                            python_local_id_val = it_eph_to_py->second;
                        }
                    }
                }
            } // End Mutex Scope
            json fillsArr = json::array();
            try { // Inner try-catch for executions of a single order
                for (auto fx_it = od->executions_begin(); fx_it != od->executions_end(); ++fx_it) {
                    const L_Execution& ex = *fx_it;
                    fillsArr.push_back({
                         {"fill_shares", std::labs(ex.L_Shares())}, {"fill_price", ex.L_AveragePrice()},
                         {"fill_time_ms_since_midnight", ex.L_ExecTimeMillis()},
                         {"liquidity", std::string(1, ex.L_Liquidity())}
                        });
                }
            }
            catch (const std::exception& e_exec_ord) {
                DebugPrint(LogLevel::LL_WARNING, BUILD_STRING(<< "HandleGetOrdersDetailed => Exception iterating executions for order " << lsOrdId << ": " << e_exec_ord.what()));
            }
            ordersArray.push_back({
                 {"ls_order_id", lsOrdId}, {"reference_id", ref},
                 {"ephemeral_corr", (original_cpp_ephemeral_val != -1) ? original_cpp_ephemeral_val : ref }, // Prefer original C++ ephemeral
                 {"local_copy_id", python_local_id_val}, {"symbol", sym_str}, {"side", side_str},
                 {"original_shares", od->L_OriginalShares()}, {"original_price", od->L_OriginalPrice()},
                 {"executed_shares", od->L_ExecutedShares()}, {"average_price", od->L_AveragePrice()},
                 {"total_price", od->L_TotalPrice()}, {"fills", fillsArr}
                });
        }
    }
    catch (const std::exception& e_ord) {
        DebugPrint(LogLevel::LL_ERROR, "HandleGetOrdersDetailed => Exception iterating orders: " + std::string(e_ord.what()));
        return { {"ok", false}, {"req_id", reqId}, {"error", "Exception fetching orders data: " + std::string(e_ord.what())} };
    }
    return { {"ok", true}, {"req_id", reqId}, {"event", "get_orders_response"}, {"orders", ordersArray} };
}

static json HandleGetPosition(const json& jreq) {
    long reqId = jreq.value("req_id", -1L);
    std::string symbol_str_req; // Use different name
    try {
        symbol_str_req = jreq.at("symbol").get<std::string>();
        if (symbol_str_req.empty()) throw std::runtime_error("Symbol cannot be empty");
    }
    catch (const std::exception& e) {
        return { {"ok", false}, {"req_id", reqId}, {"error", "Invalid symbol: " + std::string(e.what())} };
    }
    if (!g_account) return { {"ok", false}, {"req_id", reqId}, {"symbol", symbol_str_req}, {"error", "Lightspeed account not available"} };

    L_Position* p = g_account->L_FindPosition(symbol_str_req.c_str());
    if (!p) {
        return { {"ok", true}, {"req_id", reqId}, {"event", "get_position_response"}, {"found", false}, {"symbol", symbol_str_req} };
    }

    return {
        {"ok", true}, {"req_id", reqId}, {"event", "get_position_response"}, {"found", true},
        {"position", {
            {"symbol", symbol_str_req}, {"shares", p->L_Shares()}, {"avg_price", p->L_AveragePrice()},
            {"realized_pnl", p->L_ClosedPL()}, {"unrealized_pnl", p->L_OpenPL()},
            {"total_pnl", p->L_ClosedPL() + p->L_OpenPL()}
        }}
    };
}

static json HandleGetAllPositions(const json& jreq) {
    long reqId = jreq.value("req_id", -1L);
    if (!g_account) return { {"event", "get_all_positions_response"}, {"ok", false}, {"req_id", reqId}, {"error", "Lightspeed account not available"} };
    json positionsArray = json::array();
    try {
        for (auto it = g_account->positions_begin(); it != g_account->positions_end(); ++it) {
            L_Position* p = *it; if (!p) continue;
            const char* sPtr = p->L_Symbol(); std::string s_str_pos = (sPtr && sPtr[0] != '\0') ? sPtr : "UNKNOWN";
            positionsArray.push_back({
                 {"symbol", s_str_pos}, {"shares", p->L_Shares()}, {"avg_price", p->L_AveragePrice()},
                 {"realized_pnl", p->L_ClosedPL()}, {"unrealized_pnl", p->L_OpenPL()},
                 {"total_pnl", p->L_ClosedPL() + p->L_OpenPL()}
                });
        }
    }
    catch (const std::exception& e_get_all) {
        DebugPrint(LogLevel::LL_ERROR, "HandleGetAllPositions => Exception: " + std::string(e_get_all.what()));
        return { {"event", "get_all_positions_response"}, {"ok", false}, {"req_id", reqId}, {"error", "Exception fetching all positions: " + std::string(e_get_all.what())} };
    }
    return { {"event", "get_all_positions_response"}, {"ok", true}, {"req_id", reqId}, {"positions", positionsArray} };
}


//--------------------------------------------------------------------------
// Broadcast Helpers
//--------------------------------------------------------------------------
static void BroadcastJson(const json& msg) {
    std::string line;
    try {
        line = msg.dump();
    }
    catch (const json::type_error& e) {
        DebugPrint(LogLevel::LL_ERROR, "[BroadcastJson] => JSON dump (type_error): " + std::string(e.what()));
        return;
    }
    catch (const std::exception& e) {
        DebugPrint(LogLevel::LL_ERROR, "[BroadcastJson] => JSON dump (std_exception): " + std::string(e.what()));
        return;
    }

    std::string messageWithNewline = line + "\n";
    std::vector<SOCKET> clientsToSendTo;
    {
        std::lock_guard<std::mutex> lock(g_clientsMutex);
        clientsToSendTo = g_clients;
    }

    for (SOCKET c : clientsToSendTo) {
        if (!SendAllNonBlocking(c, messageWithNewline.c_str(), (int)messageWithNewline.length())) {
            // SendAllNonBlocking logs errors.
        }
    }
}

static void BroadcastAllPositions() {
    if (!g_account) { DebugPrint(LogLevel::LL_WARNING, "BroadcastAllPositions => Cannot broadcast, g_account is null."); return; }
    json positionsArray = json::array();
    try {
        for (auto it = g_account->positions_begin(); it != g_account->positions_end(); ++it) {
            L_Position* p = *it; if (!p) continue;
            const char* symPtr = p->L_Symbol();
            std::string sym_str_bcast = (symPtr && symPtr[0] != '\0') ? symPtr : "UNKNOWN_SYMBOL";

            positionsArray.push_back({
                {"symbol", sym_str_bcast}, {"shares", p->L_Shares()}, {"avg_price", p->L_AveragePrice()},
                {"realized_pnl", p->L_ClosedPL()}, {"unrealized_pnl", p->L_OpenPL()},
                {"total_pnl", p->L_ClosedPL() + p->L_OpenPL()}
                });
        }
        json j = { {"event", "positions_update"}, {"event_type", "positions_update"}, {"positions", positionsArray} };
        BroadcastJson(j);
    }
    catch (const json::exception& e) {
        DebugPrint(LogLevel::LL_ERROR, "BroadcastAllPositions => JSON exception preparing position data: " + std::string(e.what()));
    }
    catch (const std::exception& e) {
        DebugPrint(LogLevel::LL_ERROR, "BroadcastAllPositions => Std exception preparing position data: " + std::string(e.what()));
    }
}


// static void SendCrossOwnError function was here, commented out as likely redundant
/*
static void SendCrossOwnError(long ls_order_id, const std::string& reason ) {
    DebugPrint(LogLevel::LL_WARNING, "SendCrossOwnError => Called for ls_order_id=" + std::to_string(ls_order_id) + ". Rejection should be handled by L_MsgOrderRequested.");
}
*/