# data_models/trading.py

"""
Consolidated trading data classes for TESTRADE system.

This module contains all trading-related data structures in a single source of truth.
Previously scattered across multiple modules including:
- modules/order_management/order_data_types.py
- modules/ocr/data_types.py
- modules/ocr/interfaces.py
- modules/trade_management/interfaces.py
- modules/risk_management/interfaces.py
- modules/gui_commands/command_result.py
- data_models/trading_data_types.py
"""

from dataclasses import dataclass, field, replace
from typing import Optional, Dict, Any, Tuple, List
from .enums import OrderSide, OrderLSAgentStatus, OrderEventType, TradeActionType, OrderOrigin, RiskLevel, CommandStatus


# =============================================================================
# ORDER MANAGEMENT DATA STRUCTURES
# =============================================================================

@dataclass(slots=True, frozen=True)
class FillRecord:
    """Individual fill record for an order."""
    # Core fill data
    shares_filled: float
    fill_price: float
    fill_time: float  # Epoch timestamp of the fill execution

    # Optional/additional data often available
    commission: float = 0.0
    side: Optional[OrderSide] = None # Side of this specific fill leg
    liquidity: Optional[str] = None  # e.g., "A" (Added), "R" (Removed)
    fill_id_broker: Optional[str] = None # Broker's unique ID for this fill

    fill_time_est_str: Optional[str] = None # Formatted EST string, e.g., "HH:MM:SS.mmm"

    meta: Dict[str, Any] = field(default_factory=dict)


@dataclass(slots=True, frozen=True)
class StatusHistoryEntry:
    """Order status history entry."""
    timestamp: float # Epoch timestamp when this status was recorded by our system
    status: OrderLSAgentStatus # The OrderLSAgentStatus Enum member
    reason: Optional[str] = None
    broker_timestamp: Optional[float] = None # Optional: Timestamp from broker for this status event


@dataclass(slots=True, frozen=True)
class Order:
    """Complete order data structure."""
    # --- Required fields (no defaults) ---
    local_id: int
    symbol: str
    side: OrderSide
    event_type: OrderEventType
    requested_shares: float
    timestamp_requested: float

    # --- Optional fields (with defaults) ---
    version: int = 0
    ls_order_id: Optional[int] = None
    ephemeral_corr_id: Optional[int] = None
    linked_ephemeral_ids: Tuple[int, ...] = field(default_factory=tuple)
    action_type: Optional[TradeActionType] = None
    master_correlation_id: Optional[str] = None
    requested_lmt_price: Optional[float] = None
    order_type: str = "LMT" # Could be an Enum: e.g., OrderInstructionType(LMT, MKT)
    requested_cost: float = 0.0
    real_time_price_at_request: Optional[float] = None
    trader_price_at_request: Optional[float] = None
    timestamp_ocr: Optional[float] = None
    timestamp_ocr_processed: Optional[float] = None
    timestamp_to_bridge_sent: Optional[float] = None # When Python BrokerBridge sent to C++
    timestamp_bridge_ack: Optional[float] = None      # When C++ ack'd receipt from Python BrokerBridge
    timestamp_ls_api_sent_ok: Optional[float] = None  # Placeholder: If L_MsgOrderRequested reports OK
    timestamp_broker_final_status: Optional[float] = None # Time of first terminal status
    timestamp_broker: Optional[float] = None # When broker first acknowledges/links the order
    timestamp_sent: Optional[float] = None   # When order was sent to broker by our system
    timestamp_broker_ack: Optional[float] = None # Specifically for when broker acknowledges

    # --- Execution State & Details ---
    ls_status: OrderLSAgentStatus = OrderLSAgentStatus.PENDING_SUBMISSION
    fills: Tuple[FillRecord, ...] = field(default_factory=tuple)
    filled_quantity: float = 0.0
    leftover_shares: float = 0.0 # Default to 0.0, should be set explicitly by the creator

    # --- Descriptive & Relational ---
    parent_trade_id: Optional[int] = None
    comment: Optional[str] = None
    rejection_reason: Optional[str] = None
    cancel_reason: Optional[str] = None
    ocr_confidence: Optional[float] = None

    # --- History & Extensibility ---
    status_history: Tuple[StatusHistoryEntry, ...] = field(default_factory=tuple)
    perf_timestamps: Dict[str, float] = field(default_factory=dict)
    meta: Dict[str, Any] = field(default_factory=dict)

    # --- Professional order tracking fields ---
    client_id: str = field(default="TESTRADE")  # Who created this order
    tags: Dict[str, Any] = field(default_factory=dict)  # Flexible metadata
    origin: OrderOrigin = field(default=OrderOrigin.PYTHON)  # Where this order originated from

    # --- Helper for creating new instances with updates ---
    def with_update(self, **changes) -> 'Order':
        """Create new Order instance with updated fields."""
        if 'version' not in changes:
            changes['version'] = self.version + 1
        return replace(self, **changes)
    
    # --- Backward compatibility helper ---
    @property
    def is_broker_originated(self) -> bool:
        """Check if this order originated from the broker (for backward compatibility)."""
        # Use origin field if available, otherwise fall back to event_type check
        if hasattr(self, 'origin'):
            return self.origin == OrderOrigin.BROKER
        # Fallback for orders created before origin field was added
        return self.event_type == OrderEventType.MANUAL_BROKER_ORDER


@dataclass(slots=True, frozen=True)
class OrderParameters:
    """Parameters for creating an order."""
    symbol: str
    side: OrderSide
    shares: float
    price: Optional[float] = None
    order_type: str = "LMT"
    time_in_force: str = "DAY"
    
    # Optional metadata
    comment: Optional[str] = None
    correlation_id: Optional[str] = None
    parent_trade_id: Optional[int] = None


# =============================================================================
# POSITION DATA STRUCTURES
# =============================================================================

@dataclass(slots=True, frozen=True)
class BrokerSnapshotPositionData:
    """Broker position snapshot data."""
    symbol: str
    quantity: float
    average_price: float
    unrealized_pnl: Optional[float] = None


# =============================================================================
# OCR DATA STRUCTURES
# =============================================================================

@dataclass(slots=True)
class OCRSnapshot:
    """Individual OCR reading for a trading symbol with parsed trading data."""
    symbol: str
    price_str: Optional[str] = None
    quantity_str: Optional[str] = None
    action_intent_str: Optional[str] = None
    cost_basis_str: Optional[str] = None
    pnl_str: Optional[str] = None
    raw_ocr_text_segment: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert OCRSnapshot to a dictionary for serialization using dataclasses.asdict()."""
        from dataclasses import asdict
        return asdict(self)


@dataclass
class OCRParsedData:
    """
    World-class OCR data structure with clean metadata/payload separation.
    
    This follows the standard TESTRADE event pattern:
    - metadata: All tracing, routing, and correlation data
    - payload fields: Business logic data (OCR results, timestamps, analysis data)
    """
    
    # --- METADATA BLOCK: Tracing, Routing, and Correlation Data ---
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # --- PAYLOAD: Business Logic Data ---
    
    # Core OCR Results
    overall_confidence: Optional[float] = None
    snapshots: Dict[str, OCRSnapshot] = field(default_factory=dict)
    full_raw_text: str = ""
    
    # T0-T3 High-Precision Milestone Timestamps (nanoseconds)
    T0_ImageIngress_ns: Optional[int] = None      # Image capture start
    T1_ImageProcessing_ns: Optional[int] = None   # Image processing complete
    T2_TesseractOutput_ns: Optional[int] = None   # Tesseract OCR complete
    T3_FormattedOutput_ns: Optional[int] = None   # Final output ready
    
    # C++ Accelerator High-Precision Timestamps (nanoseconds)
    cxx_milestones_ns: Dict[str, int] = field(default_factory=dict)
    
    # Legacy Compatibility Fields (will be deprecated)
    frame_timestamp: float = 0.0  # T0 as float seconds
    raw_ocr_processing_finish_timestamp: float = 0.0  # T2 as float epoch
    
    # Performance and Debug Data
    ocr_process_perf_timestamps: Optional[Dict[str, float]] = None
    processed_frame_base64: Optional[str] = None  # Base64 encoded processed frame
    
    # Analysis Metadata
    roi_used_for_capture: Optional[List[int]] = field(default_factory=list)
    preprocessing_params_used: Optional[Dict[str, Any]] = field(default_factory=dict)
    current_roi_for_ocr: Optional[List[int]] = None
    
    # Legacy Fields (for backward compatibility during transition)
    master_correlation_id: Optional[str] = field(default=None, init=False)  # Use metadata.correlationId instead
    event_id: Optional[str] = field(default=None, init=False)  # Use metadata.eventId instead
    validation_id: Optional[str] = field(default=None, init=False)  # For pipeline validation tracking
    raw_image_grab_ns: Optional[int] = field(default=None, init=False)  # Use T0_ImageIngress_ns instead
    image_processing_finish_ns: Optional[int] = field(default=None, init=False)  # Use T1_ImageProcessing_ns instead
    tesseract_finish_ns: Optional[int] = field(default=None, init=False)  # Use T2_TesseractOutput_ns instead
    ocr_processing_timestamp_ns: Optional[int] = field(default=None, init=False)  # Use T3_FormattedOutput_ns instead
    origin_timestamp_ns: Optional[int] = field(default=None, init=False)  # Use T0_ImageIngress_ns instead
    t4_handoff_to_conditioner_ns: Optional[int] = None  # Captured by OCRProcessManager

    def __post_init__(self):
        """
        Ensure backward compatibility by populating legacy fields from metadata.
        Also ensure metadata has required fields for correlation tracking.
        """
        # If metadata doesn't have eventId, create one
        if not self.metadata.get('eventId'):
            from utils.thread_safe_uuid import get_thread_safe_uuid
            self.metadata['eventId'] = get_thread_safe_uuid()
        
        # Populate legacy fields for backward compatibility
        self.event_id = self.metadata.get('eventId')
        self.master_correlation_id = self.metadata.get('correlationId')
        
        # Map new T0-T3 fields to legacy fields for compatibility
        if self.T0_ImageIngress_ns is not None:
            self.raw_image_grab_ns = self.T0_ImageIngress_ns
            self.origin_timestamp_ns = self.T0_ImageIngress_ns
        if self.T1_ImageProcessing_ns is not None:
            self.image_processing_finish_ns = self.T1_ImageProcessing_ns
        if self.T2_TesseractOutput_ns is not None:
            self.tesseract_finish_ns = self.T2_TesseractOutput_ns
        if self.T3_FormattedOutput_ns is not None:
            self.ocr_processing_timestamp_ns = self.T3_FormattedOutput_ns

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert OCRParsedData to a dictionary for serialization using dataclasses.asdict().
        This automatically handles nested OCRSnapshot objects and other dataclass fields.
        """
        from dataclasses import asdict
        return asdict(self)


@dataclass(slots=True, frozen=True)
class OcrConfigData:
    """OCR configuration parameters."""
    enabled: bool = True
    
    # Processing parameters
    polling_interval_ms: int = 100
    confidence_threshold: float = 0.7
    max_processing_time_ms: int = 5000
    
    # Image processing
    image_scale_factor: float = 1.0
    gaussian_blur_kernel_size: int = 3
    gaussian_blur_sigma: float = 1.0
    
    # OCR engine settings
    ocr_engine: str = "tesseract"
    ocr_language: str = "eng"
    ocr_page_segmentation_mode: int = 6
    ocr_engine_mode: int = 3
    
    # Symbol detection
    symbols_to_track: Tuple[str, ...] = field(default_factory=tuple)
    max_symbols_per_frame: int = 50
    
    # Performance tuning
    parallel_processing: bool = True
    max_worker_threads: int = 4
    
    # Debugging
    save_debug_images: bool = False
    debug_output_path: Optional[str] = None


# =============================================================================
# TRADE MANAGEMENT DATA STRUCTURES
# =============================================================================

@dataclass(slots=True, frozen=True)
class TradeSignal:
    """Signal for trade actions."""
    symbol: str
    action: TradeActionType
    timestamp: float
    
    # Signal details
    confidence: float = 0.0
    signal_strength: float = 0.0
    source: str = "unknown"
    
    # Price information
    entry_price: Optional[float] = None
    target_price: Optional[float] = None
    stop_price: Optional[float] = None
    
    # Position sizing
    suggested_shares: Optional[float] = None
    max_risk_per_share: Optional[float] = None
    
    # Context
    correlation_id: Optional[str] = None
    parent_signal_id: Optional[str] = None
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)


# =============================================================================
# RISK MANAGEMENT DATA STRUCTURES
# =============================================================================

@dataclass(slots=True, frozen=True)
class RiskAssessment:
    """Risk assessment result."""
    symbol: str
    risk_level: RiskLevel
    timestamp: float
    
    # Risk metrics
    position_risk: float = 0.0
    portfolio_risk: float = 0.0
    max_drawdown: float = 0.0
    
    # Risk factors
    volatility_score: float = 0.0
    correlation_risk: float = 0.0
    liquidity_risk: float = 0.0
    
    # Recommendations
    max_position_size: Optional[float] = None
    recommended_stop_loss: Optional[float] = None
    risk_adjusted_target: Optional[float] = None
    
    # Decision
    approved: bool = False
    rejection_reason: Optional[str] = None
    
    # Context
    correlation_id: Optional[str] = None
    assessment_id: Optional[str] = None
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)


# =============================================================================
# GUI COMMAND DATA STRUCTURES
# =============================================================================

@dataclass(slots=True, frozen=True)
class CommandResult:
    """Standardized command result structure."""
    status: CommandStatus
    message: str
    timestamp: float
    
    # Additional data
    data: Optional[Dict[str, Any]] = None
    error_details: Optional[str] = None
    
    # Context
    command_id: Optional[str] = None
    correlation_id: Optional[str] = None
    
    # Performance
    execution_time_ms: Optional[float] = None
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)


# =============================================================================
# MARKET DATA STRUCTURES
# =============================================================================

@dataclass(slots=True, frozen=True)
class MarketDataTick:
    """Market data tick information."""
    symbol: str
    timestamp: float
    
    # Price data
    bid_price: Optional[float] = None
    ask_price: Optional[float] = None
    last_price: Optional[float] = None
    
    # Size data
    bid_size: Optional[int] = None
    ask_size: Optional[int] = None
    last_size: Optional[int] = None
    
    # Volume and other data
    volume: Optional[int] = None
    high: Optional[float] = None
    low: Optional[float] = None
    open_price: Optional[float] = None
    close_price: Optional[float] = None
    
    # Market status
    market_status: Optional[str] = None
    
    # Source information
    source: str = "unknown"
    latency_ms: Optional[float] = None
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass(slots=True, frozen=True)
class MarketDataQuote:
    """Market data quote information."""
    symbol: str
    timestamp: float
    
    # Quote data
    bid_price: float
    ask_price: float
    bid_size: int
    ask_size: int
    
    # Derived data
    spread: float = field(init=False)
    midpoint: float = field(init=False)
    
    # Quality metrics
    quote_condition: Optional[str] = None
    quote_source: Optional[str] = None
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Calculate derived fields."""
        object.__setattr__(self, 'spread', self.ask_price - self.bid_price)
        object.__setattr__(self, 'midpoint', (self.bid_price + self.ask_price) / 2.0)