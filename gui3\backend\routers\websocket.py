"""
WebSocket router for real-time GUI communication.
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from typing import Dict, Any
import json
import logging

router = APIRouter()
logger = logging.getLogger("GUI.WebSocket")


@router.websocket("/")
async def websocket_endpoint(websocket: WebSocket):
    """Main WebSocket endpoint for GUI clients."""
    
    # Get app state
    app_state = websocket.app.state.app_state
    ws_manager = app_state.ws_manager
    
    # Connect client
    await ws_manager.connect(websocket)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle different message types
            await handle_client_message(websocket, message, app_state)
            
    except WebSocketDisconnect:
        logger.info("Client disconnected normally")
    except Exception as e:
        logger.error(f"WebSocket error: {e}", exc_info=True)
    finally:
        ws_manager.disconnect(websocket)


async def handle_client_message(
    websocket: WebSocket,
    message: Dict[str, Any],
    app_state: Any
) -> None:
    """Handle messages from GUI clients."""
    
    msg_type = message.get('type', '')
    
    if msg_type == 'ping':
        # Simple ping/pong
        await websocket.send_json({'type': 'pong', 'timestamp': message.get('timestamp')})
        
    elif msg_type == 'command':
        # Handle commands (following Horseshoe pattern)
        command = message.get('command', '')
        data = message.get('data', {})
        
        # Send command via Redis
        command_id = await app_state.send_command(command, data)
        
        # Send acknowledgment
        await websocket.send_json({
            'type': 'command_ack',
            'command': command,
            'command_id': command_id,
            'status': 'sent' if command_id else 'failed'
        })
        
    elif msg_type == 'subscribe':
        # Handle subscription requests
        streams = message.get('streams', [])
        # Implementation for selective subscriptions
        await websocket.send_json({
            'type': 'subscribe_ack',
            'streams': streams,
            'status': 'subscribed'
        })
        
    else:
        # Unknown message type
        await websocket.send_json({
            'type': 'error',
            'message': f'Unknown message type: {msg_type}'
        })