"""
IntelliSense Data Sources Package

This package contains data source implementations for the IntelliSense system,
including Redis-based data sources for real-time TESTRADE integration.
"""

from .redis_ocr_raw_datasource import RedisRawOCRDataSource
from .redis_ocr_cleaned_datasource import RedisCleanedOCRDataSource
from .redis_market_data_source import RedisMarketDataDataSource
from .redis_broker_datasource import RedisBrokerDataSource

# Import legacy datasources for compatibility
from ..legacy_datasources import (
    LiveOCRDataSource, IntelliSenseOCRReplaySource,
    LivePriceDataSource, IntelliSensePriceReplaySource,
    LiveBrokerDataSource, IntelliSenseBrokerReplaySource
)

__all__ = [
    'RedisRawOCRDataSource',
    'RedisCleanedOCRDataSource',
    'RedisMarketDataDataSource',
    'RedisBrokerDataSource',
    'LiveOCRDataSource',
    'IntelliSenseOCRReplaySource',
    'LivePriceDataSource',
    'IntelliSensePriceReplaySource',
    'LiveBrokerDataSource',
    'IntelliSenseBrokerReplaySource'
]
