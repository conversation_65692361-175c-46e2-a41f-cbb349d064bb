# time_sync.py

import logging
from typing import Optional, Any

###############################################
# Imports for the current price repository
###############################################
from interfaces.data.services import IPriceProvider

###############################################
# Helper: Check if Rolling Dict is "All Unique"
###############################################
def all_prices_unique(rpd) -> bool:
    """
    Returns True if all price values in the rolling data (rpd.data)
    are unique (i.e., no duplicates).

    :param rpd: RollingPriceDeque object from price_fetching_module
    :return: bool, True if no duplicate prices, else False
    """
    # If there's no rolling data or it's empty, we can't measure uniqueness.
    if not rpd or not rpd.data:
        return False

    seen = set()
    for (ts, px) in rpd.data:
        if px in seen:
            # Found a duplicate => not unique
            return False
        seen.add(px)
    # If we never saw a duplicate => it's all unique
    return True

###############################################
# Main Entry: On Trader's Live Price Update
###############################################
def on_trader_price_update(symbol: str, trader_live_price: float, ocr_frame_timestamp: float,
                          price_provider_ref: IPriceProvider, benchmarker_ref: Optional[Any],
                          config_ref: Optional[Any]):
    """
    Called whenever we have a fresh 'trader_live_price' for 'symbol' derived from OCR.

    Args:
        symbol: The symbol being analyzed
        trader_live_price: The price derived from OCR (cost_basis + pnl_per_share)
        ocr_frame_timestamp: The timestamp when the OCR frame was captured
        price_provider_ref: Reference to the price provider instance
        benchmarker_ref: Reference to the benchmarker instance (optional)
        config_ref: Reference to the config instance (optional)

    Steps:
     1) Get the rolling data for this symbol
     2) Check if prices are all unique => if not, skip
     3) Find matching price and measure offset => capture in PerformanceBenchmarker
    """
    if not price_provider_ref:
        logging.warning("Price provider reference not provided, can't measure time offset.")
        return

    # Get price tolerance from config, default to 0.01 (1 cent)
    price_tolerance = 0.01  # Default
    if config_ref and hasattr(config_ref, 'feed_latency_price_match_tolerance_cents'):
        price_tolerance = config_ref.feed_latency_price_match_tolerance_cents

    # 1) Retrieve the rolling data for this symbol
    rpd = price_provider_ref.get_rolling_data(symbol)
    if not rpd:
        # If no rolling data exists for this symbol, we can't measure offset
        logging.debug(f"[time_sync] No rolling data for {symbol}, skipping latency measurement")
        return

    # 2) Check if the rolling dictionary is "all unique"
    if not all_prices_unique(rpd):
        # If it's not unique, we skip => we only measure offset in a best-case scenario
        logging.debug(f"[time_sync] Prices not unique for {symbol}, skipping latency measurement")
        return

    # 3) Find matching price in rolling data
    matched_market_data_ts = None

    # Iterate through (ts, px) tuples to find matching price within tolerance
    for (ts, px) in rpd.data:
        if abs(px - trader_live_price) <= price_tolerance:
            matched_market_data_ts = ts
            break  # Use first match (chronologically earliest)

    if matched_market_data_ts is not None:
        # Calculate offset: matched_market_data_ts - ocr_frame_timestamp
        offset_sec = matched_market_data_ts - ocr_frame_timestamp
        offset_ms = offset_sec * 1000.0  # Convert to milliseconds

        # Capture metric in PerformanceBenchmarker if available
        if benchmarker_ref:
            context_for_metric = {
                'symbol': symbol,
                'ocr_ts': ocr_frame_timestamp,
                'market_ts': matched_market_data_ts
            }
            benchmarker_ref.capture_metric("latency.ocr_gui_to_matched_market_price_ms", offset_ms, context=context_for_metric)

            offset_type = "positive" if offset_ms > 0 else "negative" if offset_ms < 0 else "zero"
            logging.debug(f"[time_sync] {offset_type} offset for {symbol} => {offset_ms:.2f} ms (captured in benchmarker)")
        else:
            logging.debug(f"[time_sync] Offset calculated for {symbol} => {offset_ms:.2f} ms (no benchmarker available)")
    else:
        logging.debug(f"[time_sync] No matching price found for {symbol} at {trader_live_price:.2f} (tolerance: {price_tolerance})")

###############################################
# End of time_sync.py
###############################################
