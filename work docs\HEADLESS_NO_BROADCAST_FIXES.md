# Headless No-Broadcast Mode Fixes

## Summary
Fixed all IPC publishers to check the `ENABLE_IPC_DATA_DUMP` configuration flag before attempting to send data to <PERSON><PERSON><PERSON>. This ensures that when running in "headless no-broadcast" mode, no data is sent to Redis.

## Files Modified

### 1. modules/ocr/ocr_data_conditioning_service.py
- **Method**: `_publish_cleaned_ocr_to_redis()`
- **Fix**: Added check for `ENABLE_IPC_DATA_DUMP` before sending cleaned OCR data

### 2. modules/order_management/order_repository.py
- **Method**: `_publish_order_rejection_to_redis()`
- **Fix**: Added check for `ENABLE_IPC_DATA_DUMP` before sending order rejections

### 3. modules/broker_bridge/lightspeed_broker.py
- **Method**: `_publish_broker_raw_message()`
- **Method**: `_publish_broker_error_to_redis()`
- **Fix**: Added check for `ENABLE_IPC_DATA_DUMP` before sending broker messages and errors

### 4. modules/trade_management/trade_manager_service.py
- **Method**: `_publish_trading_state_change_to_redis()`
- **Fix**: Added check for `ENABLE_IPC_DATA_DUMP` before sending trading state changes

### 5. modules/trade_management/trade_lifecycle_manager_service.py
- **Method**: `_publish_trade_lifecycle_event_to_redis()`
- **Fix**: Added check for `ENABLE_IPC_DATA_DUMP` before sending trade lifecycle events

### 6. modules/roi/roi_service.py
- **Method**: `publish_roi_update()`
- **Fix**: Added check for `ENABLE_IPC_DATA_DUMP` before sending ROI updates

### 7. modules/gui_commands/gui_command_service.py
- **Method**: `_publish_enhanced_position_summary()`
- **Fix**: Added check for `ENABLE_IPC_DATA_DUMP` before sending enhanced position summaries

### 8. core/application_core.py
- **Method**: `_handle_raw_image_grab_data()`
- **Fix**: Added check for `ENABLE_IPC_DATA_DUMP` before sending raw image grab data

## How It Works

When `ENABLE_IPC_DATA_DUMP` is set to `false` in the configuration:
- All IPC publishing is skipped
- Debug messages are logged instead: "TANK_MODE: Skipping IPC send for [data type] - ENABLE_IPC_DATA_DUMP is False"
- The system continues to process data internally but doesn't send anything to Redis

## Usage

The `start_testrade_select_mode.bat` script automatically sets this flag:
- **Broadcast Mode**: `ENABLE_IPC_DATA_DUMP = true`
- **Headless Buffered Mode**: `ENABLE_IPC_DATA_DUMP = true`
- **Headless No-Broadcast Mode**: `ENABLE_IPC_DATA_DUMP = false`

## Verification

To verify the fixes are working:
1. Run in headless no-broadcast mode
2. Check the logs - you should see "TANK_MODE: Skipping IPC send..." messages
3. Verify no data is being sent to Redis streams