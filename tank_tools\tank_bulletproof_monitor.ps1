# tank_bulletproof_monitor.ps1 - Enhanced monitoring for Bulletproof IPC system
param(
    [string]$ProcessName = "ApplicationCore",
    [int]$IntervalSeconds = 15,
    [int]$GraphWidth = 80,
    [string]$LogFile = "tank_bulletproof_detailed.csv",
    [switch]$MonitorMmapFiles = $true,
    [switch]$CheckRedisConnection = $true,
    [string]$RedisHost = "127.0.0.1",
    [int]$RedisPort = 6379
)

# Initialize
$startTime = Get-Date
$maxMemory = 0
$measurements = @()
$mmapPaths = @(
    "\dev\shm\bulletproof_ipc_buffer_trading.mmap",
    "\dev\shm\bulletproof_ipc_buffer_system.mmap", 
    "\dev\shm\bulletproof_ipc_buffer_bulk.mmap"
)

# Create enhanced CSV header
"Timestamp,ElapsedMinutes,MemoryMB,MemoryGB,CPUPercent,HandleCount,ThreadCount,WorkingSetPeak,PagedMemory,NonPagedMemory,MmapTradingMB,MmapSystemMB,MmapBulkMB,RedisConnected,ZmqPorts" | Out-File $LogFile

Write-Host "🚀 TANK Bulletproof IPC Monitor Started" -ForegroundColor Green
Write-Host "Process: $ProcessName | Interval: $IntervalSeconds seconds" -ForegroundColor Cyan
Write-Host "Enhanced Features: Mmap Monitoring, Redis Check, ZMQ Port Detection" -ForegroundColor Magenta
Write-Host "Log File: $LogFile" -ForegroundColor Yellow
Write-Host "=" * 100

function Get-MmapFileSize {
    param([string]$Path)
    try {
        if (Test-Path $Path) {
            return [math]::Round((Get-Item $Path).Length / 1MB, 2)
        }
    } catch { }
    return 0
}

function Test-RedisConnection {
    param([string]$Host, [int]$Port)
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $tcpClient.ConnectAsync($Host, $Port).Wait(1000)
        $connected = $tcpClient.Connected
        $tcpClient.Close()
        return $connected
    } catch {
        return $false
    }
}

function Get-ZmqPorts {
    param([int]$ProcessId)
    try {
        $connections = netstat -ano | Where-Object { $_ -match ":555[5-7].*LISTENING.*$ProcessId" }
        return ($connections | Measure-Object).Count
    } catch {
        return 0
    }
}

while ($true) {
    try {
        $process = Get-Process $ProcessName -ErrorAction Stop
        $currentTime = Get-Date
        $elapsed = ($currentTime - $startTime).TotalMinutes
        
        # Standard memory metrics
        $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
        $memoryGB = [math]::Round($process.WorkingSet64 / 1GB, 3)
        $peakMB = [math]::Round($process.PeakWorkingSet64 / 1MB, 2)
        $pagedMB = [math]::Round($process.PagedMemorySize64 / 1MB, 2)
        $nonPagedMB = [math]::Round($process.NonpagedSystemMemorySize64 / 1MB, 2)
        
        # CPU usage
        $cpuBefore = $process.TotalProcessorTime
        Start-Sleep -Milliseconds 100
        $process.Refresh()
        $cpuAfter = $process.TotalProcessorTime
        $cpuUsage = [math]::Round((($cpuAfter - $cpuBefore).TotalMilliseconds / 100), 2)
        
        # Enhanced monitoring
        $mmapSizes = @()
        if ($MonitorMmapFiles) {
            $mmapSizes = $mmapPaths | ForEach-Object { Get-MmapFileSize $_ }
        }
        
        $redisConnected = if ($CheckRedisConnection) { Test-RedisConnection $RedisHost $RedisPort } else { $false }
        $zmqPorts = Get-ZmqPorts $process.Id
        
        # Update max memory
        if ($memoryMB -gt $maxMemory) { $maxMemory = $memoryMB }
        
        # Store measurement
        $measurement = [PSCustomObject]@{
            Time = $currentTime
            ElapsedMin = [math]::Round($elapsed, 1)
            MemoryMB = $memoryMB
            MemoryGB = $memoryGB
            Growth = if ($measurements.Count -gt 0) { $memoryMB - $measurements[-1].MemoryMB } else { 0 }
            CPUPercent = $cpuUsage
            Handles = $process.HandleCount
            Threads = $process.Threads.Count
            MmapSizes = $mmapSizes
            RedisConnected = $redisConnected
            ZmqPorts = $zmqPorts
        }
        $measurements += $measurement
        
        # Visual memory graph
        $graphScale = if ($maxMemory -gt 0) { [math]::Min($GraphWidth, [math]::Max(1, $memoryMB / $maxMemory * $GraphWidth)) } else { 1 }
        $graphBar = "█" * $graphScale + "░" * ($GraphWidth - $graphScale)
        
        # Color coding
        $growthColor = if ($measurement.Growth -gt 50) { "Red" } 
                      elseif ($measurement.Growth -gt 10) { "Yellow" }
                      elseif ($measurement.Growth -lt -10) { "Green" }
                      else { "White" }
        
        # Display current status
        Clear-Host
        Write-Host "🚀 TANK Bulletproof IPC Monitor - Real Time Analysis" -ForegroundColor Green
        Write-Host "=" * 100
        Write-Host "Current Time: $($currentTime.ToString('HH:mm:ss'))" -ForegroundColor Cyan
        Write-Host "Elapsed Time: $([math]::Round($elapsed, 1)) minutes" -ForegroundColor Cyan
        Write-Host ""
        
        Write-Host "💾 MEMORY METRICS:" -ForegroundColor Yellow
        Write-Host "  Current Memory: $memoryMB MB ($memoryGB GB)" -ForegroundColor $growthColor
        Write-Host "  Peak Memory:    $peakMB MB" -ForegroundColor White
        Write-Host "  Growth Rate:    $(if($measurement.Growth -ge 0){'+'})$($measurement.Growth) MB" -ForegroundColor $growthColor
        Write-Host "  Paged Memory:   $pagedMB MB" -ForegroundColor Gray
        Write-Host "  NonPaged Mem:   $nonPagedMB MB" -ForegroundColor Gray
        Write-Host ""
        
        Write-Host "⚡ PERFORMANCE METRICS:" -ForegroundColor Yellow
        Write-Host "  CPU Usage:      $cpuUsage%" -ForegroundColor White
        Write-Host "  Handle Count:   $($process.HandleCount)" -ForegroundColor White
        Write-Host "  Thread Count:   $($process.Threads.Count)" -ForegroundColor White
        Write-Host ""
        
        if ($MonitorMmapFiles) {
            Write-Host "🗂️  BULLETPROOF IPC MMAP BUFFERS:" -ForegroundColor Magenta
            Write-Host "  Trading Buffer: $($mmapSizes[0]) MB" -ForegroundColor $(if($mmapSizes[0] -gt 0) { "Green" } else { "Red" })
            Write-Host "  System Buffer:  $($mmapSizes[1]) MB" -ForegroundColor $(if($mmapSizes[1] -gt 0) { "Green" } else { "Red" })
            Write-Host "  Bulk Buffer:    $($mmapSizes[2]) MB" -ForegroundColor $(if($mmapSizes[2] -gt 0) { "Green" } else { "Red" })
            Write-Host "  Total Mmap:     $([math]::Round(($mmapSizes | Measure-Object -Sum).Sum, 2)) MB" -ForegroundColor Cyan
            Write-Host ""
        }
        
        Write-Host "🔗 CONNECTIVITY STATUS:" -ForegroundColor Yellow
        Write-Host "  Redis ($RedisHost`:$RedisPort): $(if($redisConnected) { '✅ Connected' } else { '❌ Disconnected' })" -ForegroundColor $(if($redisConnected) { "Green" } else { "Red" })
        Write-Host "  ZMQ Listening Ports: $zmqPorts/3 expected" -ForegroundColor $(if($zmqPorts -eq 3) { "Green" } elseif($zmqPorts -gt 0) { "Yellow" } else { "Red" })
        Write-Host ""
        
        Write-Host "📊 MEMORY GRAPH (Scale: 0 - $maxMemory MB):" -ForegroundColor Yellow
        Write-Host "[$graphBar] $memoryMB MB" -ForegroundColor $growthColor
        Write-Host ""
        
        # Enhanced trend analysis
        if ($measurements.Count -ge 5) {
            $recent5 = $measurements | Select-Object -Last 5
            $avgGrowth = ($recent5 | Measure-Object Growth -Average).Average
            $trendText = if ($avgGrowth -gt 5) { "📈 INCREASING" }
                        elseif ($avgGrowth -lt -5) { "📉 DECREASING" }
                        else { "📊 STABLE" }
            
            Write-Host "📈 TREND ANALYSIS (Last 5 measurements):" -ForegroundColor Yellow
            Write-Host "  Average Growth: $('{0:F2}' -f $avgGrowth) MB per interval" -ForegroundColor White
            Write-Host "  Trend Status:   $trendText" -ForegroundColor $(if ($avgGrowth -gt 5) { "Red" } elseif ($avgGrowth -lt -5) { "Green" } else { "Cyan" })
            Write-Host ""
        }
        
        # Enhanced alert conditions
        $alerts = @()
        if ($measurement.Growth -gt 100) {
            $alerts += "🚨 CRITICAL: Memory growth >100MB in $IntervalSeconds seconds!"
        }
        if (-not $redisConnected -and $CheckRedisConnection) {
            $alerts += "🚨 CRITICAL: Redis connection lost!"
        }
        if ($zmqPorts -lt 3) {
            $alerts += "⚠️  WARNING: Missing ZMQ listening ports ($zmqPorts/3)"
        }
        if ($elapsed -gt 30 -and $measurement.Growth -gt 10) {
            $alerts += "⚠️  WARNING: Continued memory growth detected"
        }
        if ($mmapSizes -and ($mmapSizes | Where-Object { $_ -eq 0 }).Count -gt 0) {
            $alerts += "⚠️  WARNING: Some mmap buffers not found"
        }
        
        if ($alerts.Count -gt 0) {
            foreach ($alert in $alerts) {
                Write-Host $alert -ForegroundColor Red -BackgroundColor Yellow
            }
        } elseif ($elapsed -gt 30 -and $measurement.Growth -eq 0 -and $redisConnected -and $zmqPorts -eq 3) {
            Write-Host "✅ EXCELLENT: All systems stable and connected" -ForegroundColor Green
        }
        
        Write-Host ""
        Write-Host "Press Ctrl+C to stop monitoring..." -ForegroundColor Gray
        
        # Enhanced CSV logging
        $mmapSizesStr = if ($mmapSizes.Count -ge 3) { "$($mmapSizes[0]),$($mmapSizes[1]),$($mmapSizes[2])" } else { "0,0,0" }
        "$($currentTime.ToString('yyyy-MM-dd HH:mm:ss')),$([math]::Round($elapsed,1)),$memoryMB,$memoryGB,$cpuUsage,$($process.HandleCount),$($process.Threads.Count),$peakMB,$pagedMB,$nonPagedMB,$mmapSizesStr,$redisConnected,$zmqPorts" | Out-File $LogFile -Append
        
        Start-Sleep $IntervalSeconds
        
    } catch {
        Write-Host "❌ Error: Process '$ProcessName' not found or access denied" -ForegroundColor Red
        Write-Host "Available processes:" -ForegroundColor Yellow
        Get-Process | Where-Object { $_.ProcessName -like "*tank*" -or $_.ProcessName -like "*application*" -or $_.ProcessName -like "*core*" } | Format-Table ProcessName, Id, WorkingSet
        Start-Sleep 5
    }
}
