# file_monitor_clean.ps1 - Simple TESTRADE file output monitor
param(
    [string]$OutputPath = "C:\TESTRADE\data",
    [int]$IntervalSeconds = 5,
    [int]$MonitorMinutes = 5
)

# Simple file tracking
$previousSizes = @{}
$startTime = Get-Date
$measurements = @()

Write-Host "🔍 TESTRADE File Output Monitor" -ForegroundColor Green
Write-Host "Path: $OutputPath" -ForegroundColor Cyan
Write-Host "Interval: $IntervalSeconds seconds" -ForegroundColor Cyan
Write-Host "Duration: $MonitorMinutes minutes" -ForegroundColor Cyan
Write-Host "=" * 60

$endTime = $startTime.AddMinutes($MonitorMinutes)
$intervalCount = 0

while ((Get-Date) -lt $endTime) {
    $intervalCount++
    $currentTime = Get-Date
    
    try {
        # Check if TESTRADE process is running
        $pythonProcesses = Get-Process python -ErrorAction SilentlyContinue
        $testradeRunning = $false
        
        if ($pythonProcesses) {
            foreach ($proc in $pythonProcesses) {
                try {
                    $cmdLine = (Get-CimInstance Win32_Process -Filter "ProcessId = $($proc.Id)" -ErrorAction SilentlyContinue).CommandLine
                    if ($cmdLine -and ($cmdLine -like "*testrade*" -or $cmdLine -like "*ApplicationCore*")) {
                        $testradeRunning = $true
                        break
                    }
                } catch { }
            }
        }
        
        # Measure file changes
        $currentFiles = @{}
        $totalGrowth = 0
        $newFiles = 0
        $modifiedFiles = 0
        
        if (Test-Path $OutputPath) {
            Get-ChildItem -Path $OutputPath -Recurse -File -ErrorAction SilentlyContinue | ForEach-Object {
                $currentFiles[$_.FullName] = $_.Length
                
                if ($previousSizes.ContainsKey($_.FullName)) {
                    $growth = $_.Length - $previousSizes[$_.FullName]
                    if ($growth -gt 0) {
                        $totalGrowth += $growth
                        $modifiedFiles++
                    }
                } else {
                    $totalGrowth += $_.Length
                    $newFiles++
                }
            }
        }
        
        $previousSizes = $currentFiles
        
        # Calculate rates
        $growthMB = [math]::Round($totalGrowth / 1MB, 3)
        $growthKBperSec = [math]::Round(($totalGrowth / 1KB) / $IntervalSeconds, 1)
        $growthMBperSec = [math]::Round($growthMB / $IntervalSeconds, 3)
        
        # Store measurement
        $measurement = [PSCustomObject]@{
            Timestamp = $currentTime
            ElapsedMinutes = [math]::Round(($currentTime - $startTime).TotalMinutes, 1)
            GrowthMB = $growthMB
            MBperSecond = $growthMBperSec
            KBperSecond = $growthKBperSec
            NewFiles = $newFiles
            ModifiedFiles = $modifiedFiles
            TotalFiles = $currentFiles.Count
            ProcessRunning = $testradeRunning
        }
        $measurements += $measurement
        
        # Display
        Clear-Host
        Write-Host "🔍 TESTRADE File Output Monitor - Live" -ForegroundColor Green
        Write-Host "=" * 60
        Write-Host "Time: $($currentTime.ToString('HH:mm:ss'))" -ForegroundColor Cyan
        Write-Host "Elapsed: $($measurement.ElapsedMinutes) minutes" -ForegroundColor Cyan
        
        if ($testradeRunning) {
            Write-Host "TESTRADE Status: 🟢 RUNNING" -ForegroundColor Green
        } else {
            Write-Host "TESTRADE Status: 🔴 NOT DETECTED" -ForegroundColor Red
        }
        Write-Host ""
        
        # Current interval
        Write-Host "📊 CURRENT INTERVAL ($IntervalSeconds seconds):" -ForegroundColor Yellow
        Write-Host "  Data Output:    $growthMB MB" -ForegroundColor White
        
        $rateColor = "Green"
        if ($growthMBperSec -gt 10) { $rateColor = "Red" }
        elseif ($growthMBperSec -gt 5) { $rateColor = "Yellow" }
        
        Write-Host "  Output Rate:    $growthMBperSec MB/s" -ForegroundColor $rateColor
        Write-Host "  Output Rate:    $growthKBperSec KB/s" -ForegroundColor White
        Write-Host "  New Files:      $newFiles" -ForegroundColor White
        Write-Host "  Modified Files: $modifiedFiles" -ForegroundColor White
        Write-Host "  Total Files:    $($currentFiles.Count)" -ForegroundColor White
        Write-Host ""
        
        # Running stats
        if ($measurements.Count -ge 3) {
            $recent = $measurements | Select-Object -Last 5
            $avgMBperSec = [math]::Round(($recent | Measure-Object MBperSecond -Average).Average, 3)
            $totalOutputMB = [math]::Round(($measurements | Measure-Object GrowthMB -Sum).Sum, 2)
            
            Write-Host "📈 RUNNING STATISTICS:" -ForegroundColor Yellow
            Write-Host "  Average Rate:   $avgMBperSec MB/s (last 5)" -ForegroundColor White
            Write-Host "  Total Output:   $totalOutputMB MB" -ForegroundColor White
            if ($measurement.ElapsedMinutes -gt 0) {
                $sessionRate = [math]::Round($totalOutputMB / $measurement.ElapsedMinutes, 2)
                Write-Host "  Session Rate:   $sessionRate MB/min" -ForegroundColor White
            }
            Write-Host ""
        }
        
        # Alerts
        if ($growthMBperSec -gt 50) {
            Write-Host "🚨 HIGH OUTPUT RATE!" -ForegroundColor Red -BackgroundColor Yellow
        } elseif ($growthMBperSec -gt 20) {
            Write-Host "⚠️  Elevated output rate" -ForegroundColor Yellow
        }
        
        if (!$testradeRunning) {
            Write-Host "⚠️  TESTRADE not detected" -ForegroundColor Yellow
        }
        
        Write-Host ""
        $totalIntervals = [math]::Ceiling($MonitorMinutes * 60 / $IntervalSeconds)
        Write-Host "Interval $intervalCount of $totalIntervals - Next in $IntervalSeconds seconds..." -ForegroundColor Gray
        
        Start-Sleep $IntervalSeconds
        
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        Start-Sleep 2
    }
}

# Final summary
Write-Host ""
Write-Host "📋 FINAL SUMMARY:" -ForegroundColor Green

if ($measurements.Count -gt 0) {
    $totalMB = ($measurements | Measure-Object GrowthMB -Sum).Sum
    $avgMBperSec = ($measurements | Measure-Object MBperSecond -Average).Average
    $maxMBperSec = ($measurements | Measure-Object MBperSecond -Maximum).Maximum
    $totalMinutes = $measurements[-1].ElapsedMinutes
    
    Write-Host "Total Data Output: $([math]::Round($totalMB, 2)) MB" -ForegroundColor White
    Write-Host "Average Rate: $([math]::Round($avgMBperSec, 3)) MB/s" -ForegroundColor White
    Write-Host "Peak Rate: $([math]::Round($maxMBperSec, 3)) MB/s" -ForegroundColor White
    Write-Host "Session Duration: $totalMinutes minutes" -ForegroundColor White
    
    if ($totalMinutes -gt 0) {
        Write-Host "Overall Rate: $([math]::Round($totalMB / $totalMinutes, 2)) MB/min" -ForegroundColor White
    }
} else {
    Write-Host "No measurements collected" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "File monitoring completed!" -ForegroundColor Green
