#!/usr/bin/env python3
"""
Safe diagnostic runner to identify Python environment and path issues.
This script helps diagnose the symbol loader hanging problem.
"""

import sys
import os
from pathlib import Path
import logging

def setup_minimal_logging():
    """Set up minimal logging to avoid potential logging deadlocks."""
    logging.basicConfig(
        level=logging.CRITICAL,
        format='%(levelname)s: %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )

def diagnose_environment():
    """Diagnose Python environment and path configuration."""
    print("=" * 60)
    print("PYTHON ENVIRONMENT DIAGNOSTICS")
    print("=" * 60)
    
    # Basic Python info
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    
    # Working directory info
    print(f"\nCurrent working directory: {os.getcwd()}")
    print(f"Script location: {__file__}")
    print(f"Script directory: {os.path.dirname(os.path.abspath(__file__))}")
    
    # Calculate project paths
    script_dir = Path(__file__).resolve().parent
    base_dir = script_dir.parent
    data_dir = base_dir / 'data'
    utils_dir = base_dir / 'utils'
    
    print(f"\nProject paths:")
    print(f"Base directory: {base_dir}")
    print(f"Data directory: {data_dir}")
    print(f"Utils directory: {utils_dir}")
    
    # Check file existence
    print(f"\nFile existence checks:")
    symbol_file = data_dir / 'tradeable_symbols.csv'
    symbol_loader = utils_dir / 'symbol_loader.py'
    
    print(f"Symbol CSV exists: {symbol_file.exists()} ({symbol_file})")
    print(f"Symbol loader exists: {symbol_loader.exists()} ({symbol_loader})")
    
    if symbol_file.exists():
        try:
            size = symbol_file.stat().st_size
            print(f"Symbol CSV size: {size} bytes")
        except Exception as e:
            print(f"Error reading symbol CSV stats: {e}")
    
    # Python path info
    print(f"\nPython path ({len(sys.path)} entries):")
    for i, path in enumerate(sys.path):
        print(f"  {i}: {path}")
    
    return base_dir, data_dir, utils_dir

def test_basic_imports():
    """Test basic Python imports to identify hanging points."""
    print("\n" + "=" * 60)
    print("BASIC IMPORT TESTS")
    print("=" * 60)
    
    # Test standard library imports
    test_imports = [
        ('os', 'import os'),
        ('sys', 'import sys'),
        ('csv', 'import csv'),
        ('re', 'import re'),
        ('logging', 'import logging'),
        ('pathlib', 'from pathlib import Path'),
    ]
    
    for name, import_stmt in test_imports:
        try:
            print(f"Testing {name}... ", end='', flush=True)
            exec(import_stmt)
            print("✅ OK")
        except Exception as e:
            print(f"❌ FAILED: {e}")
    
    return True

def test_file_operations():
    """Test basic file operations."""
    print("\n" + "=" * 60)
    print("FILE OPERATION TESTS")
    print("=" * 60)
    
    base_dir = Path(__file__).resolve().parent.parent
    data_dir = base_dir / 'data'
    symbol_file = data_dir / 'tradeable_symbols.csv'
    
    # Test directory access
    try:
        print(f"Testing data directory access... ", end='', flush=True)
        files = list(data_dir.iterdir()) if data_dir.exists() else []
        print(f"✅ OK ({len(files)} files)")
        for f in files[:5]:  # Show first 5 files
            print(f"  - {f.name}")
    except Exception as e:
        print(f"❌ FAILED: {e}")
    
    # Test CSV file reading
    if symbol_file.exists():
        try:
            print(f"Testing CSV file reading... ", end='', flush=True)
            with open(symbol_file, 'r', encoding='utf-8-sig') as f:
                first_line = f.readline().strip()
                line_count = sum(1 for _ in f)
            print(f"✅ OK ({line_count + 1} lines, first: '{first_line}')")
        except Exception as e:
            print(f"❌ FAILED: {e}")
    else:
        print(f"❌ Symbol file not found: {symbol_file}")

def test_symbol_loader_import():
    """Test symbol loader import with detailed error reporting."""
    print("\n" + "=" * 60)
    print("SYMBOL LOADER IMPORT TEST")
    print("=" * 60)
    
    # Add project root to path
    base_dir = Path(__file__).resolve().parent.parent
    if str(base_dir) not in sys.path:
        sys.path.insert(0, str(base_dir))
        print(f"Added to Python path: {base_dir}")
    
    # Test import step by step
    try:
        print("Testing utils module import... ", end='', flush=True)
        import utils
        print("✅ OK")
        
        print("Testing symbol_loader import... ", end='', flush=True)
        from utils import symbol_loader
        print("✅ OK")
        
        print("Testing symbol sets import... ", end='', flush=True)
        from utils.symbol_loader import tradeable_symbols, non_tradeable_symbols
        print("✅ OK")
        
        print(f"Initial symbol counts: tradeable={len(tradeable_symbols)}, non_tradeable={len(non_tradeable_symbols)}")
        
        # Test loading function
        print("Testing load_symbols_from_csv function... ", end='', flush=True)
        from utils.symbol_loader import load_symbols_from_csv
        print("✅ OK")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main diagnostic function."""
    setup_minimal_logging()
    
    print("Starting Python environment diagnostics...")
    print("This script will help identify the symbol loader hanging issue.")
    
    try:
        # Step 1: Environment diagnosis
        base_dir, data_dir, utils_dir = diagnose_environment()
        
        # Step 2: Basic imports
        test_basic_imports()
        
        # Step 3: File operations
        test_file_operations()
        
        # Step 4: Symbol loader import
        success = test_symbol_loader_import()
        
        print("\n" + "=" * 60)
        print("DIAGNOSTIC SUMMARY")
        print("=" * 60)
        
        if success:
            print("✅ All diagnostics passed!")
            print("The symbol loader should be working correctly.")
        else:
            print("❌ Symbol loader import failed.")
            print("Check the error messages above for specific issues.")
        
        print("\nNext steps:")
        print("1. If imports work here, the issue might be terminal-specific")
        print("2. Try running this script in different terminals (CMD vs PowerShell)")
        print("3. Check if virtual environment is properly configured")
        
    except Exception as e:
        print(f"\n❌ Diagnostic script failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
