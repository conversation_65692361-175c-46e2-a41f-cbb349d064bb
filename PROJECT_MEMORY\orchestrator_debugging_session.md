# OCR Orchestrator Queue Backup Debugging Session

## PROBLEM SUMMARY
- OCR orchestrator queue backing up to 90+ items (should be ~0-1 with powerful hardware)
- Worker thread appears to be hanging/dying silently during symbol processing
- Application starts successfully but fails to process OCR signals through to broker trades

## DIAGNOSTIC FINDINGS

### ✅ **Startup Issue FIXED**
- **Original Error**: `ServiceNotRegisteredError: Service not registered: IOrderStrategy`
- **Root Cause**: Incomplete migration from scattered DI registration to centralized system
- **Fix Applied**: Added `IOrderStrategy` registration to `core/di_registration.py:253-269` and removed old duplicate registrations

### 🔍 **Current Issue: Worker Thread Hanging**
- **Symptoms**: 
  - Queue grows from 1 → 90+ items over time
  - Worker dequeues items but never completes processing
  - Last worker activity: `"Starting symbol processing loop for 1 symbols"`
  - No error logs, suggesting silent thread death

- **Suspected Location**: `_process_symbol_state_change()` method in OCR orchestrator service

## DEBUG LOGGING ADDED

### File: `/mnt/c/TESTRADE/modules/trade_management/ocr_scalping_signal_orchestrator_service.py`

Added **19 critical debug checkpoints** to pinpoint exact hang location:

```python
# Checkpoint 1 - Method entry
logger.critical(f"HANG_DEBUG: Checkpoint 1 - Started _process_symbol_state_change for {symbol_upper}")

# Checkpoint 2-4 - State lock operations
logger.critical(f"HANG_DEBUG: Checkpoint 2 - About to acquire state lock for {symbol_upper}")
# ... (inside with self._state_lock:)
logger.critical(f"HANG_DEBUG: Checkpoint 3 - Acquired state lock for {symbol_upper}")
# ... (after with block)
logger.critical(f"HANG_DEBUG: Checkpoint 4 - Released state lock for {symbol_upper}")

# Checkpoint 5-7 - State change detection
logger.critical(f"HANG_DEBUG: Checkpoint 5 - About to check for state changes for {symbol_upper}")
# if no change: Checkpoint 6
# if change detected: Checkpoint 7

# Checkpoint 8-9 - PositionManager calls
logger.critical(f"HANG_DEBUG: Checkpoint 8 - About to call PositionManager.get_position for {symbol_upper}")
pos_data = self.position_manager.get_position(symbol_upper) or {}
logger.critical(f"HANG_DEBUG: Checkpoint 9 - PositionManager.get_position returned for {symbol_upper}")

# Checkpoint 10-13 - SnapshotInterpreterService
logger.critical(f"HANG_DEBUG: Checkpoint 10 - About to call SnapshotInterpreterService for {symbol_upper}")
logger.critical(f"HANG_DEBUG: Checkpoint 11 - Calling interpret_single_snapshot for {symbol_upper}")
# ... service call ...
logger.critical(f"HANG_DEBUG: Checkpoint 12 - interpret_single_snapshot returned for {symbol_upper}")

# Checkpoint 14-18 - MasterActionFilterService
logger.critical(f"HANG_DEBUG: Checkpoint 14 - About to call MasterActionFilterService for {symbol_upper}")
logger.critical(f"HANG_DEBUG: Checkpoint 15 - Calling filter_signal for {symbol_upper}")
# ... service call ...
logger.critical(f"HANG_DEBUG: Checkpoint 16 - filter_signal returned for {symbol_upper}")

# Checkpoint 19a-19b - Price provider calls
logger.critical(f"HANG_DEBUG: Checkpoint 19a - About to call price_provider.get_reference_price for {symbol_upper}")
current_price = self.price_provider.get_reference_price(symbol_upper)
logger.critical(f"HANG_DEBUG: Checkpoint 19b - price_provider.get_reference_price returned {current_price} for {symbol_upper}")

# Checkpoint 19 - Method completion
logger.critical(f"HANG_DEBUG: Checkpoint 19 - COMPLETED _process_symbol_state_change for {symbol_upper}")

# Exception handling
except Exception as e:
    logger.critical(f"HANG_DEBUG: EXCEPTION in _process_symbol_state_change for {symbol_upper}: {e}")
```

## ARCHITECTURE CONTEXT

### Service Dependencies in Processing Chain:
1. **PositionManager** - Tracks current positions
2. **SnapshotInterpreterService** - Analyzes OCR data for trade signals  
3. **MasterActionFilterService** - Filters/validates trade signals
4. **PriceProvider** - Gets current market prices
5. **EventBus** - Publishes order request events

### DI Container Migration Status:
- ✅ **IOCRServiceFactory** - Registered and working
- ✅ **IOrderStrategy** - Registered and working  
- ✅ **Centralized registration** - Active in `core/di_registration.py`
- ✅ **Service factories** - Working via `ServiceFactoryRegistry`

## 🎯 **ROOT CAUSE IDENTIFIED: EventBus.publish() Blocking**

### BREAKTHROUGH - Debug Results:
- ✅ **Orchestrator processing works perfectly** - all checkpoints 1-19b pass
- ✅ **OrderRequestEvent created successfully**: 
  - Symbol: YIBO, Action: BUY 1000 shares @ $1.63 limit
  - Request ID: `OPEN_LONG_YIBO_1750991555`
- ✅ **Services properly subscribed**:
  - RiskManagementService subscribed to OrderRequestEvent ✓
  - TradeManagerService subscribed to ValidatedOrderRequestEvent ✓
- ❌ **HANG POINT**: `self.event_bus.publish(OrderRequestEvent(data=order_request_data))`
- ❌ **Never reaches**: Checkpoint 19 (COMPLETED)

### The Problem:
**EventBus.publish() call is blocking/hanging**, causing worker thread to never complete and queue to back up.

Log evidence:
```
22:32:35,451 - [YIBO] Publishing OrderRequestEvent: [OrderRequestData...]
# <-- HANG HERE, never reaches Checkpoint 19 COMPLETED -->
22:32:37,200 - Next OCR frame processed (queue grows)
```

## NEXT STEPS FOR NEW INSTANCE

1. **FOCUS**: Investigate EventBus.publish() method in `/mnt/c/TESTRADE/core/event_bus.py`
2. **Look for**: Threading issues, deadlocks, or blocking operations in publish mechanism
3. **Check**: Event handler execution - OrderRequestEvent handlers might be blocking
4. **Debug Path**: Add logging to EventBus.publish() to see where it hangs during event delivery

## LOG ANALYSIS COMMANDS

```bash
# Check latest hang debug messages
grep -n "HANG_DEBUG" /mnt/c/TESTRADE/logs/headless_core_runner.log | tail -20

# Check worker activity
grep -n "Worker.*dequeued\|ORCH_QUEUE.*qsize" /mnt/c/TESTRADE/logs/headless_core_runner.log | tail -10

# Check for exceptions
grep -n "EXCEPTION\|ERROR.*orchestrator" /mnt/c/TESTRADE/logs/headless_core_runner.log | tail -5
```

## CRITICAL INSIGHT
The worker thread is **silently dying** without logging completion or errors. This suggests:
- **Blocking call** in one of the service dependencies
- **Deadlock** in threading/locking mechanism  
- **Silent exception** that's not being caught properly

The debug checkpoints will reveal exactly which service call is causing the hang, allowing for targeted resolution.