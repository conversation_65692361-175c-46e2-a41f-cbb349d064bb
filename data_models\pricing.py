# data_models/pricing.py
"""
Pricing data models for the TESTRADE trading system.

This module defines the core data structures for price information,
designed for high-speed premarket trading with emphasis on freshness and clarity.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass(frozen=True)
class PriceData:
    """
    A standardized, immutable snapshot of a price with nanosecond precision.
    
    This is the atomic unit of price information in the trading system.
    Uses perf_counter_ns for high-precision latency tracking in premarket conditions.
    """
    symbol: str
    price: float
    timestamp_ns: int  # time.perf_counter_ns() for nanosecond precision
    source: str        # e.g., 'stream_trade', 'stream_ask', 'rest_trade', 'rest_ask'
    
    @property
    def age_ns(self) -> int:
        """Calculate the age of this price data in nanoseconds."""
        import time
        return time.perf_counter_ns() - self.timestamp_ns
    
    @property
    def age_ms(self) -> float:
        """Calculate the age of this price data in milliseconds."""
        return self.age_ns / 1_000_000.0
    
    @property
    def age_seconds(self) -> float:
        """Calculate the age of this price data in seconds."""
        return self.age_ns / 1_000_000_000.0
    
    def __str__(self) -> str:
        return f"{self.symbol}: ${self.price:.4f} ({self.source}, {self.age_seconds:.3f}s old)"


class StalePriceError(Exception):
    """
    Raised when no fresh price can be obtained within the configured tolerance.
    
    This exception enforces the "fresh data or explicit failure" contract
    of the JIT Price Oracle philosophy.
    """
    def __init__(self, message: str, symbol: Optional[str] = None, staleness_ms: Optional[float] = None):
        super().__init__(message)
        self.symbol = symbol
        self.staleness_ms = staleness_ms


class PriceSourceUnavailableError(Exception):
    """
    Raised when a specific price source (streaming, REST) is completely unavailable.
    
    This allows for more granular error handling than the general StalePriceError.
    """
    def __init__(self, message: str, source: str, symbol: Optional[str] = None):
        super().__init__(message)
        self.source = source
        self.symbol = symbol


class PriceUnavailableError(Exception):
    """
    Raised when no price data is available at all for a symbol.
    
    This exception is used when neither cache nor API fallback can provide
    any price information for a requested symbol.
    """
    def __init__(self, message: str, symbol: Optional[str] = None):
        super().__init__(message)
        self.symbol = symbol