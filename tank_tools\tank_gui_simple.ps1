# tank_gui_simple.ps1 - Simple TANK Tools GUI using Windows Forms
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Create the main form
$form = New-Object System.Windows.Forms.Form
$form.Text = "TANK Tools Control Center"
$form.Size = New-Object System.Drawing.Size(800, 600)
$form.StartPosition = "CenterScreen"
$form.BackColor = [System.Drawing.Color]::FromArgb(46, 52, 64)
$form.ForeColor = [System.Drawing.Color]::White

# Create title label
$titleLabel = New-Object System.Windows.Forms.Label
$titleLabel.Text = "TANK Tools Control Center"
$titleLabel.Font = New-Object System.Drawing.Font("Arial", 16, [System.Drawing.FontStyle]::Bold)
$titleLabel.ForeColor = [System.Drawing.Color]::White
$titleLabel.Location = New-Object System.Drawing.Point(20, 20)
$titleLabel.Size = New-Object System.Drawing.Size(400, 30)
$form.Controls.Add($titleLabel)

# Create subtitle label
$subtitleLabel = New-Object System.Windows.Forms.Label
$subtitleLabel.Text = "Real-time monitoring and analysis for TESTRADE ApplicationCore"
$subtitleLabel.Font = New-Object System.Drawing.Font("Arial", 10)
$subtitleLabel.ForeColor = [System.Drawing.Color]::LightGray
$subtitleLabel.Location = New-Object System.Drawing.Point(20, 50)
$subtitleLabel.Size = New-Object System.Drawing.Size(500, 20)
$form.Controls.Add($subtitleLabel)

# Function to create a button
function New-ToolButton {
    param(
        [string]$Text,
        [System.Drawing.Point]$Location,
        [System.Drawing.Size]$Size,
        [System.Drawing.Color]$BackColor,
        [scriptblock]$ClickAction
    )
    
    $button = New-Object System.Windows.Forms.Button
    $button.Text = $Text
    $button.Location = $Location
    $button.Size = $Size
    $button.BackColor = $BackColor
    $button.ForeColor = [System.Drawing.Color]::White
    $button.Font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Bold)
    $button.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
    $button.FlatAppearance.BorderSize = 0
    $button.Add_Click($ClickAction)
    
    return $button
}

# Function to add output to the text box
function Add-Output {
    param([string]$Message, [string]$Type = "Info")
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    $prefix = switch ($Type) {
        "Success" { "[OK]" }
        "Warning" { "[WARN]" }
        "Error" { "[ERROR]" }
        default { "[INFO]" }
    }
    
    $newText = "$prefix [$timestamp] $Message`r`n" + $outputTextBox.Text
    $outputTextBox.Text = $newText
}

# Function to run a tool script
function Start-ToolScript {
    param([string]$ScriptName, [string]$Arguments = "", [string]$Description)
    
    Add-Output "Starting $Description..." "Info"
    
    try {
        $scriptPath = Join-Path $PSScriptRoot $ScriptName
        if (-not (Test-Path $scriptPath)) {
            Add-Output "Script not found: $ScriptName" "Error"
            return
        }
        
        Add-Output "Launching: $ScriptName $Arguments" "Info"
        
        $processArgs = "-ExecutionPolicy Bypass -WindowStyle Normal -File `"$scriptPath`" $Arguments"
        Start-Process "powershell.exe" -ArgumentList $processArgs -WorkingDirectory $PSScriptRoot
        
        Add-Output "Successfully launched $Description" "Success"
        Add-Output "Check the new PowerShell window for output" "Info"
        
    } catch {
        Add-Output "Error launching $Description`: $($_.Exception.Message)" "Error"
    }
}

# Create Quick Actions section
$quickLabel = New-Object System.Windows.Forms.Label
$quickLabel.Text = "Quick Actions"
$quickLabel.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
$quickLabel.ForeColor = [System.Drawing.Color]::LightBlue
$quickLabel.Location = New-Object System.Drawing.Point(20, 90)
$quickLabel.Size = New-Object System.Drawing.Size(200, 25)
$form.Controls.Add($quickLabel)

# Quick Demo Button
$btnQuickDemo = New-ToolButton -Text "Quick Demo (3 min)" -Location (New-Object System.Drawing.Point(20, 120)) -Size (New-Object System.Drawing.Size(180, 50)) -BackColor ([System.Drawing.Color]::FromArgb(163, 190, 140)) -ClickAction {
    Start-ToolScript "tank_monitor_demo_simple.ps1" "-ProcessName python -DemoMinutes 3" "Quick Demo (3 minutes)"
}
$form.Controls.Add($btnQuickDemo)

# Multi-Process Monitor Button
$btnMultiMonitor = New-ToolButton -Text "Multi-Process Monitor" -Location (New-Object System.Drawing.Point(220, 120)) -Size (New-Object System.Drawing.Size(180, 50)) -BackColor ([System.Drawing.Color]::FromArgb(94, 129, 172)) -ClickAction {
    Start-ToolScript "tank_multi_launcher_simple.ps1" "-Mode monitor" "Multi-Process Monitor"
}
$form.Controls.Add($btnMultiMonitor)

# Leak Detection Button
$btnLeakDetect = New-ToolButton -Text "Leak Detection" -Location (New-Object System.Drawing.Point(420, 120)) -Size (New-Object System.Drawing.Size(180, 50)) -BackColor ([System.Drawing.Color]::FromArgb(191, 97, 106)) -ClickAction {
    Start-ToolScript "tank_leak_quickstart.ps1" "-ProcessName python" "Leak Detection"
}
$form.Controls.Add($btnLeakDetect)

# Memory Monitoring section
$memoryLabel = New-Object System.Windows.Forms.Label
$memoryLabel.Text = "Memory Monitoring"
$memoryLabel.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
$memoryLabel.ForeColor = [System.Drawing.Color]::LightBlue
$memoryLabel.Location = New-Object System.Drawing.Point(20, 190)
$memoryLabel.Size = New-Object System.Drawing.Size(200, 25)
$form.Controls.Add($memoryLabel)

# Memory Monitor Button
$btnMemoryMonitor = New-ToolButton -Text "Memory Monitor" -Location (New-Object System.Drawing.Point(20, 220)) -Size (New-Object System.Drawing.Size(140, 40)) -BackColor ([System.Drawing.Color]::FromArgb(136, 192, 208)) -ClickAction {
    Start-ToolScript "tank_memory_monitor.ps1" "-ProcessName python" "Memory Monitor"
}
$form.Controls.Add($btnMemoryMonitor)

# Bulletproof Monitor Button
$btnBulletproof = New-ToolButton -Text "Bulletproof Monitor" -Location (New-Object System.Drawing.Point(180, 220)) -Size (New-Object System.Drawing.Size(140, 40)) -BackColor ([System.Drawing.Color]::FromArgb(136, 192, 208)) -ClickAction {
    Start-ToolScript "tank_bulletproof_monitor.ps1" "-ProcessName python" "Bulletproof Monitor"
}
$form.Controls.Add($btnBulletproof)

# Data Analyzer Button
$btnAnalyzer = New-ToolButton -Text "Data Analyzer" -Location (New-Object System.Drawing.Point(340, 220)) -Size (New-Object System.Drawing.Size(140, 40)) -BackColor ([System.Drawing.Color]::FromArgb(136, 192, 208)) -ClickAction {
    Start-ToolScript "tank_monitor_analyzer.ps1" "" "Data Analyzer"
}
$form.Controls.Add($btnAnalyzer)

# Baseline & Advanced section
$baselineLabel = New-Object System.Windows.Forms.Label
$baselineLabel.Text = "Baseline & Advanced"
$baselineLabel.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
$baselineLabel.ForeColor = [System.Drawing.Color]::LightBlue
$baselineLabel.Location = New-Object System.Drawing.Point(20, 280)
$baselineLabel.Size = New-Object System.Drawing.Size(200, 25)
$form.Controls.Add($baselineLabel)

# Baseline Compare Button
$btnBaseline = New-ToolButton -Text "Baseline Compare" -Location (New-Object System.Drawing.Point(20, 310)) -Size (New-Object System.Drawing.Size(140, 40)) -BackColor ([System.Drawing.Color]::FromArgb(235, 203, 139)) -ClickAction {
    Start-ToolScript "tank_baseline_simple.ps1" "-ProcessName python -MonitorMinutes 10" "Baseline Comparison"
}
$form.Controls.Add($btnBaseline)

# Create Baseline Button
$btnCreateBaseline = New-ToolButton -Text "Create Baseline" -Location (New-Object System.Drawing.Point(180, 310)) -Size (New-Object System.Drawing.Size(140, 40)) -BackColor ([System.Drawing.Color]::FromArgb(235, 203, 139)) -ClickAction {
    Start-ToolScript "tank_baseline_launcher.ps1" "-Mode create-single -ProcessName python" "Create Baseline"
}
$form.Controls.Add($btnCreateBaseline)

# Process Test Button
$btnProcessTest = New-ToolButton -Text "Process Test" -Location (New-Object System.Drawing.Point(340, 310)) -Size (New-Object System.Drawing.Size(140, 40)) -BackColor ([System.Drawing.Color]::FromArgb(208, 135, 112)) -ClickAction {
    Start-ToolScript "test_python_processes.ps1" "" "Process Detection Test"
}
$form.Controls.Add($btnProcessTest)

# Output section
$outputLabel = New-Object System.Windows.Forms.Label
$outputLabel.Text = "Output & Feedback"
$outputLabel.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
$outputLabel.ForeColor = [System.Drawing.Color]::White
$outputLabel.Location = New-Object System.Drawing.Point(20, 370)
$outputLabel.Size = New-Object System.Drawing.Size(200, 25)
$form.Controls.Add($outputLabel)

# Clear button
$btnClear = New-ToolButton -Text "Clear" -Location (New-Object System.Drawing.Point(700, 370)) -Size (New-Object System.Drawing.Size(60, 25)) -BackColor ([System.Drawing.Color]::FromArgb(191, 97, 106)) -ClickAction {
    $outputTextBox.Text = "Output cleared.`r`n"
}
$form.Controls.Add($btnClear)

# Output text box
$outputTextBox = New-Object System.Windows.Forms.TextBox
$outputTextBox.Multiline = $true
$outputTextBox.ScrollBars = "Vertical"
$outputTextBox.Location = New-Object System.Drawing.Point(20, 400)
$outputTextBox.Size = New-Object System.Drawing.Size(740, 140)
$outputTextBox.BackColor = [System.Drawing.Color]::FromArgb(59, 66, 82)
$outputTextBox.ForeColor = [System.Drawing.Color]::White
$outputTextBox.Font = New-Object System.Drawing.Font("Consolas", 9)
$outputTextBox.ReadOnly = $true
$outputTextBox.Text = "Welcome to TANK Tools Control Center!`r`nClick any button to run monitoring tools...`r`n"
$form.Controls.Add($outputTextBox)

# Form load event
$form.Add_Shown({
    Add-Output "TANK Tools Control Center initialized" "Success"
    Add-Output "Ready to launch monitoring tools" "Info"
    
    # Check for Python processes
    try {
        $processes = Get-Process python -ErrorAction SilentlyContinue
        if ($processes.Count -gt 0) {
            $totalMemoryMB = [math]::Round(($processes | Measure-Object WorkingSet64 -Sum).Sum / 1MB, 2)
            Add-Output "Detected $($processes.Count) Python processes ($totalMemoryMB MB total)" "Success"
        } else {
            Add-Output "No Python processes detected - some tools may not work" "Warning"
        }
    } catch {
        Add-Output "Could not check for Python processes" "Warning"
    }
})

# Show the form
Add-Output "Launching TANK Tools GUI..." "Info"
$form.ShowDialog() | Out-Null
