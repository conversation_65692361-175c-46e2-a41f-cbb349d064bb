# TESTRADE Babysitter Service

## Overview

The Babysitter Service provides **tank mode** operation for TESTRADE by completely isolating the trading core from Redis dependencies. TESTRADE Core can operate independently while the Baby<PERSON>tter handles all Redis operations in the background.

## Architecture

```
TESTRADE Core → ZeroMQ IPC → Babysitter Service → Redis
     ↓                              ↓
  Continues Trading            Disk Buffer
  (Never Blocks)              (Data Persistence)
```

## Features

- **Non-blocking**: TESTRADE Core never waits for Redis operations
- **Data Persistence**: All data buffered to disk during Redis outages
- **Auto-Recovery**: Automatic Redis reconnection and data replay
- **Resilient**: Handles Redis crashes, network issues, and restarts gracefully
- **Cross-Platform**: Uses TCP sockets for reliable communication

## Quick Start

### 1. Start the Babysitter Service
```bash
# Terminal 1: Start Babysitter
python start_babysitter.py
```

### 2. Start TESTRADE Core
```bash
# Terminal 2: Start TESTRADE (after Baby<PERSON><PERSON> is running)
python run_headless_core.py
```

## Configuration

Edit `utils/global_config.py`:

```python
# Babysitter Service Configuration
BABYSITTER_IPC_ADDRESS: str = "tcp://localhost:5555"
BABYSITTER_ENABLED: bool = True
```

## Data Flow

1. **TESTRADE Core** generates trading/OCR/market data
2. **BabysitterIPCClient** sends data via ZeroMQ (non-blocking)
3. **Babysitter Service** receives data and:
   - Writes to disk buffer (always)
   - Publishes to Redis (if available)
   - Queues for retry (if Redis down)

## Redis Outage Handling

When Redis is unavailable:
- TESTRADE continues trading normally
- All data saved to disk buffer
- Babysitter monitors Redis health
- Automatic data replay when Redis returns

## File Structure

```
services/
├── babysitter_service.py    # Main service implementation
├── README.md                # This file
data/
├── babysitter_buffer/       # Disk buffer files
logs/
├── babysitter_service.log   # Service logs
```

## Monitoring

Check service status:
- **Babysitter logs**: `logs/babysitter_service.log`
- **TESTRADE logs**: Look for "BabysitterIPCClient" messages
- **Redis status**: Babysitter logs show connection status

## Troubleshooting

### Babysitter Won't Start
- Check if port 5555 is available
- Ensure `pyzmq` is installed: `pip install pyzmq`
- Check logs in `logs/babysitter_service.log`

### TESTRADE Can't Connect to Babysitter
- Ensure Babysitter is started first
- Check `BABYSITTER_IPC_ADDRESS` in config
- Verify firewall settings for localhost:5555

### Redis Connection Issues
- Babysitter will continue buffering to disk
- Check Redis server status
- Verify Redis configuration in `babysitter_service.py`

## Production Deployment

For production use:
1. Run Babysitter as a system service
2. Configure proper log rotation
3. Monitor disk space for buffer files
4. Set up Redis clustering for high availability

## Tank Mode Benefits

- **Trading Safety**: Redis issues never affect trading operations
- **Data Integrity**: All data preserved during outages
- **Operational Simplicity**: TESTRADE "doesn't know Redis exists"
- **Scalability**: Easy to add multiple Redis instances or clusters
