#pragma once
#if !defined(LS_L_ORDERERRORS_H)
#define LS_L_ORDERERRORS_H

// Copyright (c) 2001-2018 Lightspeed Financial, Inc. All rights reserved.

namespace LightspeedTrader
{
	namespace L_OrderError
	{
		enum SendOrderError
		{
			ORDER_SENT_OK = 0,
			SOES_ORDER_DISABLED = 1,
			MM_IN_EXCLUSION_LIST = 2,
			ZERO_SHARES_ORDERED = 3,
			EXECUTIONS_DISABLED = 4,
			BUYING_POWER_EXCEEDED = 5,
			SHORT_SELL_VIOLATION = 6,
			STOCK_NOT_SHORTABLE = 7,
			EXECUTOR_NOT_CONNECTED = 8,
			MAXORDERSHARES_EXCEEDED = 9,
			WAIT_CONSTRAINT_VIOLATION = 10,
			STOCK_HALTED = 11,
			MKXT_BOOK_OR_KILL = 12,
			SMALL_CAPS_NOT_SOESABLE = 13,
			OWN_CROSSING = 14,
			CANN<PERSON>_TRADE_SYMBOL = 15,
			CANNOT_TRADE_BULLETS_AFTER_MARKET = 16,
			MAXBULLETS_EXCEEDED = 17,
			MARKET_HALTED = 18,
			FUTURES_MARGINABILITY_UNKNOWN = 19,
			NO_EXEMPT_ORDERS = 20,
			TRADINGMONITOR_BLOCKED_ORDER = 21,
			DECLINED_AT_CONFIRM_BY_USER = 22,
			SUPERVISOR_CREATED_NO_ORDERS = 23,
			ROUTING_BLOCKED_ORDER = 24,
			OTHER_REJECTION = 25,
			EXECUTOR_NOT_LOGGED_IN = 26,
			UNINITIALIZED_SUMMARY = 27,
			INVALID_SUMMARY = 28,
			INVALID_ORDER_TYPE = 29,
			DUPLICATE_ORDER_REJECT = 30,
			INSUFFICIENT_SHARES = 31,
			NO_SIMUL_OPEN_CLOSE = 32,
			INVALID_LINK = 33
		};
	}
	namespace L_OrderResult
	{
		using namespace L_OrderError;
	}
}

#endif // !defined(LS_L_ORDERERRORS_H)
