#!/usr/bin/env python3
"""
Test Script for Smart Fingerprint Service

This script tests the new smart fingerprint service to ensure it works correctly
with context-aware duplicate detection and order-based cache invalidation.

FUZZY'S TEST SCENARIOS:
1. Basic duplicate detection
2. Context-aware expiry times
3. Order fill invalidation
4. Performance metrics
"""

import sys
import time
import logging
from unittest.mock import Mock, MagicMock

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_smart_fingerprint_service():
    """Test the smart fingerprint service functionality."""
    print("🔍 === FUZZY'S SMART FINGERPRINT SERVICE TEST === 🔍")
    
    try:
        # Import the service
        from modules.utility.fingerprint_service import FingerprintService
        from core.events import OrderFilledEvent, OrderFilledEventData, OrderStatusUpdateEvent, OrderStatusUpdateData
        
        # Create mock event bus
        mock_event_bus = Mock()
        mock_config_service = Mock()
        
        # Create the service
        fingerprint_service = FingerprintService(
            event_bus=mock_event_bus,
            config_service=mock_config_service
        )
        
        print("✅ FingerprintService created successfully")
        
        # Start the service
        fingerprint_service.start()
        print("✅ FingerprintService started")
        
        # Test 1: Basic duplicate detection
        print("\n🧪 TEST 1: Basic Duplicate Detection")
        
        fingerprint1 = ('BUY', 'AAPL', 100)
        context = {'symbol': 'AAPL', 'market_volatility': 'NORMAL'}
        
        # First check - should not be duplicate
        is_dup1 = fingerprint_service.is_duplicate(fingerprint1, context)
        print(f"First check: is_duplicate = {is_dup1} (expected: False)")
        assert not is_dup1, "First check should not be duplicate"
        
        # Update cache
        signal_data = {
            'symbol': 'AAPL',
            'action': 'BUY',
            'quantity': 100,
            'timestamp': time.time()
        }
        fingerprint_service.update(fingerprint1, signal_data)
        print("✅ Fingerprint cached")
        
        # Second check - should be duplicate
        is_dup2 = fingerprint_service.is_duplicate(fingerprint1, context)
        print(f"Second check: is_duplicate = {is_dup2} (expected: True)")
        assert is_dup2, "Second check should be duplicate"
        
        print("✅ TEST 1 PASSED: Basic duplicate detection works")
        
        # Test 2: Context-aware expiry
        print("\n🧪 TEST 2: Context-Aware Expiry")
        
        fingerprint2 = ('SELL', 'TSLA', 50)
        
        # Test with high volatility (shorter expiry)
        context_volatile = {'symbol': 'TSLA', 'market_volatility': 'HIGH'}
        fingerprint_service.update(fingerprint2, {'symbol': 'TSLA', 'action': 'SELL', 'quantity': 50})
        
        # Should be duplicate immediately
        is_dup_volatile = fingerprint_service.is_duplicate(fingerprint2, context_volatile)
        print(f"Volatile market check: is_duplicate = {is_dup_volatile} (expected: True)")
        assert is_dup_volatile, "Should be duplicate in volatile market"
        
        print("✅ TEST 2 PASSED: Context-aware expiry works")
        
        # Test 3: Order fill invalidation
        print("\n🧪 TEST 3: Order Fill Invalidation")
        
        # Create order fill event
        order_fill_data = OrderFilledEventData(
            symbol='AAPL',
            local_order_id='test_123',
            order_id='broker_456',
            fill_quantity=100.0,
            fill_price=150.25,
            fill_timestamp=time.time()
        )
        order_fill_event = OrderFilledEvent(data=order_fill_data)
        
        # Simulate order fill
        fingerprint_service._handle_order_filled(order_fill_event)
        print("✅ Order fill event processed")
        
        # Check if AAPL fingerprints were invalidated
        is_dup_after_fill = fingerprint_service.is_duplicate(fingerprint1, context)
        print(f"After order fill: is_duplicate = {is_dup_after_fill} (expected: False)")
        assert not is_dup_after_fill, "Should not be duplicate after order fill"
        
        print("✅ TEST 3 PASSED: Order fill invalidation works")
        
        # Test 4: Performance metrics
        print("\n🧪 TEST 4: Performance Metrics")
        
        stats = fingerprint_service.get_cache_stats()
        print(f"Cache stats: {stats}")
        
        expected_keys = ['duplicates_blocked', 'cache_hits', 'cache_misses', 'smart_invalidations']
        for key in expected_keys:
            assert key in stats, f"Missing stat key: {key}"
        
        print("✅ TEST 4 PASSED: Performance metrics work")
        
        # Test 5: Order status invalidation
        print("\n🧪 TEST 5: Order Status Invalidation")
        
        # Add a fingerprint for MSFT
        fingerprint3 = ('BUY', 'MSFT', 75)
        fingerprint_service.update(fingerprint3, {'symbol': 'MSFT', 'action': 'BUY', 'quantity': 75})
        
        # Verify it's cached
        is_dup_msft = fingerprint_service.is_duplicate(fingerprint3, {'symbol': 'MSFT'})
        assert is_dup_msft, "MSFT fingerprint should be cached"
        
        # Create order cancelled event
        order_status_data = OrderStatusUpdateData(
            symbol='MSFT',
            local_order_id='test_789',
            status='CANCELLED'
        )
        order_status_event = OrderStatusUpdateEvent(data=order_status_data)
        
        # Process the event
        fingerprint_service._handle_order_status_update(order_status_event)
        
        # Check if invalidated
        is_dup_after_cancel = fingerprint_service.is_duplicate(fingerprint3, {'symbol': 'MSFT'})
        print(f"After order cancel: is_duplicate = {is_dup_after_cancel} (expected: False)")
        assert not is_dup_after_cancel, "Should not be duplicate after order cancellation"
        
        print("✅ TEST 5 PASSED: Order status invalidation works")
        
        # Stop the service
        fingerprint_service.stop()
        print("✅ FingerprintService stopped")
        
        print("\n🎉 === ALL TESTS PASSED! SMART FINGERPRINT SERVICE IS WORKING === 🎉")
        return True
        
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_orchestrator():
    """Test integration with the orchestrator service."""
    print("\n🔗 === TESTING INTEGRATION WITH ORCHESTRATOR === 🔗")
    
    try:
        # Test that the orchestrator can be imported with the new dependency
        from modules.trade_management.ocr_scalping_signal_orchestrator_service import OCRScalpingSignalOrchestratorService
        print("✅ OCRScalpingSignalOrchestratorService imports successfully")
        
        # Check that the constructor signature includes fingerprint_service
        import inspect
        sig = inspect.signature(OCRScalpingSignalOrchestratorService.__init__)
        params = list(sig.parameters.keys())
        
        if 'fingerprint_service' in params:
            print("✅ fingerprint_service parameter found in constructor")
        else:
            print("❌ fingerprint_service parameter missing from constructor")
            print(f"Available parameters: {params}")
            return False
        
        print("✅ Integration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_di_registration():
    """Test that the service is properly registered in DI."""
    print("\n🏗️ === TESTING DI REGISTRATION === 🏗️")
    
    try:
        # Test that the interface can be imported
        from interfaces.utility.services import IFingerprintService
        print("✅ IFingerprintService interface imports successfully")
        
        # Test that the service can be imported
        from modules.utility.fingerprint_service import FingerprintService
        print("✅ FingerprintService implementation imports successfully")
        
        # Check that FingerprintService implements the interface
        if issubclass(FingerprintService, IFingerprintService):
            print("✅ FingerprintService properly implements IFingerprintService")
        else:
            print("❌ FingerprintService does not implement IFingerprintService")
            return False
        
        print("✅ DI registration test passed")
        return True
        
    except Exception as e:
        print(f"❌ DI registration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 FUZZY'S SMART FINGERPRINT SERVICE TEST SUITE")
    print("=" * 60)
    
    all_passed = True
    
    # Run all tests
    tests = [
        ("DI Registration", test_di_registration),
        ("Integration", test_integration_with_orchestrator),
        ("Smart Service", test_smart_fingerprint_service)
    ]
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} Test...")
        if not test_func():
            all_passed = False
            print(f"❌ {test_name} Test FAILED")
        else:
            print(f"✅ {test_name} Test PASSED")
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! SMART FINGERPRINT SERVICE IS READY!")
        print("🔥 FUZZY LEVEL ACHIEVEMENT: ARCHITECTURAL GENIUS CONFIRMED!")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED - CHECK IMPLEMENTATION")
        sys.exit(1)
