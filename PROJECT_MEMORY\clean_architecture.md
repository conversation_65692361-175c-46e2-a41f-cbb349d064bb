# TESTRADE Clean Architecture Guide

## Overview

This document describes the clean architecture implementation for TESTRADE, focusing on dependency inversion, interface segregation, and proper service lifecycle management.

## Core Principles

### 1. Depend on Interfaces, Not Implementations

All services should import from `/interfaces/`, not from implementation modules:

```python
# ✅ GOOD - Import from central interfaces
from interfaces import IEventBus, IPositionManager

# ❌ BAD - Import from implementation
from core.event_bus import EventBus
from modules.trade_management.position_manager import PositionManager
```

### 2. Interface Ownership

Interfaces are owned by their consumers, not providers. This means:
- The interface defines what the consumer needs
- Implementations adapt to the interface, not vice versa
- Changes to interfaces require careful consideration of all consumers

### 3. Domain Separation

Interfaces are organized by domain:
- `/interfaces/core/` - Core infrastructure (EventBus, Config, Lifecycle)
- `/interfaces/infrastructure/` - Low-level services (IPC, Messaging)
- `/interfaces/trading/` - Trading domain (Orders, Positions, Broker)
- `/interfaces/market_data/` - Market data (Prices, Publishing)
- `/interfaces/ocr/` - OCR services
- `/interfaces/risk/` - Risk management

### 4. Dependency Injection

All services are wired through the DI container:
- Service registration in `core/di_registration.py`
- Factory functions handle complex instantiation
- LazyProxy pattern breaks circular dependencies

## Service Lifecycle Management

### The ILifecycleService Interface

ALL services MUST implement `ILifecycleService`:

```python
from interfaces import ILifecycleService

class MyService(ILifecycleService):
    def start(self) -> None:
        """Start the service - subscribe to events, start threads"""
        pass
        
    def stop(self) -> None:
        """Stop gracefully - unsubscribe, stop threads, cleanup"""
        pass
        
    @property
    def is_ready(self) -> bool:
        """Check if service is operational"""
        return self._initialized
```

### Startup Phases

Services start in phases based on dependencies:
- Phase 0: Infrastructure (Config, EventBus, IPC)
- Phase 1: Internal Listeners (PositionManager, MarketDataPublisher)
- Phase 2: External Connections (Broker, Database)
- Phase 3: Data Feeds (PriceFetching, MarketData)
- Phase 4: UI/API (GUI, REST endpoints)

## Breaking Circular Dependencies

### The LazyProxy Pattern

When two services need each other, use LazyProxy:

```python
def order_repository_factory(container: DIContainer):
    from modules.order_management.order_repository import OrderRepository
    
    # Create LazyProxy for broker to break circular dependency
    lazy_broker = LazyProxy(container, IBrokerService)
    
    return OrderRepository(
        broker_service=lazy_broker  # Resolved on first use
    )
```

### Setter Injection

For complex circular dependencies, use setter injection:

```python
# In PositionManagerFactory
position_manager = PositionManager(...)
lifecycle_manager = container.resolve(ILifecycleManager)

# Break circular dependency with setter
if hasattr(lifecycle_manager, 'set_position_manager'):
    lifecycle_manager.set_position_manager(position_manager)
```

## The Allow List Pattern

For performance-critical filtering (e.g., FilteredMarketDataPublisher):

1. **Subscribe to state changes**: Listen for SymbolPublishingStateChangedEvent
2. **Maintain allow list**: Keep a set of publishable symbols
3. **Fast filtering**: Check `if symbol in self._publishable_symbols`

```python
class FilteredMarketDataPublisher(IMarketDataPublisher, ILifecycleService):
    def __init__(self, event_bus: IEventBus, ...):
        self._event_bus = event_bus
        self._publishable_symbols: Set[str] = set()
        
    def start(self):
        # Subscribe to position state changes
        self._event_bus.subscribe(
            SymbolPublishingStateChangedEvent,
            self._handle_symbol_state_change
        )
        
    def publish_market_data(self, symbol: str, ...):
        # Fast check
        if symbol not in self._publishable_symbols:
            return False
        # ... publish data ...
```

## Adding New Services

### 1. Define the Interface

Create interface in appropriate domain directory:

```python
# /interfaces/risk/risk_management.py
from abc import ABC, abstractmethod

class IRiskManagementService(ABC):
    @abstractmethod
    def validate_order(self, order: OrderParameters) -> bool:
        pass
```

### 2. Register in Central __init__.py

Add to `/interfaces/__init__.py` and domain `__init__.py`.

### 3. Implement the Service

```python
from interfaces import IRiskManagementService, ILifecycleService

class RiskManagementService(IRiskManagementService, ILifecycleService):
    # Implementation here
```

### 4. Register with DI Container

In `core/di_registration.py`:

```python
def risk_service_factory(container: DIContainer):
    event_bus = container.resolve(IEventBus)
    config = container.resolve(IConfigService)
    return RiskManagementService(event_bus, config)

container.register_factory_with_metadata(
    IRiskManagementService,
    risk_service_factory,
    phase=1,  # Listener service
    name="RiskManagementService"
)
```

## Common Patterns

### Event-Driven Services

Most services follow this pattern:
1. Inject EventBus in constructor
2. Subscribe to events in `start()`
3. Unsubscribe in `stop()`
4. Process events asynchronously

### Configuration Access

Use `IConfigService` instead of accessing GlobalConfig directly:
```python
config = container.resolve(IConfigService)
api_key = getattr(config, 'API_KEY', 'default')
```

### Service Health Monitoring

Implement meaningful `is_ready` checks:
```python
@property
def is_ready(self) -> bool:
    return (
        self._initialized and
        self._connection_active and
        not self._shutting_down
    )
```

## Migration Guide

To migrate existing code:

1. **Extract interfaces** from implementation classes
2. **Move to /interfaces/** in appropriate domain
3. **Update imports** throughout codebase
4. **Register with DI** using factory pattern
5. **Implement ILifecycleService** for automatic management
6. **Test in isolation** using mock implementations

## Benefits

This architecture provides:
- **Testability**: Easy to mock interfaces
- **Flexibility**: Swap implementations without changing consumers
- **Clarity**: Clear contracts between components
- **Maintainability**: Changes isolated to implementations
- **Performance**: Allow List pattern for efficient filtering