"""
Decorators Interface

This module provides a unified interface for decorators used in the application.
It conditionally imports either the real decorators or dummy implementations
based on a global configuration setting.

Usage:
    from utils.decorators.interface import log_function_call

    @log_function_call('module_key')
    def my_function():
        pass
"""

import os
import logging
import importlib.util
from typing import Dict, Callable, Any

# Set up logging
logger = logging.getLogger(__name__)

# Try to import global config to check if decorators should be enabled
try:
    from utils.global_config import config as global_app_config
    # Check if the enable_log_decorators setting exists
    _use_real_decorators = getattr(global_app_config, 'enable_log_decorators', False)
except Exception as e:
    logger.error(f"Error importing global_config or accessing config: {e}")
    _use_real_decorators = False

# Check if we're in diagnostic mode
try:
    import sys
    main_module = sys.modules.get('__main__')
    if main_module and hasattr(main_module, 'DECORATORS_FORCED_OFF'):
        logger.error("DIAGNOSTIC MODE DETECTED: Decorators FORCED OFF")
        _use_real_decorators = False
except Exception as e:
    logger.error(f"Error checking for diagnostic mode: {e}")

# Log the decision
if _use_real_decorators:
    logger.info("Using REAL decorators module")
else:
    logger.info("Using DUMMY decorators module")

# Import the appropriate module based on the configuration
if _use_real_decorators:
    try:
        # Import the real decorators
        from .log_decorators import (
            log_function_call,
            set_module_logging,
            enable_decorators,
            is_decorators_enabled,
            LOGGING_ENABLED_FLAGS
        )

        # Explicitly enable the real decorators
        enable_decorators(True)
        logger.info("Real log_decorators module loaded and ENABLED")
    except ImportError as e:
        logger.error(f"Error importing log_decorators: {e}")
        # Fall back to dummy implementation
        _use_real_decorators = False

if not _use_real_decorators:
    try:
        # Import the dummy decorators
        from .dummy_log_decorators import (
            log_function_call,
            set_module_logging
        )

        # Define missing functions from the real implementation
        def enable_decorators(enabled=True):
            """No-op implementation of enable_decorators."""
            return False

        def is_decorators_enabled():
            """No-op implementation of is_decorators_enabled."""
            return False

        # Define missing variables from the real implementation
        LOGGING_ENABLED_FLAGS = {}

        logger.info("DUMMY decorators module loaded. Real decorators are OFF")
    except ImportError as e:
        logger.error(f"Error importing dummy decorators: {e}")
        # Define minimal fallback implementations
        def log_function_call(module_key):
            """Minimal fallback implementation of log_function_call."""
            def decorator(func):
                return func
            return decorator

        def set_module_logging(module_key, enabled):
            """Minimal fallback implementation of set_module_logging."""
            pass

        def enable_decorators(enabled=True):
            """Minimal fallback implementation of enable_decorators."""
            return False

        def is_decorators_enabled():
            """Minimal fallback implementation of is_decorators_enabled."""
            return False

        LOGGING_ENABLED_FLAGS = {}

        logger.error("FALLBACK dummy decorators functions defined")

# Add a global function to update the GlobalConfig with the decorators setting
def update_global_config_with_decorators(enabled=True):
    """
    Update the global config with the decorators setting.

    Args:
        enabled: True to enable decorators, False to disable them

    Returns:
        True if the update was successful, False otherwise
    """
    try:
        from utils.global_config import config, save_global_config

        # Create a new attribute if it doesn't exist
        if not hasattr(config, 'enable_log_decorators'):
            config.enable_log_decorators = enabled
        else:
            config.enable_log_decorators = enabled

        # Save the updated config
        save_global_config(config)
        logger.info(f"Updated global config with enable_log_decorators={enabled}")

        return True
    except Exception as e:
        logger.error(f"Error updating global config with decorators setting: {e}")
        return False
