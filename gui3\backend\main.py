"""
Simplified GUI Backend Main Application
Following the Horseshoe Architecture pattern
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from .routers import websocket_router, api_router
from .core.app_state import AppState
from .core.message_handler import MessageRouter
from .handlers.ocr_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, CleanedOCRHandler
from .handlers.position_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ccountHand<PERSON>, TradeHandler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    Sets up and tears down application resources.
    """
    logger.info("Starting GUI Backend...")
    
    # Initialize application state
    app_state = AppState()
    app.state.app_state = app_state
    
    # Initialize message router
    router = MessageRouter(app_state)
    
    # Register all handlers
    router.register_handler('testrade:raw-ocr-snapshots', OCRHandler)
    router.register_handler('testrade:cleaned-ocr-snapshots', CleanedOCRHandler)
    router.register_handler('testrade:position-updates', PositionHandler)
    router.register_handler('testrade:account-updates', AccountHandler)
    router.register_handler('testrade:trade-lifecycle', TradeHandler)
    
    app_state.message_router = router
    
    # Start background tasks
    redis_task = asyncio.create_task(app_state.start_redis_consumer())
    
    yield
    
    # Cleanup
    logger.info("Shutting down GUI Backend...")
    await app_state.shutdown()
    redis_task.cancel()


# Create FastAPI app
app = FastAPI(
    title="TESTRADE GUI Backend",
    version="2.0.0",
    description="Simplified modular GUI backend following Horseshoe Architecture",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(websocket_router, prefix="/ws", tags=["websocket"])
app.include_router(api_router, prefix="/api/v2", tags=["api"])


@app.get("/")
async def root():
    """Root endpoint with system information."""
    return {
        "service": "TESTRADE GUI Backend",
        "version": "2.0.0",
        "architecture": "Horseshoe Pattern",
        "status": "running",
        "endpoints": {
            "websocket": "/ws",
            "api": "/api/v2",
            "docs": "/docs"
        }
    }


@app.get("/health")
async def health():
    """Health check endpoint."""
    app_state = app.state.app_state
    return {
        "status": "healthy",
        "redis_connected": app_state.redis_connected,
        "websocket_clients": len(app_state.ws_manager.active_connections),
        "message_handlers": app_state.message_router.get_registered_streams()
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)