# DI Registration Mode-Aware Factories - Implementation Summary

**Agent**: Agent 2  
**Date**: July 13, 2025  
**Task**: Update DI factories to only inject dependencies based on Tank mode

## Overview

Successfully implemented mode-aware dependency injection for all services in `core/di_registration.py`. The DI container now respects the TESTRADE operational modes (TANK_SEALED, TANK_BUFFERED, LIVE) and only injects telemetry and IPC services when required.

## Changes Made

### 1. Import Added
```python
from utils.testrade_modes import requires_telemetry_service, requires_ipc_services
```

### 2. Updated Factories

#### Telemetry Service Registration
- **Telemetry Service Factory**: Only registered when `requires_telemetry_service()` returns True
- **Correlation Logger Factory**: Falls back to NullCorrelationLogger if telemetry not available
- **Bulk Data Service**: Conditional telemetry_service resolution

#### IPC Service Registration  
- **Babysitter IPC Client**: Only registered when `requires_ipc_services()` returns True
- Returns None when not in LIVE mode

#### Service-Specific Updates

1. **GUI Command Service**
   - Telemetry service: Conditional based on `requires_telemetry_service()`
   - IPC client: Conditional based on `requires_ipc_services()`

2. **System Health Monitoring Service**
   - Telemetry service: Conditional resolution with warning if unavailable
   - IPC client: Conditional based on mode

3. **OCR Process Manager**
   - Telemetry service: Conditional with warning
   - IPC client: Conditional based on mode

4. **IPC Manager**
   - Mission control notifier: Only created when telemetry service required

5. **OCR Parameters Service**
   - IPC client: Conditional based on mode

6. **ROI Service**
   - IPC client: Conditional based on mode

7. **Master Action Filter Service**
   - IPC client: Conditional based on mode

### 3. Removed/Fixed

- Removed duplicate `stale_order_service_factory` that was using old telemetry pattern
- Removed unused telemetry service resolutions in:
  - OCR Data Conditioning Service factory
  - OCR Scalping Signal Orchestrator Service factory

## Mode Behavior

### TANK_SEALED Mode
- ❌ ITelemetryService not registered
- ❌ IBulletproofBabysitterIPCClient not registered
- ✅ Services receive None for these dependencies

### TANK_BUFFERED Mode  
- ✅ ITelemetryService registered (local buffering only)
- ❌ IBulletproofBabysitterIPCClient not registered
- ✅ Services receive telemetry service, None for IPC

### LIVE Mode
- ✅ ITelemetryService registered
- ✅ IBulletproofBabysitterIPCClient registered (if ZMQ available)
- ✅ Services receive all dependencies

## Pattern Used

```python
# Conditional telemetry service
telemetry_service = None
if requires_telemetry_service():
    try:
        telemetry_service = container.resolve(ITelemetryService)
    except Exception as e:
        logger.warning(f"ServiceName: Telemetry service required but not available: {e}")

# Conditional IPC client  
ipc_client = None
if requires_ipc_services():
    try:
        ipc_client = container.resolve(IBulletproofBabysitterIPCClient)
    except Exception:
        logger.debug("ServiceName: IPC client not available")
```

## Testing

Created and ran `test_mode_aware_di.py` which verified:
- TANK_SEALED: No telemetry or IPC services available ✅
- TANK_BUFFERED: Telemetry available, no IPC ✅  
- LIVE: Both telemetry and IPC available ✅

## Benefits

1. **Reduced Startup Warnings**: Services no longer fail to resolve dependencies in Tank modes
2. **Clean Architecture**: Dependencies only injected when actually needed
3. **Mode Flexibility**: Easy to run in different modes without code changes
4. **Better Testing**: Can test in isolation without external dependencies

## Next Steps

- Monitor logs for any remaining dependency resolution warnings
- Consider making more services mode-aware if needed
- Update service constructors to handle None values gracefully

---
**Mode-Aware DI** - Building flexible, mode-respecting dependency injection ✅