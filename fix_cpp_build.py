#!/usr/bin/env python3
"""
Attempt to fix the C++ build issue
"""

import os
import shutil

def backup_and_fix_project_file():
    """Backup and fix the project file with correct settings"""
    
    project_file = "ocr_accelerator/ocr_accelerator.vcxproj"
    backup_file = "ocr_accelerator/ocr_accelerator.vcxproj.backup"
    
    if not os.path.exists(project_file):
        print(f"❌ Project file not found: {project_file}")
        return False
    
    # Create backup
    shutil.copy2(project_file, backup_file)
    print(f"✅ Created backup: {backup_file}")
    
    # Read current project file
    with open(project_file, 'r') as f:
        content = f.read()
    
    # Check if the file has the correct Python extension setting
    if '.pyd' not in content:
        print("❌ Project file missing .pyd extension setting")
        return False
    
    # Check if PYBIND11_MODULE is being compiled correctly
    # The issue might be that the module init function isn't being exported
    
    # Look for potential issues in the project configuration
    issues_found = []
    
    if 'python311.lib' not in content:
        issues_found.append("Missing python311.lib dependency")
    
    if 'OCRACCELERATOR_EXPORTS' not in content:
        issues_found.append("Missing OCRACCELERATOR_EXPORTS definition")
    
    if '/EXPORT' not in content:
        issues_found.append("No explicit export directives")
    
    if issues_found:
        print("Potential issues found:")
        for issue in issues_found:
            print(f"  - {issue}")
    else:
        print("✅ Project file looks correct")
    
    return True

def create_minimal_test_cpp():
    """Create a minimal test C++ file to verify pybind11 is working"""
    
    test_cpp = """#include <pybind11/pybind11.h>

namespace py = pybind11;

// Simple test function
int test_add(int a, int b) {
    return a + b;
}

// Module definition
PYBIND11_MODULE(test_module, m) {
    m.doc() = "Minimal test module";
    m.def("test_add", &test_add, "Add two numbers");
}
"""
    
    with open("test_module.cpp", "w") as f:
        f.write(test_cpp)
    
    print("✅ Created minimal test C++ file: test_module.cpp")
    
    # Create simple build script
    build_script = """@echo off
echo Building minimal test module...
cl /I"C:\\Program Files\\Python311\\include" /DPYBIND11_EXPORTS /D_WINDLL /D_MBCS /EHsc test_module.cpp /link /DLL /OUT:test_module.pyd "C:\\Program Files\\Python311\\libs\\python311.lib"
echo Done.
"""
    
    with open("build_test.bat", "w") as f:
        f.write(build_script)
    
    print("✅ Created build script: build_test.bat")

def analyze_build_log():
    """Analyze the build log for issues"""
    
    log_file = "ocr_accelerator/ocr_accelerator/x64/Release/ocr_accelerator.log"
    
    if not os.path.exists(log_file):
        print(f"❌ Build log not found: {log_file}")
        return
    
    print(f"=== Analyzing build log: {log_file} ===")
    
    with open(log_file, 'r') as f:
        log_content = f.read()
    
    # Look for specific issues
    issues = []
    
    if "error" in log_content.lower():
        issues.append("Build errors found")
    
    if "warning LNK4210" in log_content:
        issues.append("Static initializer warning (may affect module init)")
    
    if "PyInit_ocr_accelerator" not in log_content:
        issues.append("No PyInit function mentioned (pybind11 issue)")
    
    if ".pyd.recipe" in log_content:
        print("✅ .pyd target was built")
    
    if issues:
        print("Issues found in build log:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ Build log looks clean")
    
    # Show relevant parts of log
    lines = log_content.split('\n')
    print("\nBuild log summary:")
    for line in lines:
        if any(keyword in line.lower() for keyword in ['error', 'warning', 'pyd', 'creating']):
            print(f"  {line.strip()}")

def main():
    print("=== C++ Build Fix Analysis ===")
    
    # Step 1: Analyze current project file
    print("\n--- Project File Analysis ---")
    backup_and_fix_project_file()
    
    # Step 2: Analyze build log
    print("\n--- Build Log Analysis ---")
    analyze_build_log()
    
    # Step 3: Create minimal test
    print("\n--- Creating Minimal Test ---")
    create_minimal_test_cpp()
    
    print("\n=== Recommendations ===")
    print("1. Try rebuilding with: rebuild_clean.bat")
    print("2. Test minimal module with: build_test.bat")
    print("3. Run simple_windows_test.py on Windows")
    print("4. Check if Visual Studio has all C++ components installed")

if __name__ == "__main__":
    main()