# Fix History Cards Display

## Problem
The history cards in the GUI are not showing up because:
1. The `tradeState.historicalTrades` array is empty
2. The backend listens to `testrade:trade-history` stream but nothing is publishing to it
3. There's no automatic request for historical trades when the GUI loads

## Solution

### Option 1: Add Bootstrap Request (Recommended)
Add a function to request historical trades when the GUI connects:

```javascript
// In app.js, add this function:
function requestHistoricalTrades() {
    if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
            type: 'command',
            command: 'GET_TRADE_HISTORY',
            parameters: {
                date: 'today'  // or specific date
            }
        }));
        console.log('📊 Requested historical trades');
    }
}

// Call it after WebSocket connects, in the ws.onopen handler:
ws.onopen = function() {
    // ... existing code ...
    
    // Request historical trades
    setTimeout(() => {
        requestHistoricalTrades();
    }, 1000);  // Small delay to ensure everything is initialized
};
```

### Option 2: Mock Data for Testing
If you want to test the UI immediately, add some mock data:

```javascript
// In app.js, after tradeState definition:
// Mock historical trades for testing
tradeState.historicalTrades = [
    {
        trade_id: 'test-001',
        symbol: 'AAPL',
        side: 'BUY',
        quantity: 100,
        open_price: 150.00,
        close_price: 152.50,
        realized_pnl: 250.00,
        timestamp: Date.now() - 3600000,  // 1 hour ago
        close_timestamp: Date.now() - 1800000  // 30 min ago
    },
    {
        trade_id: 'test-002',
        symbol: 'TSLA',
        side: 'SELL',
        quantity: 50,
        open_price: 250.00,
        close_price: 248.00,
        realized_pnl: -100.00,
        timestamp: Date.now() - 7200000,  // 2 hours ago
        close_timestamp: Date.now() - 3600000  // 1 hour ago
    }
];

// Then call updateHistoricalTradesDisplay() after page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        updateHistoricalTradesDisplay();
    }, 500);
});
```

### Option 3: Backend Implementation
The backend needs to implement a handler for `GET_TRADE_HISTORY` command that:
1. Queries the order repository for closed trades
2. Formats them properly
3. Publishes to the `testrade:trade-history` stream

## Quick Fix to Test
To quickly test if the display is working, you can manually trigger it in the browser console:

```javascript
// Run this in browser console:
tradeState.historicalTrades = [
    {
        trade_id: 'test-001',
        symbol: 'AAPL',
        side: 'BUY',
        quantity: 100,
        realized_pnl: 250.00,
        timestamp: Date.now()
    }
];
updateHistoricalTradesDisplay();
```

## What the History Cards Should Show
When working properly, the history cards will display:
- Trade symbol and side (BUY/SELL)
- Quantity
- P&L (profit/loss) with color coding
- Entry and exit times
- Grouped by parent/child relationships if applicable

The tabs will filter:
- **Today**: Only trades from current day
- **Winners**: Trades with positive P&L
- **Losers**: Trades with negative P&L
- **All**: All historical trades