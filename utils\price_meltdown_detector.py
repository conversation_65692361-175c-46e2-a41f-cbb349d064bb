import time
import threading
import logging
from collections import deque
from dataclasses import dataclass
from typing import Deque, Dict, Any, Optional, List, Tuple

logger = logging.getLogger(__name__)

@dataclass
class MeltdownConfig:
    # Time windows
    short_window_sec: float = 30.0
    long_window_sec: float = 90.0
    price_drop_window: float = 1.5       # Shorter window for fast moves
    momentum_window: float = 3.0

    # Thresholds
    heavy_sell_multiplier: float = 2.5    # Lowered for volatile premarket
    price_drop_pct: float = 0.025        # 2.5% drop threshold
    consecutive_bid_hits: int = 3        # Fewer hits needed for low-float
    block_multiplier: float = 4.0         # Big blocks more significant
    spread_widen_factor: float = 3.0
    sell_ratio_threshold: float = 0.85
    spread_threshold_pct: float = 0.20    # 20% of spread for side detection
    halt_inactivity: float = 10.0         # Shorter halt detection
    halt_min_trades: int = 5

    # Risk scoring weights
    weights: Dict[str, float] = None

    def __post_init__(self):
        self.weights = {
            'heavy_selling': 0.25,
            'fast_drop': 0.3,
            'consecutive_bids': 0.15,
            'big_block': 0.1,
            'spread_widen': 0.1,
            'one_sided': 0.1
        }

class PriceMeltdownDetector:
    def __init__(self, config: MeltdownConfig = None, benchmarker: Optional[Any] = None):
        self.config = config or MeltdownConfig()
        self.lock = threading.RLock()
        self._benchmarker = benchmarker

        # Data storage
        self.trade_history_short: Dict[str, Deque[Dict]] = {}
        self.trade_history_long: Dict[str, Deque[Dict]] = {}
        self.quote_history_short: Dict[str, Deque[Dict]] = {}
        self.latest_quote_map: Dict[str, Dict] = {}

        # Track last activity time for each symbol for pruning
        self._symbol_last_activity_ts: Dict[str, float] = {}

        # Cached metrics
        self.metrics_cache = {
            'long_term_avg_size': {},
            'min_spread': {},
            'current_spread': {}
        }

    def reset(self):
        """Clears all internal historical data and caches."""
        with self.lock:
            logger.info("Resetting PriceMeltdownDetector state...")
            # Clear deques
            self.trade_history_short.clear()
            self.trade_history_long.clear()
            self.quote_history_short.clear()
            # Clear maps/dictionaries
            self.latest_quote_map.clear()
            # Clear symbol activity tracking
            self._symbol_last_activity_ts.clear()
            # Clear metrics cache
            # Be careful if cache structure is complex, this assumes simple dict of deques/lists
            if hasattr(self, 'metrics_cache') and isinstance(self.metrics_cache, dict):
                 for key in self.metrics_cache:
                      if hasattr(self.metrics_cache[key], 'clear'):
                          self.metrics_cache[key].clear()
            # Reset any other state variables if necessary
            # e.g., self.last_check_time = 0.0
            logger.info("PriceMeltdownDetector state reset completed.")

    def process_quote(self, symbol: str, bid: float, ask: float, ts: float = None):
        if not self._benchmarker:
            # Without benchmarking
            symbol = symbol.upper()
            ts = ts or time.time()

            with self.lock:
                # Update latest quote
                self.latest_quote_map[symbol] = {
                    'bid': bid,
                    'ask': ask,
                    'timestamp': ts,
                    'spread': ask - bid
                }

                # Maintain quote history
                q_deque = self.quote_history_short.setdefault(symbol, deque())
                q_deque.append(self.latest_quote_map[symbol])

                # Prune old quotes
                self._prune_deque(q_deque, ts, self.config.short_window_sec)

                # Update spread metrics
                self._update_spread_metrics(symbol)

                # Update last activity timestamp for this symbol
                self._symbol_last_activity_ts[symbol] = ts
            return

        # With benchmarking
        start_time = time.time()
        context = {'symbol': symbol, 'type': 'quote'}

        try:
            symbol = symbol.upper()
            ts = ts or time.time()

            with self.lock:
                # Update latest quote
                self.latest_quote_map[symbol] = {
                    'bid': bid,
                    'ask': ask,
                    'timestamp': ts,
                    'spread': ask - bid
                }

                # Maintain quote history
                q_deque = self.quote_history_short.setdefault(symbol, deque())
                q_deque.append(self.latest_quote_map[symbol])

                # Prune old quotes
                self._prune_deque(q_deque, ts, self.config.short_window_sec)

                # Update spread metrics
                self._update_spread_metrics(symbol)

                # Update last activity timestamp for this symbol
                self._symbol_last_activity_ts[symbol] = ts
        finally:
            duration_ms = (time.time() - start_time) * 1000
            self._benchmarker.capture_metric("pmd.process_quote_time_ms", duration_ms, context=context)

    def process_trade(self, symbol: str, price: float, size: float, ts: float = None):
        if not self._benchmarker:
            # Without benchmarking
            symbol = symbol.upper()
            ts = ts or time.time()

            with self.lock:
                # Get latest quote for side inference
                quote = self.latest_quote_map.get(symbol, {})
                bid = quote.get('bid', 0)
                ask = quote.get('ask', 0)

                side = self._infer_side(price, bid, ask, quote.get('spread', 0))

                trade_record = {
                    'price': price,
                    'size': size,
                    'timestamp': ts,
                    'side': side,
                    'bid': bid,
                    'ask': ask
                }

                # Update trade histories
                for store, window in [
                    (self.trade_history_short, self.config.short_window_sec),
                    (self.trade_history_long, self.config.long_window_sec)
                ]:
                    d = store.setdefault(symbol, deque())  # 'd' is a local variable
                    d.append(trade_record)
                    self._prune_deque(d, ts, window)

                # Update cached metrics
                self._update_size_metrics(symbol)

                # Update last activity timestamp for this symbol
                self._symbol_last_activity_ts[symbol] = ts
            return

        # With benchmarking
        start_time = time.time()
        context = {'symbol': symbol, 'type': 'trade'}

        try:
            symbol = symbol.upper()
            ts = ts or time.time()

            with self.lock:
                # Get latest quote for side inference
                quote = self.latest_quote_map.get(symbol, {})
                bid = quote.get('bid', 0)
                ask = quote.get('ask', 0)

                side = self._infer_side(price, bid, ask, quote.get('spread', 0))

                trade_record = {
                    'price': price,
                    'size': size,
                    'timestamp': ts,
                    'side': side,
                    'bid': bid,
                    'ask': ask
                }

                # Update trade histories
                for store, window in [
                    (self.trade_history_short, self.config.short_window_sec),
                    (self.trade_history_long, self.config.long_window_sec)
                ]:
                    d = store.setdefault(symbol, deque())  # 'd' is a local variable
                    d.append(trade_record)
                    self._prune_deque(d, ts, window)

                # Update cached metrics
                self._update_size_metrics(symbol)

                # Update last activity timestamp for this symbol
                self._symbol_last_activity_ts[symbol] = ts
        finally:
            duration_ms = (time.time() - start_time) * 1000
            self._benchmarker.capture_metric("pmd.process_trade_time_ms", duration_ms, context=context)

    def _infer_side(self, price: float, bid: float, ask: float, spread: float) -> str:
        if spread < 1e-9:
            return "UNKNOWN"

        threshold = spread * self.config.spread_threshold_pct
        if abs(price - ask) <= threshold:
            return "BUY"
        if abs(price - bid) <= threshold:
            return "SELL"
        return "UNKNOWN"

    def check_meltdown_conditions(self, symbol: str, correlation_id: str = None) -> Dict[str, Any]:
        symbol = symbol.upper()
        
        # PMD_CheckStart milestone
        check_start_time = time.time()
        if self._benchmarker and correlation_id:
            self._benchmarker.capture_milestone("PMD_CheckStart", correlation_id, 
                                              context={'symbol': symbol, 'component': 'price_meltdown_detector'})
        
        with self.lock:
            halted = self.is_halted(symbol)
            if halted:
                # Include timing for halted response
                result = self._halted_response()
                if correlation_id:
                    check_end_time = time.time()
                    total_duration_ms = (check_end_time - check_start_time) * 1000
                    result['performance_metrics'] = {
                        'total_duration_ms': total_duration_ms,
                        'factor_calculation_ms': 0.0,
                        'score_calculation_ms': 0.0,
                        'correlation_id': correlation_id
                    }
                    if self._benchmarker:
                        self._benchmarker.capture_milestone("PMD_CheckEnd", correlation_id,
                                                          context={'symbol': symbol, 'component': 'price_meltdown_detector', 
                                                                 'halted': True, 'total_ms': total_duration_ms})
                return result

            # Factor calculation phase
            factor_calc_start = time.time()
            factors = {
                'heavy_selling': self.is_heavy_selling(symbol),
                'fast_drop': self.is_fast_price_drop(symbol),
                'consecutive_bids': self.has_consecutive_bid_hits(symbol),
                'big_block': self.has_big_block(symbol),
                'spread_widen': self.is_spread_widened(symbol),
                'one_sided': self.is_one_sided_tape(symbol),
                'selling_momentum': self.has_selling_momentum(symbol)
            }
            factor_calc_end = time.time()
            factor_duration_ms = (factor_calc_end - factor_calc_start) * 1000
            
            # PMD_FactorCalculationEnd milestone
            if self._benchmarker and correlation_id:
                self._benchmarker.capture_milestone("PMD_FactorCalculationEnd", correlation_id,
                                                  context={'symbol': symbol, 'component': 'price_meltdown_detector', 
                                                         'factor_calculation_ms': factor_duration_ms})

            # Score calculation phase
            score_calc_start = time.time()
            risk_score = self.calculate_risk_score(factors)
            score_calc_end = time.time()
            score_duration_ms = (score_calc_end - score_calc_start) * 1000
            
            # PMD_ScoreCalculationEnd milestone
            if self._benchmarker and correlation_id:
                self._benchmarker.capture_milestone("PMD_ScoreCalculationEnd", correlation_id,
                                                  context={'symbol': symbol, 'component': 'price_meltdown_detector', 
                                                         'score_calculation_ms': score_duration_ms})

            # Final timing calculations
            check_end_time = time.time()
            total_duration_ms = (check_end_time - check_start_time) * 1000
            
            # PMD_CheckEnd milestone
            if self._benchmarker and correlation_id:
                self._benchmarker.capture_milestone("PMD_CheckEnd", correlation_id,
                                                  context={'symbol': symbol, 'component': 'price_meltdown_detector', 
                                                         'total_ms': total_duration_ms, 'risk_score': risk_score})

            result = {
                'is_halted': False,
                'risk_score': risk_score,
                'factors': factors,
                'metrics': {
                    'current_price': self._current_price(symbol),
                    'avg_spread': self.metrics_cache['current_spread'].get(symbol, 0),
                    'volume_profile': self._get_volume_profile(symbol)
                }
            }
            
            # Add performance metrics if correlation_id is provided
            if correlation_id:
                result['performance_metrics'] = {
                    'total_duration_ms': total_duration_ms,
                    'factor_calculation_ms': factor_duration_ms,
                    'score_calculation_ms': score_duration_ms,
                    'correlation_id': correlation_id
                }
                
                # Add detailed latency logging
                logger.debug(f"PMD Performance [{correlation_id}] {symbol}: "
                           f"Total={total_duration_ms:.3f}ms, "
                           f"Factors={factor_duration_ms:.3f}ms, "
                           f"Score={score_duration_ms:.3f}ms, "
                           f"RiskScore={risk_score:.3f}")

            return result

    def is_heavy_selling(self, symbol: str) -> Tuple[bool, float]:
        now = time.time()
        short_trades = self._get_window_trades(symbol, self.config.price_drop_window)
        long_trades = self._get_window_trades(symbol, self.config.long_window_sec, False)

        short_sell = sum(t['size'] for t in short_trades if t['side'] == "SELL")
        long_sell = sum(t['size'] for t in long_trades if t['side'] == "SELL")

        if long_sell < 1e-9:
            return (False, 0.0)

        baseline = (long_sell / self.config.long_window_sec) * self.config.price_drop_window
        ratio = short_sell / baseline if baseline > 0 else 0
        return (ratio >= self.config.heavy_sell_multiplier, ratio)

    def is_fast_price_drop(self, symbol: str) -> Tuple[bool, float]:
        trades = self._get_window_trades(symbol, self.config.price_drop_window)
        if len(trades) < 3:
            return (False, 0.0)

        prices = [t['price'] for t in trades]
        peak = max(prices)
        trough = min(prices)
        drop_pct = (peak - trough) / peak
        return (drop_pct >= self.config.price_drop_pct, drop_pct)

    def has_consecutive_bid_hits(self, symbol: str) -> bool:
        trades = self._get_window_trades(symbol, 1.0)
        consecutive = 0

        for t in sorted(trades, key=lambda x: x['timestamp']):
            if t['side'] == "SELL":
                consecutive += 1
                if consecutive >= self.config.consecutive_bid_hits:
                    return True
            else:
                consecutive = 0
        return False

    def has_big_block(self, symbol: str) -> bool:
        avg_size = self.metrics_cache['long_term_avg_size'].get(symbol, 0)
        if avg_size == 0:
            return False

        trades = self._get_window_trades(symbol, self.config.short_window_sec)
        return any(
            t['size'] >= avg_size * self.config.block_multiplier
            and t['side'] == "SELL"
            for t in trades
        )

    def is_spread_widened(self, symbol: str) -> Tuple[bool, float]:
        min_spread = self.metrics_cache['min_spread'].get(symbol, 0)
        current_spread = self.metrics_cache['current_spread'].get(symbol, 0)

        if min_spread < 1e-9:
            return (False, 0.0)

        factor = current_spread / min_spread
        return (factor >= self.config.spread_widen_factor, factor)

    def is_one_sided_tape(self, symbol: str) -> Tuple[bool, float]:
        trades = self._get_window_trades(symbol, 2.0)
        total = sum(t['size'] for t in trades)
        sell = sum(t['size'] for t in trades if t['side'] == "SELL")
        ratio = sell / total if total > 0 else 0
        return (ratio >= self.config.sell_ratio_threshold, ratio)

    def has_selling_momentum(self, symbol: str) -> bool:
        trades = self._get_window_trades(symbol, self.config.momentum_window)
        if len(trades) < 5:
            return False

        third = self.config.momentum_window / 3
        now = time.time()

        periods = [
            [t for t in trades if t['timestamp'] > now - third],
            [t for t in trades if now - 2*third < t['timestamp'] <= now - third],
            [t for t in trades if now - self.config.momentum_window < t['timestamp'] <= now - 2*third]
        ]

        volumes = [
            sum(t['size'] for t in period if t['side'] == "SELL")
            for period in periods
        ]

        return volumes[0] > volumes[1] > volumes[2] and volumes[0] > 0

    def calculate_risk_score(self, factors: Dict) -> float:
        score = 0.0
        weights = self.config.weights

        # Heavy selling
        _, ratio = factors['heavy_selling']
        score += weights['heavy_selling'] * min(ratio / 3, 1.0)

        # Price drop
        _, drop_pct = factors['fast_drop']
        score += weights['fast_drop'] * min(drop_pct / 0.05, 1.0)  # Cap at 5% drop

        # Binary factors
        score += weights['consecutive_bids'] * factors['consecutive_bids']
        score += weights['big_block'] * factors['big_block']

        # Spread widening
        _, spread_factor = factors['spread_widen']
        score += weights['spread_widen'] * min(spread_factor / 5, 1.0)

        # One-sided tape
        _, sell_ratio = factors['one_sided']
        score += weights['one_sided'] * min(sell_ratio / 0.9, 1.0)

        # Momentum (extra weight)
        if factors['selling_momentum']:
            score *= 1.2

        return min(score, 1.0)

    # Helper methods
    def _get_window_trades(self, symbol: str, window: float, short_term: bool = True) -> List:
        store = self.trade_history_short if short_term else self.trade_history_long
        existing_deque = store.get(symbol.upper(), deque())
        now = time.time()
        return [t for t in existing_deque if (now - t['timestamp']) <= window]


    def _prune_deque(self, deque: deque, current_ts: float, window: float):
        cutoff = current_ts - window
        while deque and deque[0]['timestamp'] < cutoff:
            deque.popleft()

    def prune_inactive_symbols(self, inactive_threshold_seconds: float):
        """Remove symbols that haven't been updated in inactive_threshold_seconds.

        Args:
            inactive_threshold_seconds: Number of seconds of inactivity after which a symbol
                                       should be pruned from all data structures.
        """
        now = time.time()
        inactive_symbols = []

        with self.lock:
            # Find inactive symbols
            for symbol, last_ts in self._symbol_last_activity_ts.items():
                if now - last_ts > inactive_threshold_seconds:
                    inactive_symbols.append(symbol)

            # Remove inactive symbols from all data structures
            for symbol in inactive_symbols:
                # Remove from trade histories
                if symbol in self.trade_history_short:
                    del self.trade_history_short[symbol]
                if symbol in self.trade_history_long:
                    del self.trade_history_long[symbol]

                # Remove from quote history
                if symbol in self.quote_history_short:
                    del self.quote_history_short[symbol]

                # Remove from latest quote map
                if symbol in self.latest_quote_map:
                    del self.latest_quote_map[symbol]

                # Clean up metrics cache
                for cache in self.metrics_cache.values():
                    if symbol in cache:
                        del cache[symbol]

                # Remove from activity tracking
                del self._symbol_last_activity_ts[symbol]

            if inactive_symbols:
                logger.info(f"Pruned {len(inactive_symbols)} inactive symbols from PriceMeltdownDetector: {', '.join(inactive_symbols[:10])}" +
                           (f" and {len(inactive_symbols) - 10} more" if len(inactive_symbols) > 10 else ""))

    def _update_size_metrics(self, symbol: str):
        long_trades = self.trade_history_long.get(symbol, [])
        if long_trades:
            sizes = [t['size'] for t in long_trades]
            self.metrics_cache['long_term_avg_size'][symbol] = sum(sizes) / len(sizes)

    def _update_spread_metrics(self, symbol: str):
        quotes = self.quote_history_short.get(symbol, [])
        if quotes:
            spreads = [q['spread'] for q in quotes]
            self.metrics_cache['min_spread'][symbol] = min(spreads)
            self.metrics_cache['current_spread'][symbol] = spreads[-1] if spreads else 0

    def _halted_response(self):
        return {
            'is_halted': True,
            'risk_score': 0.0,
            'factors': {},
            'metrics': {}
        }

    def is_halted(self, symbol: str) -> bool:
        trades = self._get_window_trades(symbol, self.config.short_window_sec)
        if len(trades) < self.config.halt_min_trades:
            return False

        latest_trade = max(t['timestamp'] for t in trades)
        quotes = self.quote_history_short.get(symbol, [])
        latest_quote = max(q['timestamp'] for q in quotes) if quotes else 0

        now = time.time()
        return (now - latest_trade) > self.config.halt_inactivity and \
               (now - latest_quote) > self.config.halt_inactivity

    def _current_price(self, symbol: str) -> float:
        trades = self.trade_history_short.get(symbol, deque())
        return trades[-1]['price'] if trades else 0.0

    def _get_volume_profile(self, symbol: str) -> Dict:
        trades = self._get_window_trades(symbol, self.config.short_window_sec)
        return {
            'total': sum(t['size'] for t in trades),
            'buy': sum(t['size'] for t in trades if t['side'] == "BUY"),
            'sell': sum(t['size'] for t in trades if t['side'] == "SELL")
        }

    def analyze_symbol(self, symbol: str) -> Dict[str, Any]:
        """
        Comprehensive analysis of a symbol's risk factors and metrics.
        Used by RiskManagementService.get_risk_details().

        Args:
            symbol: The stock symbol to analyze

        Returns:
            Dict containing all risk factors, metrics, and conditions
        """
        symbol = symbol.upper()
        start_time = time.time()

        with self.lock:
            # Get meltdown conditions (includes factors and basic metrics)
            result = self.check_meltdown_conditions(symbol)

            # Add additional analysis data
            result['symbol'] = symbol
            result['timestamp'] = time.time()

            # Add trade statistics
            short_trades = self._get_window_trades(symbol, self.config.short_window_sec)
            long_trades = self._get_window_trades(symbol, self.config.long_window_sec, False)

            result['trade_stats'] = {
                'short_window_count': len(short_trades),
                'long_window_count': len(long_trades),
                'short_window_volume': sum(t['size'] for t in short_trades),
                'long_window_volume': sum(t['size'] for t in long_trades)
            }

            # Add quote statistics
            quotes = self.quote_history_short.get(symbol, [])
            if quotes:
                result['quote_stats'] = {
                    'quote_count': len(quotes),
                    'min_spread': self.metrics_cache['min_spread'].get(symbol, 0),
                    'current_spread': self.metrics_cache['current_spread'].get(symbol, 0),
                    'latest_bid': quotes[-1]['bid'] if quotes else 0,
                    'latest_ask': quotes[-1]['ask'] if quotes else 0
                }
            else:
                result['quote_stats'] = {
                    'quote_count': 0,
                    'min_spread': 0,
                    'current_spread': 0,
                    'latest_bid': 0,
                    'latest_ask': 0
                }

            # Add performance timing only if performance tracking is enabled
            try:
                from utils.performance_tracker import is_performance_tracking_enabled
            except ImportError:
                # If import fails, define a local function that always returns False
                def is_performance_tracking_enabled():
                    return False

            if is_performance_tracking_enabled():
                result['perf_timestamps'] = {
                    'analysis_time_ms': (time.time() - start_time) * 1000
                }

            return result