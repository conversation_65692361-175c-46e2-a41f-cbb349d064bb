# tank_multi_leak_detector.ps1 - Advanced leak detection for all Python processes
param(
    [string]$ProcessPattern = "python",
    [int]$SampleIntervalSeconds = 60,
    [int]$AnalysisWindowMinutes = 30,
    [string]$LogFile = "tank_multi_leak_analysis.csv",
    [switch]$EnableAlerts = $true,
    [switch]$EnableCSVLogging = $true
)

class MultiProcessMemoryAnalyzer {
    [System.Collections.ArrayList]$Samples
    [datetime]$StartTime
    [string]$ProcessPattern
    [int]$AnalysisWindowMinutes
    
    MultiProcessMemoryAnalyzer([string]$processPattern, [int]$windowMinutes) {
        $this.Samples = New-Object System.Collections.ArrayList
        $this.StartTime = Get-Date
        $this.ProcessPattern = $processPattern
        $this.AnalysisWindowMinutes = $windowMinutes
    }
    
    [void]AddSample([PSCustomObject]$sample) {
        $this.Samples.Add($sample) | Out-Null
        
        # Keep only samples within analysis window
        $cutoffTime = (Get-Date).AddMinutes(-$this.AnalysisWindowMinutes)
        $this.Samples = $this.Samples | Where-Object { $_.Timestamp -gt $cutoffTime }
    }
    
    [PSCustomObject]AnalyzeTotalMemoryTrend() {
        if ($this.Samples.Count -lt 5) {
            return [PSCustomObject]@{
                Status = "INSUFFICIENT_DATA"
                TrendType = "UNKNOWN"
                GrowthRate = 0
                Confidence = 0
                SampleCount = $this.Samples.Count
            }
        }
        
        # Linear regression for total memory trend
        $x = 1..$this.Samples.Count
        $y = $this.Samples.TotalMemoryMB
        
        $n = $this.Samples.Count
        $sumX = ($x | Measure-Object -Sum).Sum
        $sumY = ($y | Measure-Object -Sum).Sum
        $sumXY = 0; for($i=0; $i -lt $n; $i++) { $sumXY += $x[$i] * $y[$i] }
        $sumX2 = ($x | ForEach-Object { $_ * $_ } | Measure-Object -Sum).Sum
        
        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX)
        $intercept = ($sumY - $slope * $sumX) / $n
        
        # Calculate R-squared for confidence
        $yMean = ($y | Measure-Object -Average).Average
        $ssReg = 0; $ssTot = 0
        for($i=0; $i -lt $n; $i++) {
            $predicted = $slope * ($i + 1) + $intercept
            $ssReg += ($predicted - $yMean) * ($predicted - $yMean)
            $ssTot += ($y[$i] - $yMean) * ($y[$i] - $yMean)
        }
        $rSquared = if ($ssTot -ne 0) { $ssReg / $ssTot } else { 0 }
        
        # Enhanced trend classification for multi-process
        $trendType = if ($slope -gt 20) { "CRITICAL_SYSTEM_LEAK" }
                    elseif ($slope -gt 10) { "SYSTEM_LEAK_DETECTED" }
                    elseif ($slope -gt 5) { "MODERATE_SYSTEM_GROWTH" }
                    elseif ($slope -gt 2) { "SLOW_SYSTEM_GROWTH" }
                    elseif ($slope -gt -2) { "SYSTEM_STABLE" }
                    else { "SYSTEM_DECREASING" }
        
        # Calculate additional statistics
        $memoryStats = $y | Measure-Object -Average -Maximum -Minimum -StandardDeviation
        $volatility = if ($memoryStats.Average -gt 0) { 
            ($memoryStats.StandardDeviation / $memoryStats.Average) * 100 
        } else { 0 }
        
        return [PSCustomObject]@{
            Status = "ANALYZED"
            TrendType = $trendType
            GrowthRate = [math]::Round($slope, 3)
            Confidence = [math]::Round($rSquared * 100, 1)
            SampleCount = $n
            ProjectedMemoryIn1Hour = [math]::Round($y[-1] + $slope * 60, 1)
            ProjectedMemoryIn4Hours = [math]::Round($y[-1] + $slope * 240, 1)
            AverageMemory = [math]::Round($memoryStats.Average, 2)
            MaxMemory = [math]::Round($memoryStats.Maximum, 2)
            MinMemory = [math]::Round($memoryStats.Minimum, 2)
            Volatility = [math]::Round($volatility, 1)
            TimeSpanMinutes = [math]::Round(((Get-Date) - $this.Samples[0].Timestamp).TotalMinutes, 1)
        }
    }
    
    [PSCustomObject]AnalyzeProcessCountTrend() {
        if ($this.Samples.Count -lt 3) {
            return [PSCustomObject]@{
                Status = "INSUFFICIENT_DATA"
                ProcessCountTrend = "UNKNOWN"
                ProcessCountGrowthRate = 0
            }
        }
        
        $processCounts = $this.Samples.ProcessCount
        $avgProcessCount = ($processCounts | Measure-Object -Average).Average
        $maxProcessCount = ($processCounts | Measure-Object -Maximum).Maximum
        $minProcessCount = ($processCounts | Measure-Object -Minimum).Minimum
        
        # Simple trend analysis for process count
        $recentCount = $processCounts[-1]
        $earlierCount = $processCounts[0]
        $processCountChange = $recentCount - $earlierCount
        
        $processCountTrend = if ($processCountChange -gt 2) { "PROCESS_COUNT_INCREASING" }
                            elseif ($processCountChange -lt -2) { "PROCESS_COUNT_DECREASING" }
                            else { "PROCESS_COUNT_STABLE" }
        
        return [PSCustomObject]@{
            Status = "ANALYZED"
            ProcessCountTrend = $processCountTrend
            ProcessCountGrowthRate = $processCountChange
            AverageProcessCount = [math]::Round($avgProcessCount, 1)
            MaxProcessCount = $maxProcessCount
            MinProcessCount = $minProcessCount
            CurrentProcessCount = $recentCount
        }
    }
    
    [PSCustomObject]DetectSystemMemoryLeaks() {
        $memoryAnalysis = $this.AnalyzeTotalMemoryTrend()
        $processAnalysis = $this.AnalyzeProcessCountTrend()
        
        $leakDetected = $false
        $severity = "NONE"
        $recommendations = @()
        $actions = @()
        
        # Analyze total memory trends
        if ($memoryAnalysis.TrendType -eq "CRITICAL_SYSTEM_LEAK") {
            $leakDetected = $true
            $severity = "CRITICAL"
            $recommendations += "CRITICAL system-wide memory leak detected with $($memoryAnalysis.GrowthRate) MB/interval growth rate"
            $recommendations += "Total projected memory in 1 hour: $($memoryAnalysis.ProjectedMemoryIn1Hour) MB"
            $actions += "IMMEDIATE ACTION REQUIRED: Investigate all Python processes"
            $actions += "Consider system-wide restart of TESTRADE components"
        }
        elseif ($memoryAnalysis.TrendType -eq "SYSTEM_LEAK_DETECTED") {
            $leakDetected = $true
            $severity = "HIGH"
            $recommendations += "System-wide memory leak detected with $($memoryAnalysis.GrowthRate) MB/interval growth rate"
            $recommendations += "Total projected memory in 1 hour: $($memoryAnalysis.ProjectedMemoryIn1Hour) MB"
            $actions += "Plan for selective process restarts within next few hours"
            $actions += "Identify which specific processes are growing"
        }
        elseif ($memoryAnalysis.TrendType -eq "MODERATE_SYSTEM_GROWTH") {
            $severity = "MEDIUM"
            $recommendations += "Moderate system-wide memory growth detected"
            $recommendations += "Growth rate: $($memoryAnalysis.GrowthRate) MB/interval across all processes"
            $actions += "Monitor individual processes for specific leak sources"
        }
        elseif ($memoryAnalysis.TrendType -eq "SLOW_SYSTEM_GROWTH") {
            $severity = "LOW"
            $recommendations += "Slow system-wide memory growth - may be normal operational growth"
            $actions += "Continue monitoring for trend confirmation"
        }
        elseif ($memoryAnalysis.TrendType -eq "SYSTEM_STABLE") {
            $recommendations += "System-wide memory usage is stable - excellent health"
            $actions += "No action needed - system operating normally"
        }
        
        # Analyze process count trends
        if ($processAnalysis.ProcessCountTrend -eq "PROCESS_COUNT_INCREASING") {
            if ($severity -eq "NONE") { $severity = "LOW" }
            $recommendations += "Process count increasing: $($processAnalysis.ProcessCountGrowthRate) new processes"
            $actions += "Investigate why new Python processes are being created"
        }
        
        # Add volatility analysis
        if ($memoryAnalysis.Volatility -gt 30) {
            $recommendations += "High system memory volatility ($($memoryAnalysis.Volatility)%) detected"
            $actions += "Check for irregular processing patterns across all processes"
        }
        
        return [PSCustomObject]@{
            LeakDetected = $leakDetected
            Severity = $severity
            MemoryAnalysis = $memoryAnalysis
            ProcessAnalysis = $processAnalysis
            Recommendations = $recommendations
            Actions = $actions
            Timestamp = Get-Date
        }
    }
}

# Initialize analyzer
$analyzer = [MultiProcessMemoryAnalyzer]::new($ProcessPattern, $AnalysisWindowMinutes)
$alertCount = 0
$startTime = Get-Date

# Create CSV header if logging enabled
if ($EnableCSVLogging) {
    "Timestamp,ProcessCount,TotalMemoryMB,AverageMemoryMB,MaxMemoryMB,TotalHandles,TotalThreads,TrendType,GrowthRate,Confidence,Severity,LeakDetected,ProcessCountTrend" | Out-File $LogFile
}

Write-Host "🔍 TANK Multi-Process Advanced Memory Leak Detector" -ForegroundColor Green
Write-Host "Process Pattern: $ProcessPattern | Sample Interval: $SampleIntervalSeconds seconds" -ForegroundColor Cyan
Write-Host "Analysis Window: $AnalysisWindowMinutes minutes | CSV Logging: $EnableCSVLogging | Alerts: $EnableAlerts" -ForegroundColor Yellow
Write-Host "=" * 100

while ($true) {
    try {
        $processes = Get-Process $ProcessPattern -ErrorAction SilentlyContinue
        
        if ($processes.Count -eq 0) {
            Write-Host "❌ No processes found matching pattern '$ProcessPattern'" -ForegroundColor Red
            Start-Sleep $SampleIntervalSeconds
            continue
        }
        
        $timestamp = Get-Date
        
        # Collect aggregate sample data
        $totalMemoryBytes = 0
        $totalHandles = 0
        $totalThreads = 0
        $memoryValues = @()
        
        foreach ($process in $processes) {
            try {
                $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
                $memoryValues += $memoryMB
                $totalMemoryBytes += $process.WorkingSet64
                $totalHandles += $process.HandleCount
                $totalThreads += $process.Threads.Count
            } catch {
                # Skip processes that can't be accessed
            }
        }
        
        $totalMemoryMB = [math]::Round($totalMemoryBytes / 1MB, 2)
        $averageMemoryMB = if ($memoryValues.Count -gt 0) { [math]::Round(($memoryValues | Measure-Object -Average).Average, 2) } else { 0 }
        $maxMemoryMB = if ($memoryValues.Count -gt 0) { [math]::Round(($memoryValues | Measure-Object -Maximum).Maximum, 2) } else { 0 }
        
        $sample = [PSCustomObject]@{
            Timestamp = $timestamp
            ProcessCount = $processes.Count
            TotalMemoryMB = $totalMemoryMB
            AverageMemoryMB = $averageMemoryMB
            MaxMemoryMB = $maxMemoryMB
            TotalHandles = $totalHandles
            TotalThreads = $totalThreads
        }
        
        $analyzer.AddSample($sample)
        
        # Perform system-wide leak analysis
        $leakAnalysis = $analyzer.DetectSystemMemoryLeaks()
        
        # CSV Logging
        if ($EnableCSVLogging) {
            "$($timestamp.ToString('yyyy-MM-dd HH:mm:ss')),$($sample.ProcessCount),$($sample.TotalMemoryMB),$($sample.AverageMemoryMB),$($sample.MaxMemoryMB),$($sample.TotalHandles),$($sample.TotalThreads),$($leakAnalysis.MemoryAnalysis.TrendType),$($leakAnalysis.MemoryAnalysis.GrowthRate),$($leakAnalysis.MemoryAnalysis.Confidence),$($leakAnalysis.Severity),$($leakAnalysis.LeakDetected),$($leakAnalysis.ProcessAnalysis.ProcessCountTrend)" | Out-File $LogFile -Append
        }
        
        # Display results
        Clear-Host
        Write-Host "🔍 TANK Multi-Process Advanced Memory Leak Detector" -ForegroundColor Green
        Write-Host "=" * 100
        Write-Host "Current Time: $($timestamp.ToString('HH:mm:ss')) | Runtime: $([math]::Round(((Get-Date) - $startTime).TotalMinutes, 1)) minutes" -ForegroundColor Cyan
        Write-Host "Sample Count: $($analyzer.Samples.Count) | Analysis Window: $AnalysisWindowMinutes minutes" -ForegroundColor Cyan
        Write-Host ""
        
        # Current system metrics
        Write-Host "📊 CURRENT SYSTEM METRICS:" -ForegroundColor Yellow
        Write-Host "  Total Processes:  $($sample.ProcessCount)" -ForegroundColor White
        Write-Host "  Total Memory:     $($sample.TotalMemoryMB) MB" -ForegroundColor White
        Write-Host "  Average Memory:   $($sample.AverageMemoryMB) MB per process" -ForegroundColor White
        Write-Host "  Largest Process:  $($sample.MaxMemoryMB) MB" -ForegroundColor White
        Write-Host "  Total Handles:    $($sample.TotalHandles)" -ForegroundColor White
        Write-Host "  Total Threads:    $($sample.TotalThreads)" -ForegroundColor White
        Write-Host ""
        
        # System-wide analysis
        if ($leakAnalysis.MemoryAnalysis.Status -eq "ANALYZED") {
            Write-Host "📈 SYSTEM-WIDE MEMORY ANALYSIS:" -ForegroundColor Yellow
            Write-Host "  Trend Type:       $($leakAnalysis.MemoryAnalysis.TrendType)" -ForegroundColor $(
                switch ($leakAnalysis.MemoryAnalysis.TrendType) {
                    "CRITICAL_SYSTEM_LEAK" { "Red" }
                    "SYSTEM_LEAK_DETECTED" { "Red" }
                    "MODERATE_SYSTEM_GROWTH" { "Yellow" }
                    "SLOW_SYSTEM_GROWTH" { "Yellow" }
                    "SYSTEM_STABLE" { "Green" }
                    "SYSTEM_DECREASING" { "Cyan" }
                    default { "White" }
                }
            )
            Write-Host "  Growth Rate:      $($leakAnalysis.MemoryAnalysis.GrowthRate) MB per interval" -ForegroundColor White
            Write-Host "  Confidence:       $($leakAnalysis.MemoryAnalysis.Confidence)%" -ForegroundColor White
            Write-Host "  Volatility:       $($leakAnalysis.MemoryAnalysis.Volatility)%" -ForegroundColor White
            Write-Host ""
            
            if ($leakAnalysis.MemoryAnalysis.ProjectedMemoryIn1Hour) {
                Write-Host "🔮 SYSTEM PROJECTIONS:" -ForegroundColor Yellow
                Write-Host "  In 1 Hour:        $($leakAnalysis.MemoryAnalysis.ProjectedMemoryIn1Hour) MB total" -ForegroundColor Cyan
                Write-Host "  In 4 Hours:       $($leakAnalysis.MemoryAnalysis.ProjectedMemoryIn4Hours) MB total" -ForegroundColor Cyan
                Write-Host ""
            }
        }
        
        # Process count analysis
        if ($leakAnalysis.ProcessAnalysis.Status -eq "ANALYZED") {
            Write-Host "🔢 PROCESS COUNT ANALYSIS:" -ForegroundColor Yellow
            Write-Host "  Process Trend:    $($leakAnalysis.ProcessAnalysis.ProcessCountTrend)" -ForegroundColor $(
                switch ($leakAnalysis.ProcessAnalysis.ProcessCountTrend) {
                    "PROCESS_COUNT_INCREASING" { "Yellow" }
                    "PROCESS_COUNT_DECREASING" { "Cyan" }
                    "PROCESS_COUNT_STABLE" { "Green" }
                    default { "White" }
                }
            )
            Write-Host "  Current Count:    $($leakAnalysis.ProcessAnalysis.CurrentProcessCount)" -ForegroundColor White
            Write-Host "  Average Count:    $($leakAnalysis.ProcessAnalysis.AverageProcessCount)" -ForegroundColor White
            Write-Host "  Count Range:      $($leakAnalysis.ProcessAnalysis.MinProcessCount) - $($leakAnalysis.ProcessAnalysis.MaxProcessCount)" -ForegroundColor White
            Write-Host ""
        }
        
        # System leak detection results
        if ($leakAnalysis.LeakDetected) {
            $alertCount++
            Write-Host "🚨 SYSTEM-WIDE MEMORY LEAK DETECTED! (Alert #$alertCount)" -ForegroundColor Red -BackgroundColor Yellow
            Write-Host "  Severity: $($leakAnalysis.Severity)" -ForegroundColor Red
            Write-Host ""
            
            Write-Host "📋 ANALYSIS:" -ForegroundColor Yellow
            $leakAnalysis.Recommendations | ForEach-Object {
                Write-Host "  • $_" -ForegroundColor White
            }
            Write-Host ""
            
            Write-Host "⚡ RECOMMENDED ACTIONS:" -ForegroundColor Red
            $leakAnalysis.Actions | ForEach-Object {
                Write-Host "  • $_" -ForegroundColor Yellow
            }
            
            # Send Windows notification if enabled
            if ($EnableAlerts) {
                try {
                    Add-Type -AssemblyName System.Windows.Forms
                    $notification = New-Object System.Windows.Forms.NotifyIcon
                    $notification.Icon = [System.Drawing.SystemIcons]::Warning
                    $notification.BalloonTipTitle = "TANK System Memory Leak Detected"
                    $notification.BalloonTipText = "Severity: $($leakAnalysis.Severity) | $($sample.ProcessCount) processes | Growth: $($leakAnalysis.MemoryAnalysis.GrowthRate) MB/interval"
                    $notification.Visible = $true
                    $notification.ShowBalloonTip(5000)
                    $notification.Dispose()
                } catch {
                    Write-Host "  (Alert notification failed)" -ForegroundColor Gray
                }
            }
            
        } else {
            Write-Host "✅ NO SYSTEM-WIDE MEMORY LEAK DETECTED" -ForegroundColor Green
            if ($leakAnalysis.Recommendations.Count -gt 0) {
                Write-Host ""
                Write-Host "📋 SYSTEM STATUS:" -ForegroundColor Yellow
                $leakAnalysis.Recommendations | ForEach-Object {
                    Write-Host "  • $_" -ForegroundColor Gray
                }
            }
        }
        
        Write-Host ""
        Write-Host "Next analysis in $SampleIntervalSeconds seconds... (Ctrl+C to stop)" -ForegroundColor Gray
        if ($EnableCSVLogging) {
            Write-Host "Data logged to: $LogFile" -ForegroundColor Gray
        }
        
        Start-Sleep $SampleIntervalSeconds
        
    } catch {
        Write-Host "❌ Error during system monitoring: $($_.Exception.Message)" -ForegroundColor Red
        Start-Sleep 10
    }
}
