# TANK Memory Monitor Usage Guide

## 🚀 Overview

Two PowerShell scripts for comprehensive monitoring of TESTRADE ApplicationCore (TANK) memory usage and system health:

1. **`tank_memory_monitor.ps1`** - Basic real-time memory monitoring
2. **`tank_bulletproof_monitor.ps1`** - Enhanced monitoring for Bulletproof IPC system

## 📋 Prerequisites

- PowerShell 5.1 or later
- Administrator privileges (recommended for full process access)
- TESTRADE ApplicationCore running

## 🔧 Basic Usage

### Standard Memory Monitoring
```powershell
# Basic monitoring with default settings
.\tank_memory_monitor.ps1

# Custom interval and process name
.\tank_memory_monitor.ps1 -ProcessName "ApplicationCore" -IntervalSeconds 15

# Custom log file location
.\tank_memory_monitor.ps1 -LogFile "C:\TESTRADE\logs\memory_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
```

### Enhanced Bulletproof IPC Monitoring
```powershell
# Full monitoring with all features
.\tank_bulletproof_monitor.ps1

# Quick monitoring (10-second intervals)
.\tank_bulletproof_monitor.ps1 -IntervalSeconds 10

# Disable Redis checking for offline testing
.\tank_bulletproof_monitor.ps1 -CheckRedisConnection:$false

# Custom Redis connection
.\tank_bulletproof_monitor.ps1 -RedisHost "**************" -RedisPort 6379
```

## 📊 Features Comparison

| Feature | Basic Monitor | Bulletproof Monitor |
|---------|---------------|-------------------|
| Memory Usage | ✅ | ✅ |
| CPU Usage | ✅ | ✅ |
| Handle/Thread Count | ✅ | ✅ |
| Visual Graph | ✅ | ✅ |
| Trend Analysis | ✅ | ✅ |
| CSV Logging | ✅ | ✅ |
| Mmap Buffer Monitoring | ❌ | ✅ |
| Redis Connection Check | ❌ | ✅ |
| ZMQ Port Detection | ❌ | ✅ |
| Enhanced Alerts | ❌ | ✅ |

## 🎯 Key Metrics Monitored

### Memory Metrics
- **Working Set**: Current memory usage
- **Peak Working Set**: Maximum memory used
- **Paged Memory**: Virtual memory paged to disk
- **Non-Paged Memory**: Physical memory that cannot be paged

### Performance Metrics
- **CPU Usage**: Processor utilization percentage
- **Handle Count**: Number of system handles
- **Thread Count**: Number of active threads

### Bulletproof IPC Specific
- **Mmap Buffers**: Size of circular buffer files
  - Trading buffer (`bulletproof_ipc_buffer_trading.mmap`)
  - System buffer (`bulletproof_ipc_buffer_system.mmap`)
  - Bulk buffer (`bulletproof_ipc_buffer_bulk.mmap`)
- **Redis Connectivity**: Connection status to Redis server
- **ZMQ Ports**: Number of listening ZeroMQ ports (5555, 5556, 5557)

## 🚨 Alert Conditions

### Memory Alerts
- **🚨 CRITICAL**: Memory growth >100MB in single interval
- **⚠️ WARNING**: Continued growth >10MB per interval after 30 minutes
- **✅ GOOD**: Stable memory usage (0 growth)

### System Alerts (Bulletproof Monitor)
- **🚨 CRITICAL**: Redis connection lost
- **⚠️ WARNING**: Missing ZMQ listening ports
- **⚠️ WARNING**: Mmap buffer files not found
- **✅ EXCELLENT**: All systems stable and connected

## 📈 Trend Analysis

The monitors track the last 5 measurements to determine trends:
- **📈 INCREASING**: Average growth >5MB per interval
- **📉 DECREASING**: Average decline >5MB per interval  
- **📊 STABLE**: Growth between -5MB and +5MB per interval

## 📝 CSV Log Format

### Basic Monitor
```
Timestamp,ElapsedMinutes,MemoryMB,MemoryGB,CPUPercent,HandleCount,ThreadCount,WorkingSetPeak,PagedMemory,NonPagedMemory
```

### Bulletproof Monitor
```
Timestamp,ElapsedMinutes,MemoryMB,MemoryGB,CPUPercent,HandleCount,ThreadCount,WorkingSetPeak,PagedMemory,NonPagedMemory,MmapTradingMB,MmapSystemMB,MmapBulkMB,RedisConnected,ZmqPorts
```

## 🔍 Troubleshooting

### Process Not Found
```
❌ Error: Process 'ApplicationCore' not found or access denied
```
**Solutions:**
1. Verify ApplicationCore is running: `Get-Process *application*`
2. Check exact process name: `Get-Process | Where-Object {$_.ProcessName -like "*tank*"}`
3. Run PowerShell as Administrator
4. Use correct process name: `-ProcessName "python"`

### Access Denied
**Solutions:**
1. Run PowerShell as Administrator
2. Check Windows Defender/Antivirus settings
3. Verify user has process monitoring permissions

### Mmap Files Not Found
**Solutions:**
1. Verify ApplicationCore is using Bulletproof IPC client
2. Check mmap file paths in `/dev/shm/` directory
3. Ensure WSL2 is properly configured for shared memory

## 💡 Best Practices

### For Development Testing
```powershell
# Quick monitoring during development
.\tank_bulletproof_monitor.ps1 -IntervalSeconds 5 -LogFile "dev_test.csv"
```

### For Production Monitoring
```powershell
# Longer intervals for production
.\tank_bulletproof_monitor.ps1 -IntervalSeconds 60 -LogFile "production_$(Get-Date -Format 'yyyyMMdd').csv"
```

### For Memory Leak Detection
```powershell
# Frequent monitoring for leak detection
.\tank_memory_monitor.ps1 -IntervalSeconds 10 -LogFile "leak_test_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
```

## 📊 Data Analysis

### PowerShell Analysis
```powershell
# Import and analyze CSV data
$data = Import-Csv "tank_memory_detailed.csv"
$data | Measure-Object MemoryMB -Average -Maximum -Minimum
$data | Where-Object {$_.ElapsedMinutes -gt 60} | Measure-Object MemoryMB -Average
```

### Excel Analysis
1. Open CSV file in Excel
2. Create charts for memory usage over time
3. Use conditional formatting for growth rate analysis
4. Calculate moving averages for trend analysis

## 🎯 Use Cases

### Memory Leak Detection
- Run for extended periods (hours/days)
- Monitor for consistent upward trends
- Alert on growth >10MB per interval

### Performance Optimization
- Monitor during high-load scenarios
- Track CPU and memory correlation
- Identify optimal buffer sizes

### System Health Monitoring
- Continuous monitoring in production
- Alert on connectivity issues
- Track system stability metrics

### Bulletproof IPC Validation
- Verify mmap buffer creation
- Monitor ZMQ port availability
- Validate Redis connectivity
- Track graduated defense activation
