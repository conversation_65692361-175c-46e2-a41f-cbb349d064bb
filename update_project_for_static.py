#!/usr/bin/env python3
"""
Update the Visual Studio project for static linking.
This will eliminate all DLL dependencies.
"""

import xml.etree.ElementTree as ET
import shutil
import os

def backup_project_file():
    """Create a backup of the current project file."""
    source = "ocr_accelerator/ocr_accelerator.vcxproj"
    backup = "ocr_accelerator/ocr_accelerator.vcxproj.backup"
    
    if os.path.exists(source):
        shutil.copy2(source, backup)
        print(f"✅ Created backup: {backup}")
        return True
    else:
        print("❌ Project file not found")
        return False

def update_project_for_static():
    """Update the project file for static linking."""
    print("=== Updating Project for Static Linking ===")
    
    project_file = "ocr_accelerator/ocr_accelerator.vcxproj"
    
    try:
        # Parse the XML
        tree = ET.parse(project_file)
        root = tree.getroot()
        
        # Define namespace
        ns = {'ms': 'http://schemas.microsoft.com/developer/msbuild/2003'}
        
        # Find the Release|x64 configuration
        for item_group in root.findall('.//ms:ItemDefinitionGroup[@Condition="\'$(Configuration)|$(Platform)\'==\'Release|x64\'"]', ns):
            
            # Update ClCompile settings
            compile_elem = item_group.find('ms:ClCompile', ns)
            if compile_elem is not None:
                # Add static runtime
                runtime_elem = compile_elem.find('ms:RuntimeLibrary', ns)
                if runtime_elem is None:
                    runtime_elem = ET.SubElement(compile_elem, 'RuntimeLibrary')
                runtime_elem.text = 'MultiThreaded'  # /MT instead of /MD
                print("✅ Set RuntimeLibrary to MultiThreaded (/MT)")
                
                # Update preprocessor definitions
                preproc_elem = compile_elem.find('ms:PreprocessorDefinitions', ns)
                if preproc_elem is not None:
                    # Add static linking definitions
                    current_defs = preproc_elem.text or ""
                    if 'TESSERACT_STATIC' not in current_defs:
                        preproc_elem.text = current_defs + ';TESSERACT_STATIC'
                    if 'LEPTONICA_STATIC' not in current_defs:
                        preproc_elem.text = preproc_elem.text + ';LEPTONICA_STATIC'
                    print("✅ Added static linking preprocessor definitions")
            
            # Update Link settings
            link_elem = item_group.find('ms:Link', ns)
            if link_elem is not None:
                # Update library dependencies for static linking
                deps_elem = link_elem.find('ms:AdditionalDependencies', ns)
                if deps_elem is not None:
                    # Static library dependencies (no opencv since we removed it)
                    static_deps = [
                        'tesseract55.lib',
                        'leptonica-1.85.0.lib',
                        'giflib.lib',
                        'jpeg.lib', 
                        'libpng16.lib',
                        'tiff.lib',
                        'zlib.lib',
                        'python311.lib',
                        'kernel32.lib',
                        'user32.lib',
                        'gdi32.lib',
                        'winspool.lib',
                        'comdlg32.lib',
                        'advapi32.lib',
                        'shell32.lib',
                        'ole32.lib',
                        'oleaut32.lib',
                        'uuid.lib',
                        'odbc32.lib',
                        'odbccp32.lib'
                    ]
                    deps_elem.text = ';'.join(static_deps)
                    print("✅ Updated to static library dependencies")
        
        # Write the updated project file
        tree.write(project_file, encoding='utf-8', xml_declaration=True)
        print("✅ Project file updated successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error updating project file: {e}")
        return False

def print_rebuild_instructions():
    """Print instructions for rebuilding."""
    print("\n=== Rebuild Instructions ===")
    print("1. Open Visual Studio 2022")
    print("2. Open the project: ocr_accelerator/ocr_accelerator.sln")
    print("3. Set configuration to 'Release' and platform to 'x64'")
    print("4. Build -> Rebuild Solution")
    print()
    print("Expected result:")
    print("✅ Self-contained .pyd file (no external DLL dependencies)")
    print("✅ Clean root directory (no DLL pollution)")
    print("✅ Portable module that works anywhere")
    print()
    print("If build fails, you may need to:")
    print("- Install static vcpkg packages first")
    print("- Update include/library paths for static versions")

if __name__ == "__main__":
    print("TESTRADE Project Update for Static Linking")
    print("=" * 50)
    
    # Backup current project
    if backup_project_file():
        # Update project settings
        if update_project_for_static():
            print_rebuild_instructions()
            print("\n🎯 This will solve the DLL issue permanently!")
        else:
            print("\n❌ Failed to update project file")
    else:
        print("\n❌ Could not create backup")