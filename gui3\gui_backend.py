# gui/gui_backend.py

import asyncio
import logging
import json
import sys
import os
from typing import Dict, List, Optional, Any
from collections import deque
from datetime import datetime
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn
import uuid
import time
import redis
from contextlib import asynccontextmanager

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from intellisense.capture.redis_stream_consumer_base import RedisStreamConsumerBase
from utils.global_config import GlobalConfig as GlobalConfigClass, config as global_app_config
from utils.redis_client_manager import RedisClientManager
from utils.redis_utils import create_redis_message_json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = FastAPI(title="TESTRADE Pro API", version="2025.1.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for CSS, JS, and other assets
app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(__file__))), name="static")

# --- WebSocket and State Management ---
class WebSocketManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.logger = logging.getLogger(__name__)
        self._last_connection_status_log = 0
        self._connection_status_interval = 300  # Log status every 5 minutes

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"🔌 GUI client connected: {websocket.client.host} (Total: {len(self.active_connections)})")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"🔌 GUI client disconnected: {websocket.client.host} (Remaining: {len(self.active_connections)})")

    async def _send_to_connection(self, connection: WebSocket, message_str: str):
        try:
            await connection.send_text(message_str)
        except WebSocketDisconnect:
            self.logger.warning(f"WebSocket client {connection.client.host} disconnected during send.")
            self.disconnect(connection) # Ensure disconnect is called
        except Exception as e:
            self.logger.error(f"Error sending to WebSocket client {connection.client.host}: {e}", exc_info=True)
            # Optionally disconnect on other errors too, or just log
            self.disconnect(connection)

    async def broadcast(self, message: dict, high_priority: bool = False):
        if not self.active_connections:
            # Periodic status logging to help understand system state
            current_time = time.time()
            if current_time - self._last_connection_status_log > self._connection_status_interval:
                self.logger.info(f"📊 GUI Backend Status: No GUI clients connected, but receiving data streams (OCR, images, etc.)")
                self.logger.info(f"💡 To connect GUI: Open browser to http://localhost:8001/gui or ws://localhost:8001/ws")
                self._last_connection_status_log = current_time

            # Only log high-priority messages or less frequent message types to reduce noise
            message_type = message.get('type', 'unknown')
            if high_priority or message_type in ['system_message', 'error', 'core_status_update', 'command_response']:
                self.logger.info(f"🔌 No GUI clients connected for broadcast: {message_type}")
            else:
                # Use debug level for frequent data messages (OCR, images, etc.)
                self.logger.debug(f"🔌 No GUI clients connected for broadcast: {message_type}")
            return

        # 🚨 CHUNK 13: Use more specific logger for broadcasting and get message_type early
        broadcast_logger = self.logger
        message_type = message.get('type', 'unknown')  # Get message_type early
        broadcast_logger.info(f"📡 Broadcasting {message_type} to {len(self.active_connections)} connections")

        # 🚨 CHUNK 13: High-priority message handling
        if high_priority:
            message['_priority'] = 'high'
            message['_timestamp_ns'] = time.time_ns()  # Ensure time is imported
            broadcast_logger.debug(f"Broadcasting high-priority message: {message_type}")

        # 🚨 CHUNK 20: Add timestamp for frontend latency measurement
        if message_type in ['image_grab', 'preview_frame']:
            message['timestamp_backend_sent_ns'] = time.perf_counter_ns()

        # Track latency for GUI display (ensure AppState has this method)
        if hasattr(app_state, 'track_message_latency'):
            app_state.track_message_latency()

        # Add latency info to health_update messages (ensure AppState has this method)
        if message.get('type') == 'health_update' and message.get('component') == 'ApplicationCore':
            if hasattr(app_state, 'get_average_latency'):
                avg_latency = app_state.get_average_latency()
                if avg_latency > 0:
                    message.setdefault('metrics', {})['gui_latency_ms'] = avg_latency

        # 🚨 CHUNK 13: Conditional detailed logging for message content
        # (Assuming broadcast_logger is configured for DEBUG level appropriately)
        if broadcast_logger.isEnabledFor(logging.DEBUG) or \
           message_type in ['system_message', 'error', 'command_response_v2', 'core_status_update']:  # Log these important types at INFO if not DEBUG
            try:
                message_to_log = message.copy()
                if message_to_log.get('type') in ['preview_frame', 'image_grab'] and 'image_data' in message_to_log:
                    message_to_log['image_data'] = f"<base64_image_data_len:{len(message_to_log['image_data'])}>"
                if message_to_log.get('type') == 'raw_ocr' and 'content' in message_to_log:
                    if isinstance(message_to_log['content'], str) and len(message_to_log['content']) > 200:
                        message_to_log['content'] = f"<ocr_text_len:{len(message_to_log['content'])}>"

                # 🚨 CHUNK 13: Log with indent only for specific important types if logger is INFO, otherwise no indent for DEBUG
                log_indent = 2 if broadcast_logger.isEnabledFor(logging.INFO) and message_type in ['system_message', 'error', 'command_response_v2'] else None
                log_level_to_use = logging.DEBUG if not (broadcast_logger.isEnabledFor(logging.INFO) and message_type in ['system_message', 'error', 'command_response_v2']) else logging.INFO

                broadcast_logger.log(log_level_to_use, f"📦 Broadcasting JSON content (type: {message_type}): {json.dumps(message_to_log, indent=log_indent)}")

            except TypeError as te:
                broadcast_logger.error(f"Error preparing message for logging (broadcast will proceed with original): {te}")
            except Exception as e_log:
                broadcast_logger.error(f"Unexpected error during message logging prep: {e_log}")

        message_str = json.dumps(message)  # Final dump for sending, no indent for performance

        tasks = []
        for connection in self.active_connections[:]:  # Iterate a copy for safe removal
            tasks.append(asyncio.create_task(self._send_to_connection(connection, message_str)))

        if not tasks:
            return

        if high_priority:
            # 🚨 CHUNK 13: High-priority messages get immediate processing with short timeout
            done, pending = await asyncio.wait(tasks, return_when=asyncio.ALL_COMPLETED, timeout=0.1)  # Short timeout for high priority
            for task in pending:
                task.cancel()
                broadcast_logger.warning(f"Cancelled slow connection for high-priority message type {message_type}")
        else:
            # 🚨 CHUNK 13: Standard priority processing WITH TIMEOUT
            standard_broadcast_timeout_seconds = 2.0  # Configurable, e.g., app_state.config.standard_broadcast_timeout
            try:
                results = await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=standard_broadcast_timeout_seconds
                )
                # Optional: Log summary of failures if needed, though _send_to_connection handles individual logs
                num_failed = sum(1 for r in results if isinstance(r, Exception))
                if num_failed > 0:
                    broadcast_logger.debug(f"Standard broadcast (type: {message_type}) to {len(tasks)} clients completed within {standard_broadcast_timeout_seconds}s with {num_failed} send failures.")

            except asyncio.TimeoutError:
                broadcast_logger.warning(
                    f"Standard broadcast (type: {message_type}) to {len(tasks)} clients timed out after {standard_broadcast_timeout_seconds}s. "
                    f"Some messages may not have been sent or fully processed by clients."
                )
                # Note: Individual tasks are not cancelled here, they will continue until completion/error.
                # _send_to_connection will handle individual disconnects if errors occur.
            except Exception as e_gather:
                broadcast_logger.error(f"Unexpected error during asyncio.gather for standard broadcast (type: {message_type}): {e_gather}", exc_info=True)


class AppState:
    def __init__(self):
        self.websocket_manager = WebSocketManager()
        self.redis_stream_consumers: Dict[str, RedisStreamConsumerBase] = {}
        self.xread_consumer_task: Optional[asyncio.Task] = None
        self.main_event_loop: Optional[asyncio.AbstractEventLoop] = None
        self.redis_mgr: Optional[RedisClientManager] = None
        self.babysitter_ipc_client = None  # Will be BulletproofBabysitterIPCClient

        # Use the imported singleton config directly
        self.config = global_app_config

        # For Bootstrap API response correlation
        self.pending_api_responses: Dict[str, Dict[str, Any]] = {}
        self.api_response_timeout_sec: float = 15.0

        logger.info(f"AppState initialized. GUI XREAD Feature Flag: {self.config.FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API}")

        self.financial_data = {"dayPnL": 0.0, "winRate": 0.0, "totalTrades": 0, "accountValue": 50000.0, "buyingPower": 25000.0, "totalPnL": 0.0}
        self.active_trades = []
        self.current_roi = [64, 159, 681, 296]  # Default ROI, will be updated from events
        self.system_config = {"initialSize": 1000, "addType": "Equal", "reducePercent": 50, "ocrActive": False, "recordingActive": False}
        self.price_data = {}  # Store bid/ask/last price data by symbol

        # 🔧 WIRING: Active symbols for event bus filtering
        self.active_symbols = set()  # Symbols with active trades for filtering market data

        # Latency tracking for GUI display
        self.latency_samples = deque(maxlen=10)  # Keep last 10 samples
        self.last_message_time = time.time()

    def track_message_latency(self):
        """Track latency between messages for GUI display"""
        current_time = time.time()
        if hasattr(self, 'last_message_time'):
            latency_ms = (current_time - self.last_message_time) * 1000
            self.latency_samples.append(latency_ms)
        self.last_message_time = current_time

    def get_average_latency(self):
        """Get average latency from recent samples"""
        if not self.latency_samples:
            return 0.0
        return sum(self.latency_samples) / len(self.latency_samples)

app_state = AppState()

class CommandRequest(BaseModel):
    command: str
    parameters: Dict[str, Any] = {}

class ConfigUpdate(BaseModel):
    initialSize: Optional[int] = None
    addType: Optional[str] = None
    reducePercent: Optional[float] = None
    ocrActive: Optional[bool] = None
    recordingActive: Optional[bool] = None

# --- Redis Message Handlers ---
def dispatch_to_event_loop(handler):
    def wrapper(message_id: str, raw_redis_message: Dict[str, Any]):
        if app_state.main_event_loop and app_state.main_event_loop.is_running():
            asyncio.run_coroutine_threadsafe(handler(message_id, raw_redis_message), app_state.main_event_loop)
        else:
            logger.warning(f"Event loop not available for handler. Message ID: {message_id}")
    return wrapper

def parse_appcore_message(raw_redis_message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    json_payload_str = raw_redis_message.get("json_payload")
    if not json_payload_str:
        logger.warning("Message missing 'json_payload' field.")
        return None
    try:
        wrapper_dict = json.loads(json_payload_str)
        payload = wrapper_dict.get("payload", {})
        metadata = wrapper_dict.get("metadata", {})

        # Preserve correlation ID from metadata in the payload for backward compatibility
        if "correlationId" in metadata and "bootstrap_correlation_id" not in payload:
            payload["bootstrap_correlation_id"] = metadata["correlationId"]

        # Return the full message structure with both metadata and payload
        return {
            "metadata": metadata,
            "payload": payload
        }
    except (json.JSONDecodeError, TypeError) as e:
        logger.error(f"Failed to parse JSON payload: {e}, payload string: {json_payload_str[:200]}") # Log part of payload
        return None

def transform_account_data_for_frontend(raw_account_data: Dict[str, Any], is_bootstrap: bool = False) -> Dict[str, Any]:
    """
    Transform account data from backend format to frontend expected format.
    Centralizes all field mappings in one place for easier maintenance.
    
    Backend fields -> Frontend fields:
    - equity -> account_value
    - buying_power -> buying_power (same)
    - cash -> cash (same)
    - realized_pnl_day -> realized_pnl_day (same)
    - unrealized_pnl_day -> unrealized_pnl_day (same)
    - (calculated) -> day_pnl (realized + unrealized)
    - (calculated) -> total_pnl (realized + unrealized)
    """
    # Calculate P&L values
    realized = raw_account_data.get("realized_pnl_day", 0) or 0
    unrealized = raw_account_data.get("unrealized_pnl_day", 0) or 0
    total_pnl = realized + unrealized
    
    return {
        "account_id": raw_account_data.get("account_id", ""),
        "account_value": raw_account_data.get("equity", 0.0),  # Map equity to account_value
        "buying_power": raw_account_data.get("buying_power", 0.0),
        "cash": raw_account_data.get("cash", 0.0),
        "realized_pnl_day": realized,
        "unrealized_pnl_day": unrealized,
        "day_pnl": total_pnl,
        "total_pnl": total_pnl,
        "is_bootstrap": is_bootstrap
    }

@dispatch_to_event_loop
async def handle_raw_ocr_message(message_id: str, raw_redis_message: Dict[str, Any]):
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning(f"No parsed message in raw OCR message {message_id}")
        return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    ocr_text = payload.get("full_raw_text", payload.get("text_detected", ""))
    confidence = payload.get("overall_confidence", 0.0)
    frame_timestamp = payload.get("frame_timestamp", time.time())

    # 🚨 PERFORMANCE FIX: Streamlined to match image_grab handler performance

    # Send OCR text data
    gui_message_ocr_text = {"type": "raw_ocr", "content": ocr_text, "confidence": confidence, "timestamp": datetime.fromtimestamp(frame_timestamp).isoformat()}
    await app_state.websocket_manager.broadcast(gui_message_ocr_text)

    # Send processed image data (black & white)
    screenshot_data = payload.get("processed_frame_base64", payload.get("frame_base64"))
    if screenshot_data:
        # 🚨 HIGH PRIORITY: Preview frame broadcasts get priority for real-time OCR display
        await app_state.websocket_manager.broadcast({"type": "preview_frame", "image_data": screenshot_data, "confidence": confidence, "content": ocr_text}, high_priority=True)

@dispatch_to_event_loop
async def handle_cleaned_ocr_message(message_id: str, raw_redis_message: Dict[str, Any]):
    logger.info(f"Processing cleaned OCR message {message_id}")
    
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message: 
        logger.warning(f"Failed to parse cleaned OCR message {message_id}")
        return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    # Check if this is a "no trade data" message
    if payload.get("type") == "no_trade_data":
        # Include timestamp from metadata or payload
        timestamp = metadata.get("timestamp", payload.get("timestamp", time.time()))
        
        # Send message to clear processed OCR display
        gui_message = {
            "type": "processed_ocr",
            "snapshots": {},
            "symbols_detected": [],
            "no_trade_data": True,
            "reason": payload.get("reason", "No trade or summary lines detected"),
            "timestamp": timestamp
        }
        await app_state.websocket_manager.broadcast(gui_message)
        logger.debug(f"Sent 'no trade data' message to clear processed OCR display")
        return

    snapshots = payload.get("snapshots", {})
    symbols_detected = list(snapshots.keys()) # Could be empty if no symbols found

    # Include timestamp from metadata or payload
    timestamp = metadata.get("timestamp", payload.get("timestamp", time.time()))
    
    gui_message = {
        "type": "processed_ocr", 
        "snapshots": snapshots, 
        "symbols_detected": symbols_detected,
        "timestamp": timestamp
    }
    
    logger.info(f"Broadcasting processed OCR: {len(symbols_detected)} symbols - {symbols_detected}")
    await app_state.websocket_manager.broadcast(gui_message)

    # This P&L calculation might be better if ApplicationCore sends a dedicated financial update
    # Or if this message *is* the definitive source for this specific P&L.
    total_pnl_from_snapshots = sum(snap.get("realized_pnl", 0.0) for snap in snapshots.values() if isinstance(snap, dict))
    # Only update if it's meaningful, or let account_summary handle it.
    # app_state.financial_data["totalPnL"] = total_pnl_from_snapshots
    # await app_state.websocket_manager.broadcast({"type": "financial_update", "payload": app_state.financial_data})

    logger.debug(f"Processed cleaned OCR: {len(symbols_detected)} symbols detected")

@dispatch_to_event_loop
async def handle_core_status_message(message_id: str, raw_redis_message: Dict[str, Any]):
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message: return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    status_event = payload.get("event", "UNKNOWN_EVENT")
    status_message = payload.get("message", "")
    gui_message = {"type": "core_status_update", "status_event": status_event, "message": status_message}
    await app_state.websocket_manager.broadcast(gui_message)
    logger.info(f"Core status: {status_event} - {status_message}")
    
# REMOVED: handle_command_response_message() - OLD LEGACY HANDLER
# Now using handle_phase2_response_message() for Phase 2-4 architecture

@dispatch_to_event_loop
async def handle_phase2_response_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """
    Phase 2: Handle responses from the new testrade:responses:to_gui stream.
    These responses include original_command_id for correlation.
    Also handles responses for Bootstrap APIs.
    """
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message: return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    # Extract correlation information from both payload and metadata
    original_command_id = payload.get("original_command_id") or metadata.get("correlationId")

    # NEW: Check if this response is for a pending Bootstrap API call
    if original_command_id and original_command_id in app_state.pending_api_responses:
        pending_info = app_state.pending_api_responses.get(original_command_id)
        if pending_info and not pending_info["event"].is_set():
            logger.info(f"Received correlated API response for {original_command_id}. Payload: {str(payload)[:200]}")
            pending_info["response_data"] = payload
            pending_info["event"].set()
            return  # Do not broadcast this further if it was for an API

    command_type_echo = payload.get("command_type_echo", payload.get("command_type"))
    status = payload.get("status", "unknown")
    message_text = payload.get("message", "")  # Renamed to avoid conflict
    response_data = payload.get("data", {})

    # Broadcast the generic command_response_v2 for client-side correlation/logging
    gui_message_v2 = {
        "type": "command_response_v2",  # New message type for Phase 2
        "original_command_id": original_command_id,
        "command_type": command_type_echo,  # Use the echoed type
        "status": status,
        "message": message_text,
        "data": response_data,  # Pass the full "data" part of the payload
        "timestamp": datetime.now().isoformat()
    }
    await app_state.websocket_manager.broadcast(gui_message_v2)  # Send the generic response first

    # Handle specific command types that result in specialized WebSocket messages (like data dumps)
    if status == "success":
        # --- BOOTSTRAP PATTERN: Handle bootstrap command responses ---
        if command_type_echo == "GET_ALL_POSITIONS_BOOTSTRAP" or command_type_echo == "GET_ALL_POSITIONS":
            logger.info(f"GUI Backend: Processing '{command_type_echo}' response data for positions bootstrap.")
            # `response_data` here IS the `command_response_data_field` from PositionManager,
            # which contains {"positions": [...], "market_data_available": ..., ...}
            await handle_all_positions_command_response(response_data)
        elif command_type_echo == "GET_POSITION_SUMMARY_BOOTSTRAP" or command_type_echo == "GET_POSITION_SUMMARY":
            logger.info(f"GUI Backend: Processing '{command_type_echo}' response data for position summary bootstrap.")
            # `response_data` here IS the `command_response_data_field` from PositionManager,
            # which contains {"summary": {...}, "market_data_available": ...}
            await handle_position_summary_command_response(response_data)
        elif command_type_echo == "GET_ACCOUNT_SUMMARY_BOOTSTRAP" or command_type_echo == "GET_ACCOUNT_SUMMARY":
            logger.info(f"GUI Backend: Processing '{command_type_echo}' response data for account summary bootstrap.")
            await handle_account_summary_command_response(response_data)
        # --- END OF BOOTSTRAP MODIFICATIONS ---
        elif command_type_echo == "GET_ALL_ORDERS":  # This is for non-bootstrap live requests if any remain
            await handle_all_orders_command_response(response_data)
        # REMOVED: GET_TRADE_HISTORY command response handler - obsolete
        # Trade history data now comes via testrade:trade-history Redis stream
        # ... any other specific command_type_echo handling ...

    logger.info(f"GUI Backend: Phase 2 Command response processed and broadcasted (OrigCmdID: {original_command_id}, TypeEcho: {command_type_echo}): Status: {status}")


# NEW: Helper function for Bootstrap APIs to send command and await response
async def send_core_command_and_await_response(
    command_type: str,
    parameters: Dict[str, Any],
    target_service: str = "CORE_SERVICE",
    event_type_str: str = "TESTRADE_GUI_API_COMMAND",
    source_component_name: str = "GUI_Backend_API"
) -> Dict[str, Any]:
    """
    Sends a command to the core service and waits for a correlated response
    via the testrade:responses:to_gui stream.
    """
    # Check Redis manager instead of IPC client
    if not app_state.redis_mgr:
        logger.error(f"RedisClientManager not available for API command '{command_type}'")
        raise HTTPException(status_code=503, detail="System not ready to process API requests (Redis unavailable).")

    correlation_id = str(uuid.uuid4())
    api_event = asyncio.Event()
    app_state.pending_api_responses[correlation_id] = {"event": api_event, "response_data": None}

    try:
        payload_dict = {
            "command_id": correlation_id,
            "target_service": target_service,
            "command_type": command_type,
            "parameters": parameters,
            "timestamp": time.time(),
            "source": source_component_name
        }
        redis_message_json = create_redis_message_json(
            payload=payload_dict,
            event_type_str=event_type_str,
            correlation_id_val=correlation_id,
            source_component_name=source_component_name
        )

        # Use stream name from loaded config
        command_stream = getattr(app_state.config, 'redis_stream_commands_from_gui', 'testrade:commands:from_gui')
        logger.info(f"API sending command '{command_type}' (CorrID: {correlation_id}) to stream '{command_stream}' for target '{target_service}'")

        # Send directly to Redis using xadd
        try:
            redis_client = app_state.redis_mgr.get_client()
            if not redis_client:
                logger.error(f"Redis client not available for API command '{command_type}'")
                raise HTTPException(status_code=503, detail="Redis connection not available.")
            
            # Use xadd to publish to the command stream
            message_id = await asyncio.to_thread(
                redis_client.xadd,
                command_stream,
                {"json_payload": redis_message_json}
            )
            logger.info(f"API command '{command_type}' sent to Redis stream '{command_stream}' with ID: {message_id}")
            
        except Exception as e:
            logger.error(f"Failed to send API command '{command_type}' to Redis: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to send command to core service: {str(e)}")

        await asyncio.wait_for(api_event.wait(), timeout=app_state.api_response_timeout_sec)
        response_payload = app_state.pending_api_responses[correlation_id].get("response_data")

        if response_payload is None:
            logger.error(f"API command '{command_type}' (CorrID: {correlation_id}) event set, but no response data found.")
            raise HTTPException(status_code=500, detail="Internal error: Response data missing after event.")

        if response_payload.get("status") != "success":
            error_message = response_payload.get("message", "Command processing failed in core service.")
            logger.warning(f"API command '{command_type}' (CorrID: {correlation_id}) failed in core. Response: {response_payload}")
            raise HTTPException(status_code=502, detail=f"Core service error: {error_message}")

        return response_payload.get("data", response_payload)

    except asyncio.TimeoutError:
        logger.error(f"API command '{command_type}' (CorrID: {correlation_id}) timed out after {app_state.api_response_timeout_sec}s.")
        raise HTTPException(status_code=504, detail=f"Request to core service timed out.")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in API command '{command_type}' (CorrID: {correlation_id}): {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error processing API request: {str(e)}")
    finally:
        if correlation_id in app_state.pending_api_responses:
            del app_state.pending_api_responses[correlation_id]


# --- NEW Bootstrap API Endpoints ---

@app.get("/api/v1/positions/current", tags=["Bootstrap API"])
async def get_current_positions():
    """
    Bootstrap API: Fetches all current open positions directly from Redis.
    NO communication with core - reads from Redis position streams.
    """
    try:
        if not app_state.redis_mgr:
            raise HTTPException(status_code=503, detail="Redis not available")
            
        redis_client = app_state.redis_mgr.get_client()
        if not redis_client:
            raise HTTPException(status_code=503, detail="Redis connection not available")
        
        positions = []
        
        # Read from position-summary stream first (comprehensive view)
        try:
            entries = redis_client.xrevrange("testrade:position-summary", count=1)
            if entries:
                message_id, data = entries[0]
                if 'json_payload' in data:
                    payload = json.loads(data['json_payload'])
                    if 'positions' in payload:
                        positions = payload['positions']
        except Exception as e:
            logger.warning(f"Failed to read position-summary: {e}")
        
        # If no positions from summary, try position-updates
        if not positions:
            try:
                # Get last 100 position updates to build current state
                entries = redis_client.xrevrange("testrade:position-updates", count=100)
                position_map = {}
                for message_id, data in entries:
                    if 'json_payload' in data:
                        payload = json.loads(data['json_payload'])
                        symbol = payload.get('symbol')
                        if symbol:
                            # Update or remove position based on quantity
                            if payload.get('quantity', 0) != 0:
                                position_map[symbol] = payload
                            else:
                                position_map.pop(symbol, None)
                positions = list(position_map.values())
            except Exception as e:
                logger.warning(f"Failed to read position-updates: {e}")
        
        return {
            "positions": positions,
            "market_data_available": True,
            "source": "redis_direct",
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in /api/v1/positions/current: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch current positions.")

@app.get("/api/v1/account/summary", tags=["Bootstrap API"])
async def get_account_summary():
    """
    Bootstrap API: Fetches the current account summary directly from Redis.
    """
    try:
        # Get the latest account data directly from Redis
        redis_client = app_state.redis_mgr.get_client()
        
        # Try account-updates first (real-time), fallback to account-summary
        entries = redis_client.xrevrange("testrade:account-updates", count=1)
        if not entries:
            entries = redis_client.xrevrange("testrade:account-summary", count=1)
        
        if entries:
            # Parse the latest entry
            entry_id, data = entries[0]
            # Handle both string and bytes keys depending on Redis client version
            json_payload = data.get('json_payload') or data.get(b'json_payload', '{}')
            if isinstance(json_payload, bytes):
                json_payload = json_payload.decode('utf-8')
            
            wrapper = json.loads(json_payload)
            account_data = wrapper.get('payload', {})
            
            # Use centralized transformation function
            return transform_account_data_for_frontend(account_data, is_bootstrap=True)
        else:
            # Return default values if no data in Redis
            return {
                "account_id": "",
                "account_value": 0.0,
                "buying_power": 0.0,
                "cash": 0.0,
                "realized_pnl_day": 0.0,
                "unrealized_pnl_day": 0.0,
                "day_pnl": 0.0,
                "total_pnl": 0.0,
                "is_bootstrap": True
            }
            
    except Exception as e:
        logger.error(f"Error in /api/v1/account/summary: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch account summary.")

@app.get("/api/v1/orders/today", tags=["Bootstrap API"])
async def get_orders_today():
    """
    Bootstrap API: Fetches all of today's orders directly from Redis.
    NO communication with core - reads from Redis order streams.
    """
    try:
        if not app_state.redis_mgr:
            raise HTTPException(status_code=503, detail="Redis not available")
            
        redis_client = app_state.redis_mgr.get_client()
        if not redis_client:
            raise HTTPException(status_code=503, detail="Redis connection not available")
        
        orders = []
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Read from multiple order streams to get complete picture
        order_streams = [
            "testrade:open-orders",
            "testrade:order-fills",
            "testrade:order-status",
            "testrade:order-requests"
        ]
        
        for stream in order_streams:
            try:
                # Get orders from today
                entries = redis_client.xrevrange(stream, count=200)
                for message_id, data in entries:
                    if 'json_payload' in data:
                        payload = json.loads(data['json_payload'])
                        # Check if order is from today
                        order_time = payload.get('timestamp', 0)
                        if isinstance(order_time, (int, float)):
                            order_dt = datetime.fromtimestamp(order_time)
                            if order_dt >= today_start:
                                # Add stream source for context
                                payload['source_stream'] = stream
                                orders.append(payload)
            except Exception as e:
                logger.warning(f"Failed to read from {stream}: {e}")
        
        # De-duplicate orders by order_id if present
        unique_orders = {}
        for order in orders:
            order_id = order.get('order_id') or order.get('orderId')
            if order_id:
                # Keep the most recent version
                if order_id not in unique_orders or order.get('timestamp', 0) > unique_orders[order_id].get('timestamp', 0):
                    unique_orders[order_id] = order
            else:
                # No order_id, include it anyway
                unique_orders[f"no_id_{len(unique_orders)}"] = order
        
        return {
            "orders": list(unique_orders.values()),
            "count": len(unique_orders),
            "source": "redis_direct",
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in /api/v1/orders/today: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch today's orders.")

@app.get("/api/v1/market/status", tags=["Bootstrap API"])
async def get_market_status():
    """
    Bootstrap API: Fetches the current market status from Redis directly.
    NO communication with core - reads from Redis health/status streams.
    """
    try:
        if not app_state.redis_mgr:
            raise HTTPException(status_code=503, detail="Redis not available")
            
        redis_cli = app_state.redis_mgr.get_client()
        if not redis_cli:
            raise HTTPException(status_code=503, detail="Redis connection not available")
        
        # Check if we have recent health data
        market_open = True  # Default assume market is open
        data_sources_healthy = True
        
        # Check core health status from in-memory state
        if hasattr(app_state, 'core_status') and app_state.core_status:
            data_sources_healthy = (app_state.core_status == 'healthy')
        
        # Return market status based on Redis data
        return {
            "market_open": market_open,
            "data_sources_healthy": data_sources_healthy,
            "connected_exchanges": ["XNYS", "XNAS"],  # Standard exchanges
            "timestamp": datetime.now().isoformat(),
            "source": "redis_direct"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in /api/v1/market/status: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to fetch market status.")

@app.get("/api/v1/trades/rejections", tags=["Bootstrap API"])
async def get_trade_rejections():
    """
    Bootstrap API: Fetches historical trade rejections from Redis.
    Reads directly from the order-rejections stream to provide historical data on startup.
    """
    try:
        # Get Redis client from app state
        if not app_state.redis_mgr:
            return {
                "trades": [],
                "total": 0,
                "isBootstrap": True,
                "error": "Redis manager not initialized"
            }
            
        redis_cli = app_state.redis_mgr.get_client()
        
        # Read all rejections from the stream
        rejections = []
        
        try:
            # Use XRANGE to read all messages
            messages = redis_cli.xrange('testrade:order-rejections')
            
            for message_id, data in messages:
                try:
                    # Get the JSON payload
                    json_str = data.get('json_payload', '{}')
                    
                    # Parse the message
                    parsed_message = json.loads(json_str)
                    payload = parsed_message.get('payload', {})
                    metadata = parsed_message.get('metadata', {})
                    
                    # Create a trade history record from the rejection with enriched data
                    rejection_trade = {
                        "symbol": payload.get("symbol", "UNKNOWN"),
                        "side": payload.get("side", "UNKNOWN"),
                        "action": payload.get("side", "UNKNOWN"),
                        "quantity": payload.get("quantity", 0),
                        "price": payload.get("price", 0.0),
                        "status": "REJECTED",
                        "order_id": payload.get("order_id", ""),
                        "local_order_id": payload.get("local_order_id", ""),
                        "broker_order_id": payload.get("broker_order_id", ""),
                        "rejection_reason": payload.get("rejection_reason", payload.get("reason", "Order rejected")),
                        "pnl": 0,
                        "realized_pnl": 0,
                        "timestamp": payload.get("timestamp", 0) * 1000,  # Convert to ms
                        "trade_id": f"REJ_{payload.get('order_id', payload.get('local_order_id', str(message_id)))}",
                        # Additional enriched fields
                        "event_id": metadata.get("eventId", ""),
                        "correlation_id": metadata.get("correlationId", ""),
                        "causation_id": metadata.get("causationId", ""),
                        "source_component": metadata.get("sourceComponent", "Unknown"),
                        "timestamp_ns": metadata.get("timestamp_ns", 0),
                        "is_rejection": True,
                        "commission": 0,
                        "strategy": payload.get("strategy", "N/A"),
                        "order_type": payload.get("order_type", "MARKET"),
                        "time_in_force": payload.get("time_in_force", "DAY"),
                        "broker_response": payload.get("broker_response", ""),
                        "exchange": payload.get("exchange", ""),
                        "account": payload.get("account", "")
                    }
                    rejections.append(rejection_trade)
                except Exception as e:
                    logger.warning(f"Error parsing rejection message {message_id}: {e}")
                    continue
            
            logger.info(f"Bootstrap: Loaded {len(rejections)} historical rejections")
            
            return {
                "trades": rejections,
                "total": len(rejections),
                "isBootstrap": True
            }
            
        except Exception as redis_error:
            logger.error(f"Redis error: {redis_error}")
            # Return empty list instead of failing
            return {
                "trades": [],
                "total": 0,
                "isBootstrap": True,
                "error": "Could not read from Redis"
            }
        
    except Exception as e:
        logger.error(f"Error in get_trade_rejections: {e}", exc_info=True)
        # Return empty response instead of 500
        return {
            "trades": [],
            "total": 0,
            "isBootstrap": True,
            "error": str(e)
        }

# The existing /health endpoint is fine, it provides system health.
# The TANK mode considerations for these APIs:
# If ENABLE_IPC_DATA_DUMP is false, ApplicationCore might still be able
# to respond to these commands if it has cached state or can query brokers directly
# without relying on the IPC data dump from Babysitter.
# The `send_core_command_and_await_response` will still function;
# the success depends on ApplicationCore's behavior in TANK mode.
# If ApplicationCore cannot fulfill these requests in TANK mode, it should
# respond with a status other than "success" or appropriate error data,
# which `send_core_command_and_await_response` will turn into an HTTPException.

# --- End of New Bootstrap API Endpoints ---

# --- CHUNK 14: Price Data Pruning ---
async def periodically_prune_price_data(app_state_ref: AppState, interval_seconds: int = 3600, stale_threshold_seconds: int = 7200):
    """
    🚨 CHUNK 14: Periodically prunes stale symbols from app_state.price_data.
    A symbol is considered stale if it's not in active_trades and its price data hasn't been updated
    for stale_threshold_seconds.
    """
    logger.info(f"🧹 Price data pruning task started (Interval: {interval_seconds}s, StaleAfter: {stale_threshold_seconds}s)")

    while True:
        await asyncio.sleep(interval_seconds)
        try:
            if not hasattr(app_state_ref, 'price_data') or not app_state_ref.price_data or \
               not hasattr(app_state_ref, 'active_trades'):
                logger.debug("PRUNING_PRICE_DATA: Skipping - no price_data or active_trades available")
                continue

            current_time = time.time()
            symbols_in_active_trades = {trade.get("symbol") for trade in app_state_ref.active_trades if trade.get("symbol")}

            keys_to_prune = []
            for symbol, data_dict in list(app_state_ref.price_data.items()):  # Iterate a copy of items for safe deletion
                if symbol in symbols_in_active_trades:
                    continue  # Always keep symbols that are in active trades

                # Check last update timestamp for this symbol's price data
                # Assuming 'quote_timestamp' or 'trade_timestamp' are updated in price_data[symbol]
                last_update_ts = data_dict.get('quote_timestamp', data_dict.get('trade_timestamp', 0))

                if (current_time - last_update_ts) > stale_threshold_seconds:
                    keys_to_prune.append(symbol)

            if keys_to_prune:
                logger.info(f"🧹 PRUNING_PRICE_DATA: Removing {len(keys_to_prune)} stale symbols: {keys_to_prune[:10]}...")  # Log first 10
                for sym_to_prune in keys_to_prune:
                    if sym_to_prune in app_state_ref.price_data:  # Double check before del
                        del app_state_ref.price_data[sym_to_prune]
                logger.info(f"🧹 PRUNING_PRICE_DATA: Pruning complete. Current price_data size: {len(app_state_ref.price_data)}")
            else:
                logger.debug(f"🧹 PRUNING_PRICE_DATA: No stale symbols found. Current price_data size: {len(app_state_ref.price_data)}, Active trades: {len(symbols_in_active_trades)}")

        except Exception as e:
            logger.error(f"❌ Error during periodic price_data pruning: {e}", exc_info=True)

# --- Generic Stream Handler for Placeholder Streams ---
@dispatch_to_event_loop
async def handle_generic_stream_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """
    Generic handler for streams that don't have specific handlers yet.
    Logs the message for debugging and optionally broadcasts to WebSocket.
    """
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.debug(f"Generic handler: No parsed message for {message_id}")
        return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})
    event_type = metadata.get("eventType", "UNKNOWN_EVENT")

    # Log for debugging
    logger.debug(f"Generic handler: {event_type} message {message_id} - Payload keys: {list(payload.keys())}")

    # Optionally broadcast generic message to WebSocket for debugging
    # Uncomment if you want to see these messages in the GUI
    # gui_message = {
    #     "type": "generic_stream_message",
    #     "event_type": event_type,
    #     "payload": payload,
    #     "metadata": metadata,
    #     "message_id": message_id
    # }
    # await app_state.websocket_manager.broadcast(gui_message)

# --- XREAD Consumer Logic ---
async def xread_gui_streams_task(app_state: AppState):
    """
    Task to consume messages from multiple Redis streams relevant to the GUI using XREAD.
    This replaces the individual RedisStreamConsumerBase instances for GUI data.
    """
    logger.info("🚀 Starting XREAD GUI streams consumer task...")
    redis_cli = None
    last_connection_attempt = 0
    connection_retry_interval = 5  # seconds

    # Define streams and their last seen IDs. Start with '$' to get only new messages.
    # Comprehensive coverage of ALL TESTRADE streams for complete GUI functionality
    streams_to_watch = {
        # CRITICAL TRADING STREAMS (High Priority - < 100ms latency)
        # DISABLED: Enriched positions - using separate streams for positions and market data
        # getattr(app_state.config, 'redis_stream_enriched_position_updates', 'testrade:enriched-position-updates'): '$',
        getattr(app_state.config, 'redis_stream_position_updates', 'testrade:position-updates'): '$',
        getattr(app_state.config, 'redis_stream_order_fills', 'testrade:order-fills'): '$',
        getattr(app_state.config, 'redis_stream_order_status', 'testrade:order-status'): '$',
        getattr(app_state.config, 'redis_stream_order_requests', 'testrade:order-requests'): '$',
        getattr(app_state.config, 'redis_stream_order_rejections', 'testrade:order-rejections'): '$',

        # MEDIUM PRIORITY STREAMS (< 500ms latency)
        getattr(app_state.config, 'redis_stream_validated_orders', 'testrade:validated-orders'): '$',
        'testrade:account-updates': '$',  # Real-time account updates from RiskManagementService
        getattr(app_state.config, 'redis_stream_position_summary', 'testrade:position-summary'): '$',
        getattr(app_state.config, 'redis_stream_risk_actions', 'testrade:risk-actions'): '$',

        # BOOTSTRAP & BATCH STREAMS
        getattr(app_state.config, 'redis_stream_order_batch_updates', 'testrade:order-batch-updates'): '$',
        getattr(app_state.config, 'redis_stream_open_orders', 'testrade:open-orders'): '$',

        # MARKET DATA STREAMS
        'testrade:filtered:market-quotes': '$',  # Active quotes stream with data
        'testrade:filtered:market-trades': '$',  # Active trades stream with data
        getattr(app_state.config, 'redis_stream_market_summary', 'testrade:market-data:summary'): '$',
        getattr(app_state.config, 'redis_stream_volume_data', 'testrade:market-data:volume'): '$',

        # TRADING HISTORY & ANALYTICS
        'testrade:trade-history': '$',
        'testrade:trade-opened': '$',
        'testrade:trade-closed': '$',
        'testrade:position-pnl': '$',
        'testrade:daily-summary': '$',
        'testrade:account-summary-enhanced': '$',
        'testrade:position-summary-enhanced': '$',

        # OCR & UI STREAMS
        getattr(app_state.config, 'redis_stream_raw_ocr', 'testrade:raw-ocr-events'): '$',
        getattr(app_state.config, 'redis_stream_cleaned_ocr', 'testrade:cleaned-ocr-snapshots'): '$',
        getattr(app_state.config, 'redis_stream_image_grabs', 'testrade:image-grabs'): '$',
        getattr(app_state.config, 'redis_stream_roi_updates', 'testrade:roi-updates'): '$',
        getattr(app_state.config, 'redis_stream_ocr_parameters_updates', 'testrade:ocr-parameters-updates'): '$',

        # SYSTEM & HEALTH STREAMS (Low Priority - < 2s latency)
        getattr(app_state.config, 'redis_stream_core_health', 'testrade:health:core'): '$',
        getattr(app_state.config, 'redis_stream_babysitter_health', 'testrade:health:babysitter'): '$',
        getattr(app_state.config, 'redis_stream_responses_to_gui', 'testrade:responses:to_gui'): '$',
        # 🚨 CHUNK 15: REMOVED - GUI backend shouldn't read from its own outbound command stream
        # getattr(app_state.config, 'redis_stream_commands_from_gui', 'testrade:commands:from_gui'): '$',
        getattr(app_state.config, 'redis_stream_system_status', 'testrade:system:status'): '$',
        'testrade:core-status-events': '$',
    }

    # Filter out any None stream names if a config attribute was missing
    streams_to_watch = {k: v for k, v in streams_to_watch.items() if k}
    if not streams_to_watch:
        logger.error("XREAD: No streams configured to watch. Consumer will not start.")
        return

    logger.info(f"XREAD consumer will watch: {list(streams_to_watch.keys())}")

    # 🚨 CHUNK 18.2: XREAD parameters optimized for faster OCR preview delivery
    block_milliseconds = 100   # OPTIMIZED: Shorter block time for faster message pickup (was 1000ms)
    count_per_stream = 50      # Max messages to fetch per stream per call (unchanged)

    while True:
        try:
            if redis_cli is None:
                current_time = time.time()
                if current_time - last_connection_attempt < connection_retry_interval:
                    await asyncio.sleep(connection_retry_interval - (current_time - last_connection_attempt))
                last_connection_attempt = time.time()
                logger.info("XREAD: Attempting to connect to Redis...")
                # Use RedisClientManager to get a client instance
                if app_state.redis_mgr:
                    redis_cli = app_state.redis_mgr.get_client()  # Remove decode_responses parameter
                    redis_cli.ping()  # Test connection
                    logger.info("XREAD: Successfully connected to Redis.")
                else:
                    logger.error("XREAD: RedisClientManager not available.")
                    await asyncio.sleep(connection_retry_interval)
                    continue

            # Non-blocking XREAD (or short block)
            raw_messages = redis_cli.xread(
                streams_to_watch,
                count=count_per_stream,
                block=block_milliseconds
            )

            if raw_messages:
                for stream_name, messages in raw_messages:
                    for message_id, message_data in messages:
                        # Update the last seen ID for this stream
                        streams_to_watch[stream_name] = message_id

                        # Find the appropriate handler based on the stream name
                        handler_to_call = None

                        # CRITICAL TRADING STREAMS
                        # DISABLED: Enriched positions to prevent conflicts with separate streams
                        # if stream_name == getattr(app_state.config, 'redis_stream_enriched_position_updates', None):
                        #     handler_to_call = handle_enriched_position_updates_message
                        if stream_name == getattr(app_state.config, 'redis_stream_position_updates', None):
                            handler_to_call = handle_position_updates_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_order_fills', None):
                            handler_to_call = handle_order_fills_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_order_status', None):
                            handler_to_call = handle_order_status_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_order_rejections', None):
                            handler_to_call = handle_order_rejection_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_order_requests', None):
                            handler_to_call = handle_generic_stream_message  # Placeholder

                        # MEDIUM PRIORITY STREAMS
                        elif stream_name == getattr(app_state.config, 'redis_stream_validated_orders', None):
                            handler_to_call = handle_generic_stream_message  # Placeholder
                        elif stream_name == 'testrade:account-updates':
                            handler_to_call = handle_account_updates_message  # Real-time account updates
                        elif stream_name == getattr(app_state.config, 'redis_stream_position_summary', None):
                            handler_to_call = handle_position_summary_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_risk_actions', None):
                            handler_to_call = handle_generic_stream_message  # Placeholder

                        # BOOTSTRAP & BATCH STREAMS
                        elif stream_name == getattr(app_state.config, 'redis_stream_order_batch_updates', None):
                            handler_to_call = handle_order_batch_update_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_open_orders', None):
                            handler_to_call = handle_open_orders_message

                        # MARKET DATA STREAMS
                        elif stream_name == 'testrade:filtered:market-quotes':
                            handler_to_call = handle_enhanced_price_quotes_message
                        elif stream_name == 'testrade:filtered:market-trades':
                            handler_to_call = handle_enhanced_price_trades_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_market_summary', None):
                            handler_to_call = handle_market_data_summary_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_volume_data', None):
                            handler_to_call = handle_volume_data_message

                        # TRADING HISTORY & ANALYTICS
                        elif stream_name == 'testrade:trade-history':
                            handler_to_call = handle_trade_history_message
                        elif stream_name == 'testrade:trade-opened':
                            handler_to_call = handle_trade_opened_message
                        elif stream_name == 'testrade:trade-closed':
                            handler_to_call = handle_trade_closed_message
                        elif stream_name == 'testrade:position-pnl':
                            handler_to_call = handle_position_pnl_update_message
                        elif stream_name == 'testrade:daily-summary':
                            handler_to_call = handle_daily_summary_message
                        elif stream_name == 'testrade:account-summary-enhanced':
                            handler_to_call = handle_enhanced_account_summary_message
                        elif stream_name == 'testrade:position-summary-enhanced':
                            handler_to_call = handle_enhanced_position_summary_message

                        # OCR & UI STREAMS
                        elif stream_name == getattr(app_state.config, 'redis_stream_raw_ocr', None) or stream_name == 'testrade:raw-ocr-events':
                            handler_to_call = handle_raw_ocr_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_cleaned_ocr', None) or stream_name == 'testrade:cleaned-ocr-snapshots':
                            handler_to_call = handle_cleaned_ocr_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_image_grabs', None) or stream_name == 'testrade:image-grabs':
                            handler_to_call = handle_image_grab_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_roi_updates', None):
                            handler_to_call = handle_roi_update_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_ocr_parameters_updates', None):
                            handler_to_call = handle_generic_stream_message  # Placeholder

                        # SYSTEM & HEALTH STREAMS
                        elif stream_name == getattr(app_state.config, 'redis_stream_core_health', None):
                            handler_to_call = handle_core_health_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_babysitter_health', None):
                            handler_to_call = handle_babysitter_health_message
                        elif stream_name == getattr(app_state.config, 'redis_stream_responses_to_gui', None):
                            handler_to_call = handle_phase2_response_message
                        # 🚨 CHUNK 15: REMOVED - No longer watching commands_from_gui stream
                        # elif stream_name == getattr(app_state.config, 'redis_stream_commands_from_gui', None):
                        #     handler_to_call = handle_generic_stream_message  # Commands are outbound, shouldn't receive
                        elif stream_name == getattr(app_state.config, 'redis_stream_system_status', None):
                            handler_to_call = handle_api_fallback_status_message
                        elif stream_name == 'testrade:core-status-events':
                            handler_to_call = handle_core_status_message

                        else:
                            logger.warning(f"XREAD: No specific handler for stream '{stream_name}'. Using generic handler.")
                            handler_to_call = handle_generic_stream_message

                        if handler_to_call:
                            # Enhanced performance monitoring for OCR streams
                            if stream_name in [
                                getattr(app_state.config, 'redis_stream_image_grabs', None),
                                getattr(app_state.config, 'redis_stream_raw_ocr', None),
                                getattr(app_state.config, 'redis_stream_cleaned_ocr', None)
                            ]:
                                # Add timestamp for latency measurement
                                msg_received_time = time.monotonic()
                                message_data['_received_at_xread'] = msg_received_time

                                logger.info(f"🖼️ XREAD: Processing OCR stream '{stream_name}' message ID {message_id}")
                                logger.info(f"🖼️ XREAD: Message data keys: {list(message_data.keys())}")

                                # Log image data size if present for bandwidth analysis
                                for img_key in ['frame_base64', 'processed_frame_base64', 'image_data']:
                                    if img_key in message_data:
                                        img_size = len(message_data[img_key]) if message_data[img_key] else 0
                                        logger.info(f"🖼️ XREAD: Image data '{img_key}' size: {img_size} chars (~{img_size*3//4} bytes)")

                            # The handlers are decorated with @dispatch_to_event_loop which already handles
                            # event loop scheduling, so we just call them directly
                            handler_to_call(message_id, message_data)

            # Brief sleep to prevent tight loop if Redis is down or no messages
            await asyncio.sleep(0.01)

        except redis.exceptions.ConnectionError as e:
            logger.error(f"XREAD: Redis connection error: {e}. Attempting to reconnect...")
            redis_cli = None  # Force re-connection
            await asyncio.sleep(connection_retry_interval)
        except redis.exceptions.TimeoutError:
            logger.warning("XREAD: Redis command timed out. Retrying.")
            await asyncio.sleep(0.1)
        except asyncio.CancelledError:
            logger.info("XREAD: Consumer task cancelled.")
            break
        except Exception as e:
            logger.error(f"XREAD: Unexpected error in consumer task: {e}", exc_info=True)
            await asyncio.sleep(connection_retry_interval)

    if redis_cli:
        try:
            redis_cli.close()
            logger.info("XREAD: Redis client closed.")
        except Exception as e_close:
            logger.error(f"XREAD: Error closing Redis client: {e_close}")
    logger.info("XREAD GUI streams consumer task finished.")


# REMOVED: handle_trade_history_command_response function - obsolete
# Trade history data now comes via testrade:trade-history Redis stream
# Use handle_trade_history_message instead

async def handle_position_summary_command_response(response_data: Dict[str, Any]):
    """Handle GET_POSITION_SUMMARY command response and broadcast as position_summary_update"""
    try:
        # Extract position summary data from command response
        summary_data = response_data.get("summary", response_data)

        # Broadcast position summary update (same message type as frontend expects)
        await app_state.websocket_manager.broadcast({
            "type": "position_summary_update",
            "summary": summary_data,
            "market_data_available": response_data.get("market_data_available", True)
        })

        logger.info(f"Position summary command response broadcasted: {summary_data.get('total_positions', 0)} positions")

    except Exception as e:
        logger.error(f"Error handling position summary command response: {e}")

async def handle_account_summary_command_response(response_data: Dict[str, Any]):
    """Handle GET_ACCOUNT_SUMMARY command response and broadcast as account_summary_update"""
    try:
        # Extract account data from command response
        # The response may contain nested structure like {"account_summary": {...}} or direct fields
        account_data = response_data.get("account_summary", response_data.get("account_data", response_data))
        
        # Simply transform and broadcast - no need to maintain duplicate state
        transformed_account = transform_account_data_for_frontend(account_data, is_bootstrap=True)
        
        await app_state.websocket_manager.broadcast({
            "type": "account_summary_update", 
            "account": transformed_account
        })
        
        logger.info(f"Account summary bootstrap broadcasted: Equity=${account_data.get('equity', 0):.2f}, BP=${account_data.get('buying_power', 0):.2f}")

    except Exception as e:
        logger.error(f"Error handling account summary command response: {e}", exc_info=True)

async def handle_all_orders_command_response(response_data: Dict[str, Any]):
    """Handle GET_ALL_ORDERS command response and broadcast as orders_update"""
    try:
        # Extract orders data from command response
        orders = response_data.get("orders", [])

        # Format orders for frontend
        formatted_orders = []
        for order in orders:
            formatted_order = {
                "order_id": order.get("order_id", ""),
                "symbol": order.get("symbol", ""),
                "side": order.get("side", "").upper(),
                "quantity": order.get("quantity", 0),
                "price": order.get("price", 0.0),
                "status": order.get("status", "UNKNOWN"),
                "order_type": order.get("order_type", "UNKNOWN"),
                "timestamp": order.get("timestamp", 0) * 1000,  # Convert to milliseconds
                "account_id": order.get("account_id", "")
            }
            formatted_orders.append(formatted_order)

        # Broadcast orders update
        await app_state.websocket_manager.broadcast({
            "type": "orders_update",
            "orders": formatted_orders,
            "total_orders": len(formatted_orders)
        })

        logger.info(f"All orders command response broadcasted: {len(formatted_orders)} orders")

    except Exception as e:
        logger.error(f"Error handling all orders command response: {e}")

@dispatch_to_event_loop
async def handle_order_batch_update_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle order batch update messages from OrderRepository bootstrap"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning("No parsed message found in order batch update message")
        return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    # Extract orders data (matches OrderRepository.publish_all_open_orders_to_stream format)
    orders = payload.get("orders", [])
    total_orders = payload.get("total_orders", len(orders))
    is_bootstrap = payload.get("is_bootstrap", False)
    # Get correlation ID from metadata first, then fallback to payload
    bootstrap_correlation_id = metadata.get("correlationId", payload.get("bootstrap_correlation_id", "unknown"))

    logger.info(f"Order batch update received: {total_orders} orders (bootstrap: {is_bootstrap}, correlation: {bootstrap_correlation_id})")

    # Format orders for frontend (same as handle_all_orders_command_response)
    formatted_orders = []
    for order in orders:
        formatted_order = {
            "order_id": order.get("order_id", ""),
            "symbol": order.get("symbol", ""),
            "side": order.get("side", "").upper(),
            "quantity": order.get("quantity", 0),
            "price": order.get("price", 0.0),
            "status": order.get("status", "UNKNOWN"),
            "order_type": order.get("order_type", "UNKNOWN"),
            "timestamp": order.get("timestamp", 0),  # Already in milliseconds from OrderRepository
            "filled_quantity": order.get("filled_quantity", 0),
            "remaining_quantity": order.get("remaining_quantity", 0),
            "message": order.get("message", ""),
            "local_order_id": order.get("local_order_id", ""),
            "broker_order_id": order.get("broker_order_id", ""),
            "parent_trade_id": order.get("parent_trade_id"),
            "event_type": order.get("event_type")
        }
        formatted_orders.append(formatted_order)

    # Broadcast orders update (same WebSocket message as handle_all_orders_command_response)
    await app_state.websocket_manager.broadcast({
        "type": "orders_update",
        "orders": formatted_orders,
        "total_orders": total_orders,
        "is_bootstrap": is_bootstrap,
        "bootstrap_correlation_id": bootstrap_correlation_id
    })

    logger.info(f"Order batch update processed: {total_orders} orders (bootstrap: {is_bootstrap})")

@dispatch_to_event_loop
async def handle_open_orders_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle open orders stream messages"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning(f"No parsed message in open orders message {message_id}")
        return
    
    payload = parsed_message.get("payload", {})
    
    # Open order data
    order_data = {
        "symbol": payload.get("symbol", "UNKNOWN"),
        "side": payload.get("side", "UNKNOWN"),
        "quantity": payload.get("quantity", 0),
        "price": payload.get("price", 0.0),
        "status": payload.get("status", "OPEN"),
        "local_order_id": payload.get("local_order_id", ""),
        "broker_order_id": payload.get("broker_order_id", ""),
        "parent_trade_id": payload.get("parent_trade_id"),
        "is_child": payload.get("is_child", False),
        "timestamp": payload.get("timestamp", time.time()),
        "order_type": payload.get("order_type", "LIMIT"),
        "time_in_force": payload.get("time_in_force", "DAY"),
        "filled_quantity": payload.get("filled_quantity", 0),
        "remaining_quantity": payload.get("remaining_quantity", payload.get("quantity", 0))
    }
    
    # Broadcast open order update
    await app_state.websocket_manager.broadcast({
        "type": "open_order_update",
        "order": order_data,
        "is_bootstrap": False
    })
    
    logger.info(f"Open order update: {order_data['symbol']} {order_data['side']} {order_data['quantity']}@{order_data['price']}")

async def handle_all_positions_command_response(response_data: Dict[str, Any]):
    """Handle GET_ALL_POSITIONS command response and broadcast as trades_update"""
    try:
        # Extract positions data from command response
        positions = response_data.get("positions", [])

        # Format positions for frontend (same format as trades_update)
        formatted_positions = []
        for position in positions:
            # FIXED: Properly determine if position is truly open
            is_open = position.get("is_open", True)
            status = position.get("status", "UNKNOWN").upper()

            # Additional checks for open status
            if status in ["CLOSED", "FILLED", "CANCELLED", "REJECTED"]:
                is_open = False
            elif status in ["OPEN", "ACTIVE", "PARTIALLY_FILLED"]:
                is_open = True

            # FIXED: Only include truly open positions
            if not is_open:
                logger.debug(f"Skipping closed position: {position.get('symbol')} - Status: {status}")
                continue

            # REVISED FOCUSED DEBUG: Log complete raw data from core services
            logger.info(f"--- RAW DATA FROM CORE FOR SYMBOL: {position.get('symbol')} ---")
            logger.info(f"{json.dumps(position, indent=2)}")  # Log the entire raw dictionary
            logger.info(f"--- END RAW DATA ---")

            # BOOTSTRAP HANDLER: Extract position_uuid from PositionManager data
            position_uuid_from_core = position.get("position_uuid")
            if not position_uuid_from_core:
                logger.error(f"[BOOTSTRAP_HANDLER] CRITICAL: PositionManager data for {position.get('symbol')} MISSING position_uuid during bootstrap. Skipping.")
                continue  # Skip this position in the loop

            logger.info(f"[BOOTSTRAP_HANDLER] Processing: Symbol='{position.get('symbol')}', StableID(position_uuid)='{position_uuid_from_core}'")

            formatted_position = {
                "trade_id": position_uuid_from_core,    # For app.js primary use
                "position_uuid": position_uuid_from_core, # For app.js mapping fallback/verification
                "symbol": position.get("symbol", ""),
                "quantity": position.get("quantity", 0),
                "average_price": position.get("average_price", 0.0),
                "realized_pnl": position.get("realized_pnl", 0.0),
                "unrealized_pnl": position.get("unrealized_pnl", 0.0),
                "is_open": is_open,  # FIXED: Use properly determined open status
                "status": status,    # FIXED: Include explicit status
                "strategy": position.get("strategy", "UNKNOWN"),
                "last_update_timestamp": position.get("last_update_timestamp", time.time()),
                # Market data
                "bid_price": position.get("bid_price", 0.0),
                "ask_price": position.get("ask_price", 0.0),
                "last_price": position.get("last_price", 0.0),
                "total_volume": position.get("total_volume", 0),
                "market_data_available": position.get("market_data_available", False), # From PositionManager data
                # Trade hierarchy
                "parent_trade_id": position.get("parent_trade_id"),
                "is_parent": position.get("is_parent", False),
                "is_child": position.get("is_child", False),
                "total_fills_count": position.get("total_fills_count", 0),
                "position_opened_timestamp": position.get("position_opened_timestamp")
            }

            # REVISED FOCUSED DEBUG: Log formatted position being sent to app.js
            logger.info(f"Formatted position being sent to app.js for {position.get('symbol')}: {json.dumps(formatted_position, indent=2)}")

            # PRICE OSCILLATION DEBUGGING: Log formatted prices for problematic symbols
            symbol_name = position.get('symbol')
            if symbol_name in ["ADIL", "IBIO", "YIBO", "AAPL"]:
                logger.info(f"--- [BACKEND PRICE DEBUG - Bootstrap/All Positions] Symbol: {symbol_name} ---")
                logger.info(f"  Raw Position Prices: last_price={position.get('last_price')}, bid_price={position.get('bid_price')}, ask_price={position.get('ask_price')}")
                logger.info(f"  Formatted Prices: last_price={formatted_position.get('last_price')}, bid_price={formatted_position.get('bid_price')}, ask_price={formatted_position.get('ask_price')}")
                logger.info(f"--- END [BACKEND PRICE DEBUG - Bootstrap] ---")

            formatted_positions.append(formatted_position)

        # Update app state with only open positions
        app_state.active_trades = formatted_positions

        # Broadcast trades update (same message type as frontend expects) - Phase 3: HIGH PRIORITY
        broadcast_message = {
            "type": "trades_update",
            "payload": {
                "trades": formatted_positions,
                "market_data_available": response_data.get("market_data_available", True),
                "hierarchy_data_available": response_data.get("hierarchy_data_available", True)
            }
        }
        logger.info(f"📡 Broadcasting TYPE: {broadcast_message['type']}, PAYLOAD/DATA: {json.dumps(broadcast_message, indent=2)}")
        await app_state.websocket_manager.broadcast(broadcast_message, high_priority=True)

        logger.info(f"Filtered positions: {len(formatted_positions)} open out of {len(positions)} total")

    except Exception as e:
        logger.error(f"Error handling all positions command response: {e}", exc_info=True)

@dispatch_to_event_loop
async def handle_core_health_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """
    Handle Core health status messages from testrade:health:core stream.
    """
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message: return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    # Extract health information
    component = payload.get("component", "ApplicationCore")
    status = payload.get("status", "unknown")
    metrics = payload.get("metrics", {})
    issues = payload.get("issues", [])
    timestamp = payload.get("timestamp", time.time())

    # Filter out old messages (older than 60 seconds) to prevent flashing from stale data
    current_time = time.time()
    message_age = current_time - timestamp
    if message_age > 60:
        logger.debug(f"Ignoring old core health message (age: {message_age:.1f}s)")
        return

    # 🚨 CHUNK 20.1: Only update last_core_health_time for healthy status
    if status == "healthy":
        app_state.last_core_health_time = current_time
        logger.debug(f"CoreHealth: Updated app_state.last_core_health_time to {current_time} due to healthy status.")
    else:
        logger.debug(f"CoreHealth: Status is '{status}', not updating last_core_health_time.")

    # Track status changes to reduce logging noise
    previous_status = getattr(app_state, 'last_core_status', None)
    status_changed = previous_status != status
    app_state.last_core_status = status

    # Calculate response time if we have timing data
    if 'last_health_check' in metrics:
        response_time_ms = (current_time - metrics['last_health_check']) * 1000
        metrics['response_time_ms'] = response_time_ms

    # Create GUI health message
    gui_message = {
        "type": "health_update",
        "component": component,
        "status": status,
        "metrics": metrics,
        "issues": issues,
        "timestamp": timestamp
    }

    await app_state.websocket_manager.broadcast(gui_message)

    # Only log on status changes or errors, not routine heartbeats
    if status_changed or status != "healthy" or len(issues) > 0:
        if status_changed:
            logger.info(f"🔄 Core status changed: {previous_status} → {status}")
        if status != "healthy" or len(issues) > 0:
            logger.warning(f"⚠️ Core health issue: {status} (Issues: {len(issues)})")
    # No logging for routine healthy heartbeats

@dispatch_to_event_loop
async def handle_babysitter_health_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """
    Handle Babysitter health status messages from testrade:health:babysitter stream.
    """
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message: return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    # Extract health information
    component = payload.get("component", "Babysitter")
    status = payload.get("status", "unknown")
    metrics = payload.get("metrics", {})
    issues = payload.get("issues", [])
    timestamp = payload.get("timestamp", time.time())

    # Filter out old messages (older than 60 seconds) to prevent flashing from stale data
    current_time = time.time()
    message_age = current_time - timestamp
    if message_age > 60:
        logger.debug(f"Ignoring old babysitter health message (age: {message_age:.1f}s)")
        return

    # Track when we last received a babysitter health update (only for recent messages)
    app_state.last_babysitter_health_time = current_time

    # Track status changes to reduce logging noise
    previous_status = getattr(app_state, 'last_babysitter_status', None)
    status_changed = previous_status != status
    app_state.last_babysitter_status = status

    # Calculate response time if we have timing data
    if 'last_health_check' in metrics:
        response_time_ms = (current_time - metrics['last_health_check']) * 1000
        metrics['response_time_ms'] = response_time_ms

    # Create GUI health message
    gui_message = {
        "type": "health_update",
        "component": component,
        "status": status,
        "metrics": metrics,
        "issues": issues,
        "timestamp": timestamp
    }

    await app_state.websocket_manager.broadcast(gui_message)

    # Only log on status changes or errors, not routine heartbeats
    if status_changed or status != "healthy" or len(issues) > 0:
        if status_changed:
            logger.info(f"🔄 Babysitter status changed: {previous_status} → {status}")
        if status != "healthy" or len(issues) > 0:
            logger.warning(f"⚠️ Babysitter health issue: {status} (Issues: {len(issues)})")
    # No logging for routine healthy heartbeats

@dispatch_to_event_loop
async def handle_account_summary_message(message_id: str, raw_redis_message: Dict[str, Any]):
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message: return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    # Simply transform and broadcast - no need to maintain duplicate state
    if payload:
        transformed_account = transform_account_data_for_frontend(payload, is_bootstrap=False)
        
        await app_state.websocket_manager.broadcast({
            "type": "account_summary_update", 
            "account": transformed_account
        })
        
        logger.info(f"Account summary processed: Equity=${payload.get('equity', 0):.2f}, BP=${payload.get('buying_power', 0):.2f}")

@dispatch_to_event_loop
async def handle_account_updates_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle real-time account updates from RiskManagementService"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message: return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    # Transform and broadcast account update
    if payload:
        transformed_account = transform_account_data_for_frontend(payload, is_bootstrap=False)
        
        # Update app state
        app_state.financial_data.update({
            "accountValue": payload.get("equity", 0.0),
            "buyingPower": payload.get("buying_power", 0.0),
            "dayPnL": payload.get("realized_pnl_day", 0.0) + payload.get("unrealized_pnl_day", 0.0),
            "totalPnL": payload.get("realized_pnl_day", 0.0) + payload.get("unrealized_pnl_day", 0.0)
        })
        
        await app_state.websocket_manager.broadcast({
            "type": "account_summary_update", 
            "account": transformed_account
        })
        
        logger.info(f"Account update: Equity=${payload.get('equity', 0):.2f}, "
                   f"BP=${payload.get('buying_power', 0):.2f}, "
                   f"Realized=${payload.get('realized_pnl_day', 0):.2f}, "
                   f"Unrealized=${payload.get('unrealized_pnl_day', 0):.2f}")
    
@dispatch_to_event_loop
async def handle_position_summary_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle position summary updates with enhanced market data"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning("No parsed message in position summary message")
        return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    logger.info(f"Position summary received: {payload}")

    # Check if summary contains market data
    has_market_data = any(key in payload for key in ['bid_prices', 'ask_prices', 'last_prices', 'volumes'])

    if not has_market_data:
        logger.warning("Position summary missing market data (bid/ask/last/volume)")
        # Still broadcast but flag as incomplete
        await app_state.websocket_manager.broadcast({
            "type": "position_summary_update",
            "summary": payload,
            "market_data_missing": True
        })
    else:
        await app_state.websocket_manager.broadcast({
            "type": "position_summary_update",
            "summary": payload,
            "market_data_available": True
        })

    logger.info(f"Position summary processed - market data: {has_market_data}")
    
# NEW: Enhanced position updates handler for enriched stream
# Enhanced backend handlers for position summary and account data

@dispatch_to_event_loop
async def handle_enhanced_account_summary_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Enhanced account summary handler with additional metrics"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning("No parsed message in enhanced account summary message")
        return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    # Enhanced account data structure
    account_data = {
        "account_value": payload.get("account_value", 0.0),
        "buying_power": payload.get("buying_power", 0.0),
        "buying_power_used": payload.get("buying_power_used", 0.0),
        "day_pnl": payload.get("day_pnl", 0.0),
        "cash_balance": payload.get("cash_balance", 0.0),
        "margin_used": payload.get("margin_used", 0.0),
        "maintenance_margin": payload.get("maintenance_margin", 0.0),
        "timestamp": payload.get("timestamp", time.time())
    }

    # Update financial data state
    app_state.financial_data.update({
        "accountValue": account_data["account_value"],
        "buyingPower": account_data["buying_power"],
        "dayPnL": account_data["day_pnl"],
        "buyingPowerUsed": account_data["buying_power_used"],
        "buyingPowerLeft": account_data["buying_power"] - account_data["buying_power_used"]
    })

    # Extract correlation ID for bootstrap tracking
    correlation_id = metadata.get("correlationId", "unknown")
    is_bootstrap = payload.get("is_bootstrap", False)

    # Broadcast enhanced account data with correlation ID
    await app_state.websocket_manager.broadcast({
        "type": "account_summary_update",
        "account": account_data,
        "is_bootstrap": is_bootstrap,
        "bootstrap_correlation_id": correlation_id
    })

    logger.info(f"Enhanced account summary updated: Value=${account_data['account_value']:.2f}, BP=${account_data['buying_power']:.2f}")

@dispatch_to_event_loop
async def handle_enhanced_position_summary_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Enhanced position summary with trading statistics"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning("No parsed message in enhanced position summary message")
        return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    # Calculate enhanced position summary
    position_summary = {
        "totalSharesTraded": payload.get("total_shares_traded", 0),
        "openPositionsCount": payload.get("open_positions_count", 0),
        "closedPositionsCount": payload.get("closed_positions_count", 0),
        "totalTradesCount": payload.get("total_trades_count", 0),
        "openPnL": payload.get("open_pnl", 0.0),
        "closedPnL": payload.get("closed_pnl", 0.0),
        "totalDayPnL": payload.get("total_day_pnl", 0.0),
        "winRate": payload.get("win_rate", 0.0),
        "avgWin": payload.get("avg_win", 0.0),
        "avgLoss": payload.get("avg_loss", 0.0),
        "totalVolume": payload.get("total_volume", 0),
        "winners": payload.get("winners", 0),
        "losers": payload.get("losers", 0),
        "timestamp": payload.get("timestamp", time.time())
    }

    # Store in app state
    app_state.position_summary = position_summary

    # Extract correlation ID for bootstrap tracking
    correlation_id = metadata.get("correlationId", "unknown")
    is_bootstrap = payload.get("is_bootstrap", False)

    # Broadcast to frontend with correlation ID
    await app_state.websocket_manager.broadcast({
        "type": "position_summary_update",
        "summary": position_summary,
        "is_bootstrap": is_bootstrap,
        "bootstrap_correlation_id": correlation_id
    })

    logger.info(f"Position summary updated: {position_summary['totalTradesCount']} trades, {position_summary['openPositionsCount']} open")

@dispatch_to_event_loop
async def handle_enriched_position_updates_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle enriched position update messages from PositionEnrichmentService - SINGLE SOURCE OF TRUTH"""
    logger.debug(f"[ENRICHED_HANDLER] Received message_id: {message_id}")

    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning("[ENRICHED_HANDLER] No parsed message.")
        return

    payload = parsed_message.get("payload", {})  # This is from PositionEnrichmentService
    symbol = payload.get("symbol")

    # 1. GET THE STABLE ID FROM THIS STREAM'S PAYLOAD
    # PositionEnrichmentService sends its stable ID as "trade_id" in its payload
    stable_id_from_enriched_payload = payload.get("trade_id")

    if not stable_id_from_enriched_payload:
        logger.error(f"[ENRICHED_HANDLER] CRITICAL: Enriched payload for symbol '{symbol}' is MISSING 'trade_id' (which should be the position_uuid). Cannot process. Payload keys: {list(payload.keys())}")
        return

    logger.info(f"[ENRICHED_HANDLER] Processing: Symbol='{symbol}', StableID(from enriched.trade_id)='{stable_id_from_enriched_payload}'")

    # Enhanced position update payload structure with market data
    quantity = payload.get("quantity", 0.0)
    average_price = payload.get("average_price", 0.0)
    realized_pnl = payload.get("realized_pnl", 0.0)
    is_open = payload.get("is_open", False)
    status = payload.get("status", "UNKNOWN").upper()
    strategy = payload.get("strategy", "UNKNOWN")
    last_update_timestamp = payload.get("last_update_timestamp", time.time())

    # FIXED: Additional checks for open status consistency
    if status in ["CLOSED", "FILLED", "CANCELLED", "REJECTED"]:
        is_open = False
    elif status in ["OPEN", "ACTIVE", "PARTIALLY_FILLED"]:
        is_open = True

    # Market data from enrichment service
    bid_price = payload.get("bid_price", 0.0)
    ask_price = payload.get("ask_price", 0.0)
    last_price = payload.get("last_price", 0.0)
    total_volume = payload.get("total_volume", 0)
    market_data_available = payload.get("market_data_available", False)
    hierarchy_data_available = payload.get("hierarchy_data_available", True)

    # PRICE OSCILLATION DEBUGGING: Log price data for problematic symbols
    if symbol in ["ADIL", "IBIO", "YIBO", "AAPL"]:  # Focus on problematic symbols
        logger.info(f"--- [BACKEND PRICE DEBUG - Handler: handle_enriched_position_updates_message] Symbol: {symbol} ---")
        logger.info(f"  Raw Redis Payload Prices: last_price={payload.get('last_price')}, bid_price={payload.get('bid_price')}, ask_price={payload.get('ask_price')}")
        logger.info(f"  Processed Prices: last_price={last_price}, bid_price={bid_price}, ask_price={ask_price}")
        logger.info(f"  Market Data Available: {market_data_available}")
        logger.info(f"--- END [BACKEND PRICE DEBUG] ---")

    # Parent/child trade hierarchy
    parent_trade_id = payload.get("parent_trade_id")
    is_parent = payload.get("is_parent", False)
    is_child = payload.get("is_child", False)
    # REVISED FOCUSED DEBUG: Log complete raw data from core services
    logger.info(f"--- RAW DATA FROM CORE FOR SYMBOL: {payload.get('symbol')} ---")
    logger.info(f"{json.dumps(payload, indent=2)}")  # Log the entire raw dictionary
    logger.info(f"--- END RAW DATA ---")

    # 2. PREPARE THE OBJECT TO SEND TO app.js
    # This object MUST include both 'trade_id' (for app.js's primary use)
    # AND 'position_uuid' (for app.js's mapping to be robust).
    # Both should hold the same stable_id_from_enriched_payload value.
    position_data_for_frontend = {
        "trade_id": stable_id_from_enriched_payload,    # app.js will primarily map to this
        "position_uuid": stable_id_from_enriched_payload, # Explicitly send for app.js 'position_uuid' mapping key
        "symbol": symbol,
        "quantity": quantity,
        "average_price": average_price,
        "is_open": is_open,
        "status": status,
        "strategy": strategy,
        "last_price": payload.get("last_price"), # Let it be null if market_data_available is false
        "bid_price": payload.get("bid_price"),
        "ask_price": payload.get("ask_price"),
        "market_data_available": payload.get("market_data_available", False),
        "realized_pnl": realized_pnl, # from enriched payload
        "last_update_timestamp": last_update_timestamp,
        # COPY ALL OTHER RELEVANT FIELDS FROM 'payload' TO 'position_data_for_frontend'
        "parent_trade_id": payload.get("parent_trade_id"), # From enriched payload
        "is_parent": payload.get("is_parent", False),             # From enriched payload
        "is_child": payload.get("is_child", False),               # From enriched payload
        "total_fills_count": payload.get("total_fills_count", 0), # From enriched payload
        "position_opened_timestamp": payload.get("position_opened_timestamp"), # From enriched payload
        "total_volume": payload.get("total_volume", 0),
        "hierarchy_data_available": payload.get("hierarchy_data_available", True)
    }

    # 3. UPDATE app_state.active_trades using the STABLE ID
    # The entries in app_state.active_trades should also use this stable ID as their primary key.
    found_idx = -1
    for i, trade_in_state in enumerate(app_state.active_trades):
        # Compare against the 'trade_id' field within the objects stored in app_state.active_trades
        if trade_in_state.get("trade_id") == stable_id_from_enriched_payload:
            found_idx = i
            break

    is_open_from_payload = payload.get("is_open", False) # Get is_open status

    if is_open_from_payload:

        if found_idx != -1:
            # Update existing entry: replace it entirely with the new comprehensive data
            app_state.active_trades[found_idx] = position_data_for_frontend
            logger.debug(f"[ENRICHED_HANDLER] Updated existing position in app_state for ID: {stable_id_from_enriched_payload}")
        else:
            # Add new entry
            app_state.active_trades.append(position_data_for_frontend)
            logger.debug(f"[ENRICHED_HANDLER] Added new position to app_state for ID: {stable_id_from_enriched_payload}")

        # PRICE OSCILLATION DEBUGGING: Log formatted prices for problematic symbols
        if symbol in ["ADIL", "IBIO", "YIBO", "AAPL"]:
            logger.info(f"--- [BACKEND PRICE DEBUG - Formatted for Frontend] Symbol: {symbol} ---")
            logger.info(f"  Formatted Prices: last_price={position_data_for_frontend.get('last_price')}, bid_price={position_data_for_frontend.get('bid_price')}, ask_price={position_data_for_frontend.get('ask_price')}")
            logger.info(f"  Other Key Fields: quantity={position_data_for_frontend.get('quantity')}, average_price={position_data_for_frontend.get('average_price')}, is_open={position_data_for_frontend.get('is_open')}")
            logger.info(f"--- END [BACKEND PRICE DEBUG - Formatted] ---")

    elif found_idx != -1: # If not is_open and found, remove it
        app_state.active_trades.pop(found_idx)
        logger.info(f"[ENRICHED_HANDLER] Removed closed position from app_state for ID: {stable_id_from_enriched_payload}")

    # 4. BROADCAST (app_state.active_trades will be used by the broadcast logic)
    # The broadcast function itself will take app_state.active_trades and send it.
    # We need to ensure the object just added/updated in app_state.active_trades *is* position_data_for_frontend

    # Construct the message to broadcast. This should contain the ENTIRE list of active trades.
    current_market_data_available_overall = any(t.get("market_data_available", False) for t in app_state.active_trades)
    # Assuming hierarchy is generally available or handled; adjust if specific per-trade flags exist in enriched data
    current_hierarchy_data_available_overall = True # Or derive from trades if available

    broadcast_message = {
        "type": "trades_update",
        "payload": {
            "trades": list(app_state.active_trades), # Send a copy of the current list
            "market_data_available": current_market_data_available_overall,
            "hierarchy_data_available": current_hierarchy_data_available_overall
        }
    }
    await app_state.websocket_manager.broadcast(broadcast_message, high_priority=True)
    logger.debug(f"[ENRICHED_HANDLER] Broadcasted trades_update. Total active trades: {len(app_state.active_trades)}")

    logger.info(f"[ENRICHED_HANDLER] Enriched position updated for {symbol}: {len(app_state.active_trades)} active positions, "
               f"Market Data: {current_market_data_available_overall}, Hierarchy: {current_hierarchy_data_available_overall}")

@dispatch_to_event_loop
async def handle_position_updates_message(message_id: str, raw_redis_message: Dict[str, Any]):
    logger.info(f"POSITION UPDATE RECEIVED: message_id={message_id}")
    logger.info(f"Raw Redis message: {raw_redis_message}")
#
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning("No parsed message found in position update message")
        return
#
    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})
    logger.info(f"Parsed payload: {payload}")
    logger.info(f"Correlation ID: {metadata.get('correlationId', 'unknown')}")
#
    # Enhanced position update payload structure
    symbol = payload.get("symbol", "")
    quantity = payload.get("quantity", 0.0)
    average_price = payload.get("average_price", 0.0)
    realized_pnl = payload.get("realized_pnl_session", 0.0)
    is_open = payload.get("is_open", False)
    strategy = payload.get("strategy", "UNKNOWN")
    last_update_timestamp = payload.get("last_update_timestamp", time.time())

#
    # NEW: Add market data fields
    bid_price = payload.get("bid_price", 0.0)
    ask_price = payload.get("ask_price", 0.0)
    last_price = payload.get("last_price", 0.0)
    total_volume = payload.get("total_volume", 0)
#
    # PRICE OSCILLATION DEBUGGING: Log price data for problematic symbols
    if symbol in ["ADIL", "IBIO", "YIBO", "AAPL"]:  # Focus on problematic symbols
        logger.info(f"--- [BACKEND PRICE DEBUG - Handler: handle_position_updates_message] Symbol: {symbol} ---")
        logger.info(f"  Raw Redis Payload Prices: last_price={payload.get('last_price')}, bid_price={payload.get('bid_price')}, ask_price={payload.get('ask_price')}")
        logger.info(f"  Processed Prices: last_price={last_price}, bid_price={bid_price}, ask_price={ask_price}")
        logger.info(f"--- END [BACKEND PRICE DEBUG] ---")
#
    # NEW: Parent/child trade hierarchy
    parent_trade_id = payload.get("parent_trade_id")
    child_legs = payload.get("child_legs", [])
    is_parent = payload.get("is_parent", False)
    is_child = payload.get("is_child", False)
    # REVISED FOCUSED DEBUG: Log complete raw data from core services
    logger.info(f"--- RAW DATA FROM CORE FOR SYMBOL: {payload.get('symbol')} ---")
    logger.info(f"{json.dumps(payload, indent=2)}")  # Log the entire raw dictionary
    logger.info(f"--- END RAW DATA ---")
#
    # Extract all potential ID fields from core
    position_uuid = payload.get("position_uuid")
    original_trade_id_from_core = payload.get("trade_id")
    original_order_id_from_core = payload.get("order_id")
    parent_trade_id_from_core = payload.get("parent_trade_id")
    master_correlation_id_from_core = payload.get("master_correlation_id")
#
    logger.info(f"For {payload.get('symbol')}: position_uuid from core='{position_uuid}', trade_id from core='{original_trade_id_from_core}', order_id from core='{original_order_id_from_core}', parent_trade_id from core='{parent_trade_id_from_core}', master_correlation_id from core='{master_correlation_id_from_core}'")
#
    # Determine stable trade_id_for_gui with priority order
    if position_uuid:  # This is the ideal stable ID
        trade_id_for_gui = position_uuid
    elif original_trade_id_from_core:  # Fallback
        trade_id_for_gui = original_trade_id_from_core
    elif parent_trade_id_from_core:  # Further fallback
        trade_id_for_gui = parent_trade_id_from_core
    elif master_correlation_id_from_core:  # Another fallback
        trade_id_for_gui = master_correlation_id_from_core
    elif original_order_id_from_core:  # Further fallback
        trade_id_for_gui = original_order_id_from_core
    else:  # Last resort, problematic
        trade_id_for_gui = payload.get("symbol", "UNKNOWN_ID")
        logger.warning(f"Missing stable ID for {payload.get('symbol')}, using symbol as trade_id_for_gui.")
#
    # Use the determined trade_id_for_gui
    trade_id = trade_id_for_gui

    # Update our active trades list - only include open positions
    if is_open:
        # Find existing position or add new one
        existing_index = next((i for i, trade in enumerate(app_state.active_trades)
                              if trade.get("symbol") == symbol), None)
#
        position_data = {
            "symbol": symbol,
            "quantity": quantity,
            "average_price": average_price,
            "realized_pnl": realized_pnl,
            "strategy": strategy,
            "last_update_timestamp": last_update_timestamp,
            "is_open": True,
            # REMOVED: Market data - should come from market data streams
            # Trade hierarchy
            "parent_trade_id": parent_trade_id,
            "child_legs": child_legs,
            "is_parent": is_parent,
            "is_child": is_child,
            # Add missing fields that JavaScript expects
            "action": "BUY" if quantity > 0 else "SELL",  # Infer action from quantity
            "side": "BUY" if quantity > 0 else "SELL",   # Alternative field name
            "status": "OPEN",   # Since this is for open positions
            # REMOVED: "price" field that was confusing frontend - average_price is the cost basis, not market price
            "timestamp": last_update_timestamp,  # Alias for last_update_timestamp
            "trade_id": trade_id_for_gui,  # This is what app.js will see as 'trade_id'
            "position_uuid_sent": position_uuid  # Also send the raw position_uuid for debugging in app.js
        }

        # REVISED FOCUSED DEBUG: Log formatted position being sent to app.js
        logger.info(f"Formatted position being sent to app.js for {symbol}: {json.dumps(position_data, indent=2)}")
#
        # PRICE OSCILLATION DEBUGGING: Log formatted prices for problematic symbols
        if symbol in ["ADIL", "IBIO", "YIBO", "AAPL"]:
            logger.info(f"--- [BACKEND PRICE DEBUG - Formatted for Frontend] Symbol: {symbol} ---")
            logger.info(f"  Formatted Prices: last_price={position_data.get('last_price')}, bid_price={position_data.get('bid_price')}, ask_price={position_data.get('ask_price')}")
            logger.info(f"  Other Key Fields: quantity={position_data.get('quantity')}, average_price={position_data.get('average_price')}, is_open={position_data.get('is_open')}")
            logger.info(f"--- END [BACKEND PRICE DEBUG - Formatted] ---")
#
        if existing_index is not None:
            app_state.active_trades[existing_index] = position_data
            logger.info(f"Updated existing position for {symbol}")
        else:
            app_state.active_trades.append(position_data)
            logger.info(f"Added new position for {symbol}")
#
        # 🔍 CRITICAL DEBUG: Log active_trades after update
        logger.info(f"🔍 DEBUG: app_state.active_trades after update: {app_state.active_trades}")
        logger.info(f"🔍 DEBUG: len(app_state.active_trades) after update: {len(app_state.active_trades)}")
    else:
        # Remove closed position
        app_state.active_trades = [trade for trade in app_state.active_trades
                                  if trade.get("symbol") != symbol]
        logger.info(f"Removed closed position for {symbol}")

    # Send position-only update (not full trades list to avoid overwriting market data)
    broadcast_message = {
        "type": "position_update",  # Changed from trades_update
        "symbol": symbol,
        "position": position_data,
        "is_open": is_open
    }
#
    # 🔍 CRITICAL DEBUG: Log app_state.active_trades before broadcast
    logger.info(f"🔍 DEBUG: app_state.active_trades before broadcast: {app_state.active_trades}")
    logger.info(f"🔍 DEBUG: len(app_state.active_trades): {len(app_state.active_trades)}")
    logger.info(f"🔍 DEBUG: broadcast_message['payload']['trades']: {broadcast_message['payload']['trades']}")
#
    # 🔍 ENHANCED DEBUG: Log trade relationships
    trades_to_send = app_state.active_trades
    logger.info(f"--- Trades being sent in trades_update ({len(trades_to_send)}) ---")
    for t in trades_to_send:
        logger.info(f"Symbol: {t.get('symbol')}, TradeID: {t.get('trade_id')}, ParentID: {t.get('parent_trade_id')}, is_parent: {t.get('is_parent')}, is_child: {t.get('is_child')}")
    logger.info(f"--- End of trades list ---")
#
    logger.info(f"🚀 BROADCASTING trades_update: {len(app_state.active_trades)} trades to {len(app_state.websocket_manager.active_connections)} WebSocket clients")
    logger.info(f"📡 Broadcasting TYPE: {broadcast_message['type']}, PAYLOAD/DATA: {json.dumps(broadcast_message, indent=2)}")
    await app_state.websocket_manager.broadcast(broadcast_message)
    logger.info(f"✅ Broadcast completed for trades_update")
#
    logger.info(f"Position updated for {symbol}: {len(app_state.active_trades)} active positions")
    pass  # Placeholder for disabled legacy function

@dispatch_to_event_loop
async def handle_trade_history_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle historical trade data"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning("No parsed message in trade history message")
        return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    # Process trade history
    trades = payload.get("trades", [])
    date = payload.get("date", "")

    # Format trades for frontend
    formatted_trades = []
    for trade in trades:
        formatted_trade = {
            "symbol": trade.get("symbol", ""),
            "action": trade.get("side", "").upper(),
            "quantity": trade.get("quantity", 0),
            "price": trade.get("price", 0.0),
            "pnl": trade.get("realized_pnl", 0.0),
            "status": trade.get("status", "UNKNOWN"),
            "timestamp": trade.get("timestamp", 0) * 1000,  # Convert to milliseconds
            "trade_id": trade.get("trade_id", "")
        }
        formatted_trades.append(formatted_trade)

    # Broadcast trade history
    broadcast_message = {
        "type": "trade_history_update",
        "trades": formatted_trades,
        "date": date,
        "total_trades": len(formatted_trades)
    }
    logger.info(f"📡 Broadcasting TYPE: {broadcast_message['type']}, PAYLOAD/DATA: {json.dumps(broadcast_message, indent=2)}")
    await app_state.websocket_manager.broadcast(broadcast_message)

    logger.info(f"Trade history updated: {len(formatted_trades)} trades for {date}")

@dispatch_to_event_loop
async def handle_trade_opened_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle trade opened events for real-time historical tracking"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        return

    payload = parsed_message.get("payload", {})

    trade_data = {
        "symbol": payload.get("symbol", ""),
        "side": payload.get("side", "").upper(),
        "quantity": payload.get("quantity", 0),
        "open_price": payload.get("open_price", 0.0),
        "open_timestamp": payload.get("open_timestamp", time.time()),
        "trade_id": payload.get("trade_id", ""),
        "strategy": payload.get("strategy", "")
    }

    # Broadcast trade opened event
    await app_state.websocket_manager.broadcast({
        "type": "trade_opened",
        "trade": trade_data
    })

    logger.info(f"Trade opened: {trade_data['symbol']} {trade_data['side']} {trade_data['quantity']}")

@dispatch_to_event_loop
async def handle_trade_closed_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle trade closed events for real-time historical tracking"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        return

    payload = parsed_message.get("payload", {})

    trade_data = {
        "symbol": payload.get("symbol", ""),
        "side": payload.get("side", "").upper(),
        "quantity": payload.get("quantity", 0),
        "close_price": payload.get("close_price", 0.0),
        "close_timestamp": payload.get("close_timestamp", time.time()),
        "realized_pnl": payload.get("realized_pnl", 0.0),
        "trade_id": payload.get("trade_id", ""),
        "strategy": payload.get("strategy", ""),
        "duration": payload.get("duration_seconds", 0)
    }

    # Broadcast trade closed event
    await app_state.websocket_manager.broadcast({
        "type": "trade_closed",
        "trade": trade_data
    })

    logger.info(f"Trade closed: {trade_data['symbol']} P&L=${trade_data['realized_pnl']:.2f}")

@dispatch_to_event_loop
async def handle_position_pnl_update_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle real-time P&L updates for positions"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        return

    payload = parsed_message.get("payload", {})

    symbol = payload.get("symbol", "")
    unrealized_pnl = payload.get("unrealized_pnl", 0.0)
    realized_pnl = payload.get("realized_pnl", 0.0)
    total_pnl = payload.get("total_pnl", 0.0)
    timestamp = payload.get("timestamp", time.time())

    # Update position in active trades
    for trade in app_state.active_trades:
        if trade.get("symbol") == symbol:
            trade.update({
                "unrealized_pnl": unrealized_pnl,
                "realized_pnl": realized_pnl,
                "total_pnl": total_pnl,
                "pnl_timestamp": timestamp
            })
            break

    # Broadcast P&L update
    await app_state.websocket_manager.broadcast({
        "type": "position_pnl_update",
        "symbol": symbol,
        "unrealized_pnl": unrealized_pnl,
        "realized_pnl": realized_pnl,
        "total_pnl": total_pnl
    })

@dispatch_to_event_loop
async def handle_daily_summary_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle end-of-day or periodic daily summary updates"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        return

    payload = parsed_message.get("payload", {})

    daily_summary = {
        "date": payload.get("date", ""),
        "total_trades": payload.get("total_trades", 0),
        "total_volume": payload.get("total_volume", 0),
        "total_pnl": payload.get("total_pnl", 0.0),
        "winners": payload.get("winners", 0),
        "losers": payload.get("losers", 0),
        "win_rate": payload.get("win_rate", 0.0),
        "largest_win": payload.get("largest_win", 0.0),
        "largest_loss": payload.get("largest_loss", 0.0),
        "avg_win": payload.get("avg_win", 0.0),
        "avg_loss": payload.get("avg_loss", 0.0),
        "trading_time": payload.get("trading_time_minutes", 0)
    }

    # Broadcast daily summary
    await app_state.websocket_manager.broadcast({
        "type": "daily_summary_update",
        "summary": daily_summary
    })

    logger.info(f"Daily summary: {daily_summary['total_trades']} trades, P&L=${daily_summary['total_pnl']:.2f}")

@dispatch_to_event_loop
async def handle_order_status_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """
    Handle order status update messages from testrade:order-status stream.
    These messages contain order status updates from the OrderRepository.
    """
    logger.debug(f"ORDER STATUS UPDATE RECEIVED: message_id={message_id}")

    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning(f"No parsed message in order status message {message_id}")
        return

    payload = parsed_message.get("payload", {})

    # Extract order status data
    local_order_id = payload.get("local_order_id")
    broker_order_id = payload.get("broker_order_id")
    symbol = payload.get("symbol", "UNKNOWN")
    status = payload.get("status", "UNKNOWN")
    filled_quantity = payload.get("filled_quantity", 0.0)
    remaining_quantity = payload.get("remaining_quantity", 0.0)
    message = payload.get("message", "")
    timestamp = payload.get("timestamp", 0.0)

    logger.info(f"Order Status Update: {symbol} LocalID={local_order_id} BrokerID={broker_order_id} Status={status} Filled={filled_quantity} Remaining={remaining_quantity}")

    # Broadcast order status update to GUI
    order_status_data = {
        "type": "order_status_update",
        "payload": {
            "local_order_id": local_order_id,
            "broker_order_id": broker_order_id,
            "symbol": symbol,
            "status": status,
            "filled_quantity": filled_quantity,
            "remaining_quantity": remaining_quantity,
            "message": message,
            "timestamp": timestamp
        }
    }

    await app_state.websocket_manager.broadcast(order_status_data)

@dispatch_to_event_loop
async def handle_image_grab_message(message_id: str, raw_redis_message: Dict[str, Any]):
    # 🚨 PERFORMANCE FIX: Removed excessive logging and latency measurement overhead

    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning(f"Failed to parse image grab message {message_id}")
        return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    image_data = payload.get("frame_base64") or payload.get("processed_frame_base64")
    event_id = payload.get("event_id", metadata.get("eventId", "unknown"))

    if image_data:
        # Streamlined message creation - no extra timestamps or logging
        gui_message = {
            "type": "image_grab",  # Raw image source for frontend
            "image_data": image_data,
            "event_id": event_id,
            "timestamp": datetime.now().isoformat()
        }

        # 🚨 HIGH PRIORITY: Image broadcasts get priority for real-time display
        await app_state.websocket_manager.broadcast(gui_message, high_priority=True)
    else:
        logger.warning(f"No image data in message {message_id}, event_id={event_id}")

@dispatch_to_event_loop
async def handle_roi_update_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle ROI update messages from OCR process."""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning(f"No parsed message in ROI update message {message_id}")
        return

    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})

    roi = payload.get("roi")
    source = payload.get("source", "unknown")

    if roi and isinstance(roi, list) and len(roi) == 4:
        # Store current ROI state
        app_state.current_roi = roi

        # Broadcast ROI update to WebSocket clients
        gui_message = {
            "type": "roi_update",
            "roi": roi,
            "source": source,
            "timestamp": datetime.now().isoformat()
        }
        await app_state.websocket_manager.broadcast(gui_message)

        # Save ROI to control.json asynchronously
        try:
            from utils.global_config import config as global_config_instance, save_global_config

            # Define a synchronous function for the blocking file I/O
            def sync_save_roi_to_file(current_roi_value, source_info):
                # This function runs in a separate thread
                global_config_instance.ROI_COORDINATES = current_roi_value
                save_global_config(global_config_instance) # This is the blocking call
                logger.info(f"ROI saved to control.json: {current_roi_value} from {source_info} (via thread)")

            # Schedule the blocking call in a separate thread without awaiting its completion here
            asyncio.create_task(asyncio.to_thread(sync_save_roi_to_file, roi, source))
            logger.debug(f"Scheduled ROI save to control.json for {roi} from {source}")

        except Exception as e_cfg_save:
            logger.error(f"Failed to schedule/execute ROI save to control.json: {e_cfg_save}", exc_info=True)

        logger.info(f"GUI Backend: Relayed ROI update {roi} from source '{source}' to GUI clients.")
    else:
        logger.warning(f"GUI Backend: Received invalid ROI data from Redis stream: {roi}")

@dispatch_to_event_loop
async def handle_order_fills_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """
    Handle order fill messages from testrade:order-fills stream.
    These messages contain detailed fill information for sub-orders.
    """
    logger.debug(f"ORDER FILL RECEIVED: message_id={message_id}")

    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning(f"No parsed message in order fill message {message_id}")
        return

    payload = parsed_message.get("payload", {})

    # Extract fill details - enrich with all available fields
    metadata = parsed_message.get("metadata", {})
    symbol = payload.get("symbol", "UNKNOWN")
    fill_quantity = payload.get("fill_quantity", 0.0)
    fill_price = payload.get("fill_price", 0.0)
    fill_timestamp = payload.get("fill_timestamp", time.time())
    local_order_id = payload.get("local_order_id", "unknown")
    broker_order_id = payload.get("broker_order_id", "")
    side = payload.get("side", "unknown")
    
    # Additional enriched fields
    commission = payload.get("commission", 1.0)  # Default $1 commission
    exchange = payload.get("exchange", "")
    account = payload.get("account", "")
    parent_trade_id = payload.get("parent_trade_id", "")
    strategy = payload.get("strategy", "N/A")
    order_type = payload.get("order_type", "MARKET")
    time_in_force = payload.get("time_in_force", "DAY")
    broker_fill_id = payload.get("broker_fill_id", "")
    
    logger.info(f"Fill processed: {symbol} {side} {fill_quantity}@{fill_price} (Order: {local_order_id})")

    # Create enriched fill data
    fill_data = {
        "symbol": symbol,
        "quantity": fill_quantity,
        "price": fill_price,
        "timestamp": fill_timestamp,
        "order_id": local_order_id,
        "local_order_id": local_order_id,
        "broker_order_id": broker_order_id,
        "side": side,
        "commission": commission,
        "exchange": exchange,
        "account": account,
        "parent_trade_id": parent_trade_id,
        "strategy": strategy,
        "order_type": order_type,
        "time_in_force": time_in_force,
        "broker_fill_id": broker_fill_id,
        "event_id": metadata.get("eventId", ""),
        "correlation_id": metadata.get("correlationId", ""),
        "source_component": metadata.get("sourceComponent", "OrderFillProcessor")
    }

    # Broadcast to frontend
    gui_message = {
        "type": "order_fill_update",
        "fill": fill_data
    }
    await app_state.websocket_manager.broadcast(gui_message)
    
    # Also send as historical trade for proper tracking
    historical_fill = {
        **fill_data,
        "status": "FILLED",
        "action": side.upper(),
        "trade_id": f"FILL_{broker_fill_id or local_order_id or str(uuid.uuid4())[:8]}",
        "timestamp": fill_timestamp * 1000,  # Convert to milliseconds
        "pnl": 0,  # P&L calculated at position level
        "realized_pnl": 0,
        "is_fill": True,
        "is_child": True  # Fills are typically child orders
    }
    
    await app_state.websocket_manager.broadcast({
        "type": "trade_history_update",
        "trades": [historical_fill],
        "date": "today",
        "total_trades": 1
    })

@dispatch_to_event_loop
async def handle_order_rejection_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """
    Handle order rejection messages from testrade:order-rejections stream.
    These messages contain rejection information that should be shown in trade history.
    """
    logger.info(f"ORDER REJECTION RECEIVED: message_id={message_id}")
    
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.warning(f"No parsed message in order rejection message {message_id}")
        return
    
    payload = parsed_message.get("payload", {})
    metadata = parsed_message.get("metadata", {})
    
    # Extract rejection data - enrich with all available fields
    symbol = payload.get("symbol", "UNKNOWN")
    side = payload.get("side", "UNKNOWN")
    quantity = payload.get("quantity", 0)
    rejected_price = payload.get("price", 0.0)
    rejection_reason = payload.get("rejection_reason", payload.get("reason", "Order rejected by broker"))
    order_id = payload.get("order_id", "")
    local_order_id = payload.get("local_order_id", "")
    broker_order_id = payload.get("broker_order_id", "")
    timestamp = payload.get("timestamp", time.time())
    
    # Extract metadata fields for additional context
    event_id = metadata.get("eventId", "")
    correlation_id = metadata.get("correlationId", "")
    causation_id = metadata.get("causationId", "")
    source_component = metadata.get("sourceComponent", "Unknown")
    timestamp_ns = metadata.get("timestamp_ns", 0)
    
    # Create rejection trade record for history with enriched data
    rejection_trade = {
        "symbol": symbol,
        "side": side.upper(),
        "action": side.upper(),
        "quantity": quantity,
        "price": rejected_price,
        "status": "REJECTED",
        "order_id": order_id,
        "local_order_id": local_order_id,
        "broker_order_id": broker_order_id,
        "rejection_reason": rejection_reason,
        "pnl": 0,  # No P&L for rejected trades
        "realized_pnl": 0,
        "timestamp": timestamp * 1000,  # Convert to milliseconds for frontend
        "trade_id": f"REJ_{order_id or local_order_id or str(uuid.uuid4())[:8]}",
        # Additional enriched fields
        "event_id": event_id,
        "correlation_id": correlation_id,
        "causation_id": causation_id,
        "source_component": source_component,
        "timestamp_ns": timestamp_ns,
        "is_rejection": True,  # Flag to identify rejections
        "commission": 0,  # No commission on rejected orders
        "strategy": payload.get("strategy", "N/A"),
        "order_type": payload.get("order_type", "MARKET"),
        "time_in_force": payload.get("time_in_force", "DAY"),
        "broker_response": payload.get("broker_response", ""),
        "exchange": payload.get("exchange", ""),
        "account": payload.get("account", "")
    }
    
    # Broadcast rejection as both order status update and trade history update
    # This ensures it shows up in the history tab
    await app_state.websocket_manager.broadcast({
        "type": "order_status_update",
        "payload": rejection_trade
    })
    
    # Also send as trade history update to ensure it's added to historical trades
    await app_state.websocket_manager.broadcast({
        "type": "trade_history_update",
        "trades": [rejection_trade],
        "date": "today",
        "total_trades": 1
    })
    
    logger.info(f"Order rejection broadcast: {symbol} {side} {quantity} - {rejection_reason}")

@dispatch_to_event_loop
async def handle_price_quotes_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """
    Handle price quote messages from testrade:internal:market-data:raw-quotes stream.
    These messages contain bid/ask price data.
    """
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.debug(f"No parsed message in price quote message {message_id}")
        return

    payload = parsed_message.get("payload", {})

    # Extract quote data
    symbol = payload.get("symbol", "UNKNOWN")
    bid_price = payload.get("bid_price", 0.0)
    ask_price = payload.get("ask_price", 0.0)
    bid_size = payload.get("bid_size", 0)
    ask_size = payload.get("ask_size", 0)
    timestamp = payload.get("timestamp", time.time())

    # Update price data in app state
    if symbol not in app_state.price_data:
        app_state.price_data[symbol] = {}

    app_state.price_data[symbol].update({
        "bid": bid_price,
        "ask": ask_price,
        "bid_size": bid_size,
        "ask_size": ask_size,
        "quote_timestamp": timestamp
    })

    # Broadcast to frontend (only for active symbols to reduce noise)
    has_active_position = any(trade.get("symbol") == symbol for trade in app_state.active_trades)
    if has_active_position:
        # Update the active position with latest market data
        for trade in app_state.active_trades:
            if trade.get("symbol") == symbol:
                trade.update({
                    "bid_price": bid_price,
                    "ask_price": ask_price,
                    "bid_size": bid_size,
                    "ask_size": ask_size
                })

        # Broadcast price-only update (not full trades_update)
        # Include cached last price if available
        market_update = {
            "type": "market_data_update",
            "symbol": symbol,
            "bid_price": bid_price,
            "ask_price": ask_price,
            "bid_size": bid_size,
            "ask_size": ask_size,
            "timestamp": timestamp
        }
        
        # Include last price from cache if available
        if symbol in app_state.price_data and "last" in app_state.price_data[symbol]:
            market_update["last_price"] = app_state.price_data[symbol]["last"]
            
        await app_state.websocket_manager.broadcast(market_update)

        logger.info(f"MARKET DATA RECEIVED: Updated {symbol} quote - bid: {bid_price}, ask: {ask_price}")

@dispatch_to_event_loop
async def handle_price_trades_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """
    Handle price trade messages from testrade:internal:market-data:raw-trades stream.
    These messages contain last trade price data.
    """
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.debug(f"No parsed message in price trade message {message_id}")
        return

    payload = parsed_message.get("payload", {})

    # Extract trade data
    symbol = payload.get("symbol", "UNKNOWN")
    price = payload.get("price", 0.0)
    size = payload.get("size", 0)
    timestamp = payload.get("timestamp", time.time())

    # Update price data in app state
    if symbol not in app_state.price_data:
        app_state.price_data[symbol] = {}

    app_state.price_data[symbol].update({
        "last": price,
        "last_size": size,
        "trade_timestamp": timestamp
    })

    # Broadcast to frontend (only for active symbols to reduce noise)
    has_active_position = any(trade.get("symbol") == symbol for trade in app_state.active_trades)
    if has_active_position:
        # Update the active position with latest market data
        for trade in app_state.active_trades:
            if trade.get("symbol") == symbol:
                trade.update({
                    "last_price": price,
                    "last_size": size
                })

        # Broadcast price-only update (not full trades_update)
        await app_state.websocket_manager.broadcast({
            "type": "market_data_update",
            "symbol": symbol,
            "bid_price": bid_price,
            "ask_price": ask_price,
            "bid_size": bid_size,
            "ask_size": ask_size,
            "timestamp": timestamp
        })

        logger.info(f"MARKET DATA RECEIVED: Updated {symbol} trade - last: {price}, size: {size}")

# NEW: Enhanced market data consumers
@dispatch_to_event_loop
async def handle_enhanced_price_quotes_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Enhanced price quote handler with position linking"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.debug(f"No parsed message in enhanced price quote message {message_id}")
        return

    payload = parsed_message.get("payload", {})

    symbol = payload.get("symbol", "UNKNOWN")
    bid_price = payload.get("bid_price", 0.0)
    ask_price = payload.get("ask_price", 0.0)
    bid_size = payload.get("bid_size", 0)
    ask_size = payload.get("ask_size", 0)
    timestamp = payload.get("timestamp", time.time())

    # Update price data in app state
    if symbol not in app_state.price_data:
        app_state.price_data[symbol] = {}

    app_state.price_data[symbol].update({
        "bid": bid_price,
        "ask": ask_price,
        "bid_size": bid_size,
        "ask_size": ask_size,
        "quote_timestamp": timestamp
    })

    # Check if this symbol has active positions
    has_active_position = any(trade.get("symbol") == symbol for trade in app_state.active_trades)

    if has_active_position:
        # Update the active position with latest market data
        for trade in app_state.active_trades:
            if trade.get("symbol") == symbol:
                trade.update({
                    "bid_price": bid_price,
                    "ask_price": ask_price,
                    "bid_size": bid_size,
                    "ask_size": ask_size
                })

        # Broadcast price-only update for last trade
        await app_state.websocket_manager.broadcast({
            "type": "market_data_update",
            "symbol": symbol,
            "last_price": price,
            "last_size": size,
            "timestamp": timestamp
        })

        logger.debug(f"Updated market data for active position: {symbol}")

@dispatch_to_event_loop
async def handle_enhanced_price_trades_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Enhanced price trade handler with position linking"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.debug(f"No parsed message in enhanced price trade message {message_id}")
        return

    payload = parsed_message.get("payload", {})

    symbol = payload.get("symbol", "UNKNOWN")
    price = payload.get("price", 0.0)
    size = payload.get("size", 0)
    timestamp = payload.get("timestamp", time.time())

    # Update price data in app state
    if symbol not in app_state.price_data:
        app_state.price_data[symbol] = {}

    app_state.price_data[symbol].update({
        "last": price,
        "last_size": size,
        "trade_timestamp": timestamp
    })

    # Check if this symbol has active positions
    has_active_position = any(trade.get("symbol") == symbol for trade in app_state.active_trades)

    if has_active_position:
        # Update the active position with latest market data
        for trade in app_state.active_trades:
            if trade.get("symbol") == symbol:
                trade.update({
                    "last_price": price,
                    "last_size": size
                })

        # Broadcast price-only update for last trade
        await app_state.websocket_manager.broadcast({
            "type": "market_data_update",
            "symbol": symbol,
            "last_price": price,
            "last_size": size,
            "timestamp": timestamp
        })

        logger.debug(f"Updated last price for active position: {symbol} @ {price}")

# NEW: Additional market data handlers
@dispatch_to_event_loop
async def handle_market_data_summary_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle market data summary messages"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.debug(f"No parsed message in market data summary message {message_id}")
        return

    payload = parsed_message.get("payload", {})

    # Process market data summary
    symbols_data = payload.get("symbols", {})
    timestamp = payload.get("timestamp", time.time())

    # Update price data for multiple symbols at once
    for symbol, data in symbols_data.items():
        if symbol not in app_state.price_data:
            app_state.price_data[symbol] = {}

        app_state.price_data[symbol].update({
            "bid": data.get("bid", 0.0),
            "ask": data.get("ask", 0.0),
            "last": data.get("last", 0.0),
            "volume": data.get("volume", 0),
            "summary_timestamp": timestamp
        })

    # Update active positions with market data
    updated_positions = False
    for trade in app_state.active_trades:
        symbol = trade.get("symbol")
        if symbol in symbols_data:
            trade.update({
                "bid_price": symbols_data[symbol].get("bid", 0.0),
                "ask_price": symbols_data[symbol].get("ask", 0.0),
                "last_price": symbols_data[symbol].get("last", 0.0),
                "total_volume": symbols_data[symbol].get("volume", 0)
            })
            updated_positions = True

    if updated_positions:
        await app_state.websocket_manager.broadcast({
            "type": "trades_update",
            "payload": {
                "trades": app_state.active_trades,
                "market_data_updated": True
            }
        })
        logger.info(f"Updated market data for {len(symbols_data)} symbols")

@dispatch_to_event_loop
async def handle_volume_data_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle volume data messages"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.debug(f"No parsed message in volume data message {message_id}")
        return

    payload = parsed_message.get("payload", {})

    symbol = payload.get("symbol", "UNKNOWN")
    volume = payload.get("volume", 0)
    timestamp = payload.get("timestamp", time.time())

    # Update price data with volume
    if symbol not in app_state.price_data:
        app_state.price_data[symbol] = {}

    app_state.price_data[symbol].update({
        "total_volume": volume,
        "volume_timestamp": timestamp
    })

    # Update active position if exists
    for trade in app_state.active_trades:
        if trade.get("symbol") == symbol:
            trade["total_volume"] = volume

            # Broadcast updated position
            await app_state.websocket_manager.broadcast({
                "type": "trades_update",
                "payload": {"trades": app_state.active_trades}
            })
            break

@dispatch_to_event_loop
async def handle_api_fallback_status_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle API fallback status messages for observability"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        logger.debug(f"No parsed message in API fallback status message {message_id}")
        return

    payload = parsed_message.get("payload", {})

    symbol = payload.get("symbol", "")
    is_active = payload.get("fallback_active", False)
    reason = payload.get("reason", "")
    data_type = payload.get("data_type", "")
    api_source = payload.get("api_source", "")
    timestamp = payload.get("timestamp", time.time())

    # Store API fallback status in app state for tracking
    if "api_fallback_status" not in app_state.system_status:
        app_state.system_status["api_fallback_status"] = {}

    # Track fallback events per symbol
    if symbol not in app_state.system_status["api_fallback_status"]:
        app_state.system_status["api_fallback_status"][symbol] = {
            "recent_fallbacks": [],
            "total_count": 0
        }

    # Add to recent fallbacks (keep last 10)
    fallback_event = {
        "data_type": data_type,
        "reason": reason,
        "api_source": api_source,
        "timestamp": timestamp
    }

    app_state.system_status["api_fallback_status"][symbol]["recent_fallbacks"].append(fallback_event)
    app_state.system_status["api_fallback_status"][symbol]["total_count"] += 1

    # Keep only last 10 fallback events per symbol
    if len(app_state.system_status["api_fallback_status"][symbol]["recent_fallbacks"]) > 10:
        app_state.system_status["api_fallback_status"][symbol]["recent_fallbacks"] = \
            app_state.system_status["api_fallback_status"][symbol]["recent_fallbacks"][-10:]

    # Broadcast API fallback update to frontend
    gui_message = {
        "type": "api_fallback_update",  # New WebSocket message type for the frontend
        "symbol": symbol,
        "active": is_active,
        "reason": reason,
        "data_type": data_type,
        "api_source": api_source,
        "timestamp": timestamp,
        "total_count": app_state.system_status["api_fallback_status"][symbol]["total_count"]
    }
    await app_state.websocket_manager.broadcast(gui_message)
    logger.info(f"API Fallback status for {symbol}: Active={is_active}, Reason='{reason}', Type='{data_type}', Source='{api_source}'")

# --- FastAPI Endpoints ---

@app.get("/gui", response_class=HTMLResponse)
async def serve_gui():
    """Serve the GUI HTML with feature flag injection"""
    try:
        # Read the HTML file
        html_file_path = os.path.join(os.path.dirname(__file__), "portrait_trading_gui.html")
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        # Inject the feature flag value
        feature_flag_value = app_state.config.FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API
        feature_flag_script = f"""
    <script>
        // Feature Flag Configuration - Injected by Backend
        var global_app_config = {{
            FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API: {str(feature_flag_value).lower()}
        }};
    </script>"""

        # Replace the placeholder script with the actual feature flag
        html_content = html_content.replace(
            """    <script>
        // This will be populated by the backend when serving the HTML
        // For development, defaults to true (Bootstrap + XREAD mode)
        var global_app_config = {
            FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API: true
        };
    </script>""",
            feature_flag_script
        )

        return HTMLResponse(content=html_content)

    except Exception as e:
        logger.error(f"Error serving GUI: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to serve GUI: {e}")

@app.get("/health")
async def health_check():
    """Comprehensive health check for GUI backend and connected systems"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "websocket_connections": len(app_state.websocket_manager.active_connections),
        "components": {}
    }

    # Check Redis connectivity
    # Status: "healthy" (green), "warning" (orange), "error" (red)
    redis_status = "unknown"
    redis_issues = []
    try:
        if app_state.redis_mgr:
            redis_client = app_state.redis_mgr.get_client()
            redis_client.ping()
            redis_status = "healthy"  # Green pill - Redis is up and responsive

            # Check Redis memory usage with percentage-based warnings
            info = redis_client.info('memory')
            memory_used_bytes = info.get('used_memory', 0)
            memory_used_mb = memory_used_bytes / (1024 * 1024)

            # Get max memory limit (0 means no limit)
            max_memory_bytes = info.get('maxmemory', 0)

            if max_memory_bytes > 0:
                # Calculate percentage if limit is set
                memory_usage_percent = (memory_used_bytes / max_memory_bytes) * 100

                if memory_usage_percent >= 90:
                    redis_status = "warning"  # Orange
                    redis_issues.append(f"High memory usage: {memory_usage_percent:.1f}% ({memory_used_mb:.1f}MB)")
                elif memory_usage_percent >= 75:
                    redis_status = "caution"  # Yellow
                    redis_issues.append(f"Elevated memory usage: {memory_usage_percent:.1f}% ({memory_used_mb:.1f}MB)")
            else:
                # No memory limit set, use absolute values
                if memory_used_mb > 1500:  # Over 1.5GB
                    redis_status = "warning"  # Orange
                    redis_issues.append(f"High memory usage: {memory_used_mb:.1f}MB (no limit set)")
                elif memory_used_mb > 750:  # Over 750MB
                    redis_status = "caution"  # Yellow
                    redis_issues.append(f"Elevated memory usage: {memory_used_mb:.1f}MB (no limit set)")

        else:
            redis_status = "error"
            redis_issues.append("RedisClientManager not initialized")

    except Exception as e:
        redis_status = "error"
        redis_issues.append(f"Connection failed: {str(e)}")

    health_status["components"]["redis"] = {
        "status": redis_status,
        "issues": redis_issues
    }

    # Check ApplicationCore health by reading latest from Redis stream
    core_status = "unknown"
    core_issues = []
    try:
        if app_state.redis_mgr:
            redis_client = app_state.redis_mgr.get_client()
            # Get latest health message from core health stream
            entries = redis_client.xrevrange("testrade:health:core", count=1)
            
            if entries:
                message_id, data = entries[0]
                # Extract timestamp from message ID (format: timestamp-sequence)
                msg_timestamp = int(message_id.split('-')[0]) / 1000.0
                current_time = time.time()
                time_since_last_health = current_time - msg_timestamp
                
                if time_since_last_health < 30:  # 30 seconds timeout
                    core_status = "healthy"
                else:
                    core_status = "error"
                    core_issues.append(f"No health updates for {time_since_last_health:.0f}s")
            else:
                core_status = "error"
                core_issues.append("No health updates in Redis stream")
    except Exception as e:
        core_status = "error"
        core_issues.append(f"Health check error: {str(e)}")

    health_status["components"]["core"] = {
        "status": core_status,
        "issues": core_issues
    }

    # Check Babysitter health by reading latest from Redis stream
    babysitter_status = "unknown"
    babysitter_issues = []
    try:
        if app_state.redis_mgr:
            redis_client = app_state.redis_mgr.get_client()
            # Get latest health message from babysitter health stream
            entries = redis_client.xrevrange("testrade:health:babysitter", count=1)
            
            if entries:
                message_id, data = entries[0]
                # Extract timestamp from message ID (format: timestamp-sequence)
                msg_timestamp = int(message_id.split('-')[0]) / 1000.0
                current_time = time.time()
                time_since_last_health = current_time - msg_timestamp
                
                if time_since_last_health < 30:  # 30 seconds timeout
                    babysitter_status = "healthy"
                else:
                    babysitter_status = "error"
                    babysitter_issues.append(f"No health updates for {time_since_last_health:.0f}s")
            else:
                babysitter_status = "error"
                babysitter_issues.append("No health updates in Redis stream")
    except Exception as e:
        babysitter_status = "error"
        babysitter_issues.append(f"Health check issue: {str(e)}")

    health_status["components"]["babysitter"] = {
        "status": babysitter_status,
        "issues": babysitter_issues
    }

    # Check Redis consumers (updated for XREAD feature flag)
    consumer_status = "healthy"
    consumer_issues = []
    active_legacy_consumers = len(app_state.redis_stream_consumers)  # Fixed attribute name
    xread_task_active = app_state.xread_consumer_task is not None and not app_state.xread_consumer_task.done()

    # Adjust expected consumers based on feature flag mode
    if app_state.config.FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API:
        # XREAD mode: expect XREAD task to be active, legacy consumers should be 0
        expected_legacy_consumers = 0
        if not xread_task_active:
            consumer_status = "error"
            consumer_issues.append("XREAD consumer task is not active")
        if active_legacy_consumers > 0:
            consumer_status = "warning"
            consumer_issues.append(f"Unexpected {active_legacy_consumers} legacy consumers active in XREAD mode")
    else:
        # Legacy mode: expect legacy consumers, XREAD task should be None
        expected_legacy_consumers = 16  # Updated count: added market_data_summary and volume_data consumers
        if active_legacy_consumers < expected_legacy_consumers:
            consumer_status = "warning"
            consumer_issues.append(f"Only {active_legacy_consumers}/{expected_legacy_consumers} legacy consumers active")
        if xread_task_active:
            consumer_status = "warning"
            consumer_issues.append("XREAD consumer task is active in legacy mode")

    health_status["components"]["redis_consumers"] = {
        "status": consumer_status,
        "issues": consumer_issues,
        "active_legacy_count": active_legacy_consumers,
        "expected_legacy_count": expected_legacy_consumers,
        "xread_consumer_active": xread_task_active,
        "mode": "XREAD" if app_state.config.FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API else "Legacy"
    }

    # Add ZMQ Links Health Data
    health_status["zmq_links"] = get_zmq_health_data()

    # Overall health assessment
    component_statuses = [comp["status"] for comp in health_status["components"].values()]
    if "error" in component_statuses:
        health_status["status"] = "error"
    elif "warning" in component_statuses:
        health_status["status"] = "warning"

    return health_status

def get_zmq_health_data():
    """Get ZMQ health data by reading from Redis health streams"""
    zmq_health = {
        "trading": {
            "connected": False,
            "emergency_buffer_fill_percent": 0,
            "emergency_buffer_size": 0,
            "emergency_buffer_max": 1000
        },
        "system": {
            "connected": False,
            "emergency_buffer_fill_percent": 0,
            "emergency_buffer_size": 0,
            "emergency_buffer_max": 1000
        },
        "bulk": {
            "connected": False,
            "emergency_buffer_fill_percent": 0,
            "emergency_buffer_size": 0,
            "emergency_buffer_max": 1000
        }
    }

    # Check if we have recent health data from Babysitter via Redis
    try:
        if app_state.redis_mgr:
            redis_client = app_state.redis_mgr.get_client()
            # Get latest health message from babysitter health stream
            entries = redis_client.xrevrange("testrade:health:babysitter", count=1)
            
            if entries:
                message_id, data = entries[0]
                # Extract timestamp from message ID
                msg_timestamp = int(message_id.split('-')[0]) / 1000.0
                current_time = time.time()
                time_since_last_health = current_time - msg_timestamp
                
                # Only mark as connected if we have recent health updates
                if time_since_last_health < 30:  # 30 seconds timeout
                    for socket_type in zmq_health:
                        zmq_health[socket_type]["connected"] = True
    except Exception as e:
        logger.debug(f"Error checking ZMQ health from Redis: {e}")
        # Return default disconnected state

    return zmq_health

@app.get("/config/roi")
async def get_current_roi():
    """Get current ROI coordinates (from live state or control.json fallback)"""
    try:
        # Return current ROI from app state (updated by ROI events)
        return {
            "status": "success",
            "roi": app_state.current_roi,
            "source": "live_state"
        }
    except Exception as e:
        logger.error(f"Error getting current ROI: {e}")
        return {
            "status": "error",
            "message": f"Failed to get ROI: {e}",
            "roi": [64, 159, 681, 296]  # Fallback
        }

@app.post("/config/roi")
async def save_roi_to_config(request: dict):
    """Save ROI coordinates to control.json"""
    try:
        import json
        import os

        # Extract ROI from request
        roi = request.get('roi')
        if not roi or not isinstance(roi, list) or len(roi) != 4:
            return {
                "status": "error",
                "message": "Invalid ROI format. Expected array of 4 integers [x1, y1, x2, y2]"
            }

        # Validate ROI values
        try:
            roi = [int(x) for x in roi]  # Ensure integers
        except (ValueError, TypeError):
            return {
                "status": "error",
                "message": "ROI coordinates must be integers"
            }

        # Load current control.json
        control_json_path = os.path.join(os.path.dirname(__file__), '..', 'utils', 'control.json')
        control_data = {}

        if os.path.exists(control_json_path):
            with open(control_json_path, 'r') as f:
                control_data = json.load(f)

        # Update ROI coordinates
        control_data['ROI_COORDINATES'] = roi

        # Save back to control.json
        with open(control_json_path, 'w') as f:
            json.dump(control_data, f, indent=4)

        logger.info(f"ROI saved to control.json: {roi}")

        return {
            "status": "success",
            "message": f"ROI saved to control.json: {roi}",
            "roi": roi
        }

    except Exception as e:
        logger.error(f"Error saving ROI to control.json: {e}")
        return {
            "status": "error",
            "message": f"Failed to save ROI: {e}"
        }

@app.get("/config/load")
async def load_config_endpoint():
    """Load current configuration values from control.json"""
    try:
        from utils.global_config import config as global_config_instance

        config_data = {
            "status": "success",
            # Basic trading configuration
            "initial_share_size": getattr(global_config_instance, 'initial_share_size', 1000),
            "add_type": getattr(global_config_instance, 'add_type', 'Equal'),
            "reduce_percentage": getattr(global_config_instance, 'reduce_percentage', 50.0),
            "manual_shares": getattr(global_config_instance, 'manual_shares', 100),

            # OCR settings
            "ocr_upscale_factor": getattr(global_config_instance, 'ocr_upscale_factor', 3.0),
            "ocr_unsharp_strength": getattr(global_config_instance, 'ocr_unsharp_strength', 1.7),
            "ocr_threshold_block_size": getattr(global_config_instance, 'ocr_threshold_block_size', 25),
            "ocr_threshold_c": getattr(global_config_instance, 'ocr_threshold_c', -6),
            "ocr_red_boost": getattr(global_config_instance, 'ocr_red_boost', 1.0),
            "ocr_green_boost": getattr(global_config_instance, 'ocr_green_boost', 1.0),
            "ocr_force_black_text_on_white": getattr(global_config_instance, 'ocr_force_black_text_on_white', True),
            "ocr_apply_text_mask_cleaning": getattr(global_config_instance, 'ocr_apply_text_mask_cleaning', True),
            "ocr_enhance_small_symbols": getattr(global_config_instance, 'ocr_enhance_small_symbols', True),
            "ocr_text_mask_min_contour_area": getattr(global_config_instance, 'ocr_text_mask_min_contour_area', 5),
            "ocr_text_mask_min_width": getattr(global_config_instance, 'ocr_text_mask_min_width', 2),
            "ocr_text_mask_min_height": getattr(global_config_instance, 'ocr_text_mask_min_height', 1),
            "ocr_symbol_max_height": getattr(global_config_instance, 'ocr_symbol_max_height', 10),
            "ocr_period_comma_ratio_min": getattr(global_config_instance, 'ocr_period_comma_ratio_min', 0.2),
            "ocr_period_comma_ratio_max": getattr(global_config_instance, 'ocr_period_comma_ratio_max', 1.2),
            "ocr_period_comma_radius": getattr(global_config_instance, 'ocr_period_comma_radius', 5),
            "ocr_hyphen_min_ratio": getattr(global_config_instance, 'ocr_hyphen_min_ratio', 3.0),
            "ocr_hyphen_min_height": getattr(global_config_instance, 'ocr_hyphen_min_height', 3),
            "OCR_INPUT_VIDEO_FILE_PATH": getattr(global_config_instance, 'OCR_INPUT_VIDEO_FILE_PATH', None),
            "video_loop_enabled": getattr(global_config_instance, 'video_loop_enabled', False),
            "tesseract_cmd": getattr(global_config_instance, 'tesseract_cmd', 'C:\\Program Files\\Tesseract-OCR\\tesseract.exe'),

            # Development settings
            "development_mode": getattr(global_config_instance, 'development_mode', True),
            "enable_image_recording": getattr(global_config_instance, 'enable_image_recording', False),
            "enable_raw_ocr_recording": getattr(global_config_instance, 'enable_raw_ocr_recording', False),
            "enable_intellisense_logging": getattr(global_config_instance, 'enable_intellisense_logging', False),
            "enable_observability_logging": getattr(global_config_instance, 'enable_observability_logging', False),
            "log_level": getattr(global_config_instance, 'log_level', 'ERROR'),
            "enable_ocr_debug_logging": getattr(global_config_instance, 'enable_ocr_debug_logging', False),
            "babysitter_log_level": getattr(global_config_instance, 'babysitter_log_level', 'ERROR')
        }

        return config_data
    except Exception as e:
        logger.error(f"Error loading configuration: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to load configuration: {e}")

@app.post("/logging/update")
async def update_gui_backend_logging(request: dict):
    """
    Update GUI backend logging level directly.
    This endpoint allows the unified logging system to control GUI backend verbosity.
    """
    try:
        log_level = request.get('log_level', 'INFO').upper()

        # Validate log level
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if log_level not in valid_levels:
            raise HTTPException(status_code=400, detail=f"Invalid log level. Must be one of: {valid_levels}")

        # Update the GUI backend logger level
        gui_logger = logging.getLogger(__name__)
        numeric_level = getattr(logging, log_level)
        gui_logger.setLevel(numeric_level)

        # Also update the root logger for this process
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)

        logger.info(f"GUI backend logging level updated to {log_level}")

        return {
            "status": "success",
            "message": f"GUI backend logging level updated to {log_level}",
            "new_level": log_level
        }

    except Exception as e:
        logger.error(f"Error updating GUI backend logging level: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update logging level: {str(e)}")

@app.post("/broadcast/status")
async def broadcast_status_update(request: dict):
    """
    Broadcast status update to all connected WebSocket clients.
    This endpoint allows direct status updates from the core system.
    """
    try:
        message_type = request.get('message_type', 'core_status_update')
        status_event = request.get('status_event', '')
        timestamp = request.get('timestamp', time.time())
        component = request.get('component', 'UNKNOWN')

        # Create WebSocket message with correct format for frontend
        ws_message = {
            "type": message_type,  # Frontend expects 'type', not 'message_type'
            "status_event": status_event,
            "timestamp": timestamp,
            "component": component
        }

        # Broadcast to all connected WebSocket clients
        await app_state.websocket_manager.broadcast(ws_message)

        logger.info(f" Broadcasted status update: {status_event} from {component}")

        return {
            "status": "success",
            "message": f"Status update '{status_event}' broadcasted to {len(app_state.websocket_manager.active_connections)} clients",
            "clients_notified": len(app_state.websocket_manager.active_connections)
        }

    except Exception as e:
        logger.error(f" Error broadcasting status update: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to broadcast status update: {str(e)}")

@app.post("/config")
async def update_config_endpoint(request: dict): # Renamed from update_config to avoid clash with JS function name if used globally
    try:
        logger.info(f"Received /config request: {request}")

        command_type = request.get("command_type")
        parameters = request.get("parameters", {})

        # Specific handling for direct config keys like initialSize, reducePercent, addType
        # These update utils.global_config and save to control.json
        # This part seems to be for direct updates to control.json, not for sending Redis commands.
        from utils.global_config import config as global_config_instance

        updated_control_json = False
        response_message = "No configuration changes applied."

        # Handle all configuration keys with unified approach
        config_mappings = {
            # Trading configuration
            "initialSize": ("initial_share_size", int, "Initial share size"),
            "initial_share_size": ("initial_share_size", int, "Initial share size"),
            "reducePercent": ("reduce_percentage", float, "Reduce percentage"),
            "reduce_percentage": ("reduce_percentage", float, "Reduce percentage"),
            "addType": ("add_type", str, "Add type"),
            "add_type": ("add_type", str, "Add type"),
            "manual_shares": ("manual_shares", int, "Manual shares"),
            "manualShares": ("manual_shares", int, "Manual shares"),

            # Development settings
            "development_mode": ("development_mode", bool, "Development mode"),
            "enable_image_recording": ("enable_image_recording", bool, "Image recording"),
            "enable_raw_ocr_recording": ("enable_raw_ocr_recording", bool, "Raw OCR recording"),
            "enable_intellisense_logging": ("enable_intellisense_logging", bool, "IntelliSense logging"),
            "enable_observability_logging": ("enable_observability_logging", bool, "Observability logging"),
            "log_level": ("log_level", str, "Log level"),
            "enable_ocr_debug_logging": ("enable_ocr_debug_logging", bool, "OCR debug logging"),
            "babysitter_log_level": ("babysitter_log_level", str, "Babysitter log level")
            # NOTE: OCR parameters MUST go through the command system, NOT direct config!
        }

        # Collect all parameters to update via command system
        params_to_update = {}
        
        for request_key, (config_attr, value_type, display_name) in config_mappings.items():
            if request_key in request:
                try:
                    value = value_type(request[request_key])
                    params_to_update[config_attr] = value
                    logger.info(f"Queuing update for {config_attr} = {value}")
                except (ValueError, TypeError) as e:
                    logger.error(f"Invalid {request_key} value: {request[request_key]} - {e}")
                    raise HTTPException(status_code=400, detail=f"Invalid {request_key} value.")

        if params_to_update:
            # Use the command system to update trading parameters
            logger.info(f"Sending UPDATE_TRADING_PARAMS command with parameters: {params_to_update}")
            
            try:
                # Send command through the proper channel using existing mechanism
                command_id = str(uuid.uuid4())
                redis_message = create_redis_message_json(
                    payload={
                        "command_id": command_id,
                        "target_service": "CORE_SERVICE",
                        "command_type": "UPDATE_TRADING_PARAMS",
                        "parameters": params_to_update,
                        "timestamp": time.time(),
                        "source": "GUI_Backend_Config"
                    },
                    event_type_str="TESTRADE_GUI_COMMAND_V2",
                    correlation_id_val=command_id,
                    source_component_name="GUI_Backend"
                )
                
                target_stream = getattr(app_state.config, 'redis_stream_commands_from_gui', 'testrade:commands:from_gui')
                success = await asyncio.to_thread(
                    app_state.babysitter_ipc_client.send_data,
                    target_stream,
                    redis_message
                )
                
                if success:
                    updated_control_json = True
                    param_list = [f"{k}={v}" for k, v in params_to_update.items()]
                    response_message = f"Configuration update command sent: {', '.join(param_list)}"
                    logger.info(f"Successfully sent UPDATE_TRADING_PARAMS command via Redis")
                else:
                    logger.error(f"Failed to send UPDATE_TRADING_PARAMS command to Redis")
                    raise HTTPException(status_code=500, detail=f"Failed to send configuration update command")
                    
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error sending UPDATE_TRADING_PARAMS command: {e}", exc_info=True)
                raise HTTPException(status_code=500, detail=f"Failed to send configuration update command: {e}")
        
        if updated_control_json:
            return {"status": "success", "message": response_message}

        # Handling for command_type based updates (like SET_ROI_ABSOLUTE via /config)
        # This part sends a Redis command.
        if not command_type:
            if "roi" in request: # Structure might be {"roi": {"x1": ..., "y1": ...}}
                roi_data = request.get("roi", {})
                command_type = "SET_ROI_ABSOLUTE"
                parameters = {
                    "x1": roi_data.get("x1", roi_data.get("x", 0)),
                    "y1": roi_data.get("y1", roi_data.get("y", 0)),
                    "x2": roi_data.get("x2", roi_data.get("x", 0) + roi_data.get("width", 100)),
                    "y2": roi_data.get("y2", roi_data.get("y", 0) + roi_data.get("height", 100))
                }
            elif any(key in request for key in ["x1", "y1", "x2", "y2"]): # Direct coords
                command_type = "SET_ROI_ABSOLUTE"
                parameters = {
                    "x1": request.get("x1", request.get("x", 0)),
                    "y1": request.get("y1", request.get("y", 0)),
                    "x2": request.get("x2", request.get("x", 0) + request.get("width", 100)),
                    "y2": request.get("y2", request.get("y", 0) + request.get("height", 100))
                }
        
        if not command_type:
            logger.warning("No command_type specified and no direct config keys matched in /config request.")
            # If we already returned due to updated_control_json, this won't be reached.
            # If not, and no command_type, then it's an issue.
            if not updated_control_json: # Only raise if nothing was done
                 raise HTTPException(status_code=400, detail="No valid configuration keys or command_type provided.")
            else: # Should not happen due to structure, but as a fallback
                return {"status": "error", "message": "Ambiguous request, some parts processed, some not."}


        logger.info(f"Processing command via /config: {command_type} with params: {parameters}")

        if command_type == "SET_ROI_ABSOLUTE":
            command_id = str(uuid.uuid4())
            # Phase 2: Use new command architecture with target_service field
            redis_message = create_redis_message_json(
                payload={
                    "command_id": command_id,
                    "target_service": "OCR_PROCESS",  # Phase 2: Explicit target routing
                    "command_type": command_type,
                    "parameters": parameters, # parameters should be {"x1": ..., "y1":..., ...}
                    "timestamp": time.time(),
                    "source": "GUI_ConfigEndpoint"
                },
                event_type_str="TESTRADE_GUI_COMMAND_V2",  # Phase 2: New event type
                correlation_id_val=command_id,
                source_component_name="GUI_Backend"
            )
            # Phase 2: Send to new command stream
            target_stream = getattr(app_state.config, 'redis_stream_commands_from_gui', 'testrade:commands:from_gui')
            
            try:
                redis_client = app_state.redis_mgr.get_client()
                if not redis_client:
                    raise Exception("Redis client not available")
                
                # Use xadd to publish to the command stream
                message_id = await asyncio.to_thread(
                    redis_client.xadd,
                    target_stream,
                    {"json_payload": redis_message}
                )
                logger.info(f" Phase 2: Sent {command_type} command (ID: {command_id}) to {target_stream} via /config")
                return {"status": "success", "command_id": command_id, "message": f"{command_type} command sent"}
                
            except Exception as e:
                logger.error(f" Phase 2: Failed to send {command_type} command via /config: {e}")
                raise HTTPException(status_code=500, detail=f"Failed to send command via Redis: {str(e)}")
        else:
            # If we reached here, means command_type was set but not SET_ROI_ABSOLUTE and no direct config keys updated
            logger.warning(f"Unknown command_type '{command_type}' in /config that wasn't a direct key.")
            raise HTTPException(status_code=400, detail=f"Unsupported command_type '{command_type}' in /config endpoint for Redis commands.")

    except HTTPException: # Re-raise HTTPExceptions
        raise
    except Exception as e:
        logger.error(f"Error handling /config update: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {e}")


@app.post("/test-roi")
async def test_roi_update(request: dict):
    try:
        x1 = int(request.get("x1", 100))
        y1 = int(request.get("y1", 100))
        x2 = int(request.get("x2", 500))
        y2 = int(request.get("y2", 300))

        roi_request_params = {"x1": x1, "y1": y1, "x2": x2, "y2": y2}
        
        # This will now use the /config endpoint's logic for SET_ROI_ABSOLUTE
        # Pass parameters directly, not nested under "parameters" or "roi" initially
        result = await update_config_endpoint(roi_request_params) 
        return result

    except Exception as e:
        logger.error(f"Error in test ROI update: {e}")
        return {"status": "error", "message": f"Error: {e}"}


async def send_positions_from_redis(websocket: WebSocket):
    """Send all current positions from Redis to newly connected WebSocket client"""
    try:
        if not app_state.redis_mgr:
            logger.warning("Cannot send positions - Redis manager not initialized")
            return
            
        redis_client = app_state.redis_mgr.get_client()
        
        # Read recent position updates from Redis stream
        # Get last 50 entries to find all unique symbols
        entries = redis_client.xrevrange("testrade:position-updates", count=50)
        
        # Track latest update for each symbol
        latest_positions = {}
        
        for entry_id, data in entries:
            json_payload = data.get('json_payload') or data.get(b'json_payload', '{}')
            if isinstance(json_payload, bytes):
                json_payload = json_payload.decode('utf-8')
                
            wrapper = json.loads(json_payload)
            payload = wrapper.get('payload', {})
            
            symbol = payload.get('symbol')
            if symbol and symbol not in latest_positions:
                # Only keep the latest update for each symbol
                latest_positions[symbol] = payload
        
        # Send each open position
        positions_sent = 0
        for symbol, position_data in latest_positions.items():
            if position_data.get('is_open', False):
                # Send position update message
                message = {
                    "type": "position_update",
                    "symbol": symbol,
                    "position": position_data,
                    "is_open": True
                }
                await websocket.send_text(json.dumps(message))
                positions_sent += 1
                
        logger.info(f"Sent {positions_sent} open positions from Redis to new WebSocket client")
        
    except Exception as e:
        logger.error(f"Error sending positions from Redis: {e}", exc_info=True)


async def send_account_data_from_redis(websocket: WebSocket):
    """Send current account data from Redis to newly connected WebSocket client"""
    try:
        if not app_state.redis_mgr:
            logger.warning("Cannot send account data - Redis manager not initialized")
            return
            
        redis_client = app_state.redis_mgr.get_client()
        
        # Try account-updates first (real-time), fallback to account-summary
        entries = redis_client.xrevrange("testrade:account-updates", count=1)
        if not entries:
            entries = redis_client.xrevrange("testrade:account-summary", count=1)
        
        if entries:
            # Parse the latest entry
            entry_id, data = entries[0]
            json_payload = data.get('json_payload') or data.get(b'json_payload', '{}')
            if isinstance(json_payload, bytes):
                json_payload = json_payload.decode('utf-8')
            
            wrapper = json.loads(json_payload)
            account_data = wrapper.get('payload', {})
            
            # Transform and send
            transformed_account = transform_account_data_for_frontend(account_data, is_bootstrap=False)
            
            message = {
                "type": "account_summary_update",
                "account": transformed_account
            }
            await websocket.send_text(json.dumps(message))
            
            logger.info(f"Sent account data from Redis to new WebSocket client: BP=${transformed_account.get('buying_power', 0)}")
            
    except Exception as e:
        logger.error(f"Error sending account data from Redis: {e}", exc_info=True)


async def send_open_orders_from_redis(websocket: WebSocket):
    """Send all current open orders from Redis to newly connected WebSocket client"""
    try:
        if not app_state.redis_mgr:
            logger.warning("Cannot send open orders - Redis manager not initialized")
            return
            
        redis_client = app_state.redis_mgr.get_client()
        
        # Read recent open orders from Redis stream
        entries = redis_client.xrevrange("testrade:open-orders", count=100)
        
        # Track latest order for each local_order_id
        latest_orders = {}
        
        for entry_id, data in entries:
            json_payload = data.get('json_payload') or data.get(b'json_payload', '{}')
            if isinstance(json_payload, bytes):
                json_payload = json_payload.decode('utf-8')
                
            wrapper = json.loads(json_payload)
            payload = wrapper.get('payload', {})
            
            local_order_id = payload.get('local_order_id')
            if local_order_id and local_order_id not in latest_orders:
                latest_orders[local_order_id] = payload
        
        # Send each open order
        orders_sent = 0
        for local_order_id, order_data in latest_orders.items():
            # Only send if order is truly open (not filled, cancelled, rejected)
            status = order_data.get('status', '').upper()
            if status in ['PENDING', 'SUBMITTED', 'PARTIALLY_FILLED', 'OPEN', 'NEW', 'ACCEPTED']:
                message = {
                    "type": "open_order_update",
                    "order": order_data,
                    "is_bootstrap": True
                }
                await websocket.send_text(json.dumps(message))
                orders_sent += 1
                
        logger.info(f"Sent {orders_sent} open orders from Redis to new WebSocket client")
        
    except Exception as e:
        logger.error(f"Error sending open orders from Redis: {e}", exc_info=True)


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await app_state.websocket_manager.connect(websocket)
    try:
        await websocket.send_text(json.dumps({"type": "system_message", "message": "Connected to TESTRADE Pro API", "level": "success"}))
        
        # Send initial data with error handling
        try:
            # Send current positions from Redis on connect
            await send_positions_from_redis(websocket)
        except Exception as e:
            logger.error(f"Error sending initial positions: {e}", exc_info=True)
            
        try:
            # Send current account data from Redis on connect
            await send_account_data_from_redis(websocket)
        except Exception as e:
            logger.error(f"Error sending initial account data: {e}", exc_info=True)
            
        try:
            # Send current open orders from Redis on connect
            await send_open_orders_from_redis(websocket)
        except Exception as e:
            logger.error(f"Error sending initial open orders: {e}", exc_info=True)
        
        while True:
            # Keep connection alive with timeout to send periodic heartbeats
            try:
                # Wait for client message with timeout
                message = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                
                # Handle ping/pong for keepalive
                if message:
                    try:
                        data = json.loads(message)
                        if data.get("type") == "ping":
                            await websocket.send_text(json.dumps({"type": "pong", "timestamp": time.time()}))
                    except:
                        pass  # Ignore non-JSON messages
                        
            except asyncio.TimeoutError:
                # Send heartbeat every 30 seconds if no messages
                try:
                    await websocket.send_text(json.dumps({"type": "heartbeat", "timestamp": time.time()}))
                except:
                    break  # Connection likely closed
    except WebSocketDisconnect:
        logger.info(f"WebSocket client {websocket.client.host} disconnected (gracefully or not).")
    except Exception as e:
        logger.error(f"WebSocket error for client {websocket.client.host}: {e}", exc_info=True)
    finally:
        app_state.websocket_manager.disconnect(websocket)


@app.post("/control/command", status_code=202)
async def forward_command_to_core(request: CommandRequest):
    """Forward GUI commands to ApplicationCore via Redis - GUI Backend does NOT execute commands."""
    # Check Redis manager instead of IPC client
    if not app_state.redis_mgr:
        raise HTTPException(status_code=503, detail="Redis connection not ready.")

    command_map = {
        "start_ocr": "START_OCR", "stop_ocr": "STOP_OCR", "get_ocr_status": "GET_OCR_STATUS",
        "set_roi_absolute": "SET_ROI_ABSOLUTE",
        "roi_adjust": "ROI_ADJUST",  # Re-added for incremental ROI adjustments
        "start_testrade_core": "START_APPLICATION_CORE", "stop_testrade_core": "STOP_APPLICATION_CORE",
        "toggle_confidence_mode": "TOGGLE_CONFIDENCE_MODE",
        "update_ocr_preprocessing_full": "UPDATE_OCR_PREPROCESSING_FULL",
        "update_trading_params": "UPDATE_TRADING_PARAMS", "save_configuration": "SAVE_CONFIGURATION",
        "dump_data": "DUMP_DATA", "get_global_configuration": "GET_GLOBAL_CONFIGURATION",
        "reset_to_default_config": "RESET_TO_DEFAULT_CONFIG",
        # Development Settings Commands
        "update_development_mode": "UPDATE_DEVELOPMENT_MODE",
        "update_recording_settings": "UPDATE_RECORDING_SETTINGS",
        "update_logging_settings": "UPDATE_LOGGING_SETTINGS",
        # Trading Commands
        "emergency_stop": "EMERGENCY_STOP",
        "manual_trade": "MANUAL_TRADE",
        "force_close_all": "FORCE_CLOSE_ALL",
        # Other Commands
        "toggle_recording_mode": "TOGGLE_RECORDING_MODE", # Assuming this is a valid backend command type
        "toggle_all_symbols_mode": "TOGGLE_ALL_SYMBOLS_MODE", # Example
        "toggle_video_mode": "TOGGLE_VIDEO_MODE", # Example
        "debug_positions": "DEBUG_POSITIONS", # Debug Position Manager
        # Debug Commands for Pipeline Testing
        "debug_trigger_on_quote": "DEBUG_TRIGGER_ON_QUOTE",
        # Bootstrap Command (Phase 2: Replaces individual GET_* commands)
        "request_initial_state": "REQUEST_INITIAL_STATE",
        # State Query Commands (only non-obsolete ones)
        "get_all_orders": "GET_ALL_ORDERS"
        # REMOVED OBSOLETE COMMANDS: get_all_positions, get_account_summary, get_position_summary, get_trade_history
        # These are now handled by REQUEST_INITIAL_STATE bootstrap + Redis streams

    }
    command_type = command_map.get(request.command.lower())
    if not command_type:
        raise HTTPException(status_code=400, detail=f"Unknown command: {request.command}. Supported: {list(command_map.keys())}")

    command_id = str(uuid.uuid4())

    # Phase 2: Determine target service based on command type
    target_service = "CORE_SERVICE"  # Default to Core
    if command_type in ["SET_ROI_ABSOLUTE", "ROI_ADJUST", "START_OCR", "STOP_OCR", "GET_OCR_STATUS",
                       "TOGGLE_CONFIDENCE_MODE", "UPDATE_OCR_PREPROCESSING_FULL"]:
        target_service = "OCR_PROCESS"

    # Phase 2: Use new command architecture with target_service field
    payload_dict = {
        "command_id": command_id,
        "target_service": target_service,  # Phase 2: Explicit target routing
        "command_type": command_type,
        "parameters": request.parameters
    }
    redis_message_json = create_redis_message_json(
        payload=payload_dict,
        event_type_str="TESTRADE_GUI_COMMAND_V2",  # Phase 2: New event type
        correlation_id_val=command_id,
        source_component_name="GUI_Backend_Control"
    )

    # Send directly to Redis
    target_stream = getattr(app_state.config, 'redis_stream_commands_from_gui', 'testrade:commands:from_gui')
    
    try:
        redis_client = app_state.redis_mgr.get_client()
        if not redis_client:
            logger.error(f"Redis client not available for command '{command_type}'")
            raise HTTPException(status_code=503, detail="Redis connection not available.")
        
        # Use xadd to publish to the command stream
        message_id = await asyncio.to_thread(
            redis_client.xadd,
            target_stream,
            {"json_payload": redis_message_json}
        )
        logger.info(f"Command '{command_type}' sent to Redis stream '{target_stream}' with ID: {message_id}")
        success = True
        
    except Exception as e:
        logger.error(f"Failed to send command '{command_type}' to Redis: {e}")
        success = False
    if success:
        return {
            "status": "command_forwarded",
            "command_id": command_id,
            "original_command": request.command,
            "target_service": target_service,
            "core_command_type": command_type,
            "message": f"Command forwarded to {target_service} for execution"
        }
    else:
        raise HTTPException(status_code=500, detail="Failed to forward command to ApplicationCore")

# NEW: Add command handler for refreshing market data
@app.post("/control/refresh_market_data")
async def refresh_market_data(request: dict):
    """Request fresh market data for specified symbols"""
    try:
        symbols = request.get("symbols", [])

        if not symbols:
            # Get symbols from active trades
            symbols = [trade.get("symbol") for trade in app_state.active_trades if trade.get("symbol")]

        if not symbols:
            return {"status": "error", "message": "No symbols specified and no active trades"}

        # Send command to request market data refresh
        command_id = str(uuid.uuid4())
        redis_message = create_redis_message_json(
            payload={
                "command_id": command_id,
                "target_service": "MARKET_DATA_SERVICE",
                "command_type": "REFRESH_MARKET_DATA",
                "parameters": {"symbols": symbols},
                "timestamp": time.time(),
                "source": "GUI_MarketDataRefresh"
            },
            event_type_str="TESTRADE_GUI_COMMAND_V2",
            correlation_id_val=command_id,
            source_component_name="GUI_Backend"
        )

        target_stream = getattr(app_state.config, 'redis_stream_commands_from_gui', 'testrade:commands:from_gui')
        
        try:
            redis_client = app_state.redis_mgr.get_client()
            if not redis_client:
                raise Exception("Redis client not available")
            
            # Use xadd to publish to the command stream
            message_id = await asyncio.to_thread(
                redis_client.xadd,
                target_stream,
                {"json_payload": redis_message}
            )
            logger.info(f"Market data refresh command sent to Redis stream '{target_stream}' with ID: {message_id}")
            success = True
            
        except Exception as e:
            logger.error(f"Failed to send market data refresh command to Redis: {e}")
            success = False

        if success:
            logger.info(f"Market data refresh requested for {len(symbols)} symbols")
            return {
                "status": "success",
                "command_id": command_id,
                "symbols": symbols,
                "message": f"Market data refresh requested for {len(symbols)} symbols"
            }
        else:
            return {"status": "error", "message": "Failed to send market data refresh command"}

    except Exception as e:
        logger.error(f"Error refreshing market data: {e}")
        return {"status": "error", "message": f"Error: {e}"}



# REMOVED: /control/get_position_summary endpoint - obsolete
# Position summary data now comes via testrade:position-summary Redis stream
# Use REQUEST_INITIAL_STATE bootstrap command instead

# REMOVED: /control/get_account_summary endpoint - obsolete
# Account summary data now comes via testrade:account-summary Redis stream
# Use REQUEST_INITIAL_STATE bootstrap command instead

# REMOVED: /control/get_trade_history endpoint - obsolete
# Trade history data now comes via testrade:trade-history Redis stream
# Use REQUEST_INITIAL_STATE bootstrap command instead


async def request_initial_state():
    """
    Request initial state from ApplicationCore on GUI backend startup.
    This ensures the GUI shows existing positions, orders, and account data
    instead of waiting for new events.
    """
    try:
        if not app_state.babysitter_ipc_client:
            logger.warning("Cannot request initial state: BabysitterIPCClient not available")
            return

        logger.info("Requesting initial state from ApplicationCore...")

        # Small delay to ensure consumers are fully ready
        await asyncio.sleep(1.0)

        # Use NEW bootstrap pattern - single REQUEST_INITIAL_STATE command
        try:
            bootstrap_request = CommandRequest(
                command="request_initial_state",
                parameters={}
            )
            await forward_command_to_core(bootstrap_request)
            logger.info("✅ Bootstrap REQUEST_INITIAL_STATE sent - all data will come via Redis streams")
        except Exception as e:
            logger.error(f"❌ Failed to send bootstrap REQUEST_INITIAL_STATE: {e}")
            # Fallback: If bootstrap fails, we could try individual requests, but this indicates a deeper issue

        logger.info("✅ Bootstrap pattern initiated - data will flow through Redis streams")

    except Exception as e:
        logger.error(f"Error requesting initial state: {e}", exc_info=True)


# Enhanced app state initialization
def initialize_enhanced_app_state():
    """Initialize enhanced app state with additional data structures"""
    app_state.position_summary = {
        "totalSharesTraded": 0,
        "openPositionsCount": 0,
        "closedPositionsCount": 0,
        "totalTradesCount": 0,
        "openPnL": 0.0,
        "closedPnL": 0.0,
        "totalDayPnL": 0.0,
        "winRate": 0.0,
        "avgWin": 0.0,
        "avgLoss": 0.0
    }

    app_state.enhanced_financial_data = {
        "accountValue": 0.0,
        "buyingPower": 0.0,
        "buyingPowerUsed": 0.0,
        "buyingPowerLeft": 0.0,
        "dayPnL": 0.0,
        "cashBalance": 0.0,
        "marginUsed": 0.0,
        "maintenanceMargin": 0.0
    }

    app_state.historical_trades = []
    app_state.daily_summaries = {}

    # Initialize health tracking timestamps (reset to 0 on startup)
    app_state.last_core_health_time = 0
    app_state.last_babysitter_health_time = 0

    # Initialize status tracking to prevent false change notifications on startup
    app_state.last_core_status = None
    app_state.last_babysitter_status = None

    logger.info("Enhanced app state initialized with position summary, financial data, and health tracking")

# --- Redis Consumer Cleanup Helper ---
async def cleanup_redis_consumer(consumer, consumer_name: str):
    """
    Properly remove a Redis consumer from its consumer group to prevent orphaned consumers.
    This prevents the accumulation of dead consumers in Redis.

    For bulk cleanup of orphaned consumers, use: python scripts/cleanup_dead_redis_consumers.py
    """
    try:
        if hasattr(consumer, 'redis_client') and consumer.redis_client:
            # Extract consumer details
            stream_name = getattr(consumer, 'stream_name', '')
            consumer_group_name = getattr(consumer, 'consumer_group_name', '')
            redis_consumer_name = getattr(consumer, 'consumer_name', '')

            if stream_name and consumer_group_name and redis_consumer_name:
                # Remove consumer from Redis consumer group
                try:
                    result = consumer.redis_client.xgroup_delconsumer(
                        stream_name,
                        consumer_group_name,
                        redis_consumer_name
                    )
                    if result:
                        logger.info(f"✅ Removed consumer '{redis_consumer_name}' from Redis group '{consumer_group_name}' in stream '{stream_name}'")
                    else:
                        logger.info(f"[INFO] Consumer '{redis_consumer_name}' was already removed from Redis")
                except Exception as e:
                    logger.warning(f"⚠️  Could not remove consumer '{redis_consumer_name}' from Redis: {e}")
            else:
                logger.warning(f"⚠️  Consumer {consumer_name} missing Redis details for cleanup")
        else:
            logger.warning(f"⚠️  Consumer {consumer_name} has no Redis client for cleanup")
    except Exception as e:
        logger.error(f"❌ Error during Redis consumer cleanup for {consumer_name}: {e}")

# --- Lifespan Manager (replaces deprecated @app.on_event) ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup logic
    logger.info("🚀 TESTRADE Pro API Backend starting up (lifespan)...")
    app_state.main_event_loop = asyncio.get_running_loop()
    initialize_enhanced_app_state()

    try:
        app_state.redis_mgr = RedisClientManager.get_instance()
        logger.info(f"RedisClientManager initialized with host: {app_state.redis_mgr.host}")
    except Exception as e:
        logger.critical(f"Failed to initialize RedisClientManager: {e}. Application cannot start.", exc_info=True)
        raise RuntimeError(f"RedisClientManager initialization failed: {e}") from e

    # Skip Babysitter IPC Client initialization - using Redis-only communication
    # This is intentional for WSL-based GUI backend that only communicates via Redis
    logger.info("Skipping BulletproofBabysitterIPCClient initialization - using Redis-only communication")
    app_state.babysitter_ipc_client = None

    if app_state.config.FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API:
        logger.info("FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API is TRUE. Starting XREAD consumer.")
        # Start the new XREAD consumer task
        app_state.xread_consumer_task = asyncio.create_task(xread_gui_streams_task(app_state))
        logger.info("XREAD consumer task created.")
        # Intellisense and other non-GUI consumers might still use RedisStreamConsumerBase
        # Start them here if needed, or manage them separately.
    else:
        logger.info("FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API is FALSE. Using old Consumer Group logic.")
        # Fallback to old consumer group logic
        consumers_config = [
            {"name": "raw_ocr", "stream_name": "testrade:raw-ocr-events", "handler": handle_raw_ocr_message, "low_latency": False},
            {"name": "cleaned_ocr", "stream_name": "testrade:cleaned-ocr-snapshots", "handler": handle_cleaned_ocr_message, "low_latency": False},
            {"name": "image_grabs", "stream_name": "testrade:image-grabs", "handler": handle_image_grab_message, "low_latency": False},
            {"name": "core_status", "stream_name": "testrade:core-status-events", "handler": handle_core_status_message, "low_latency": False},
            {"name": "phase2_responses", "stream_name": "testrade:responses:to_gui", "handler": handle_phase2_response_message, "low_latency": True},
            {"name": "roi_updates", "stream_name": getattr(app_state.config, 'redis_stream_roi_updates', 'testrade:roi-updates'), "handler": handle_roi_update_message, "low_latency": False},
            {"name": "account_summary", "stream_name": "testrade:account-summary", "handler": handle_account_summary_message, "low_latency": True},
            {"name": "position_summary", "stream_name": "testrade:position-summary", "handler": handle_position_summary_message, "low_latency": True},
            {"name": "position_updates", "stream_name": getattr(app_state.config, 'redis_stream_enriched_position_updates', 'testrade:enriched-position-updates'), "handler": handle_enriched_position_updates_message, "low_latency": True},
            # DISABLED: Legacy stream causing price oscillation conflicts with enriched stream
            # {"name": "raw_position_updates", "stream_name": getattr(app_state.config, 'redis_stream_position_updates', 'testrade:position-updates'), "handler": handle_position_updates_message, "low_latency": True},
            {"name": "order_status", "stream_name": getattr(app_state.config, 'redis_stream_order_status', 'testrade:order-status'), "handler": handle_order_status_message, "low_latency": True},
            {"name": "order_fills", "stream_name": getattr(app_state.config, 'redis_stream_order_fills', 'testrade:order-fills'), "handler": handle_order_fills_message, "low_latency": True},
            {"name": "order_rejections", "stream_name": getattr(app_state.config, 'redis_stream_order_rejections', 'testrade:order-rejections'), "handler": handle_order_rejection_message, "low_latency": True},
            # ENHANCED: Use the new enhanced market data handlers
            {"name": "price_quotes", "stream_name": getattr(app_state.config, 'redis_stream_internal_raw_quotes', 'testrade:internal:market-data:raw-quotes'), "handler": handle_enhanced_price_quotes_message},
            {"name": "price_trades", "stream_name": getattr(app_state.config, 'redis_stream_internal_raw_trades', 'testrade:internal:market-data:raw-trades'), "handler": handle_enhanced_price_trades_message},
            # Health monitoring streams
            {"name": "core_health", "stream_name": getattr(app_state.config, 'redis_stream_core_health', 'testrade:health:core'), "handler": handle_core_health_message},
            {"name": "babysitter_health", "stream_name": getattr(app_state.config, 'redis_stream_babysitter_health', 'testrade:health:babysitter'), "handler": handle_babysitter_health_message},
            # NEW: Additional market data streams (if available)
            {"name": "market_data_summary", "stream_name": getattr(app_state.config, 'redis_stream_market_summary', 'testrade:market-data:summary'), "handler": handle_market_data_summary_message},
            {"name": "volume_data", "stream_name": getattr(app_state.config, 'redis_stream_volume_data', 'testrade:market-data:volume'), "handler": handle_volume_data_message},

            # Enhanced consumers for position summary and trade tracking
            {"name": "enhanced_account_summary", "stream_name": "testrade:account-summary-enhanced", "handler": handle_enhanced_account_summary_message},
            {"name": "enhanced_position_summary", "stream_name": "testrade:position-summary-enhanced", "handler": handle_enhanced_position_summary_message},
            {"name": "trade_history", "stream_name": "testrade:trade-history", "handler": handle_trade_history_message},
            {"name": "trade_opened", "stream_name": "testrade:trade-opened", "handler": handle_trade_opened_message},
            {"name": "trade_closed", "stream_name": "testrade:trade-closed", "handler": handle_trade_closed_message},
            {"name": "position_pnl_updates", "stream_name": "testrade:position-pnl", "handler": handle_position_pnl_update_message},
            {"name": "daily_summary", "stream_name": "testrade:daily-summary", "handler": handle_daily_summary_message},

            # --- BOOTSTRAP PATTERN: Order batch updates consumer ---
            {
                "name": "order_batch_updates",
                "stream_name": getattr(app_state.config, 'redis_stream_order_batch_updates', 'testrade:order-batch-updates'),
                "handler": handle_order_batch_update_message,
                "low_latency": True
            },

            # --- API FALLBACK OBSERVABILITY: System status consumer ---
            {
                "name": "api_fallback_status",
                "stream_name": getattr(app_state.config, 'redis_stream_system_status', 'testrade:system:status'),
                "handler": handle_api_fallback_status_message,
                "low_latency": False
            }
        ]

        for config_item in consumers_config:
            try:
                consumer_name = f"gui_consumer_{config_item['name']}_{os.getpid()}"
                logger.info(f" Starting consumer for {config_item['name']} on stream {config_item['stream_name']}")

                low_latency_mode = config_item.get("low_latency", False)

                consumer = RedisStreamConsumerBase(
                    redis_host=app_state.redis_mgr.host,
                    redis_port=app_state.redis_mgr.port,
                    redis_password=app_state.redis_mgr.password,
                    stream_name=config_item["stream_name"],
                    consumer_group_name=f"gui_group_{config_item['name']}",
                    consumer_name=consumer_name,
                    message_handler=config_item["handler"],
                    main_event_loop=app_state.main_event_loop,
                    logger_instance=logger,
                    low_latency_mode=low_latency_mode
                )

                if low_latency_mode:
                    logger.info(f" LOW-LATENCY MODE enabled for {config_item['name']} (count=1, block=100ms)")

                if consumer.start():
                    app_state.redis_stream_consumers[config_item["name"]] = consumer
                    logger.info(f" Started Redis consumer: {config_item['name']} -> {config_item['stream_name']}")
                else:
                    logger.error(f" Failed to start consumer: {config_item['name']} -> {config_item['stream_name']}")
            except Exception as e:
                logger.error(f" Error starting consumer '{config_item['name']}': {e}", exc_info=True)

    # 🚨 CHUNK 14: Start the price_data pruning task
    prune_interval = getattr(app_state.config, 'GUI_PRICE_DATA_PRUNE_INTERVAL_SEC', 3600)  # Default 1 hour
    stale_threshold = getattr(app_state.config, 'GUI_PRICE_DATA_STALE_THRESHOLD_SEC', 7200)  # Default 2 hours
    asyncio.create_task(periodically_prune_price_data(app_state, prune_interval, stale_threshold))
    logger.info(f"🧹 Periodic price_data pruning task started (Interval: {prune_interval}s, StaleAfter: {stale_threshold}s)")

    logger.info(f"GUI Backend startup complete. {len(app_state.redis_stream_consumers)} legacy consumers active (if any). XREAD task: {app_state.xread_consumer_task is not None}")
    logger.info("🌐 GUI Backend ready for connections at http://localhost:8001 (FastAPI default) or your configured host/port.")

    yield  # This is where the application runs

    # Shutdown logic
    logger.info("🔄 Shutting down TESTRADE GUI Backend (lifespan)...")
    if app_state.xread_consumer_task and not app_state.xread_consumer_task.done():
        logger.info("Cancelling XREAD consumer task...")
        app_state.xread_consumer_task.cancel()
        try:
            await asyncio.wait_for(app_state.xread_consumer_task, timeout=5.0)
            logger.info("XREAD consumer task finished.")
        except asyncio.TimeoutError:
            logger.warning("XREAD consumer task did not finish in time during shutdown.")
        except asyncio.CancelledError:
            logger.info("XREAD consumer task was cancelled as expected.")

    # Stop legacy consumers if they were started
    if not app_state.config.FEATURE_FLAG_GUI_USE_XREAD_AND_BOOTSTRAP_API:
        active_consumer_names = list(app_state.redis_stream_consumers.keys())
        logger.info(f"Cleaning up {len(active_consumer_names)} legacy Redis consumers...")
        for name in active_consumer_names:
            consumer = app_state.redis_stream_consumers.get(name)
            if consumer:
                try:
                    await cleanup_redis_consumer(consumer, name)
                    consumer.stop_consuming()
                except Exception as e:
                    logger.error(f"Error stopping legacy consumer {name}: {e}")

    # Skip IPC client cleanup - not using IPC client in Redis-only mode
    logger.info("Skipping BabysitterIPCClient cleanup - using Redis-only communication")

    if app_state.websocket_manager:
        logger.info(f"Closing {len(app_state.websocket_manager.active_connections)} WebSocket connections...")
        for connection in app_state.websocket_manager.active_connections[:]:
            try:
                await connection.close(code=1000)
            except Exception as e_ws_close:
                logger.warning(f"Error closing WebSocket {connection.client.host if connection.client else 'unknown'}: {e_ws_close}")
        app_state.websocket_manager.active_connections.clear()
        logger.info("All WebSocket connections closed.")

    logger.info("[SUCCESS] TESTRADE GUI Backend shutdown complete.")

# Assign the lifespan manager to the app
app.router.lifespan_context = lifespan

if __name__ == "__main__":
    # For development, reload=True is fine. For production, consider reload=False.
    uvicorn.run("gui_backend:app", host="0.0.0.0", port=8001, reload=False, log_level="info")