# GUI Command System Implementation

## Overview

This document describes the implementation of the GUI command system for TESTRADE, which allows the GUI to send commands to the ApplicationCore through Redis streams, enabling real-time ROI control and other OCR process management.

## Architecture

The system uses a Redis-based command/response pattern with the following components:

1. **GUI Command Stream**: `testrade:gui-commands` - Commands from GUI to ApplicationCore
2. **Command Response Stream**: `testrade:gui-command-responses` - Responses from ApplicationCore to GUI
3. **OCR Command Pipe**: Direct pipe communication from ApplicationCore to OCR process

## Implementation Details

### ApplicationCore Changes

#### 1. Command Infrastructure Setup
- Added `_setup_gui_command_infrastructure()` method to initialize Redis consumers and publishers
- Created command response queue and publisher for sending responses back to GUI
- Setup GUI command consumer using `RedisStreamConsumerBase`

#### 2. Command Processing
- Added `_handle_gui_command()` method to process incoming commands
- Supports the following command types:
  - `SET_ROI_ABSOLUTE`: Updates OCR region of interest
  - `START_OCR`: Starts the OCR process
  - `STOP_OCR`: Stops the OCR process

#### 3. Command Response Publishing
- Added `_publish_command_response()` method to send standardized responses
- Responses include status (success/error/info), message, and optional data

#### 4. OCR Process Communication
- Added command pipe creation in `_initialize_ocr_process_management()`
- Modified OCR process arguments to include command pipe
- Added `_start_gui_command_consumer()` to start the command infrastructure

### OCR Process Changes

#### 1. Function Signature Update
Updated `run_ocr_service_process()` to accept command pipe:
```python
def run_ocr_service_process(child_pipe_conn: Connection,
                           command_pipe: Connection,
                           ocr_config_dict_from_parent: Dict[str, Any],
                           log_base_dir_from_parent: str):
```

#### 2. Command Listening Loop
Added command processing in the main OCR process loop:
```python
if command_pipe and command_pipe.poll():
    try:
        message = command_pipe.recv()
        command = message.get("command")
        data = message.get("data")
        
        if command == "SET_ROI" and isinstance(data, (list, tuple)) and len(data) == 4:
            ocr_service_instance.set_roi(*data)
            logger.info(f"OCR Child: ROI updated to {data}")
    except Exception as e:
        logger.error(f"OCR Child: Error processing command: {e}", exc_info=True)
```

### Configuration Changes

Added new Redis stream configuration in `utils/global_config.py`:
```python
redis_stream_gui_command_responses: str = "testrade:gui-command-responses"
```

## Command Message Format

### GUI Command Message
```json
{
  "metadata": {
    "eventId": "evt_12345678",
    "correlationId": "cmd_12345678",
    "causationId": null,
    "timestamp_ns": 1234567890123456789,
    "eventType": "GUI_COMMAND",
    "sourceComponent": "GUI"
  },
  "payload": {
    "command_type": "SET_ROI_ABSOLUTE",
    "command_id": "cmd_12345678",
    "parameters": {
      "x1": 100,
      "y1": 200,
      "x2": 800,
      "y2": 600
    },
    "timestamp": 1234567890.123
  }
}
```

### Command Response Message
```json
{
  "metadata": {
    "eventId": "evt_87654321",
    "correlationId": "cmd_12345678",
    "causationId": null,
    "timestamp_ns": 1234567890123456789,
    "eventType": "TESTRADE_GUI_COMMAND_RESPONSE",
    "sourceComponent": "ApplicationCore"
  },
  "payload": {
    "original_command_id": "cmd_12345678",
    "command_type": "SET_ROI_ABSOLUTE",
    "status": "success",
    "message": "ROI updated to (100, 200, 800, 600) and saved.",
    "response_timestamp": 1234567890.123,
    "data": {}
  }
}
```

## Supported Commands

### SET_ROI_ABSOLUTE
Updates the OCR region of interest coordinates.

**Parameters:**
- `x1`: Left coordinate
- `y1`: Top coordinate  
- `x2`: Right coordinate
- `y2`: Bottom coordinate

**Response:** Success/error status with confirmation message

### START_OCR
Starts the OCR process if not already running.

**Parameters:** None

**Response:** Success/info status

### STOP_OCR
Stops the OCR process if running.

**Parameters:** None

**Response:** Success/info status

## Testing

A test script is provided at `intellisense/tests/test_gui_command_system.py` that demonstrates:
1. Sending ROI update commands
2. Sending OCR start commands
3. Waiting for and parsing responses

## Usage Example

```python
import redis
import json
import uuid
import time

# Connect to Redis
redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)

# Create ROI command
command_message = {
    "metadata": {
        "eventId": f"evt_{uuid.uuid4().hex[:8]}",
        "correlationId": "my_command_id",
        "eventType": "GUI_COMMAND",
        "sourceComponent": "GUI"
    },
    "payload": {
        "command_type": "SET_ROI_ABSOLUTE",
        "command_id": "my_command_id",
        "parameters": {"x1": 100, "y1": 200, "x2": 800, "y2": 600}
    }
}

# Send command
redis_client.xadd("testrade:gui-commands", {"json_payload": json.dumps(command_message)})

# Listen for response on testrade:gui-command-responses
```

## Benefits

1. **Real-time Control**: GUI can update OCR settings without restarting the system
2. **Decoupled Architecture**: GUI and core systems communicate through Redis
3. **Persistent Configuration**: ROI changes are saved to configuration file
4. **Error Handling**: Comprehensive error reporting through response messages
5. **Extensible**: Easy to add new command types

## Future Enhancements

- Add more OCR parameter controls (preprocessing settings, confidence thresholds)
- Implement batch command processing
- Add command queuing and prioritization
- Support for asynchronous command execution with progress updates
