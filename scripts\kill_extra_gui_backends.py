#!/usr/bin/env python3
"""
Kill extra GUI backend processes to fix latency issues
"""

import psutil
import sys
import time

def find_gui_backend_processes():
    """Find all GUI backend processes"""
    gui_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'memory_info']):
        try:
            cmdline = ' '.join(proc.info['cmdline'] or [])
            
            # Look for GUI backend processes
            if ('gui_backend.py' in cmdline or 
                ('python' in proc.info['name'].lower() and 'gui' in cmdline.lower() and 'backend' in cmdline.lower())):
                
                gui_processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'cmdline': cmdline,
                    'create_time': proc.info['create_time'],
                    'memory_mb': proc.info['memory_info'].rss / 1024 / 1024,
                    'process': proc
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # Sort by creation time (oldest first)
    gui_processes.sort(key=lambda x: x['create_time'])
    return gui_processes

def main():
    print("🔍 TESTRADE GUI Backend Process Cleanup")
    print("=" * 50)
    
    gui_processes = find_gui_backend_processes()
    
    if not gui_processes:
        print("✅ No GUI backend processes found")
        return
    
    print(f"Found {len(gui_processes)} GUI backend processes:")
    print()
    
    for i, proc in enumerate(gui_processes):
        create_time = time.strftime('%H:%M:%S', time.localtime(proc['create_time']))
        print(f"{i+1}. PID {proc['pid']} - Started: {create_time} - Memory: {proc['memory_mb']:.1f}MB")
        print(f"   Command: {proc['cmdline'][:80]}...")
        print()
    
    if len(gui_processes) == 1:
        print("✅ Only one GUI backend process found - this is correct!")
        return
    
    print(f"⚠️  Multiple GUI backend processes detected!")
    print(f"   This causes resource contention and high latency.")
    print(f"   Recommended: Keep the newest process, kill the others.")
    print()
    
    # Keep the newest (last) process, kill the others
    processes_to_kill = gui_processes[:-1]  # All except the last one
    keep_process = gui_processes[-1]
    
    print(f"📋 Plan:")
    print(f"   ✅ KEEP: PID {keep_process['pid']} (newest)")
    print(f"   ❌ KILL: {len(processes_to_kill)} older processes")
    print()
    
    for proc in processes_to_kill:
        create_time = time.strftime('%H:%M:%S', time.localtime(proc['create_time']))
        print(f"   - PID {proc['pid']} (started {create_time})")
    
    print()
    response = input("Proceed with cleanup? (yes/no): ").lower().strip()
    
    if response in ['yes', 'y']:
        print()
        print("🧹 Cleaning up extra GUI backend processes...")
        
        killed_count = 0
        for proc in processes_to_kill:
            try:
                print(f"   Killing PID {proc['pid']}...")
                proc['process'].terminate()
                
                # Wait for graceful termination
                try:
                    proc['process'].wait(timeout=5)
                    print(f"   ✅ PID {proc['pid']} terminated gracefully")
                except psutil.TimeoutExpired:
                    print(f"   ⚠️  PID {proc['pid']} didn't terminate, force killing...")
                    proc['process'].kill()
                    print(f"   ✅ PID {proc['pid']} force killed")
                
                killed_count += 1
                
            except psutil.NoSuchProcess:
                print(f"   ✅ PID {proc['pid']} already terminated")
                killed_count += 1
            except Exception as e:
                print(f"   ❌ Failed to kill PID {proc['pid']}: {e}")
        
        print()
        print(f"🎉 Cleanup complete!")
        print(f"   Killed: {killed_count} processes")
        print(f"   Remaining: 1 GUI backend process (PID {keep_process['pid']})")
        print()
        print("💡 Your latency should now be much better!")
        print("   Expected improvement: 2000ms → 50-100ms")
        
    else:
        print("❌ Cleanup cancelled")
        print()
        print("💡 To fix latency manually:")
        print("   1. Stop all GUI backend processes")
        print("   2. Start only one: python gui/gui_backend.py")

if __name__ == "__main__":
    main()
