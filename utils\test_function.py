def test_function(app_core=None):
    """
    Debug Position Manager state - shows what positions PM thinks you have.

    :param app_core: ApplicationCore instance (if available)
    """
    print(f"🔍 [TEST] => Position Manager Debug Started...")

    # Try to get Position Manager from app_core
    position_manager = None
    if app_core and hasattr(app_core, 'position_manager'):
        position_manager = app_core.position_manager
        print(f"🔍 [TEST] => Found Position Manager via app_core")
    else:
        print(f"🔍 [TEST] => No app_core or position_manager available")
        print(f"🔍 [TEST] => app_core: {app_core}")
        if app_core:
            print(f"🔍 [TEST] => app_core attributes: {dir(app_core)}")
        return

    if not position_manager:
        print(f"🔍 [TEST] => Position Manager is None - cannot debug")
        return

    # Debug Position Manager state
    try:
        with position_manager._positions_lock:
            total_positions = len(position_manager._positions)
            print(f"🔍 [TEST] => Position Manager has {total_positions} positions tracked")

            if total_positions == 0:
                print(f"🔍 [TEST] => No positions found in Position Manager")
            else:
                for symbol, pos in position_manager._positions.items():
                    shares = pos.get('total_shares', 0)
                    avg_price = pos.get('total_avg_price', 0)
                    realized_pnl = pos.get('realized_pnl_session', 0)
                    print(f"🔍 [TEST] => {symbol}: {shares:.0f} shares @ ${avg_price:.4f}, PnL: ${realized_pnl:.2f}")

    except Exception as e:
        print(f"🔍 [TEST] => Error accessing Position Manager: {e}")

    print(f"🔍 [TEST] => Position Manager Debug Complete")
