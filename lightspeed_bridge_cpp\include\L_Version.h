#pragma once
#if !defined(LS_L_VERSION_H)
#define LS_L_VERSION_H

// Copyright (c) 2001-2018 Lightspeed Financial, Inc. All rights reserved.

#define LS_API_VERSION_1 1
#define LS_API_VERSION_2 6
#define LS_API_VERSION_3 0
#define LS_API_VERSION_4 0

typedef long long unsigned int ls_unsignedlonglong;
#define L_MAKEVERSION(C1, C2, C3, C4) ( ls_unsignedlonglong(C4) | (ls_unsignedlonglong(C3) << 16) | (ls_unsignedlonglong(C2) << 32) | (ls_unsignedlonglong(C1) << 48) )

#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
#if !defined(LS_EXCLUDE_INLINE_EXPORT)
extern "C" __declspec(dllexport) inline unsigned long long LSGetAPIVersion()
{
	return L_MAKEVERSION(LS_API_VERSION_1, LS_API_VERSION_2, LS_API_VERSION_3, LS_API_VERSION_4);
}
#endif
#endif


#endif // !defined(LS_L_VERSION_H)

