#!/usr/bin/env python3
"""
TESTRADE Clean Startup Script
=============================
Starts TESTRADE services in the correct order with proper cleanup.
"""

import subprocess
import time
import sys
import psutil
import signal
import os
from pathlib import Path

def kill_processes_on_ports(ports):
    """Kill any processes using the specified ports"""
    print(f"🧹 Cleaning up processes on ports: {ports}")
    
    for port in ports:
        try:
            # Find processes using the port
            result = subprocess.run(
                f'netstat -ano | findstr :{port}',
                shell=True, capture_output=True, text=True
            )
            
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                pids = set()
                
                for line in lines:
                    parts = line.split()
                    if len(parts) >= 5 and 'LISTENING' in line:
                        pid = parts[-1]
                        if pid.isdigit():
                            pids.add(int(pid))
                
                for pid in pids:
                    try:
                        process = psutil.Process(pid)
                        print(f"  🔪 Killing process {pid} ({process.name()}) on port {port}")
                        process.terminate()
                        time.sleep(1)
                        if process.is_running():
                            process.kill()
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                        
        except Exception as e:
            print(f"  ⚠️ Error cleaning port {port}: {e}")

def start_service(name, command, wait_time=3):
    """Start a service and wait for it to initialize"""
    print(f"🚀 Starting {name}...")
    
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            cwd=Path.cwd(),
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print(f"  ✅ {name} started (PID: {process.pid})")
        print(f"  ⏳ Waiting {wait_time}s for initialization...")
        time.sleep(wait_time)
        
        return process
        
    except Exception as e:
        print(f"  ❌ Failed to start {name}: {e}")
        return None

def main():
    print("=" * 60)
    print("🎯 TESTRADE Clean Startup")
    print("=" * 60)
    
    # Define the ports we need
    testrade_ports = [5555, 5556, 5557, 5560, 5559]
    
    # Step 1: Clean up any existing processes
    kill_processes_on_ports(testrade_ports)
    time.sleep(2)
    
    # Step 2: Start BabysitterService first
    babysitter_process = start_service(
        "BabysitterService",
        ".venv\\Scripts\\python.exe core\\babysitter_service.py",
        wait_time=5
    )
    
    if not babysitter_process:
        print("❌ Failed to start BabysitterService. Exiting.")
        return 1
    
    # Step 3: Start ApplicationCore
    appcore_process = start_service(
        "ApplicationCore", 
        ".venv\\Scripts\\python.exe run_headless_core.py",
        wait_time=10
    )
    
    if not appcore_process:
        print("❌ Failed to start ApplicationCore. Cleaning up...")
        babysitter_process.terminate()
        return 1
    
    print("\n" + "=" * 60)
    print("🎉 TESTRADE Services Started Successfully!")
    print("=" * 60)
    print(f"📊 BabysitterService PID: {babysitter_process.pid}")
    print(f"🧠 ApplicationCore PID: {appcore_process.pid}")
    print("\n🔧 Next steps:")
    print("  1. Run: python inject_position_simple.py --symbol LDTC --qty 1000 --avg_px 0.22")
    print("  2. Start GUI backend: python gui\\gui_backend.py")
    print("  3. Open GUI: file:///C:/TESTRADE/gui/portrait_trading_gui.html")
    print("\n⏹️  Press Ctrl+C to stop all services")
    
    try:
        # Keep the script running and monitor processes
        while True:
            time.sleep(5)
            
            # Check if processes are still running
            try:
                if not psutil.Process(babysitter_process.pid).is_running():
                    print("⚠️ BabysitterService stopped unexpectedly!")
                    break
                if not psutil.Process(appcore_process.pid).is_running():
                    print("⚠️ ApplicationCore stopped unexpectedly!")
                    break
            except psutil.NoSuchProcess:
                print("⚠️ One or more services stopped unexpectedly!")
                break
                
    except KeyboardInterrupt:
        print("\n🛑 Shutdown requested...")
        
        # Graceful shutdown
        print("  🔄 Stopping ApplicationCore...")
        try:
            appcore_process.terminate()
            appcore_process.wait(timeout=10)
        except:
            appcore_process.kill()
            
        print("  🔄 Stopping BabysitterService...")
        try:
            babysitter_process.terminate()
            babysitter_process.wait(timeout=10)
        except:
            babysitter_process.kill()
            
        print("✅ All services stopped.")
        
    return 0

if __name__ == "__main__":
    sys.exit(main())
