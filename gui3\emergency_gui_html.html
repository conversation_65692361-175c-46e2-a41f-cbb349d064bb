<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TANK Emergency Control</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0a0a0a, #1a1a1a);
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            line-height: 1.4;
            overflow-x: auto;
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
            min-height: 100vh;
        }

        .panel {
            background: rgba(42, 42, 42, 0.8);
            border: 2px solid #333;
            border-radius: 10px;
            padding: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 255, 0, 0.1);
        }

        .panel h2 {
            color: #00ffff;
            margin-bottom: 15px;
            font-size: 20px;
            text-transform: uppercase;
            border-bottom: 1px solid #444;
            padding-bottom: 10px;
        }

        .header {
            grid-column: 1 / -1;
            text-align: center;
            background: rgba(0, 255, 0, 0.1);
            border-color: #00ff00;
        }

        .header h1 {
            font-size: 32px;
            color: #00ff00;
            text-shadow: 0 0 20px #00ff00;
            margin-bottom: 10px;
        }

        .status-display {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            box-shadow: 0 0 10px currentColor;
        }

        .healthy { color: #00ff00; background: #00ff00; }
        .degraded { color: #ffaa00; background: #ffaa00; }
        .critical { color: #ff0000; background: #ff0000; }
        .unknown { color: #888888; background: #888888; }

        .big-status {
            font-size: 28px;
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            text-shadow: 0 0 15px currentColor;
            text-transform: uppercase;
            font-weight: bold;
        }

        .progress-container {
            margin: 15px 0;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #555;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00aa00, #00ff00);
            transition: width 0.5s ease, background 0.3s ease;
            border-radius: 12px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            color: #000;
        }

        .progress-fill.warning {
            background: linear-gradient(90deg, #ff8800, #ffaa00);
        }

        .progress-fill.critical {
            background: linear-gradient(90deg, #cc0000, #ff0000);
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .emergency-btn {
            background: linear-gradient(135deg, #cc0000, #ff0000);
            color: white;
            border: none;
            padding: 15px 25px;
            font-size: 16px;
            font-weight: bold;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            text-transform: uppercase;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 0, 0, 0.3);
        }

        .emergency-btn:hover {
            background: linear-gradient(135deg, #ff0000, #ff3333);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 0, 0, 0.5);
        }

        .emergency-btn:active {
            transform: translateY(0);
        }

        .control-btn {
            background: linear-gradient(135deg, #006600, #00aa00);
            color: white;
            border: none;
            padding: 12px 20px;
            font-size: 14px;
            font-weight: bold;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: linear-gradient(135deg, #00aa00, #00dd00);
            transform: translateY(-1px);
        }

        .metric-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
        }

        .metric-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #00ff00;
        }

        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: #00ffff;
        }

        .metric-label {
            font-size: 12px;
            color: #aaa;
            text-transform: uppercase;
        }

        .alert-banner {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #ff0000;
            color: white;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            z-index: 1000;
            display: none;
            animation: flashAlert 1s infinite;
            box-shadow: 0 4px 20px rgba(255, 0, 0, 0.7);
        }

        @keyframes flashAlert {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        .connection-status {
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: bold;
        }

        .connected {
            background: #00aa00;
            color: white;
        }

        .disconnected {
            background: #cc0000;
            color: white;
            animation: pulse 1s infinite;
        }

        .timestamp {
            color: #888;
            font-size: 12px;
            text-align: center;
            margin-top: 10px;
        }

        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connection-status">CONNECTING...</div>
    <div class="alert-banner" id="alert-banner"></div>

    <div class="container">
        <!-- Header -->
        <div class="panel header">
            <h1>🛡️ TANK EMERGENCY CONTROL</h1>
            <div class="timestamp" id="last-update">Connecting to TANK...</div>
        </div>

        <!-- TANK Core Status -->
        <div class="panel">
            <h2>🚀 TANK Core Status</h2>
            <div class="big-status healthy" id="tank-status">●OPERATIONAL●</div>
            
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-value" id="uptime">0h 0m</div>
                    <div class="metric-label">Uptime</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="ocr-status">UNKNOWN</div>
                    <div class="metric-label">OCR Status</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="position-count">0</div>
                    <div class="metric-label">Open Positions</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="total-pnl">$0.00</div>
                    <div class="metric-label">Total P&L</div>
                </div>
            </div>
        </div>

        <!-- IPC Channel Status -->
        <div class="panel">
            <h2>📡 IPC Channel Health</h2>
            
            <div class="status-display">
                <div class="status-indicator healthy" id="trading-indicator"></div>
                <span>Trading Channel: <strong id="trading-status">HEALTHY</strong></span>
            </div>
            
            <div class="status-display">
                <div class="status-indicator healthy" id="system-indicator"></div>
                <span>System Channel: <strong id="system-status">HEALTHY</strong></span>
            </div>
            
            <div class="status-display">
                <div class="status-indicator healthy" id="bulk-indicator"></div>
                <span>Bulk Channel: <strong id="bulk-status">HEALTHY</strong></span>
            </div>

            <div class="metric-grid" style="margin-top: 20px;">
                <div class="metric-item">
                    <div class="metric-value" id="total-messages">0</div>
                    <div class="metric-label">Total Messages</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value" id="total-discards">0</div>
                    <div class="metric-label">Total Discards</div>
                </div>
            </div>
        </div>

        <!-- Buffer Levels -->
        <div class="panel">
            <h2>💾 Buffer Levels</h2>
            
            <div class="progress-container">
                <div class="progress-label">
                    <span>Trading Buffer</span>
                    <span id="trading-percent">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="trading-buffer" style="width: 0%;">0%</div>
                </div>
            </div>
            
            <div class="progress-container">
                <div class="progress-label">
                    <span>System Buffer</span>
                    <span id="system-percent">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="system-buffer" style="width: 0%;">0%</div>
                </div>
            </div>
            
            <div class="progress-container">
                <div class="progress-label">
                    <span>Bulk Buffer</span>
                    <span id="bulk-percent">0%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="bulk-buffer" style="width: 0%;">0%</div>
                </div>
            </div>
        </div>

        <!-- Emergency Actions -->
        <div class="panel">
            <h2>🚨 Emergency Actions</h2>
            
            <div style="text-align: center; margin: 20px 0;">
                <button class="emergency-btn" onclick="forceCloseAll()">
                    🛑 FORCE CLOSE ALL POSITIONS
                </button>
            </div>
            
            <div style="text-align: center;">
                <button class="control-btn" onclick="stopOCR()">📷 Stop OCR</button>
                <button class="control-btn" onclick="startOCR()">▶️ Start OCR</button>
                <button class="control-btn" onclick="refreshStatus()">🔄 Refresh</button>
            </div>

            <div style="margin-top: 20px; padding: 15px; background: rgba(0,0,0,0.3); border-radius: 5px;">
                <h3 style="color: #ffaa00; margin-bottom: 10px;">ROI Controls</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                    <input type="number" id="roi-x1" placeholder="X1" style="padding: 8px; background: #333; color: #fff; border: 1px solid #555; border-radius: 4px;">
                    <input type="number" id="roi-y1" placeholder="Y1" style="padding: 8px; background: #333; color: #fff; border: 1px solid #555; border-radius: 4px;">
                    <input type="number" id="roi-x2" placeholder="X2" style="padding: 8px; background: #333; color: #fff; border: 1px solid #555; border-radius: 4px;">
                    <input type="number" id="roi-y2" placeholder="Y2" style="padding: 8px; background: #333; color: #fff; border: 1px solid #555; border-radius: 4px;">
                </div>
                <button class="control-btn" onclick="setROI()" style="width: 100%;">Set ROI Coordinates</button>
            </div>
        </div>
    </div>

    <script>
        // WebSocket connection to Emergency GUI server
        const socket = io('http://localhost:8766');
        let lastUpdateTime = 0;

        // Connection status handling
        socket.on('connect', function() {
            console.log('Connected to Emergency GUI server');
            updateConnectionStatus(true);
            socket.emit('request_status'); // Request initial status
        });

        socket.on('disconnect', function() {
            console.log('Disconnected from Emergency GUI server');
            updateConnectionStatus(false);
        });

        socket.on('error', function(error) {
            console.error('Socket error:', error);
            showAlert('Connection Error: ' + error.message, 'error');
        });

        // Real-time status updates
        socket.on('status_update', function(data) {
            updateDisplay(data);
            lastUpdateTime = Date.now();
        });

        // Critical alerts
        socket.on('critical_alert', function(alert) {
            showCriticalAlert(alert);
            playAlertSound();
        });

        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connection-status');
            if (connected) {
                statusEl.textContent = 'CONNECTED';
                statusEl.className = 'connection-status connected';
            } else {
                statusEl.textContent = 'DISCONNECTED';
                statusEl.className = 'connection-status disconnected';
            }
        }

        function updateDisplay(data) {
            try {
                // Update timestamp
                const timestamp = new Date(data.timestamp * 1000).toLocaleTimeString();
                document.getElementById('last-update').textContent = `Last update: ${timestamp}`;

                // Update TANK core status
                updateTankStatus(data);

                // Update IPC channel status
                if (data.ipc_client_stats && data.ipc_client_stats.channels) {
                    updateChannelStatus(data.ipc_client_stats);
                }

                // Update position summary
                if (data.position_summary) {
                    updatePositionSummary(data.position_summary);
                }

                // Update ROI display
                if (data.current_roi) {
                    updateROIDisplay(data.current_roi);
                }

            } catch (error) {
                console.error('Error updating display:', error);
            }
        }

        function updateTankStatus(data) {
            const statusEl = document.getElementById('tank-status');
            const status = data.tank_core_status || 'UNKNOWN';
            
            statusEl.textContent = `●${status}●`;
            statusEl.className = 'big-status ' + status.toLowerCase();

            // Update uptime
            if (data.tank_uptime_sec) {
                const hours = Math.floor(data.tank_uptime_sec / 3600);
                const minutes = Math.floor((data.tank_uptime_sec % 3600) / 60);
                document.getElementById('uptime').textContent = `${hours}h ${minutes}m`;
            }

            // Update OCR status
            document.getElementById('ocr-status').textContent = data.ocr_status || 'UNKNOWN';
        }

        function updateChannelStatus(ipcStats) {
            const channels = ['trading', 'system', 'bulk'];
            
            channels.forEach(channel => {
                const channelData = ipcStats.channels[channel];
                if (!channelData) return;

                const statusEl = document.getElementById(`${channel}-status`);
                const indicatorEl = document.getElementById(`${channel}-indicator`);
                
                let status = 'HEALTHY';
                let className = 'healthy';
                
                if (channelData.is_clogged) {
                    status = 'CLOGGED';
                    className = 'critical';
                } else if (channelData.channel_health_state !== 'HEALTHY') {
                    status = channelData.channel_health_state;
                    className = 'degraded';
                }
                
                statusEl.textContent = status;
                statusEl.className = className;
                indicatorEl.className = `status-indicator ${className}`;

                // Update buffer levels
                updateBufferLevel(channel, channelData.mmap_fill_percent_approx || 0);
            });

            // Update aggregate stats
            document.getElementById('total-messages').textContent = 
                ipcStats.current_total_mmap_messages || 0;
            
            const totalDiscards = (ipcStats.session_discards_new_clogged || 0) + 
                                (ipcStats.session_discards_grad_defense || 0) + 
                                (ipcStats.session_discards_worker_iq_full || 0);
            document.getElementById('total-discards').textContent = totalDiscards;
        }

        function updateBufferLevel(channel, percentage) {
            const fillEl = document.getElementById(`${channel}-buffer`);
            const percentEl = document.getElementById(`${channel}-percent`);
            
            const clampedPercent = Math.max(0, Math.min(100, percentage));
            
            fillEl.style.width = clampedPercent + '%';
            fillEl.textContent = clampedPercent.toFixed(1) + '%';
            percentEl.textContent = clampedPercent.toFixed(1) + '%';
            
            // Color coding based on fill level
            fillEl.className = 'progress-fill';
            if (clampedPercent > 90) {
                fillEl.classList.add('critical');
            } else if (clampedPercent > 75) {
                fillEl.classList.add('warning');
            }
        }

        function updatePositionSummary(summary) {
            document.getElementById('position-count').textContent = summary.total_open_positions || 0;
            
            const pnl = summary.total_pnl_open || 0;
            const pnlEl = document.getElementById('total-pnl');
            pnlEl.textContent = '$' + pnl.toFixed(2);
            pnlEl.style.color = pnl >= 0 ? '#00ff00' : '#ff0000';
        }

        function updateROIDisplay(roi) {
            if (roi && roi.length >= 4) {
                document.getElementById('roi-x1').value = roi[0];
                document.getElementById('roi-y1').value = roi[1];
                document.getElementById('roi-x2').value = roi[2];
                document.getElementById('roi-y2').value = roi[3];
            }
        }

        function showCriticalAlert(alert) {
            const alertEl = document.getElementById('alert-banner');
            const message = `🚨 ${alert.type}: ${alert.channel || ''} - ${alert.timestamp}`;
            
            alertEl.textContent = message;
            alertEl.style.display = 'block';
            
            // Hide alert after 10 seconds
            setTimeout(() => {
                alertEl.style.display = 'none';
            }, 10000);
        }

        function showAlert(message, type = 'info') {
            console.log(`Alert (${type}): ${message}`);
            // Simple alert implementation
            const alertEl = document.getElementById('alert-banner');
            alertEl.textContent = message;
            alertEl.style.display = 'block';

            setTimeout(() => {
                alertEl.style.display = 'none';
            }, 5000);
        }

        function playAlertSound() {
            // Simple beep sound for alerts
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.value = 800;
                oscillator.type = 'sine';
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);

                oscillator.start();
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (e) {
                console.log('Audio not available');
            }
        }

        // Emergency action functions
        function forceCloseAll() {
            if (confirm('⚠️ FORCE CLOSE ALL POSITIONS?\n\nThis will immediately close all open positions. This action cannot be undone.')) {
                sendEmergencyCommand('FORCE_CLOSE_ALL_POSITIONS', { reason: 'Emergency GUI Force Close All' });
            }
        }

        function stopOCR() {
            sendEmergencyCommand('STOP_OCR_PROCESS', { reason: 'Emergency GUI Stop OCR' });
        }

        function startOCR() {
            sendEmergencyCommand('START_OCR_PROCESS', { reason: 'Emergency GUI Start OCR' });
        }

        function refreshStatus() {
            socket.emit('request_status');
        }

        function setROI() {
            const x1 = parseInt(document.getElementById('roi-x1').value) || 0;
            const y1 = parseInt(document.getElementById('roi-y1').value) || 0;
            const x2 = parseInt(document.getElementById('roi-x2').value) || 0;
            const y2 = parseInt(document.getElementById('roi-y2').value) || 0;

            if (x1 >= x2 || y1 >= y2) {
                alert('Invalid ROI coordinates. X2 must be > X1 and Y2 must be > Y1');
                return;
            }

            sendEmergencyCommand('SET_ROI_ABSOLUTE', {
                coordinates: [x1, y1, x2, y2],
                reason: 'Emergency GUI ROI Update'
            });
        }

        function sendEmergencyCommand(command, params = {}) {
            fetch('/send-command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    command: command,
                    params: params
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(`✅ ${command} command sent successfully`, 'success');
                } else {
                    showAlert(`❌ ${command} command failed: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showAlert(`❌ Network error sending ${command}: ${error.message}`, 'error');
            });
        }

        // Auto-refresh status every 5 seconds
        setInterval(() => {
            if (socket.connected) {
                socket.emit('request_status');
            }
        }, 5000);

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Emergency GUI loaded');
            updateConnectionStatus(false);
        });
    </script>
</body>
</html>