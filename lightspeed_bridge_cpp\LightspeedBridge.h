﻿// LightspeedBridge.h : main header file for the LightspeedBridge DLL
//

#pragma once

#ifndef __AFXWIN_H__
	#error "include 'pch.h' before including this file for PCH"
#endif

#include "resource.h"		// main symbols


// CLightspeedBridgeApp
// See LightspeedBridge.cpp for the implementation of this class
//

class CLightspeedBridgeApp : public CWinApp
{
public:
	CLightspeedBridgeApp();

// Overrides
public:
	virtual BOOL InitInstance();

	DECLARE_MESSAGE_MAP()
};
