import psutil
import time
import os
import datetime
import csv
import sys

def find_process_by_name(name):
    """Find a process by name."""
    matching_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if name.lower() in proc.info['name'].lower():
                # Get command line to help identify the right process
                cmdline = " ".join(proc.cmdline()) if proc.cmdline() else ""
                print(f"Found process: PID={proc.pid}, Name={proc.info['name']}, Cmdline={cmdline}")
                matching_processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass

    if not matching_processes:
        return None

    # If multiple matches, let user choose
    if len(matching_processes) > 1:
        print("\nMultiple matching processes found. Please select one by number:")
        for i, proc in enumerate(matching_processes):
            cmdline = " ".join(proc.cmdline()) if proc.cmdline() else ""
            print(f"{i+1}. PID={proc.pid}, Name={proc.info['name']}, Cmdline={cmdline}")

        try:
            choice = int(input("Enter number: ")) - 1
            if 0 <= choice < len(matching_processes):
                return matching_processes[choice]
        except (ValueError, IndexError):
            print("Invalid selection. Using first process.")

        # Default to first process if selection fails
        return matching_processes[0]

    return matching_processes[0]

def get_process_info(process):
    """Get CPU, memory, and network usage for a process."""
    if not process or not process.is_running():
        return None

    try:
        # Get CPU usage (%)
        cpu_percent = process.cpu_percent(interval=None)

        # Get memory usage (MB)
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / (1024 * 1024)

        # Get thread count
        thread_count = len(process.threads())

        # Get network I/O counters (system-wide)
        net_io = psutil.net_io_counters()
        bytes_sent = net_io.bytes_sent
        bytes_recv = net_io.bytes_recv

        return {
            'timestamp': time.time(),
            'cpu_percent': cpu_percent,
            'memory_mb': memory_mb,
            'thread_count': thread_count,
            'bytes_sent': bytes_sent,
            'bytes_recv': bytes_recv
        }
    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
        return None

def monitor_process(process_name, duration_seconds=60, interval_seconds=1):
    """Monitor a process for a specified duration."""
    # Find the process
    process = find_process_by_name(process_name)
    if not process:
        print(f"Process '{process_name}' not found.")
        return []

    print(f"Monitoring process '{process_name}' (PID: {process.pid}) for {duration_seconds} seconds...")

    # Prime the CPU percent measurement
    process.cpu_percent(interval=0.1)
    time.sleep(0.1)

    # Initialize variables for network rate calculation
    last_bytes_sent = 0
    last_bytes_recv = 0
    last_timestamp = time.time()

    # Collect data
    data = []
    start_time = time.time()

    while time.time() - start_time < duration_seconds:
        info = get_process_info(process)
        if info:
            # Calculate network rates
            current_timestamp = info['timestamp']
            time_diff = current_timestamp - last_timestamp

            if last_bytes_sent > 0 and time_diff > 0:
                bytes_sent_rate = (info['bytes_sent'] - last_bytes_sent) / time_diff
                bytes_recv_rate = (info['bytes_recv'] - last_bytes_recv) / time_diff
                info['bytes_sent_rate'] = bytes_sent_rate
                info['bytes_recv_rate'] = bytes_recv_rate
            else:
                info['bytes_sent_rate'] = 0
                info['bytes_recv_rate'] = 0

            # Update last values
            last_bytes_sent = info['bytes_sent']
            last_bytes_recv = info['bytes_recv']
            last_timestamp = current_timestamp

            data.append(info)

            # Print current stats
            print(f"Time: {time.strftime('%H:%M:%S')} | "
                  f"CPU: {info['cpu_percent']:.2f}% | "
                  f"Memory: {info['memory_mb']:.2f} MB | "
                  f"Threads: {info['thread_count']} | "
                  f"Network Send: {info['bytes_sent_rate']/1024:.2f} KB/s | "
                  f"Network Recv: {info['bytes_recv_rate']/1024:.2f} KB/s")

        time.sleep(interval_seconds)

    return data

def save_to_csv(data, filename):
    """Save monitoring data to a CSV file."""
    if not data:
        print("No data to save.")
        return

    with open(filename, 'w', newline='') as csvfile:
        fieldnames = ['timestamp', 'cpu_percent', 'memory_mb', 'thread_count',
                     'bytes_sent', 'bytes_recv', 'bytes_sent_rate', 'bytes_recv_rate']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for row in data:
            writer.writerow(row)

    print(f"Data saved to {filename}")

def monitor_process_by_pid(pid, duration_seconds=60, interval_seconds=1):
    """Monitor a process by PID for a specified duration."""
    try:
        # Get the process by PID
        process = psutil.Process(pid)
        print(f"Monitoring process PID: {pid} ({process.name()}) for {duration_seconds} seconds...")

        # Prime the CPU percent measurement
        process.cpu_percent(interval=0.1)
        time.sleep(0.1)

        # Initialize variables for network rate calculation
        last_bytes_sent = 0
        last_bytes_recv = 0
        last_timestamp = time.time()

        # Collect data
        data = []
        start_time = time.time()

        while time.time() - start_time < duration_seconds:
            info = get_process_info(process)
            if info:
                # Calculate network rates
                current_timestamp = info['timestamp']
                time_diff = current_timestamp - last_timestamp

                if last_bytes_sent > 0 and time_diff > 0:
                    bytes_sent_rate = (info['bytes_sent'] - last_bytes_sent) / time_diff
                    bytes_recv_rate = (info['bytes_recv'] - last_bytes_recv) / time_diff
                    info['bytes_sent_rate'] = bytes_sent_rate
                    info['bytes_recv_rate'] = bytes_recv_rate
                else:
                    info['bytes_sent_rate'] = 0
                    info['bytes_recv_rate'] = 0

                # Update last values
                last_bytes_sent = info['bytes_sent']
                last_bytes_recv = info['bytes_recv']
                last_timestamp = current_timestamp

                data.append(info)

                # Print current stats
                print(f"Time: {time.strftime('%H:%M:%S')} | "
                      f"CPU: {info['cpu_percent']:.2f}% | "
                      f"Memory: {info['memory_mb']:.2f} MB | "
                      f"Threads: {info['thread_count']} | "
                      f"Network Send: {info['bytes_sent_rate']/1024:.2f} KB/s | "
                      f"Network Recv: {info['bytes_recv_rate']/1024:.2f} KB/s")

            time.sleep(interval_seconds)

        return data
    except psutil.NoSuchProcess:
        print(f"Process with PID {pid} not found.")
        return []
    except Exception as e:
        print(f"Error monitoring process: {e}")
        return []

if __name__ == "__main__":
    # Default values
    pid = None
    duration = 60  # Default monitoring duration in seconds

    # Parse command line arguments
    if len(sys.argv) > 1:
        try:
            pid = int(sys.argv[1])
        except ValueError:
            print(f"Invalid PID: {sys.argv[1]}. Please provide a valid process ID.")
            sys.exit(1)
    else:
        # If no PID provided, list Python processes
        print("No PID provided. Listing Python processes:")
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = " ".join(proc.cmdline()) if proc.cmdline() else ""
                    if 'main.py' in cmdline and 'monitor_performance.py' not in cmdline:
                        print(f"PID={proc.pid}, Name={proc.info['name']}, Cmdline={cmdline}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        sys.exit(1)

    if len(sys.argv) > 2:
        try:
            duration = int(sys.argv[2])
        except ValueError:
            print(f"Invalid duration: {sys.argv[2]}. Using default: 60 seconds.")

    # Generate timestamp for filename
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"performance_log_{timestamp}.csv"

    # Monitor the process
    data = monitor_process_by_pid(pid, duration, 1)

    # Save data to CSV
    if data:
        save_to_csv(data, filename)
