{"permissions": {"allow": ["Bash(pip install:*)", "<PERSON><PERSON>(timeout:*)", "Bash(ss:*)", "mcp__intellisense-trading__get_market_data_summary", "mcp__intellisense-trading__get_recent_trading_events", "mcp__intellisense-trading__query_redis_stream", "Bash(redis-cli:*)", "mcp__intellisense-trading__analyze_risk_events", "Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(curl:*)", "Bash(ls:*)", "Bash(powershell.exe:*)", "Bash(cmd.exe:*)", "mcp__intellisense-trading__diagnose_system_status", "mcp__intellisense-trading__get_system_health_metrics", "Bash(find:*)", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(cat:*)", "Bash(awk:*)", "Bash(git push:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(python:*)", "Bash(cp:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(git stash show:*)", "Bash(mount)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(kill:*)", "Bash(git stash push:*)", "Bash(git reset:*)", "Bash(git ls-tree:*)", "Bash(git grep:*)", "Bash(git checkout:*)", "Bash(git stash:*)", "Bash(echo)", "mcp__intellisense-trading__get_error_analysis", "Bash(git fetch:*)", "Bash(git cherry-pick:*)", "mcp__intellisense-trading__list_all_trading_sessions", "mcp__intellisense-trading__get_position_risk_summary", "Bash(truncate:*)", "Bash(node:*)", "Bash(sudo lsof:*)", "Bash(bash:*)", "Bash(py-spy dump:*)", "Bash(echo \"=== OCRDataConditioningService (4 calls) ===\" grep -n \"send_data\\|send_trading_data\" /mnt/c/TESTRADE/modules/ocr/ocr_data_conditioning_service.py)", "Bash(echo \"=== Checking PositionManager calls context ===\" grep -B5 \"babysitter_ipc_client\\.\" /mnt/c/TESTRADE/modules/trade_management/position_manager.py)", "Bash(echo \"=== MasterActionFilterService IPC calls ===\" grep -n \"send_data\\|send_trading_data\" /mnt/c/TESTRADE/modules/trade_management/master_action_filter_service.py)", "Bash(fuser:*)", "<PERSON><PERSON>(jobs)", "<PERSON><PERSON>(diff:*)", "<PERSON><PERSON>(tesseract:*)", "Bash(__NEW_LINE__ python -c \"\nfrom core.dependency_injection import DIContainer\nfrom interfaces.core.services import IConfigService, IEventBus, ITelemetryService\nfrom interfaces.trading.services import ITradeExecutor\nfrom interfaces.data.services import IPriceProvider\nfrom core.config_service import ConfigService\nfrom utils.global_config import config as global_config\n\nprint(''🧪 Testing DI Container Basic Resolution...'')\n\ntry:\n    container = DIContainer()\n    \n    # Test basic registration and resolution\n    config_service = ConfigService(global_config)\n    container.register_instance(IConfigService, config_service)\n    \n    resolved_config = container.resolve(IConfigService)\n    if resolved_config is config_service:\n        print(''✅ Basic instance registration/resolution works'')\n    else:\n        print(''❌ Basic instance registration/resolution failed'')\n        \n    # Test interface hierarchy\n    print(''✅ DI Container basic functionality verified'')\n    \nexcept Exception as e:\n    print(f''❌ DI Container test failed: {e}'')\n    import traceback\n    traceback.print_exc()\n\")", "Bash(__NEW_LINE__ echo \"🔗 Testing ApplicationCore import chain:\")", "Bash(__NEW_LINE__ echo)", "Bash(__NEW_LINE__ echo \"🚀 TESTING ALL CRITICAL IMPORT CHAINS:\")", "Bash(__NEW_LINE__ echo \"1️⃣ Main Entry Point:\")", "Bash(# Simply clear the current log file for a fresh start\n> logs/testrade_main.log\necho \"\"✅ Log file cleared. Next startup will show complete initialization sequence.\"\"\necho \"\"File size now:\"\" \nls -lh logs/testrade_main.log)", "Bash(TESTRADE_OFFLINE_MODE=1 python main.py)", "Bash(TESTRADE_OFFLINE_MODE=1 timeout 15 python main.py)", "Bash(TESTRADE_OFFLINE_MODE=1 timeout 10 python main.py)", "Bash(pgrep:*)", "Bash(ps:*)", "Bash(ip route:*)", "Bash(ping:*)", "Bash(route:*)", "Bash(for ip in ************ ********** ************ ************)", "Bash(do echo \"Testing $ip:9999...\")", "<PERSON><PERSON>(echo:*)", "Bash(done)", "Bash(for:*)", "Bash(do sed -i 's/from modules\\.trade_management\\.interfaces/from interfaces.trading.services/g' \"$file\")", "Bash(./verify_tank_sealing.sh:*)", "<PERSON><PERSON>(dos2unix:*)", "Bash(/mnt/c/TESTRADE/verify_agent5_services.sh:*)", "Bash(./check_deleted_metadata.sh:*)", "Bash(do echo \"Checking $file:\")", "Bash(do)", "Bash(if grep -q \"ITelemetryService\" \"/mnt/c/TESTRADE/modules/trade_management/$service\")", "<PERSON><PERSON>(then)", "<PERSON><PERSON>(else)", "Bash(fi)", "Bash(__NEW_LINE__ echo \"\")", "Bash(if grep -q \"correlation_logger: ICorrelationLogger\" \"/mnt/c/TESTRADE/modules/trade_management/$service\")", "Bash(do echo \"=== $service ===\")", "Bash(./verify_replacement.sh:*)", "Bash(do echo \"$f: $(grep -c ITelemetryService $f 2>/dev/null || echo 0) ITelemetryService references\")", "Bash(/tmp/verify_tank_sealing.sh:*)", "Bash(do echo \"$file:\")", "Bash(./verify_all_streams.sh:*)", "Bash(__NEW_LINE__ echo -n \"  Correlation logger implementations: \")", "Bash(do echo -n \"$f: \")", "Bash(do echo -n \"$f payload stream_override: \")", "Bash(do echo -n \"$f param stream_override: \")", "Bash(./TANK_SEALING_VERIFICATION_SUITE.sh:*)", "Bash(if [ -d \"/mnt/c/TESTRADE/.pytest_cache\" ])", "Bash(if [ -d \"/mnt/c/TESTRADE/.mypy_cache\" ])", "Bash(__NEW_LINE__ echo \"3. Checking existing project files:\")", "<PERSON><PERSON>(pip uninstall:*)", "Bash(cd:*)", "Bash(./find_services_to_update.sh:*)"], "deny": []}}