#!/usr/bin/env python3
"""
Convert All CSV Files to JSON

Converts all CSV files in a directory to JSON format for optimized replay testing.
Specifically designed for Alpaca market data files.

Usage:
    python convert_all_csv_to_json.py
    python convert_all_csv_to_json.py --input-dir data/friday_may30_2025 --output-dir data/friday_may30_2025_json
"""

import os
import sys
import glob
import argparse
import logging
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import LoadTestHarness for conversion functionality
try:
    from tests.load_testing.load_test_harness import LoadTestHarness
except ImportError as e:
    print(f"Error importing LoadTestHarness: {e}")
    sys.exit(1)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def detect_data_type(filename):
    """
    Detect data type (trades or quotes) from filename.
    
    Args:
        filename: Name of the CSV file
        
    Returns:
        str: 'trades' or 'quotes' or 'unknown'
    """
    filename_lower = filename.lower()
    if 'trades' in filename_lower:
        return 'trades'
    elif 'quotes' in filename_lower:
        return 'quotes'
    else:
        return 'unknown'

def convert_all_csv_files(input_dir, output_dir=None):
    """
    Convert all CSV files in a directory to JSON format.
    
    Args:
        input_dir: Directory containing CSV files
        output_dir: Directory to save JSON files (default: same as input_dir)
        
    Returns:
        dict: Results of conversion process
    """
    if output_dir is None:
        output_dir = input_dir
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all CSV files
    csv_pattern = os.path.join(input_dir, "*.csv")
    csv_files = glob.glob(csv_pattern)
    
    if not csv_files:
        logger.warning(f"No CSV files found in {input_dir}")
        return {'success': 0, 'failed': 0, 'files': []}
    
    logger.info(f"Found {len(csv_files)} CSV files in {input_dir}")
    
    # Initialize LoadTestHarness for conversion
    harness = LoadTestHarness()
    
    results = {
        'success': 0,
        'failed': 0,
        'files': [],
        'conversions': []
    }
    
    for csv_file in csv_files:
        filename = os.path.basename(csv_file)
        logger.info(f"Processing: {filename}")
        
        # Detect data type
        data_type = detect_data_type(filename)
        if data_type == 'unknown':
            logger.warning(f"Could not detect data type for {filename}. Skipping.")
            results['failed'] += 1
            continue
        
        # Generate output filename
        json_filename = filename.replace('.csv', '.json')
        json_path = os.path.join(output_dir, json_filename)
        
        # Convert CSV to JSON
        try:
            success = harness.convert_csv_to_json_for_replay(
                csv_file_path=csv_file,
                output_json_path=json_path,
                data_type=data_type
            )
            
            if success:
                # Get file sizes for reporting
                csv_size = os.path.getsize(csv_file)
                json_size = os.path.getsize(json_path) if os.path.exists(json_path) else 0
                
                conversion_info = {
                    'csv_file': filename,
                    'json_file': json_filename,
                    'data_type': data_type,
                    'csv_size': csv_size,
                    'json_size': json_size,
                    'compression_ratio': json_size / csv_size if csv_size > 0 else 0
                }
                
                results['conversions'].append(conversion_info)
                results['success'] += 1
                
                logger.info(f"✅ Converted {filename} → {json_filename} ({data_type})")
                logger.info(f"   Size: {csv_size:,} bytes → {json_size:,} bytes")
            else:
                logger.error(f"❌ Failed to convert {filename}")
                results['failed'] += 1
                
        except Exception as e:
            logger.error(f"❌ Error converting {filename}: {e}")
            results['failed'] += 1
    
    return results

def print_conversion_summary(results):
    """Print a summary of the conversion results."""
    print("\n" + "="*80)
    print("CONVERSION SUMMARY")
    print("="*80)
    
    print(f"Total files processed: {results['success'] + results['failed']}")
    print(f"Successful conversions: {results['success']}")
    print(f"Failed conversions: {results['failed']}")
    
    if results['conversions']:
        print(f"\n📁 CONVERTED FILES:")
        print("-" * 80)
        
        total_csv_size = 0
        total_json_size = 0
        
        # Group by data type
        trades_files = [c for c in results['conversions'] if c['data_type'] == 'trades']
        quotes_files = [c for c in results['conversions'] if c['data_type'] == 'quotes']
        
        if trades_files:
            print(f"\n📈 TRADES FILES ({len(trades_files)}):")
            for conv in trades_files:
                print(f"   {conv['csv_file']} → {conv['json_file']}")
                print(f"      Size: {conv['csv_size']:,} → {conv['json_size']:,} bytes")
                total_csv_size += conv['csv_size']
                total_json_size += conv['json_size']
        
        if quotes_files:
            print(f"\n📊 QUOTES FILES ({len(quotes_files)}):")
            for conv in quotes_files:
                print(f"   {conv['csv_file']} → {conv['json_file']}")
                print(f"      Size: {conv['csv_size']:,} → {conv['json_size']:,} bytes")
                total_csv_size += conv['csv_size']
                total_json_size += conv['json_size']
        
        print(f"\n📊 TOTAL SIZE COMPARISON:")
        print(f"   CSV Total: {total_csv_size:,} bytes")
        print(f"   JSON Total: {total_json_size:,} bytes")
        if total_csv_size > 0:
            ratio = total_json_size / total_csv_size
            print(f"   Size Ratio: {ratio:.2f}x (JSON vs CSV)")
    
    print("\n" + "="*80)

def create_combined_files(input_dir, output_dir=None):
    """
    Create combined JSON files for all symbols.
    
    Args:
        input_dir: Directory containing individual JSON files
        output_dir: Directory to save combined files (default: same as input_dir)
    """
    if output_dir is None:
        output_dir = input_dir
    
    # Find all JSON files
    json_pattern = os.path.join(input_dir, "*.json")
    json_files = glob.glob(json_pattern)
    
    if not json_files:
        logger.warning(f"No JSON files found in {input_dir}")
        return
    
    # Separate trades and quotes files
    trades_files = [f for f in json_files if 'trades' in os.path.basename(f).lower()]
    quotes_files = [f for f in json_files if 'quotes' in os.path.basename(f).lower()]
    
    logger.info(f"Found {len(trades_files)} trades files and {len(quotes_files)} quotes files")
    
    # Combine trades files
    if trades_files:
        combined_trades_path = os.path.join(output_dir, "all_trades.json")
        combine_json_files(trades_files, combined_trades_path, "trades")
    
    # Combine quotes files
    if quotes_files:
        combined_quotes_path = os.path.join(output_dir, "all_quotes.json")
        combine_json_files(quotes_files, combined_quotes_path, "quotes")

def combine_json_files(file_list, output_path, data_type):
    """
    Combine multiple JSON files into a single file.
    
    Args:
        file_list: List of JSON file paths to combine
        output_path: Path for the combined output file
        data_type: Type of data ('trades' or 'quotes')
    """
    import json
    
    logger.info(f"Combining {len(file_list)} {data_type} files into {os.path.basename(output_path)}")
    
    all_data = []
    
    for json_file in file_list:
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
                all_data.extend(data)
                logger.debug(f"Loaded {len(data)} records from {os.path.basename(json_file)}")
        except Exception as e:
            logger.error(f"Error loading {json_file}: {e}")
    
    if all_data:
        # Sort by timestamp
        all_data.sort(key=lambda x: x.get('timestamp', 0))
        
        # Save combined file
        with open(output_path, 'w') as f:
            json.dump(all_data, f, indent=2)
        
        file_size = os.path.getsize(output_path)
        logger.info(f"✅ Created {os.path.basename(output_path)} with {len(all_data):,} records ({file_size:,} bytes)")
    else:
        logger.warning(f"No data to combine for {data_type}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Convert all CSV files to JSON format')
    parser.add_argument('--input-dir', default='data/friday_may30_2025',
                       help='Input directory containing CSV files (default: data/friday_may30_2025)')
    parser.add_argument('--output-dir', 
                       help='Output directory for JSON files (default: same as input-dir)')
    parser.add_argument('--combine', action='store_true',
                       help='Also create combined all_trades.json and all_quotes.json files')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Set output directory
    output_dir = args.output_dir or args.input_dir
    
    print("="*80)
    print("CSV TO JSON CONVERSION")
    print("="*80)
    print(f"Input directory: {args.input_dir}")
    print(f"Output directory: {output_dir}")
    print(f"Combine files: {args.combine}")
    print("="*80)
    
    # Check if input directory exists
    if not os.path.exists(args.input_dir):
        logger.error(f"Input directory does not exist: {args.input_dir}")
        return 1
    
    # Convert all CSV files
    results = convert_all_csv_files(args.input_dir, output_dir)
    
    # Print summary
    print_conversion_summary(results)
    
    # Create combined files if requested
    if args.combine and results['success'] > 0:
        print(f"\n🔄 Creating combined files...")
        create_combined_files(output_dir, output_dir)
    
    # Final status
    if results['success'] > 0:
        print(f"\n✅ Successfully converted {results['success']} files!")
        if args.combine:
            print(f"📁 Combined files created in {output_dir}")
        print(f"\n💡 To test the converted files, run:")
        print(f"   python tests/load_testing/test_market_data_replay.py")
    else:
        print(f"\n❌ No files were successfully converted.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
