// Simplified app.js - Modular architecture with classes
// Much cleaner than 6,443 lines of spaghetti code!

// --- Application Configuration ---
const APP_CONFIG = {
    apiUrl: 'http://localhost:8001',
    maxReconnectAttempts: 5,
    healthPollingIntervalMs: 5000,
    reconnectDelayMs: 2000,
    maxImageSizeBytes: 2000000,
    maxStreamLines: 30,
    buttonResetDelayMs: 2000,
    buttonErrorDelayMs: 3000,
    maxSharesLimit: 10000,
    debugMode: false,
    imageUpdateDebounceMs: 50,
    memoryWarningThresholdMB: 50,
    maxImageHistoryItems: 100
};

// --- WebSocket Manager Class ---
class WebSocketManager {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.messageHandlers = new Map();
        this.connectionCallbacks = [];
    }

    connect() {
        try {
            const wsUrl = APP_CONFIG.apiUrl.replace('http', 'ws') + '/ws';
            this.socket = new WebSocket(wsUrl);
            
            this.socket.onopen = () => {
                console.log('✅ WebSocket connected');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.connectionCallbacks.forEach(callback => callback(true));
            };

            this.socket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleMessage(message);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };

            this.socket.onclose = () => {
                console.log('❌ WebSocket disconnected');
                this.isConnected = false;
                this.connectionCallbacks.forEach(callback => callback(false));
                this.attemptReconnect();
            };

            this.socket.onerror = (error) => {
                console.error('WebSocket error:', error);
            };

        } catch (error) {
            console.error('Failed to create WebSocket:', error);
            this.attemptReconnect();
        }
    }

    handleMessage(message) {
        const handler = this.messageHandlers.get(message.type);
        if (handler) {
            try {
                handler(message);
            } catch (error) {
                console.error(`Error in handler for ${message.type}:`, error);
            }
        } else {
            console.debug('No handler for message type:', message.type);
        }
    }

    registerHandler(messageType, handler) {
        this.messageHandlers.set(messageType, handler);
    }

    onConnectionChange(callback) {
        this.connectionCallbacks.push(callback);
    }

    attemptReconnect() {
        if (this.reconnectAttempts < APP_CONFIG.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`🔄 Reconnecting... (${this.reconnectAttempts}/${APP_CONFIG.maxReconnectAttempts})`);
            setTimeout(() => this.connect(), APP_CONFIG.reconnectDelayMs);
        } else {
            console.error('❌ Max reconnection attempts reached');
        }
    }

    send(data) {
        if (this.isConnected && this.socket) {
            this.socket.send(JSON.stringify(data));
        } else {
            console.warn('Cannot send message - WebSocket not connected');
        }
    }
}

// --- Button Factory for Clean Button Management ---
class ButtonFactory {
    static createActionButton(config) {
        const button = document.createElement('button');
        button.className = config.className || 'btn btn-primary';
        button.textContent = config.label;
        button.disabled = config.disabled || false;
        
        if (config.onClick) {
            button.addEventListener('click', async (e) => {
                e.preventDefault();
                await this.handleButtonClick(button, config);
            });
        }
        
        return button;
    }

    static async handleButtonClick(button, config) {
        const originalText = button.textContent;
        
        try {
            // Show loading state
            button.disabled = true;
            button.textContent = config.loadingText || 'Processing...';
            
            // Execute action
            await config.onClick();
            
            // Show success state
            button.textContent = config.successText || '✅ Success';
            button.className = 'btn btn-success';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.className = config.className || 'btn btn-primary';
                button.disabled = false;
            }, APP_CONFIG.buttonResetDelayMs);
            
        } catch (error) {
            console.error('Button action failed:', error);
            
            // Show error state
            button.textContent = config.errorText || '❌ Error';
            button.className = 'btn btn-danger';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.className = config.className || 'btn btn-primary';
                button.disabled = false;
            }, APP_CONFIG.buttonErrorDelayMs);
        }
    }

    static createTradeButton(symbol, side, quantity) {
        return this.createActionButton({
            label: `${side.toUpperCase()} ${quantity} ${symbol}`,
            className: side === 'buy' ? 'btn btn-success' : 'btn btn-danger',
            onClick: () => CommandManager.sendTradeCommand(symbol, side, quantity),
            loadingText: 'Sending...',
            successText: `${side.toUpperCase()} Sent`,
            errorText: 'Failed'
        });
    }
}

// --- Command Manager for API Calls ---
class CommandManager {
    static async sendCommand(command, parameters = {}) {
        try {
            const response = await fetch(`${APP_CONFIG.apiUrl}/control/command`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ command, parameters })
            });

            if (!response.ok) {
                throw new Error(`Command failed: ${response.status}`);
            }

            const result = await response.json();
            console.log('✅ Command sent:', result);
            return result;
        } catch (error) {
            console.error('❌ Command failed:', error);
            throw error;
        }
    }

    static async sendTradeCommand(symbol, side, quantity) {
        return this.sendCommand('manual_trade', {
            symbol: symbol,
            side: side,
            quantity: parseInt(quantity),
            order_type: 'MARKET'
        });
    }

    static async emergencyStop() {
        return this.sendCommand('emergency_stop');
    }

    static async startOCR() {
        return this.sendCommand('start_ocr');
    }

    static async stopOCR() {
        return this.sendCommand('stop_ocr');
    }
}

// --- Data Display Manager ---
class DataDisplayManager {
    constructor() {
        this.positions = new Map();
        this.accountData = {};
        this.ocrData = {};
        this.priceData = new Map();
    }

    updatePositions(positionsData) {
        if (positionsData && positionsData.positions) {
            positionsData.positions.forEach(position => {
                this.positions.set(position.symbol, position);
            });
            this.renderPositions();
        }
    }

    updateAccountData(accountData) {
        this.accountData = { ...this.accountData, ...accountData };
        this.renderAccountSummary();
    }

    updateOCRData(ocrData) {
        this.ocrData = ocrData;
        this.renderOCRData();
    }

    updatePriceData(symbol, priceData) {
        this.priceData.set(symbol, { 
            ...this.priceData.get(symbol), 
            ...priceData,
            timestamp: Date.now()
        });
        this.renderPriceCard(symbol);
    }

    renderPositions() {
        const container = document.getElementById('positions-container');
        if (!container) return;

        container.innerHTML = '';
        
        this.positions.forEach((position, symbol) => {
            const card = this.createPositionCard(position);
            container.appendChild(card);
        });
    }

    createPositionCard(position) {
        const card = document.createElement('div');
        card.className = 'position-card';
        
        const pnlClass = position.unrealized_pnl >= 0 ? 'profit' : 'loss';
        const priceData = this.priceData.get(position.symbol) || {};
        
        card.innerHTML = `
            <div class="position-header">
                <h4>${position.symbol}</h4>
                <span class="quantity">${position.quantity} shares</span>
            </div>
            <div class="position-details">
                <div class="price-info">
                    <span>Avg Price: $${position.average_price?.toFixed(2) || '0.00'}</span>
                    <span>Last: $${priceData.last_price?.toFixed(2) || '0.00'}</span>
                    <span>Bid/Ask: $${priceData.bid?.toFixed(2) || '0.00'}/$${priceData.ask?.toFixed(2) || '0.00'}</span>
                </div>
                <div class="pnl-info ${pnlClass}">
                    <span>P&L: $${position.unrealized_pnl?.toFixed(2) || '0.00'}</span>
                    <span>(${position.pnl_percent?.toFixed(2) || '0.00'}%)</span>
                </div>
            </div>
            <div class="position-actions">
                ${this.createPositionButtons(position.symbol, position.quantity)}
            </div>
        `;
        
        return card;
    }

    createPositionButtons(symbol, quantity) {
        const closeButton = ButtonFactory.createTradeButton(symbol, 'sell', Math.abs(quantity));
        closeButton.textContent = 'Close Position';
        closeButton.className = 'btn btn-warning btn-sm';
        
        return closeButton.outerHTML;
    }

    renderAccountSummary() {
        const container = document.getElementById('account-summary');
        if (!container || !this.accountData) return;

        const dayPnLClass = (this.accountData.day_pnl || 0) >= 0 ? 'profit' : 'loss';
        
        container.innerHTML = `
            <div class="account-card">
                <h3>Account Summary</h3>
                <div class="account-details">
                    <div class="account-row">
                        <span>Account Value:</span>
                        <span>$${this.accountData.account_value?.toFixed(2) || '0.00'}</span>
                    </div>
                    <div class="account-row">
                        <span>Buying Power:</span>
                        <span>$${this.accountData.buying_power?.toFixed(2) || '0.00'}</span>
                    </div>
                    <div class="account-row">
                        <span>Cash:</span>
                        <span>$${this.accountData.cash?.toFixed(2) || '0.00'}</span>
                    </div>
                    <div class="account-row ${dayPnLClass}">
                        <span>Day P&L:</span>
                        <span>$${this.accountData.day_pnl?.toFixed(2) || '0.00'}</span>
                    </div>
                </div>
            </div>
        `;
    }

    renderOCRData() {
        const container = document.getElementById('ocr-display');
        if (!container || !this.ocrData) return;

        const confidenceClass = (this.ocrData.aggregate_confidence || 0) > 0.8 ? 'high' : 'low';
        
        container.innerHTML = `
            <div class="ocr-card">
                <h3>OCR Status</h3>
                <div class="ocr-details">
                    <div class="ocr-row">
                        <span>Frame:</span>
                        <span>#${this.ocrData.frame_number || 0}</span>
                    </div>
                    <div class="ocr-row">
                        <span>Confidence:</span>
                        <span class="${confidenceClass}">${((this.ocrData.aggregate_confidence || 0) * 100).toFixed(1)}%</span>
                    </div>
                    <div class="ocr-row">
                        <span>Positions:</span>
                        <span>${this.ocrData.positions?.length || 0} detected</span>
                    </div>
                </div>
            </div>
        `;
    }

    renderPriceCard(symbol) {
        const priceData = this.priceData.get(symbol);
        if (!priceData) return;

        // Update existing position card if it exists
        const positionCards = document.querySelectorAll('.position-card');
        positionCards.forEach(card => {
            const symbolElement = card.querySelector('h4');
            if (symbolElement && symbolElement.textContent === symbol) {
                const priceInfo = card.querySelector('.price-info');
                if (priceInfo) {
                    const lastSpan = priceInfo.children[1];
                    const bidAskSpan = priceInfo.children[2];
                    
                    if (lastSpan && priceData.last_price) {
                        lastSpan.textContent = `Last: $${priceData.last_price.toFixed(2)}`;
                    }
                    if (bidAskSpan && priceData.bid && priceData.ask) {
                        bidAskSpan.textContent = `Bid/Ask: $${priceData.bid.toFixed(2)}/$${priceData.ask.toFixed(2)}`;
                    }
                }
            }
        });
    }
}

// --- Global Instances ---
const wsManager = new WebSocketManager();
const dataManager = new DataDisplayManager();

// --- Message Handlers Registration ---
function setupMessageHandlers() {
    // OCR Updates
    wsManager.registerHandler('ocr_update', (message) => {
        dataManager.updateOCRData(message);
    });

    // Position Updates
    wsManager.registerHandler('trades_update', (message) => {
        if (message.payload && message.payload.trades) {
            dataManager.updatePositions({ positions: message.payload.trades });
        }
    });

    // Account Updates  
    wsManager.registerHandler('account_summary_update', (message) => {
        if (message.account) {
            dataManager.updateAccountData(message.account);
        }
    });

    // Price Updates
    wsManager.registerHandler('price_quote_update', (message) => {
        dataManager.updatePriceData(message.symbol, {
            bid: message.bid,
            ask: message.ask,
            bid_size: message.bid_size,
            ask_size: message.ask_size
        });
    });

    wsManager.registerHandler('price_trade_update', (message) => {
        dataManager.updatePriceData(message.symbol, {
            last_price: message.last_price,
            last_size: message.last_size
        });
    });

    // Health Updates
    wsManager.registerHandler('health_update', (message) => {
        console.log(`Health update: ${message.component} - ${message.status}`);
    });

    // Order Updates
    wsManager.registerHandler('order_status_update', (message) => {
        console.log('Order status update:', message);
    });

    // Trade History
    wsManager.registerHandler('trade_history_update', (message) => {
        console.log('Trade history update:', message);
    });
}

// --- Application Initialization ---
function initializeApp() {
    console.log('🚀 Initializing simplified trading app...');
    
    // Setup message handlers
    setupMessageHandlers();
    
    // Connect WebSocket
    wsManager.connect();
    
    // Setup connection status indicator
    wsManager.onConnectionChange((connected) => {
        const indicator = document.getElementById('connection-status');
        if (indicator) {
            indicator.textContent = connected ? '🟢 Connected' : '🔴 Disconnected';
            indicator.className = connected ? 'status-connected' : 'status-disconnected';
        }
    });

    // Setup control buttons
    setupControlButtons();
    
    console.log('✅ App initialized successfully!');
}

function setupControlButtons() {
    // Emergency Stop
    const emergencyBtn = document.getElementById('emergency-stop');
    if (emergencyBtn) {
        emergencyBtn.replaceWith(ButtonFactory.createActionButton({
            label: '🛑 EMERGENCY STOP',
            className: 'btn btn-danger btn-lg',
            onClick: CommandManager.emergencyStop,
            loadingText: 'Stopping...',
            successText: 'STOPPED',
            errorText: 'FAILED'
        }));
    }

    // OCR Controls
    const startOcrBtn = document.getElementById('start-ocr');
    if (startOcrBtn) {
        startOcrBtn.replaceWith(ButtonFactory.createActionButton({
            label: '▶️ Start OCR',
            className: 'btn btn-success',
            onClick: CommandManager.startOCR
        }));
    }

    const stopOcrBtn = document.getElementById('stop-ocr');
    if (stopOcrBtn) {
        stopOcrBtn.replaceWith(ButtonFactory.createActionButton({
            label: '⏹️ Stop OCR',
            className: 'btn btn-warning',
            onClick: CommandManager.stopOCR
        }));
    }
}

// --- Start the application when DOM is ready ---
document.addEventListener('DOMContentLoaded', initializeApp);

console.log('📄 Simplified app.js loaded - Much cleaner than 6,443 lines!');