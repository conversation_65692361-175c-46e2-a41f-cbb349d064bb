#!/usr/bin/env python3
"""
Comprehensive test script for C++ OCR accelerator module.
Run this from Windows Command Prompt: python test_cpp_ocr_accelerator.py
"""

import sys
import platform
import time
import traceback
import os
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def create_test_image():
    """Create a test image with known text for OCR testing."""
    # Create a white background image
    img = Image.new('RGB', (400, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # Try to use a built-in font, fallback to default if not available
    try:
        # Try to load a system font
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        try:
            font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 24)
        except:
            font = ImageFont.load_default()
    
    # Draw test text
    test_text = "AAPL 150.25 +2.50"
    draw.text((20, 80), test_text, fill='black', font=font)
    
    # Convert to numpy array (RGB format)
    img_array = np.array(img)
    return img_array, test_text

def test_cpp_accelerator_basic():
    """Test basic C++ accelerator import and functions."""
    print("=== Testing C++ OCR Accelerator Basic Functions ===")

    try:
        # FUZZY'S SIMPLE, RELIABLE LOADER (same as OCR service)
        build_dir = os.path.join("ocr_accelerator", "x64", "Release")
        build_dir = os.path.abspath(build_dir)

        if not os.path.exists(build_dir):
            raise ImportError(f"Build directory not found: {build_dir}")

        # Check critical files
        pyd_file = os.path.join(build_dir, "ocr_accelerator.pyd")
        if not os.path.exists(pyd_file):
            raise ImportError(f"ocr_accelerator.pyd not found in {build_dir}")

        print(f"✅ Found build directory: {build_dir}")
        print(f"✅ Found ocr_accelerator.pyd")

        # Add to DLL search path
        if hasattr(os, 'add_dll_directory'):
            os.add_dll_directory(build_dir)
            print(f"✅ Added DLL directory: {build_dir}")

        # Add to PATH
        current_path = os.environ.get('PATH', '')
        if build_dir not in current_path:
            os.environ['PATH'] = build_dir + os.pathsep + current_path
            print(f"✅ Added to PATH")

        # Add to sys.path
        if build_dir not in sys.path:
            sys.path.insert(0, build_dir)
            print(f"✅ Added to sys.path")

        # Change to build directory and import
        original_cwd = os.getcwd()
        try:
            os.chdir(build_dir)
            import ocr_accelerator
            print("✅ Successfully imported ocr_accelerator module")
        finally:
            os.chdir(original_cwd)

        # List available functions
        functions = [attr for attr in dir(ocr_accelerator) if not attr.startswith('_')]
        print(f"✅ Available functions: {functions}")

        # Test basic function if available
        if hasattr(ocr_accelerator, 'test_function'):
            result = ocr_accelerator.test_function()
            print(f"✅ test_function() result: {result}")
        else:
            print("⚠️  test_function not found")

        return True, ocr_accelerator

    except Exception as e:
        print(f"❌ Error testing basic functions: {e}")
        traceback.print_exc()
        return False, None

def test_cpp_accelerator_ocr():
    """Test OCR processing with the C++ accelerator."""
    print("\n=== Testing C++ OCR Processing ===")
    
    try:
        import ocr_accelerator
        
        # Create test image
        img_array, expected_text = create_test_image()
        print(f"✅ Created test image with text: '{expected_text}'")
        print(f"✅ Image shape: {img_array.shape}, dtype: {img_array.dtype}")
        
        # Test OCR processing
        if hasattr(ocr_accelerator, 'process_image_and_ocr'):
            print("🔄 Processing image with C++ accelerator...")
            start_time = time.time()
            
            # Call with all required parameters (using OCR service defaults)
            result = ocr_accelerator.process_image_and_ocr(
                img_array,                    # input_frame
                3.0,                         # upscale_factor
                True,                        # force_black_text_on_white
                0.5,                         # unsharp_strength
                11,                          # threshold_block_size
                2,                           # threshold_c
                1.0,                         # red_boost
                1.0,                         # green_boost
                False,                       # apply_text_mask_cleaning
                100,                         # text_mask_min_contour_area
                5,                           # text_mask_min_width
                5,                           # text_mask_min_height
                False,                       # enhance_small_symbols
                20,                          # symbol_max_height_for_enhancement_upscaled
                (0.5, 1.5),                  # period_comma_aspect_ratio_range_upscaled
                3,                           # period_comma_draw_radius_upscaled
                2.0,                         # hyphen_like_min_aspect_ratio_upscaled
                3                            # hyphen_like_draw_min_height_upscaled
            )
            
            processing_time = time.time() - start_time
            print(f"✅ Processing completed in {processing_time:.4f} seconds")
            print(f"✅ OCR result: {result}")
            
            # Validate result structure
            if isinstance(result, dict):
                if 'text' in result:
                    extracted_text = result['text'].strip()
                    print(f"✅ Extracted text: '{extracted_text}'")
                    
                    # Check if we got meaningful text
                    if extracted_text and len(extracted_text) > 0:
                        print("✅ OCR successfully extracted text")
                        
                        # Check confidence if available
                        if 'confidence' in result:
                            confidence = result['confidence']
                            print(f"✅ Confidence score: {confidence:.2f}")
                            
                            if confidence > 0.8:
                                print("✅ High confidence OCR result")
                            elif confidence > 0.5:
                                print("⚠️  Medium confidence OCR result")
                            else:
                                print("⚠️  Low confidence OCR result")
                        
                        return True
                    else:
                        print("⚠️  OCR returned empty text")
                        return False
                else:
                    print("⚠️  Result missing 'text' field")
                    return False
            else:
                print(f"⚠️  Unexpected result type: {type(result)}")
                return False
                
        else:
            print("❌ process_image_and_ocr function not found in module")
            return False
            
    except Exception as e:
        print(f"❌ Error testing OCR processing: {e}")
        traceback.print_exc()
        return False

def test_ocr_service_integration():
    """Test integration with the OCR service."""
    print("\n=== Testing OCR Service Integration ===")
    
    try:
        # Import OCR service components
        from modules.ocr.ocr_service import CPP_ACCELERATOR_AVAILABLE, CPP_ACCELERATOR_LOAD_STATUS
        
        print(f"✅ OCR Service C++ Accelerator Available: {CPP_ACCELERATOR_AVAILABLE}")
        print(f"✅ OCR Service Status: {CPP_ACCELERATOR_LOAD_STATUS}")
        
        if CPP_ACCELERATOR_AVAILABLE:
            print("✅ OCR Service successfully detected C++ accelerator")
            return True
        else:
            print("❌ OCR Service failed to detect C++ accelerator")
            return False
            
    except Exception as e:
        print(f"❌ Error testing OCR service integration: {e}")
        traceback.print_exc()
        return False

def main():
    """Run comprehensive C++ OCR accelerator tests."""
    print(f"Platform: {platform.system()}")
    print(f"Python Version: {sys.version}")
    print(f"Architecture: {platform.architecture()}")
    
    if platform.system() != "Windows":
        print("❌ This test must be run on Windows")
        return
    
    print("\n" + "="*60)
    print("C++ OCR ACCELERATOR COMPREHENSIVE TEST")
    print("="*60)
    
    # Test results tracking
    test_results = []
    
    # Test 1: Basic module import and functions
    basic_success, accelerator_module = test_cpp_accelerator_basic()
    test_results.append(("Basic Import", basic_success))
    
    if basic_success:
        # Test 2: OCR processing
        ocr_success = test_cpp_accelerator_ocr()
        test_results.append(("OCR Processing", ocr_success))
        
        # Test 3: Service integration
        integration_success = test_ocr_service_integration()
        test_results.append(("Service Integration", integration_success))
    else:
        print("❌ Skipping further tests due to import failure")
        test_results.append(("OCR Processing", False))
        test_results.append(("Service Integration", False))
    
    # Print test summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 ALL TESTS PASSED - C++ OCR Accelerator is working correctly!")
    else:
        print("⚠️  SOME TESTS FAILED - Check errors above")
    print("="*60)

if __name__ == "__main__":
    main()