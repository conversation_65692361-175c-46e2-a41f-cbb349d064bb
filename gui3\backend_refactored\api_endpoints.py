"""
API endpoints extracted from gui_backend.py
Same endpoints, cleaner organization
"""

from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel
from typing import Dict, List, Optional, Any
import logging
import time

logger = logging.getLogger(__name__)


# Same Pydantic models as original
class CommandRequest(BaseModel):
    command: str
    args: Optional[Dict[str, Any]] = {}

class ManualOrderRequest(BaseModel):
    symbol: str
    side: str
    quantity: int
    order_type: str = "MKT"
    limit_price: Optional[float] = None

class PositionStatusUpdate(BaseModel):
    symbol: str
    status: str


def create_api_router(app_state):
    """Create API router with all endpoints from original"""
    router = APIRouter()
    
    # --- Bootstrap API Endpoints (same as original) ---
    
    @router.get("/positions/current", tags=["Bootstrap API"])
    async def get_current_positions():
        """Get current positions - same implementation"""
        return {
            "positions": app_state.latest_positions,
            "last_update": time.time()
        }
    
    @router.get("/account/summary", tags=["Bootstrap API"])
    async def get_account_summary():
        """Get account summary - same implementation"""
        return {
            "account": app_state.latest_account_data,
            "last_update": time.time()
        }
    
    @router.get("/orders/today", tags=["Bootstrap API"])
    async def get_orders_today():
        """Get today's orders - same implementation"""
        return {
            "orders": app_state.latest_orders,
            "count": len(app_state.latest_orders),
            "last_update": time.time()
        }
    
    @router.get("/market/status", tags=["Bootstrap API"])
    async def get_market_status():
        """Get market status - same implementation"""
        # Same logic as original
        return {
            "market_open": True,  # This would come from real data
            "current_time": time.time(),
            "trading_enabled": True
        }
    
    @router.get("/trades/rejections", tags=["Bootstrap API"])
    async def get_trade_rejections():
        """Get trade rejections - same implementation"""
        # Would filter from orders/events
        rejections = []  # Filter logic here
        return {
            "rejections": rejections,
            "count": len(rejections),
            "last_update": time.time()
        }
    
    # --- Command Endpoints ---
    
    @router.post("/command/execute")
    async def execute_command(request: CommandRequest):
        """Execute command - same as original"""
        try:
            # Same command handling logic
            command_id = await send_core_command_and_await_response(
                app_state,
                request.command,
                request.args
            )
            
            return {
                "status": "sent",
                "command_id": command_id,
                "command": request.command
            }
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.post("/orders/manual")
    async def place_manual_order(request: ManualOrderRequest):
        """Place manual order - same as original"""
        try:
            # Validate request - same validation
            if request.side not in ["BUY", "SELL"]:
                raise ValueError("Side must be BUY or SELL")
            
            if request.quantity <= 0:
                raise ValueError("Quantity must be positive")
            
            # Send order command - same as original
            command_args = {
                "symbol": request.symbol,
                "side": request.side,
                "quantity": request.quantity,
                "order_type": request.order_type,
                "limit_price": request.limit_price
            }
            
            command_id = await send_core_command_and_await_response(
                app_state,
                "place_manual_order",
                command_args
            )
            
            return {
                "status": "sent",
                "command_id": command_id,
                "order": command_args
            }
            
        except Exception as e:
            logger.error(f"Manual order failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.patch("/positions/{symbol}/status")
    async def update_position_status(symbol: str, update: PositionStatusUpdate):
        """Update position status - same as original"""
        try:
            command_args = {
                "symbol": symbol,
                "new_status": update.status
            }
            
            command_id = await send_core_command_and_await_response(
                app_state,
                "update_position_status",
                command_args
            )
            
            return {
                "status": "sent",
                "command_id": command_id,
                "symbol": symbol,
                "new_status": update.status
            }
            
        except Exception as e:
            logger.error(f"Position status update failed: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    return router


# Helper function - same as original
async def send_core_command_and_await_response(
    app_state,
    command: str,
    args: Dict[str, Any],
    timeout: float = 30.0
) -> str:
    """Send command and await response - same implementation"""
    import uuid
    import asyncio
    
    command_id = str(uuid.uuid4())
    
    # Store pending command
    response_future = asyncio.Future()
    app_state.active_commands[command_id] = {
        "command": command,
        "args": args,
        "future": response_future,
        "timestamp": time.time()
    }
    
    # Send via Redis - same as original
    redis_client = app_state.redis_manager.get_client('default')
    
    command_message = {
        "command_id": command_id,
        "command": command,
        "args": args,
        "source": "gui_backend",
        "timestamp": time.time()
    }
    
    redis_client.xadd("testrade:gui-commands", command_message)
    
    logger.info(f"Sent command {command} with ID {command_id}")
    
    # Wait for response - same timeout handling
    try:
        response = await asyncio.wait_for(response_future, timeout=timeout)
        return command_id
    except asyncio.TimeoutError:
        logger.error(f"Command {command_id} timed out")
        raise Exception(f"Command timeout after {timeout}s")
    finally:
        # Cleanup
        app_state.active_commands.pop(command_id, None)