#!/usr/bin/env python3
"""
Debug script to investigate C++ module function export issue.
"""

import os
import sys
import inspect

def debug_module_loading():
    """Debug C++ module loading and function exports."""
    print("=== C++ Module Function Export Debugging ===")
    
    # Add path and change directory for DLL loading
    cpp_module_path = "ocr_accelerator/x64/Release"
    abs_cpp_module_path = os.path.abspath(cpp_module_path)
    print(f"Module path: {abs_cpp_module_path}")
    
    if os.path.exists(abs_cpp_module_path):
        print(f"✅ Module directory exists")
        
        # List files in module directory
        pyd_files = [f for f in os.listdir(abs_cpp_module_path) if f.endswith('.pyd')]
        print(f"PYD files found: {pyd_files}")
        
        if pyd_files:
            pyd_path = os.path.join(abs_cpp_module_path, pyd_files[0])
            pyd_size = os.path.getsize(pyd_path)
            print(f"PYD file size: {pyd_size} bytes")
            
        # Add DLL search paths
        if hasattr(os, 'add_dll_directory'):
            try:
                os.add_dll_directory(abs_cpp_module_path)
                print(f"✅ Added DLL directory: {abs_cpp_module_path}")
            except Exception as e:
                print(f"❌ Failed to add DLL directory: {e}")
        
        # Add to PATH
        current_path = os.environ.get('PATH', '')
        if abs_cpp_module_path not in current_path:
            os.environ['PATH'] = abs_cpp_module_path + os.pathsep + current_path
            print(f"✅ Added to PATH")
        
        # Add to sys.path
        if abs_cpp_module_path not in sys.path:
            sys.path.insert(0, abs_cpp_module_path)
            print(f"✅ Added to sys.path")
        
        # Change working directory for import
        original_cwd = os.getcwd()
        try:
            os.chdir(abs_cpp_module_path)
            print(f"✅ Changed working directory to: {abs_cpp_module_path}")
            
            # Try importing the module
            print("\n--- Attempting module import ---")
            try:
                import ocr_accelerator
                print("✅ Module imported successfully!")
                
                # Inspect module contents
                print(f"\nModule type: {type(ocr_accelerator)}")
                print(f"Module file: {getattr(ocr_accelerator, '__file__', 'N/A')}")
                
                # Get all attributes
                all_attrs = dir(ocr_accelerator)
                print(f"\nAll module attributes: {all_attrs}")
                
                # Filter for functions
                functions = []
                for attr_name in all_attrs:
                    if not attr_name.startswith('_'):
                        attr = getattr(ocr_accelerator, attr_name)
                        if callable(attr):
                            functions.append(attr_name)
                            print(f"Function found: {attr_name} - {type(attr)}")
                            
                            # Try to get function signature
                            try:
                                sig = inspect.signature(attr)
                                print(f"  Signature: {sig}")
                            except Exception as sig_e:
                                print(f"  Could not get signature: {sig_e}")
                
                print(f"\nCallable functions: {functions}")
                
                # Test specific functions
                if 'test_function' in functions:
                    print("\n--- Testing test_function ---")
                    try:
                        result = ocr_accelerator.test_function()
                        print(f"✅ test_function() result: {result}")
                    except Exception as e:
                        print(f"❌ test_function() failed: {e}")
                
                if 'process_image_and_ocr' in functions:
                    print("\n--- Testing process_image_and_ocr availability ---")
                    func = getattr(ocr_accelerator, 'process_image_and_ocr')
                    print(f"✅ process_image_and_ocr function: {func}")
                    print(f"✅ Function type: {type(func)}")
                else:
                    print("❌ process_image_and_ocr function not found")
                    
                # Check module dict
                if hasattr(ocr_accelerator, '__dict__'):
                    module_dict = ocr_accelerator.__dict__
                    print(f"\nModule __dict__ keys: {list(module_dict.keys())}")
                    
                # Use pybind11 specific introspection if available
                if hasattr(ocr_accelerator, '__doc__'):
                    print(f"\nModule docstring: {ocr_accelerator.__doc__}")
                    
            except ImportError as e:
                print(f"❌ Import failed: {e}")
                return False
            except Exception as e:
                print(f"❌ Unexpected error during import: {e}")
                return False
                
        finally:
            # Restore original working directory
            if 'original_cwd' in locals():
                os.chdir(original_cwd)
                print(f"✅ Restored working directory to: {original_cwd}")
        
        return True
    else:
        print(f"❌ Module directory does not exist: {abs_cpp_module_path}")
        return False

if __name__ == "__main__":
    debug_module_loading()