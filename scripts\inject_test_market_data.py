#!/usr/bin/env python3
"""
Script to inject test market data for testing the Position Enrichment Service
"""
import redis
import json
import time
import uuid
from datetime import datetime

def create_redis_message_json(payload, event_type_str, correlation_id_val=None, source_component_name="TestScript"):
    """Create a Redis message in the expected format"""
    if correlation_id_val is None:
        correlation_id_val = str(uuid.uuid4())
    
    event_id = str(uuid.uuid4())
    timestamp = time.time()
    
    message = {
        "metadata": {
            "eventId": event_id,
            "correlationId": correlation_id_val,
            "causationId": None,
            "timestamp": timestamp,
            "eventType": event_type_str,
            "sourceComponent": source_component_name
        },
        "payload": payload
    }
    
    return json.dumps(message)

def inject_test_market_data():
    """Inject test market data for symbols that have positions"""
    try:
        # Connect to Redis
        redis_client = redis.Redis(host='**************', port=6379, db=0, decode_responses=True)
        redis_client.ping()
        print("✅ Connected to Redis successfully")
        
        # Get symbols from position updates to see what symbols have positions
        print("\n📊 Checking for symbols with positions...")
        try:
            position_messages = redis_client.xrevrange('testrade:position-updates', count=10)
            symbols_with_positions = set()
            
            for message_id, fields in position_messages:
                if 'data' in fields:
                    try:
                        message_data = json.loads(fields['data'])
                        payload = message_data.get('payload', {})
                        symbol = payload.get('symbol')
                        is_open = payload.get('is_open', False)
                        if symbol and is_open:
                            symbols_with_positions.add(symbol)
                    except:
                        continue
            
            print(f"Found symbols with open positions: {list(symbols_with_positions)}")
            
            if not symbols_with_positions:
                # Use default test symbols
                symbols_with_positions = {'AAPL', 'MSFT', 'TESTSYM'}
                print(f"No open positions found, using test symbols: {list(symbols_with_positions)}")
                
        except Exception as e:
            print(f"Error reading position updates: {e}")
            symbols_with_positions = {'AAPL', 'MSFT', 'TESTSYM'}
            print(f"Using default test symbols: {list(symbols_with_positions)}")
        
        # Inject test market data for each symbol
        print(f"\n💰 Injecting test market data for {len(symbols_with_positions)} symbols...")
        
        for symbol in symbols_with_positions:
            # Create test quote data
            quote_payload = {
                "symbol": symbol,
                "bid_price": 150.25,
                "ask_price": 150.27,
                "bid_size": 100,
                "ask_size": 200,
                "timestamp": time.time(),
                "conditions": [],
                "exchange": "NASDAQ"
            }
            
            quote_message = create_redis_message_json(
                payload=quote_payload,
                event_type_str="TESTRADE_INTERNAL_RAW_QUOTE",
                source_component_name="TestMarketDataInjector"
            )
            
            # Create test trade data
            trade_payload = {
                "symbol": symbol,
                "price": 150.26,
                "size": 100,
                "timestamp": time.time(),
                "conditions": [],
                "exchange": "NASDAQ"
            }
            
            trade_message = create_redis_message_json(
                payload=trade_payload,
                event_type_str="TESTRADE_INTERNAL_RAW_TRADE",
                source_component_name="TestMarketDataInjector"
            )
            
            # Publish to Redis streams
            try:
                quote_id = redis_client.xadd('testrade:internal:market-data:raw-quotes', {'data': quote_message})
                trade_id = redis_client.xadd('testrade:internal:market-data:raw-trades', {'data': trade_message})
                
                print(f"✅ {symbol}: Quote {quote_id}, Trade {trade_id}")
                
            except Exception as e:
                print(f"❌ {symbol}: Error injecting data - {e}")
        
        print(f"\n📈 Injected test market data for {len(symbols_with_positions)} symbols")
        
        # Wait a moment and check if PositionEnrichmentService processes the data
        print("\n⏳ Waiting 5 seconds for PositionEnrichmentService to process...")
        time.sleep(5)
        
        # Check if enriched position updates were created
        try:
            enriched_messages = redis_client.xrevrange('testrade:enriched-position-updates', count=5)
            if enriched_messages:
                print(f"✅ Found {len(enriched_messages)} enriched position updates!")
                for message_id, fields in enriched_messages:
                    if 'data' in fields:
                        try:
                            message_data = json.loads(fields['data'])
                            payload = message_data.get('payload', {})
                            symbol = payload.get('symbol', 'N/A')
                            market_data_available = payload.get('market_data_available', False)
                            bid = payload.get('bid_price', 0)
                            ask = payload.get('ask_price', 0)
                            print(f"   {symbol}: Market data available: {market_data_available}, Bid: {bid}, Ask: {ask}")
                        except:
                            continue
            else:
                print("🟡 No enriched position updates found yet")
                print("   This could mean:")
                print("   - PositionEnrichmentService is not running")
                print("   - No open positions to enrich")
                print("   - Service is still starting up")
                
        except Exception as e:
            print(f"❌ Error checking enriched position updates: {e}")
        
        print("\n✅ Test market data injection complete!")
        
    except redis.ConnectionError:
        print("❌ Failed to connect to Redis at **************:6379")
    except Exception as e:
        print(f"❌ Error injecting test market data: {e}")

if __name__ == "__main__":
    print("🧪 TESTRADE Test Market Data Injector")
    print("=" * 60)
    inject_test_market_data()
