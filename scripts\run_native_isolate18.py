import sys
import os
import subprocess

def main():
    # Get the path to the native_isolate18.py script
    script_path = os.path.join(os.path.dirname(__file__), 'native_isolate18.py')
    
    # Get the path to the patch_main.py script
    patch_path = os.path.join(os.path.dirname(__file__), 'patch_main.py')
    
    # Run the script with the patch
    cmd = [sys.executable, '-m', 'trace', '--trace', patch_path, script_path]
    
    print(f"Running command: {' '.join(cmd)}")
    subprocess.run(cmd)

if __name__ == "__main__":
    main()
