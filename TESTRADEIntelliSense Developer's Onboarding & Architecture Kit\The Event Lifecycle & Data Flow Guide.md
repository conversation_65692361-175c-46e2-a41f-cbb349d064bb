Document 2 of 4: The System At-a-Glance
Purpose: To provide a high-level architectural overview of the TESTRADE ecosystem. This document defines the primary applications and the core design philosophy that governs their interaction.
I. The TANK Philosophy: The Guiding Principle
The entire system is built on a single, non-negotiable principle we call the "TANK" philosophy.
Rule #1: The Core is a Fortress. The TESTRADE core trading engine is completely shielded from the outside world. It must never make a direct function call to an external component (like a GUI or logging service). It must never block or wait on a network call. Its sole responsibility is to perform its calculations and emit events.
Rule #2: Communication is One-Way Out. TESTRADE's only link to the outside world is through a single, buffered egress point (BulletproofBabysitterIPCClient). It fires its events and immediately moves on.
Rule #3: All Consumers Read from the Bus. All other applications (GUI Backend, Intellisense, etc.) are considered "consumers." They must never attempt to connect to or call TESTRADE directly. They read the event data from the central message bus (Redis).
II. The Horseshoe Architecture Diagram
This diagram illustrates the TANK philosophy in practice. Data flows up from the producer on the right, across the top via the message bus, and down to the consumers on the left.
Generated text
+-----------------+
                               |      Redis      |
                               | (Message Bus)   |
                               +--^---------+----+
                                  |         |
 (Left Side/Consumers)            |         |              (Right Side/Producer)
                                  |         |
                  /---------------V---------^----------------\
                  |       Baby Sitter Service (The Router)   |
                  \----------------+---------^---------------/
                                   |         |
      ZMQ (Commands) <-------------|         |-------------> ZMQ (Events)
                                   |         |
+-------------------+          +-----------------+          +-----------------+
|   GUI Backend     |                                       |    TESTRADE     |
|   Intellisense    |          (Local IPC Channels)         | (The TANK Core) |
|   (Consumers)     |                                       | (The Producer)  |
+-------------------+                                       +-----------------+
Use code with caution.
Text
III. Component Roster & Responsibilities
TESTRADE (The Core Engine / The TANK)
Responsibility: The producer. Executes all core trading logic (OCR, signal generation, order management). Its only job is to do its work and emit events about what it has done.
BulletproofBabysitterIPCClient (The Egress Point)
Responsibility: A client library used by any process that needs to send data into the pipeline. It is the gatekeeper of the TANK, providing resilient, buffered, non-blocking ZMQ communication.
babysitter_service.py (The Router)
Responsibility: A simple, high-performance process that acts as the intermediary. It receives events from TESTRADE's IPC client and writes them to Redis. It also routes commands from consumers back to TESTRADE.
Redis (The Message Bus)
Responsibility: The central event store and top of the horseshoe. It decouples the producer from all consumers. All system-wide data is available here in specific, named streams.
gui_backend.py (The Primary Consumer)
Responsibility: A consumer. It reads data only from Redis to serve the front-end GUI via WebSockets. It sends user commands back into the system using its own BulletproofBabysitterIPCClient.
intellisense/main.py (The Analysis Platform)
Responsibility: A specialized tool with two distinct roles.
Consumer Role: In live mode, it connects only to Redis to capture and log all event data for later analysis.
Simulator Role: In offline mode, it runs its own instance of the TESTRADE core to replay and analyze the captured logs. This core instance must be managed on-demand via API calls and must never be running by default.
