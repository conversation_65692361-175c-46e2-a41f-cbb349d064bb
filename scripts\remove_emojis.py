#!/usr/bin/env python3
"""
Script to remove emojis from the GUI HTML file
"""
import re

def remove_emojis_from_file(file_path):
    """Remove emojis from the specified file"""
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define emoji patterns to remove
    emoji_replacements = {
        '💾': '',
        '✅': '',
        '⚠️': '',
        '🚨': '',
        '❌': '',
        '🔄': '',
        '📊': '',
        '🔧': '',
        '📝': '',
        '📂': '',
        '▶️': '',
        '⏹️': '',
        '🕐': '',
        '🖥️': '',
        '📱': '',
        '⚡': '',
        '🔥': '',
        '💪': '',
        '🎉': '',
        '🎊': '',
        '🎁': '',
        '🎈': '',
        '🎀': '',
        '🎂': '',
        '🎄': '',
        '🎆': '',
        '🎇': '',
        '🎋': '',
        '🎌': '',
        '🎍': '',
        '🎎': '',
        '🎏': '',
        '🎐': '',
        '🎑': '',
        '🎒': '',
        '🎓': '',
        '🎖️': '',
        '🎗️': '',
        '🎙️': '',
        '🎚️': '',
        '🎛️': '',
        '🎜': '',
        '🎝': '',
        '🎞️': '',
        '🎟️': '',
        '🎠': '',
        '🎡': '',
        '🎢': '',
        '🎣': '',
        '🎤': '',
        '🎥': '',
        '🎦': '',
        '🎧': '',
        '🎨': '',
        '🎩': '',
        '🎪': '',
        '🎫': '',
        '🎬': '',
        '🎭': '',
        '🎮': '',
        '🎯': '',
        '🎰': '',
        '🎱': '',
        '🎲': '',
        '🎳': '',
        '🎴': '',
        '🎵': '',
        '🎶': '',
        '🎷': '',
        '🎸': '',
        '🎹': '',
        '🎺': '',
        '🎻': '',
        '🎼': '',
        '🎽': '',
        '🎾': '',
        '🎿': '',
        '🏀': '',
        '🏁': '',
        '🏂': '',
        '🏃': '',
        '🏄': '',
        '🏅': '',
        '🏆': '',
        '🏇': '',
        '🏈': '',
        '🏉': '',
        '🏊': '',
        '🏋️': '',
        '🏌️': '',
        '🏍️': '',
        '🏎️': '',
        '🏏': '',
        '🏐': '',
        '🏑': '',
        '🏒': '',
        '🏓': '',
        '🏔️': '',
        '🏕️': '',
        '🏖️': '',
        '🏗️': '',
        '🏘️': '',
        '🏙️': '',
        '🏚️': '',
        '🏛️': '',
        '🏜️': '',
        '🏝️': '',
        '🏞️': '',
        '🏟️': '',
        '🏠': '',
        '🏡': '',
        '🏢': '',
        '🏣': '',
        '🏤': '',
        '🏥': '',
        '🏦': '',
        '🏧': '',
        '🏨': '',
        '🏩': '',
        '🏪': '',
        '🏫': '',
        '🏬': '',
        '🏭': '',
        '🏮': '',
        '🏯': '',
        '🏰': '',
        '🏱': '',
        '🏲': '',
        '🏳️': '',
        '🏴': '',
        '🏵️': '',
        '🏶': '',
        '🏷️': '',
        '🏸': '',
        '🏹': '',
        '🏺': '',
        '🏻': '',
        '🏼': '',
        '🏽': '',
        '🏾': '',
        '🏿': '',
    }
    
    # Apply replacements
    for emoji, replacement in emoji_replacements.items():
        content = content.replace(emoji, replacement)
    
    # Remove any remaining emoji-like characters using regex
    # This pattern matches most Unicode emoji ranges
    emoji_pattern = re.compile(
        "["
        "\U0001F600-\U0001F64F"  # emoticons
        "\U0001F300-\U0001F5FF"  # symbols & pictographs
        "\U0001F680-\U0001F6FF"  # transport & map symbols
        "\U0001F1E0-\U0001F1FF"  # flags (iOS)
        "\U00002702-\U000027B0"  # dingbats
        "\U000024C2-\U0001F251"  # enclosed characters
        "]+", 
        flags=re.UNICODE
    )
    
    content = emoji_pattern.sub('', content)
    
    # Write the file back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Emojis removed from {file_path}")

if __name__ == "__main__":
    remove_emojis_from_file("gui/portrait_trading_gui.html")
