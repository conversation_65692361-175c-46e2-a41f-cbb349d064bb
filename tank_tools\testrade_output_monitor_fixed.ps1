# testrade_output_monitor.ps1 - Monitor TESTRADE data output rate
param(
    [string]$ProcessName = "python",
    [string]$OutputPath = "C:\TESTRADE\data",
    [int]$IntervalSeconds = 5,
    [int]$MonitorMinutes = 60,
    [switch]$ShowDetails
)

# Track file changes and sizes
class OutputMonitor {
    [hashtable]$PreviousFileSizes
    [datetime]$StartTime
    [System.Collections.ArrayList]$Measurements
    
    OutputMonitor() {
        $this.PreviousFileSizes = @{}
        $this.StartTime = Get-Date
        $this.Measurements = New-Object System.Collections.ArrayList
    }
    
    [PSCustomObject]MeasureOutputRate([string]$path) {
        $currentFiles = @{}
        $totalGrowth = 0
        $newFiles = 0
        $modifiedFiles = 0
        
        try {
            if (Test-Path $path) {
                Get-ChildItem -Path $path -Recurse -File -ErrorAction SilentlyContinue | ForEach-Object {
                    try {
                        $currentFiles[$_.FullName] = $_.Length
                        
                        if ($this.PreviousFileSizes.ContainsKey($_.FullName)) {
                            # Existing file - check growth
                            $growth = $_.Length - $this.PreviousFileSizes[$_.FullName]
                            if ($growth -gt 0) {
                                $totalGrowth += $growth
                                $modifiedFiles++
                            }
                        } else {
                            # New file
                            $totalGrowth += $_.Length
                            $newFiles++
                        }
                    } catch {
                        # Skip files that can't be accessed
                    }
                }
            }
        } catch {
            # Handle path access errors gracefully
        }
        
        $this.PreviousFileSizes = $currentFiles
        
        return [PSCustomObject]@{
            TotalGrowthBytes = $totalGrowth
            NewFiles = $newFiles
            ModifiedFiles = $modifiedFiles
            TotalFiles = $currentFiles.Count
            Timestamp = Get-Date
        }
    }
}

# Function to check if TESTRADE process is running
function Test-TESTRADEProcess {
    param([string]$ProcessName)
    
    try {
        # Look for python processes running TESTRADE-related scripts
        $processes = Get-Process $ProcessName -ErrorAction SilentlyContinue
        if ($processes) {
            foreach ($proc in $processes) {
                try {
                    $commandLine = (Get-CimInstance Win32_Process -Filter "ProcessId = $($proc.Id)").CommandLine
                    if ($commandLine -and ($commandLine -like "*testrade*" -or $commandLine -like "*ApplicationCore*" -or $commandLine -like "*run_headless_core*")) {
                        return $true
                    }
                } catch {
                    # Can't access command line, but process exists
                }
            }
        }
        return $false
    } catch {
        return $false
    }
}

# Initialize monitor
$monitor = [OutputMonitor]::new()
$endTime = (Get-Date).AddMinutes($MonitorMinutes)

Write-Host "🔍 TESTRADE Output Rate Monitor" -ForegroundColor Green
Write-Host "Process: $ProcessName (TESTRADE-related)" -ForegroundColor Cyan
Write-Host "Path: $OutputPath" -ForegroundColor Cyan  
Write-Host "Interval: $IntervalSeconds seconds" -ForegroundColor Cyan
Write-Host "Duration: $MonitorMinutes minutes" -ForegroundColor Cyan
Write-Host "=" * 80

while ((Get-Date) -lt $endTime) {
    try {
        # Check if TESTRADE process is running
        $processRunning = Test-TESTRADEProcess -ProcessName $ProcessName
        
        # Measure output
        $measurement = $monitor.MeasureOutputRate($OutputPath)
        
        # Calculate rates (handle division by zero)
        $growthMB = if ($measurement.TotalGrowthBytes -gt 0) { [math]::Round($measurement.TotalGrowthBytes / 1MB, 3) } else { 0 }
        $growthMBperSec = if ($IntervalSeconds -gt 0) { [math]::Round($growthMB / $IntervalSeconds, 3) } else { 0 }
        $growthKBperSec = if ($IntervalSeconds -gt 0) { [math]::Round(($measurement.TotalGrowthBytes / 1KB) / $IntervalSeconds, 1) } else { 0 }
        
        # Store measurement
        $dataPoint = [PSCustomObject]@{
            Timestamp = $measurement.Timestamp
            ElapsedMinutes = [math]::Round(($measurement.Timestamp - $monitor.StartTime).TotalMinutes, 1)
            GrowthMB = $growthMB
            MBperSecond = $growthMBperSec
            KBperSecond = $growthKBperSec
            NewFiles = $measurement.NewFiles
            ModifiedFiles = $measurement.ModifiedFiles
            TotalFiles = $measurement.TotalFiles
            ProcessRunning = $processRunning
        }
        $null = $monitor.Measurements.Add($dataPoint)
        
        # Display current measurement
        Clear-Host
        Write-Host "🔍 TESTRADE Output Rate Monitor - Live Analysis" -ForegroundColor Green
        Write-Host "=" * 80
        Write-Host "Time: $($measurement.Timestamp.ToString('HH:mm:ss'))" -ForegroundColor Cyan
        Write-Host "Elapsed: $($dataPoint.ElapsedMinutes) minutes" -ForegroundColor Cyan
        Write-Host "Process Status: $(if($processRunning){'🟢 RUNNING'}else{'🔴 NOT RUNNING'})" -ForegroundColor $(if($processRunning){'Green'}else{'Red'})
        Write-Host ""
        
        # Current interval stats
        Write-Host "📊 CURRENT INTERVAL ($IntervalSeconds seconds):" -ForegroundColor Yellow
        Write-Host "  Data Output:    $growthMB MB" -ForegroundColor White
        Write-Host "  Output Rate:    $growthMBperSec MB/s" -ForegroundColor $(
            if ($growthMBperSec -gt 10) { "Red" }
            elseif ($growthMBperSec -gt 5) { "Yellow" } 
            else { "Green" }
        )
        Write-Host "  Output Rate:    $growthKBperSec KB/s" -ForegroundColor White
        Write-Host "  New Files:      $($measurement.NewFiles)" -ForegroundColor White
        Write-Host "  Modified Files: $($measurement.ModifiedFiles)" -ForegroundColor White
        Write-Host "  Total Files:    $($measurement.TotalFiles)" -ForegroundColor White
        Write-Host ""
        
        # Running averages (last 10 measurements)
        if ($monitor.Measurements.Count -ge 5) {
            $recent = $monitor.Measurements | Select-Object -Last 10
            $avgMBperSec = [math]::Round(($recent | Measure-Object MBperSecond -Average).Average, 3)
            $maxMBperSec = [math]::Round(($recent | Measure-Object MBperSecond -Maximum).Maximum, 3)
            $totalOutputMB = [math]::Round(($monitor.Measurements | Measure-Object GrowthMB -Sum).Sum, 2)
            
            Write-Host "📈 RUNNING STATISTICS:" -ForegroundColor Yellow
            Write-Host "  Average Rate:   $avgMBperSec MB/s (last 10 intervals)" -ForegroundColor White
            Write-Host "  Peak Rate:      $maxMBperSec MB/s" -ForegroundColor White
            Write-Host "  Total Output:   $totalOutputMB MB since start" -ForegroundColor White
            if ($dataPoint.ElapsedMinutes -gt 0) {
                Write-Host "  Session Rate:   $([math]::Round($totalOutputMB / $dataPoint.ElapsedMinutes, 3)) MB/min average" -ForegroundColor White
            }
            Write-Host ""
        }
        
        # Show details if requested
        if ($ShowDetails -and $measurement.ModifiedFiles -gt 0) {
            Write-Host "📁 ACTIVE FILES (this interval):" -ForegroundColor Yellow
            try {
                Get-ChildItem -Path $OutputPath -Recurse -File -ErrorAction SilentlyContinue | 
                    Where-Object { $_.LastWriteTime -gt (Get-Date).AddSeconds(-$IntervalSeconds * 2) } |
                    Sort-Object LastWriteTime -Descending |
                    Select-Object -First 10 |
                    ForEach-Object {
                        $sizeMB = [math]::Round($_.Length / 1MB, 2)
                        Write-Host "  $($_.Name) - $sizeMB MB - $($_.LastWriteTime.ToString('HH:mm:ss'))" -ForegroundColor Gray
                    }
            } catch {
                Write-Host "  Unable to access file details" -ForegroundColor Gray
            }
            Write-Host ""
        }
        
        # Alerts
        if ($growthMBperSec -gt 50) {
            Write-Host "🚨 HIGH OUTPUT RATE DETECTED!" -ForegroundColor Red -BackgroundColor Yellow
        } elseif ($growthMBperSec -gt 20) {
            Write-Host "⚠️  Elevated output rate detected" -ForegroundColor Yellow
        }
        
        if (!$processRunning) {
            Write-Host "⚠️  TESTRADE process not detected - no new output expected" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "Next measurement in $IntervalSeconds seconds... (Ctrl+C to stop)" -ForegroundColor Gray
        
        Start-Sleep $IntervalSeconds
        
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        Start-Sleep 5
    }
}

# Final summary
Write-Host ""
Write-Host "📋 FINAL SUMMARY:" -ForegroundColor Green
if ($monitor.Measurements.Count -gt 0) {
    $totalMB = ($monitor.Measurements | Measure-Object GrowthMB -Sum).Sum
    $avgMBperSec = ($monitor.Measurements | Measure-Object MBperSecond -Average).Average
    $maxMBperSec = ($monitor.Measurements | Measure-Object MBperSecond -Maximum).Maximum
    $totalMinutes = $monitor.Measurements[-1].ElapsedMinutes
    
    Write-Host "Total Data Output: $([math]::Round($totalMB, 2)) MB" -ForegroundColor White
    Write-Host "Average Rate: $([math]::Round($avgMBperSec, 3)) MB/s" -ForegroundColor White
    Write-Host "Peak Rate: $([math]::Round($maxMBperSec, 3)) MB/s" -ForegroundColor White
    Write-Host "Session Duration: $totalMinutes minutes" -ForegroundColor White
    if ($totalMinutes -gt 0) {
        Write-Host "Overall Rate: $([math]::Round($totalMB / $totalMinutes, 2)) MB/min" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "Monitor completed successfully!" -ForegroundColor Green
