"""
Task NATIVE-ISOLATE1: Test with NO GUI

This script runs a modified version of the application without the GUI
to determine if the Tkinter GUI itself is the source of the persistent ~69MB native leak.

It will:
1. Initialize the application components (except GUI)
2. Run for 15 minutes
3. Take a tracemalloc snapshot
4. Report memory usage

Usage:
    python scripts\native_isolate1.py
"""

import os
import sys
import time
import logging
import tracemalloc
import datetime
import psutil
import threading
from pathlib import Path

# Add the parent directory to the path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"native_isolate1_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

# Configure a file handler for the logs
file_handler = logging.FileHandler(log_file, mode="w", encoding="utf-8")
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter("[%(asctime)s] [%(levelname)-8s] [%(name)s] %(message)s")
file_handler.setFormatter(file_formatter)

# Configure a console handler for the logs
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter("[%(levelname)-8s] %(message)s")
console_handler.setFormatter(console_formatter)

# Get the root logger and add the handlers
root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG)
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

# Create a logger for this script
logger = logging.getLogger("native_isolate1")

# Constants
WAIT_DURATION_MINUTES = 15
SNAPSHOT_INTERVAL_MINUTES = 5

def log_memory_usage():
    """Log current memory usage and network I/O."""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    logger.info(f"Memory usage: RSS={memory_info.rss / (1024 * 1024):.2f} MB, VMS={memory_info.vms / (1024 * 1024):.2f} MB")

    # Log network I/O
    try:
        net_io_counters = psutil.net_io_counters()
        logger.info(f"Network I/O: Bytes sent={net_io_counters.bytes_sent / (1024):.2f} KB, Bytes received={net_io_counters.bytes_recv / (1024):.2f} KB")
    except Exception as e:
        logger.error(f"Failed to get network I/O counters: {e}")

def take_tracemalloc_snapshot(snapshot_dir):
    """Take a tracemalloc snapshot and save it to a file."""
    snapshot = tracemalloc.take_snapshot()
    snapshot_path = os.path.join(snapshot_dir, f"tracemalloc_snapshot_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.pickle")
    snapshot.dump(snapshot_path)
    logger.info(f"Tracemalloc snapshot saved to: {snapshot_path}")

    # Log top 20 statistics
    logger.info("Top 20 memory allocations by size:")
    top_stats = snapshot.statistics('lineno')
    for i, stat in enumerate(top_stats[:20], 1):
        frame = stat.traceback[0]
        filename = os.path.basename(frame.filename)
        logger.info(f"#{i}: {filename}:{frame.lineno}: {stat.size / 1024:.1f} KiB")
        logger.info(f"    {frame.filename}:{frame.lineno}")

def monitor_memory_usage(stop_event, snapshot_dir):
    """Monitor memory usage at regular intervals."""
    start_time = time.time()
    last_snapshot_time = start_time

    # Initialize network I/O counters for rate calculation
    try:
        last_net_io = psutil.net_io_counters()
        last_net_io_time = time.time()
    except Exception as e:
        logger.error(f"Failed to initialize network I/O counters: {e}")
        last_net_io = None
        last_net_io_time = None

    while not stop_event.is_set():
        current_time = time.time()
        elapsed_minutes = (current_time - start_time) / 60

        # Log memory usage every minute
        log_memory_usage()

        # Calculate and log network I/O rate
        if last_net_io is not None:
            try:
                current_net_io = psutil.net_io_counters()
                time_diff = current_time - last_net_io_time

                # Calculate rates in KB/s
                sent_rate = (current_net_io.bytes_sent - last_net_io.bytes_sent) / (1024 * time_diff)
                recv_rate = (current_net_io.bytes_recv - last_net_io.bytes_recv) / (1024 * time_diff)

                logger.info(f"Network I/O Rate: Sent={sent_rate:.2f} KB/s, Received={recv_rate:.2f} KB/s")

                # Update for next iteration
                last_net_io = current_net_io
                last_net_io_time = current_time
            except Exception as e:
                logger.error(f"Failed to calculate network I/O rate: {e}")

        # Take a tracemalloc snapshot every SNAPSHOT_INTERVAL_MINUTES
        if (current_time - last_snapshot_time) / 60 >= SNAPSHOT_INTERVAL_MINUTES:
            take_tracemalloc_snapshot(snapshot_dir)
            last_snapshot_time = current_time

        # Sleep for 60 seconds
        time.sleep(60)

def main():
    """Main function to run the application without GUI."""
    logger.info("Starting native_isolate1.py")
    logger.info(f"Logs will be saved to: {log_file}")

    # Create directory for tracemalloc snapshots
    snapshot_dir = os.path.join(log_dir, f"native_isolate1_snapshots_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}")
    os.makedirs(snapshot_dir, exist_ok=True)
    logger.info(f"Tracemalloc snapshots will be saved to: {snapshot_dir}")

    # Start tracemalloc
    tracemalloc.start()
    logger.info("Tracemalloc started for memory tracking")

    # Import necessary modules
    try:
        from main import setup_main_logging
        from utils.global_config import load_global_config, GlobalConfig
        from utils.symbol_loader import load_symbols_from_csv
        from modules.price_fetching.price_repository import PriceRepository
        from modules.price_fetching.price_fetching_service import PriceFetchingService
        from modules.risk_management.risk_service import RiskManagementService
        from modules.order_management.order_repository import OrderRepository
        from modules.trade_management.position_manager import PositionManager
        import alpaca_trade_api as tradeapi

        logger.info("Successfully imported all required modules")
    except ImportError as e:
        logger.error(f"Failed to import required modules: {e}")
        return

    # Set up logging
    setup_main_logging()
    logger.info("Set up main logging")

    # Disable performance tracking
    try:
        from utils.performance_tracker import enable_performance_tracking
        enable_performance_tracking(False)
        logger.info("Performance tracking explicitly disabled")
    except ImportError:
        logger.warning("Could not import performance_tracker module")

    # Load global config
    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "utils", "control.json")
    config = load_global_config(config_path)
    logger.info("Loaded global config")

    # Ensure price fetching service and lightspeed broker are disabled
    config.disable_price_fetching_service = True
    config.disable_lightspeed_broker = True
    logger.info("Ensured price fetching service and lightspeed broker are disabled in config")

    # Load symbols
    try:
        from config import TRADEABLE_SYMBOLS_CSV
        load_symbols_from_csv(TRADEABLE_SYMBOLS_CSV, fix_bom=True, force_reload=True)
        logger.info(f"Loaded symbols from {TRADEABLE_SYMBOLS_CSV}")
    except Exception as e:
        logger.error(f"Failed to load symbols: {e}")
        return

    # Initialize Alpaca REST client
    try:
        # Check if the config has Alpaca API credentials
        if hasattr(config, 'alpaca_api_key') and hasattr(config, 'alpaca_api_secret') and hasattr(config, 'alpaca_api_url'):
            alpaca_config = {
                'key_id': config.alpaca_api_key,
                'secret_key': config.alpaca_api_secret,
                'base_url': config.alpaca_api_url
            }
        else:
            # Use default values from environment variables or hardcoded test values
            alpaca_config = {
                'key_id': os.environ.get('ALPACA_API_KEY', 'PKZGC0NXVXR9YHCGMHYR'),
                'secret_key': os.environ.get('ALPACA_API_SECRET', 'FPDCYmUF8JQlCgPFIrIYB1yFQgRXpOwn2SgkbTNy'),
                'base_url': os.environ.get('ALPACA_API_URL', 'https://paper-api.alpaca.markets')
            }
            logger.warning("Using default/environment Alpaca API credentials")

        rest_client = tradeapi.REST(**alpaca_config)
        logger.info("Initialized Alpaca REST client")
    except Exception as e:
        logger.error(f"Failed to initialize Alpaca REST client: {e}")
        rest_client = None

    # Initialize PriceRepository
    try:
        price_repository = PriceRepository(
            risk_service=None,
            rest_client=rest_client,
            config_service=config
        )
        logger.info("Initialized PriceRepository")
    except Exception as e:
        logger.error(f"Failed to initialize PriceRepository: {e}")
        price_repository = None

    # Initialize OrderRepository
    try:
        order_repository = OrderRepository(broker_service=None)
        logger.info("Initialized OrderRepository")
    except Exception as e:
        logger.error(f"Failed to initialize OrderRepository: {e}")
        order_repository = None

    # Initialize PositionManager
    try:
        position_manager = PositionManager()
        logger.info("Initialized PositionManager")

        # Set position_manager in OrderRepository
        if order_repository:
            order_repository.set_position_manager(position_manager)
            logger.info("Set position_manager in OrderRepository")
    except Exception as e:
        logger.error(f"Failed to initialize PositionManager: {e}")
        position_manager = None

    # Initialize RiskManagementService
    try:
        if price_repository and order_repository and position_manager:
            risk_service = RiskManagementService(
                config_service=config,
                price_provider=price_repository,
                order_repository=order_repository,
                position_manager=position_manager,
                gui_logger_func=None
            )
            logger.info("Initialized RiskManagementService")

            # Set risk_service in PriceRepository
            price_repository.set_risk_service(risk_service)
            logger.info("Set risk_service in PriceRepository")
        else:
            logger.error("Cannot initialize RiskManagementService: Missing dependencies")
            risk_service = None
    except Exception as e:
        logger.error(f"Failed to initialize RiskManagementService: {e}")
        risk_service = None

    # Initialize PriceFetchingService (but don't start it)
    try:
        if price_repository and rest_client:
            # Use default values from environment variables or hardcoded test values if not in config
            api_key = os.environ.get('ALPACA_API_KEY', 'PKZGC0NXVXR9YHCGMHYR')
            api_secret = os.environ.get('ALPACA_API_SECRET', 'FPDCYmUF8JQlCgPFIrIYB1yFQgRXpOwn2SgkbTNy')

            if hasattr(config, 'alpaca_api_key') and hasattr(config, 'alpaca_api_secret'):
                api_key = config.alpaca_api_key
                api_secret = config.alpaca_api_secret

            price_service = PriceFetchingService(
                config={
                    'key_id': api_key,
                    'secret_key': api_secret,
                    'sip_url': 'wss://stream.data.alpaca.markets/v2/sip'
                },
                receiver=price_repository,
                initial_symbols=set(),
                rest_client=rest_client,
                tkinter_root_for_callbacks=None
            )
            logger.info("Initialized PriceFetchingService (not started)")
        else:
            logger.error("Cannot initialize PriceFetchingService: Missing dependencies")
            price_service = None
    except Exception as e:
        logger.error(f"Failed to initialize PriceFetchingService: {e}")
        price_service = None

    # Initialize CoreLogicManager
    try:
        from modules.core_logic.core_logic_manager import CoreLogicManager
        core_logic_manager = CoreLogicManager(
            order_repository=order_repository,
            trade_manager_service=None  # We don't have TradeManagerService initialized
        )
        logger.info("Initialized CoreLogicManager")

        # Set CoreLogicManager in OrderRepository
        order_repository.set_core_logic_manager(core_logic_manager)
        logger.info("Set CoreLogicManager in OrderRepository")
    except Exception as e:
        logger.error(f"Failed to initialize CoreLogicManager: {e}")
        core_logic_manager = None

    # Log initial memory usage
    logger.info("Initial memory usage:")
    log_memory_usage()

    # Start memory monitoring thread
    stop_event = threading.Event()
    memory_monitor_thread = threading.Thread(
        target=monitor_memory_usage,
        args=(stop_event, snapshot_dir),
        daemon=True,
        name="MemoryMonitorThread"
    )
    memory_monitor_thread.start()
    logger.info("Started memory monitoring thread")

    # Wait for specified duration
    logger.info(f"Waiting for {WAIT_DURATION_MINUTES} minutes...")
    wait_end_time = time.time() + (WAIT_DURATION_MINUTES * 60)

    try:
        while time.time() < wait_end_time:
            remaining_minutes = int((wait_end_time - time.time()) / 60)
            remaining_seconds = int((wait_end_time - time.time()) % 60)
            logger.info(f"Remaining time: {remaining_minutes} minutes {remaining_seconds} seconds")
            time.sleep(60)  # Update every minute
    except KeyboardInterrupt:
        logger.info("Interrupted by user")

    # Stop memory monitoring thread
    stop_event.set()
    memory_monitor_thread.join(timeout=5)
    logger.info("Stopped memory monitoring thread")

    # Take final tracemalloc snapshot
    logger.info("Taking final tracemalloc snapshot...")
    take_tracemalloc_snapshot(snapshot_dir)

    # Log final memory usage
    logger.info("Final memory usage:")
    log_memory_usage()

    logger.info("Done.")
    logger.info(f"Logs saved to: {log_file}")
    logger.info(f"Tracemalloc snapshots saved to: {snapshot_dir}")

if __name__ == "__main__":
    main()
