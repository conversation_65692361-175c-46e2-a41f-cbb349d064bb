# Suspicious System Incidents Log

## Incident 1: Missing Clean Architecture Work
**Date**: July 1, 2025
**What happened**: 
- Extensive clean architecture refactoring done yesterday (June 30)
- `/interfaces/` directory with ~13+ interface files created
- Work was never committed (left untracked)
- Files mysteriously disappeared after branch switches
- Another Claude instance recreated them today at 19:21 as "damage control"

**Evidence**:
- Stash from June 30 20:38 shows code referencing the interfaces
- Today's commit: "Preserve clean architecture interfaces that were never committed"
- All interface files have today's timestamps (July 1 19:21-19:25)

## Incident 2: Missing Long-Standing Dependency
**Date**: June 30, 2025
**What happened**:
- `alpaca-trade-api` package (installed for 6+ months) suddenly missing
- PriceFetchingService silently failed due to try/except ImportError handling
- Spent hours debugging market data issues before discovering the missing dependency
- No clear reason why a stable package would disappear from virtual environment
- Package is critical for all market data functionality

## Patterns of Concern:

1. **Files disappearing**: Work vanishing between sessions
2. **Dependencies vanishing**: Stable packages suddenly missing  
3. **Timestamp anomalies**: Files showing wrong creation dates
4. **Branch switching issues**: Unusual behavior when switching branches

## Possible Explanations:

### 1. File System Issues (WSL/Windows)
- WSL2 file system sync problems
- Windows Defender quarantining files
- OneDrive or other sync software interfering

### 2. Development Environment Corruption
- Python virtual environment corruption
- Package manager cache issues
- IDE workspace corruption

### 3. Git Configuration Issues
- Aggressive git clean hooks
- Pre-commit hooks deleting files
- Git attributes misconfiguration

### 4. External Interference
- Antivirus/antimalware false positives
- Corporate security software
- Backup software moving files
- Another user/process on the system

### 5. Malicious Activity
- Targeted file deletion
- Environment tampering
- Package manipulation

## Recommended Actions:

1. **Check system logs**:
   ```bash
   # Windows Event Viewer
   # WSL logs
   dmesg | tail -100
   ```

2. **Audit git hooks**:
   ```bash
   ls -la .git/hooks/
   cat .git/config
   ```

3. **Check running processes**:
   ```bash
   ps aux | grep -E "(git|python|clean)"
   ```

4. **Verify file system integrity**:
   ```bash
   # Check for file system errors
   df -h
   mount | grep -E "(cifs|9p)"
   ```

5. **Create protective measures**:
   - Commit work immediately
   - Use local backups
   - Document suspicious incidents
   - Use `git stash -u` for work in progress

## Questions to Investigate:

1. What dependency went missing after 6 months?
2. Are there patterns to when things disappear?
3. Are certain file types more affected?
4. Does this happen across all projects or just TESTRADE?
5. Are there any unusual processes running during these incidents?

---
This log created: July 1, 2025
Will be updated as new incidents occur.