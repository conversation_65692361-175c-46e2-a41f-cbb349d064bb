Document 3 of 4: The "Core Components & Public APIs" Reference
Purpose: To define the public-facing responsibilities and key methods of the most critical classes in the system. This serves as the official API contract between components. Any method not listed here should be considered private and must not be called from outside the class.
1. Class: ApplicationCore (core/application_core.py)
Role: The main trading engine (The TANK). It is an orchestrator that owns and manages all the internal trading services (risk, order management, etc.).
Key Principle: This class is a "black box" to the outside world. Its internal state should not be manipulated directly. It communicates its state via the events it emits.
Public API / Key Methods:
__init__(config_path: str): Initializes the core engine from a configuration file.
start(): Starts all internal services and begins processing. This is a non-blocking call that launches background threads.
stop(): Performs a graceful shutdown of all internal services, threads, and subprocesses. This is the ONLY correct way to stop the engine.
Usage Constraints:
NEVER call any other method on this class from an external component.
This class DOES NOT have a public send_event() method. All event publishing is handled internally via its BulletproofBabysitterIPCClient.
2. Class: BulletproofBabysitterIPCClient (core/bulletproof_ipc_client.py)
Role: The sole, official client for sending data into the TESTRADE event pipeline. It is a self-contained library that handles all ZMQ communication, buffering, and connection resilience.
Key Principle: Any process that needs to produce data for the system (e.g., ApplicationCore, gui_backend.py) must instantiate and use this client.
Public API / Key Methods:
__init__(zmq_context, ipc_config, ...): Initializes the client.
send_data(target_redis_stream: str, data_json_string: str) -> bool: The primary method for sending data. It is non-blocking. It will attempt to send immediately via ZMQ; if it fails (due to a full buffer or connection issue), it automatically writes the data to its internal disk buffer and returns True. It only returns False on a critical, unrecoverable error.
close(): Gracefully shuts down the client, its worker threads, and its ZMQ sockets.
Usage Constraints:
Always instantiate one client per process. Do not share instances across processes.
Always call close() when the parent process is shutting down to ensure all buffers are flushed and sockets are closed cleanly.
3. Application: babysitter_service.py (core/babysitter_service.py)
Role: A standalone, high-performance router process. It is the central hub of the horseshoe.
Key Principle: This is a simple, "dumb" service. It contains no business logic. Its only job is to route messages.
Public API / Key Methods:
This is a standalone application, not a class to be imported. It has no callable API.
It listens for ZMQ PULL connections on ports 5555, 5556, and 5557 for incoming events.
It forwards these events to the configured Redis server.
It also listens for incoming commands from consumers and forwards them to the ApplicationCore's command port (5560).
4. Class: IntelliSenseMasterController (intellisense/controllers/master_controller.py)
Role: The lifecycle manager for a single, on-demand IntelliSense analysis/replay session.
Key Principle: This class, along with its associated IntelliSenseApplicationCore, must not be a long-running singleton. It must be created to run a specific session and destroyed when that session is complete.
Public API / Key Methods:
__init__(application_core, ...): Initializes the controller for a specific session.
create_test_session(session_config) -> str: Creates the configuration for a new test session and returns its session_id.
start_replay_session(session_id: str) -> bool: Begins the replay and analysis for a created session. This is a non-blocking call.
stop_replay_session(session_id: str) -> bool: Requests a graceful stop of an in-progress replay.
shutdown(): The only correct way to terminate the session. This method calls stop() on its internal IntelliSenseApplicationCore, ensuring all resources and processes are cleanly released.
Usage Constraints:
The API server (intellisense/main.py) is responsible for managing the lifecycle of these objects. It must call shutdown() when a user deletes a session via the API.