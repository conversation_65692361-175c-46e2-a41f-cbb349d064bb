# TANK Memory Leak Detection Suite

## 🔍 Overview

Comprehensive PowerShell-based memory leak detection system for TESTRADE ApplicationCore (TANK) with advanced statistical analysis, real-time monitoring, and automated alerting.

## 📦 Components

### **1. tank_leak_detector.ps1** - Advanced Real-Time Detection
- **Statistical Analysis**: Linear regression with R-squared confidence scoring
- **Trend Classification**: 6-level classification from CRITICAL_LEAK to DECREASING
- **Real-Time Monitoring**: Configurable sampling intervals with sliding window analysis
- **Automated Alerts**: Windows notifications with severity-based messaging
- **CSV Logging**: Comprehensive data logging for historical analysis

### **2. tank_leak_analyzer.ps1** - Historical Analysis & Reporting
- **Comprehensive Statistics**: Memory usage, growth rates, volatility analysis
- **Health Scoring**: 0-100 health score with automated rating system
- **Trend Distribution**: Statistical breakdown of system behavior patterns
- **HTML Reports**: Professional reports with visual indicators and recommendations
- **System Assessment**: Automated health assessment with actionable recommendations

### **3. tank_leak_quickstart.ps1** - Easy Launcher & Demo
- **Quick Start**: One-command launch for detection and analysis
- **Demo Mode**: Time-limited demonstration with fast sampling
- **Process Detection**: Automatic process discovery and validation
- **Mode Selection**: detect, analyze, demo modes for different use cases

## 🚀 Quick Start

### **Start Real-Time Detection:**
```powershell
.\tank_leak_quickstart.ps1 -Mode detect
```

### **Analyze Historical Data:**
```powershell
.\tank_leak_quickstart.ps1 -Mode analyze
```

### **Run Demo (5 minutes):**
```powershell
.\tank_leak_quickstart.ps1 -Mode demo -DemoMinutes 5
```

## 📊 Advanced Usage

### **Custom Detection Parameters:**
```powershell
.\tank_leak_detector.ps1 -ProcessName "python" -SampleIntervalSeconds 30 -AnalysisWindowMinutes 60 -AlertThresholdMB 100
```

### **Historical Analysis with Custom Window:**
```powershell
.\tank_leak_analyzer.ps1 -CsvFile "custom_data.csv" -AnalysisHours 48 -GenerateReport
```

## 🔬 Statistical Analysis Features

### **Linear Regression Analysis:**
- **Slope Calculation**: Growth rate in MB per sampling interval
- **R-Squared Confidence**: Statistical confidence in trend analysis (0-100%)
- **Projection Modeling**: 1-hour and 4-hour memory usage projections
- **Volatility Analysis**: Memory usage stability assessment

### **Trend Classification System:**
```
CRITICAL_LEAK    : >10 MB/interval growth (IMMEDIATE ACTION)
LEAK_DETECTED    : >5 MB/interval growth (HIGH PRIORITY)
MODERATE_GROWTH  : >2 MB/interval growth (MONITOR CLOSELY)
SLOW_GROWTH      : >0.5 MB/interval growth (NORMAL OPERATION)
STABLE           : ±0.5 MB/interval (EXCELLENT)
DECREASING       : <-0.5 MB/interval (CLEANUP/REDUCED LOAD)
```

### **Health Scoring Algorithm:**
- **Base Score**: 100 points
- **Leak Detection Rate**: -15 to -50 points based on frequency
- **Critical Events**: -30 points per critical leak event
- **High Growth Rate**: -20 points for sustained high growth
- **Memory Volatility**: -15 points for high volatility (>30%)

## 📈 Monitoring Metrics

### **Core Metrics Collected:**
- **Memory Usage**: Working Set (MB/GB)
- **System Resources**: Handle count, thread count
- **Performance**: Page faults, CPU time
- **Statistical**: Growth rate, confidence, volatility
- **Classification**: Trend type, severity, leak detection status

### **Advanced Analytics:**
- **Moving Window Analysis**: Configurable time window (default: 30 minutes)
- **Confidence Scoring**: R-squared statistical confidence measurement
- **Projection Modeling**: Linear extrapolation for future memory usage
- **Volatility Assessment**: Standard deviation-based stability analysis

## 🚨 Alert System

### **Severity Levels:**
- **CRITICAL**: >10 MB/interval growth - Immediate restart recommended
- **HIGH**: >5 MB/interval growth - Plan restart within hours
- **MEDIUM**: >2 MB/interval growth - Monitor closely
- **LOW**: >0.5 MB/interval growth - Normal operation

### **Alert Mechanisms:**
- **Windows Notifications**: Balloon tip notifications with severity and growth rate
- **Console Alerts**: Color-coded visual alerts with recommendations
- **CSV Logging**: Persistent alert logging for historical analysis
- **HTML Reports**: Comprehensive alert summaries in generated reports

## 📊 Data Analysis & Reporting

### **CSV Data Format:**
```
Timestamp,MemoryMB,MemoryGB,HandleCount,ThreadCount,PageFaultsMB,TrendType,GrowthRate,Confidence,Severity,LeakDetected
```

### **HTML Report Features:**
- **Executive Summary**: Health score, memory usage, leak detection rate
- **Trend Analysis**: Statistical breakdown with visual indicators
- **Health Assessment**: Automated scoring with color-coded status
- **Recommendations**: Actionable recommendations based on analysis
- **Professional Styling**: Clean, readable format suitable for stakeholders

## 🔧 Configuration Options

### **Detection Parameters:**
- **SampleIntervalSeconds**: Frequency of memory sampling (default: 60)
- **AnalysisWindowMinutes**: Rolling window for trend analysis (default: 30)
- **AlertThresholdMB**: Memory growth threshold for alerts (default: 50)
- **EnableAlerts**: Windows notification system (default: true)
- **EnableCSVLogging**: Data persistence (default: true)

### **Analysis Parameters:**
- **AnalysisHours**: Historical data window (default: 24)
- **GenerateReport**: HTML report generation (default: true)
- **ReportFile**: Custom report filename

## 🎯 Use Cases

### **Development & Testing:**
- **Memory Leak Validation**: Verify fixes for known memory issues
- **Performance Testing**: Monitor memory behavior under load
- **Regression Testing**: Detect new memory leaks in code changes
- **Baseline Establishment**: Create memory usage baselines for comparison

### **Production Monitoring:**
- **Continuous Monitoring**: 24/7 memory leak detection
- **Proactive Maintenance**: Early warning system for memory issues
- **Capacity Planning**: Memory usage trend analysis for scaling decisions
- **Incident Response**: Rapid detection and classification of memory issues

### **Operational Analysis:**
- **Historical Trends**: Long-term memory usage pattern analysis
- **System Health**: Comprehensive health scoring and assessment
- **Performance Optimization**: Identify memory usage optimization opportunities
- **Maintenance Planning**: Data-driven restart and maintenance scheduling

## 🔍 Troubleshooting

### **Common Issues:**
- **Process Not Found**: Verify ApplicationCore is running and process name is correct
- **Insufficient Data**: Requires minimum 5 samples for statistical analysis
- **Permission Errors**: Run PowerShell as Administrator for full process access
- **CSV File Issues**: Ensure write permissions in script directory

### **Performance Considerations:**
- **Sampling Frequency**: Higher frequency provides better detection but uses more resources
- **Analysis Window**: Larger windows provide better statistical confidence but use more memory
- **CSV Logging**: Disable for minimal resource usage in production environments

## 📋 Best Practices

### **Detection Setup:**
- **Sampling Interval**: 30-60 seconds for production, 15 seconds for testing
- **Analysis Window**: 30-60 minutes for balanced accuracy and responsiveness
- **Alert Thresholds**: Adjust based on normal memory usage patterns
- **Continuous Monitoring**: Run during peak usage periods for best results

### **Analysis & Reporting:**
- **Regular Analysis**: Daily analysis of historical data
- **Trend Monitoring**: Weekly trend analysis for long-term patterns
- **Health Scoring**: Monitor health score trends over time
- **Report Sharing**: Share HTML reports with stakeholders for transparency

This comprehensive leak detection suite provides enterprise-grade memory monitoring capabilities specifically designed for TESTRADE's production environment, ensuring optimal system performance and proactive issue detection.
