import time
import threading
from collections import deque
from dataclasses import dataclass
from typing import Deque, Dict, Any, Optional, List, Tuple

# Import performance tracking utilities
from utils.performance_tracker import create_timestamp_dict, add_timestamp, is_performance_tracking_enabled

@dataclass
class MeltdownConfig:
    # Time windows
    short_window_sec: float = 30.0
    long_window_sec: float = 90.0
    price_drop_window: float = 1.5       # Shorter window for fast moves
    momentum_window: float = 3.0

    # Thresholds
    heavy_sell_multiplier: float = 2.5    # Lowered for volatile premarket
    price_drop_pct: float = 0.025        # 2.5% drop threshold
    consecutive_bid_hits: int = 3        # Fewer hits needed for low-float
    block_multiplier: float = 4.0         # Big blocks more significant
    spread_widen_factor: float = 3.0
    sell_ratio_threshold: float = 0.85
    spread_threshold_pct: float = 0.20    # 20% of spread for side detection
    halt_inactivity: float = 10.0         # Shorter halt detection
    halt_min_trades: int = 5

    # Risk scoring weights
    weights: Dict[str, float] = None

    def __post_init__(self):
        self.weights = {
            'heavy_selling': 0.25,
            'fast_drop': 0.3,
            'consecutive_bids': 0.15,
            'big_block': 0.1,
            'spread_widen': 0.1,
            'one_sided': 0.1
        }

class PriceMeltdownDetector:
    def __init__(self, config: MeltdownConfig = None):
        self.config = config or MeltdownConfig()
        self.lock = threading.RLock()

        # Data storage
        self.trade_history_short: Dict[str, Deque[Dict]] = {}
        self.trade_history_long: Dict[str, Deque[Dict]] = {}
        self.quote_history_short: Dict[str, Deque[Dict]] = {}
        self.latest_quote_map: Dict[str, Dict] = {}

        # Cached metrics
        self.metrics_cache = {
            'long_term_avg_size': {},
            'min_spread': {},
            'current_spread': {}
        }

    def process_quote(self, symbol: str, bid: float, ask: float, ts: float = None, perf_timestamps: Dict[str, float] = None):
        symbol = symbol.upper()
        ts = ts or time.time()

        # Create or update performance timestamps only if performance tracking is enabled
        if perf_timestamps is None and is_performance_tracking_enabled():
            perf_timestamps = create_timestamp_dict()

        # Only add timestamp if performance tracking is enabled and perf_timestamps exists
        if is_performance_tracking_enabled() and perf_timestamps is not None:
            add_timestamp(perf_timestamps, 'quote_processing_start')

        with self.lock:
            # Update latest quote
            self.latest_quote_map[symbol] = {
                'bid': bid,
                'ask': ask,
                'timestamp': ts,
                'spread': ask - bid
            }

            # Maintain quote history
            q_deque = self.quote_history_short.setdefault(symbol, deque())
            q_deque.append(self.latest_quote_map[symbol])

            # Prune old quotes
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'prune_deque_start')
            self._prune_deque(q_deque, ts, self.config.short_window_sec)
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'prune_deque_end')

            # Update spread metrics
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'update_spread_metrics_start')
            self._update_spread_metrics(symbol)
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'update_spread_metrics_end')

        if is_performance_tracking_enabled() and perf_timestamps is not None:
            add_timestamp(perf_timestamps, 'quote_processing_end')
        return perf_timestamps

    def process_trade(self, symbol: str, price: float, size: float, ts: float = None, perf_timestamps: Dict[str, float] = None):
        symbol = symbol.upper()
        ts = ts or time.time()

        # Create or update performance timestamps only if performance tracking is enabled
        if perf_timestamps is None and is_performance_tracking_enabled():
            perf_timestamps = create_timestamp_dict()

        # Only add timestamp if performance tracking is enabled and perf_timestamps exists
        if is_performance_tracking_enabled() and perf_timestamps is not None:
            add_timestamp(perf_timestamps, 'trade_processing_start')

        with self.lock:
            # Get latest quote for side inference
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'quote_lookup_start')
            quote = self.latest_quote_map.get(symbol, {})
            bid = quote.get('bid', 0)
            ask = quote.get('ask', 0)
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'quote_lookup_end')

            # Infer trade side
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'side_inference_start')
            side = self._infer_side(price, bid, ask, quote.get('spread', 0))
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'side_inference_end')

            trade_record = {
                'price': price,
                'size': size,
                'timestamp': ts,
                'side': side,
                'bid': bid,
                'ask': ask
            }

            # Update trade histories
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'update_histories_start')
            for store, window in [(self.trade_history_short, self.config.short_window_sec),
                                (self.trade_history_long, self.config.long_window_sec)]:
                deque = store.setdefault(symbol, deque())
                deque.append(trade_record)
                self._prune_deque(deque, ts, window)
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'update_histories_end')

            # Update cached metrics
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'update_metrics_start')
            self._update_size_metrics(symbol)
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'update_metrics_end')

        if is_performance_tracking_enabled() and perf_timestamps is not None:
            add_timestamp(perf_timestamps, 'trade_processing_end')
        return perf_timestamps

    def _infer_side(self, price: float, bid: float, ask: float, spread: float) -> str:
        if spread < 1e-9:
            return "UNKNOWN"

        threshold = spread * self.config.spread_threshold_pct
        if abs(price - ask) <= threshold:
            return "BUY"
        if abs(price - bid) <= threshold:
            return "SELL"
        return "UNKNOWN"

    def analyze_symbol(self, symbol: str, perf_timestamps: Dict[str, float] = None) -> Dict[str, Any]:
        symbol = symbol.upper()

        # Create or update performance timestamps only if performance tracking is enabled
        if perf_timestamps is None and is_performance_tracking_enabled():
            perf_timestamps = create_timestamp_dict()

        # Only add timestamp if performance tracking is enabled and perf_timestamps exists
        if is_performance_tracking_enabled() and perf_timestamps is not None:
            add_timestamp(perf_timestamps, 'analyze_symbol_start')

        with self.lock:
            # Check if we have enough data
            if symbol not in self.trade_history_short or len(self.trade_history_short[symbol]) < 3:
                if is_performance_tracking_enabled() and perf_timestamps is not None:
                    add_timestamp(perf_timestamps, 'analyze_symbol_end')
                result = self._halted_response()
                result['perf_timestamps'] = perf_timestamps
                return result

            # Check if market is halted
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'halt_check_start')
            if self.is_halted(symbol):
                if is_performance_tracking_enabled() and perf_timestamps is not None:
                    add_timestamp(perf_timestamps, 'halt_check_end')
                    add_timestamp(perf_timestamps, 'analyze_symbol_end')
                result = self._halted_response()
                result['perf_timestamps'] = perf_timestamps
                return result
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'halt_check_end')

            # Compute risk factors
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'risk_factors_start')
            factors = {
                'heavy_selling': self.is_heavy_selling(symbol),
                'fast_drop': self.is_fast_price_drop(symbol),
                'consecutive_bids': self.has_consecutive_bid_hits(symbol),
                'big_block': self.has_big_block(symbol),
                'spread_widen': self.is_spread_widened(symbol),
                'one_sided': self.is_one_sided_tape(symbol),
                'selling_momentum': self.has_selling_momentum(symbol)
            }
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'risk_factors_end')

            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'risk_score_calc_start')
            risk_score = self.calculate_risk_score(factors)
            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'risk_score_calc_end')

            if is_performance_tracking_enabled() and perf_timestamps is not None:
                add_timestamp(perf_timestamps, 'analyze_symbol_end')

            return {
                'is_halted': False,
                'risk_score': risk_score,
                'factors': factors,
                'metrics': {
                    'current_price': self._current_price(symbol),
                    'avg_spread': self.metrics_cache['current_spread'].get(symbol, 0),
                    'volume_profile': self._get_volume_profile(symbol)
                },
                'perf_timestamps': perf_timestamps
            }

    def check_meltdown_conditions(self, symbol: str) -> Dict[str, Any]:
        symbol = symbol.upper()
        with self.lock:
            halted = self.is_halted(symbol)
            if halted:
                return self._halted_response()

            factors = {
                'heavy_selling': self.is_heavy_selling(symbol),
                'fast_drop': self.is_fast_price_drop(symbol),
                'consecutive_bids': self.has_consecutive_bid_hits(symbol),
                'big_block': self.has_big_block(symbol),
                'spread_widen': self.is_spread_widened(symbol),
                'one_sided': self.is_one_sided_tape(symbol),
                'selling_momentum': self.has_selling_momentum(symbol)
            }

            risk_score = self.calculate_risk_score(factors)

            return {
                'is_halted': False,
                'risk_score': risk_score,
                'factors': factors,
                'metrics': {
                    'current_price': self._current_price(symbol),
                    'avg_spread': self.metrics_cache['current_spread'].get(symbol, 0),
                    'volume_profile': self._get_volume_profile(symbol)
                }
            }

    def is_heavy_selling(self, symbol: str) -> Tuple[bool, float]:
        now = time.time()
        short_trades = self._get_window_trades(symbol, self.config.price_drop_window)
        long_trades = self._get_window_trades(symbol, self.config.long_window_sec, False)

        short_sell = sum(t['size'] for t in short_trades if t['side'] == "SELL")
        long_sell = sum(t['size'] for t in long_trades if t['side'] == "SELL")

        if long_sell < 1e-9:
            return (False, 0.0)

        baseline = (long_sell / self.config.long_window_sec) * self.config.price_drop_window
        ratio = short_sell / baseline if baseline > 0 else 0
        return (ratio >= self.config.heavy_sell_multiplier, ratio)

    def is_fast_price_drop(self, symbol: str) -> Tuple[bool, float]:
        trades = self._get_window_trades(symbol, self.config.price_drop_window)
        if len(trades) < 3:
            return (False, 0.0)

        prices = [t['price'] for t in trades]
        peak = max(prices)
        trough = min(prices)
        drop_pct = (peak - trough) / peak
        return (drop_pct >= self.config.price_drop_pct, drop_pct)

    def has_consecutive_bid_hits(self, symbol: str) -> bool:
        trades = self._get_window_trades(symbol, 1.0)
        consecutive = 0

        for t in sorted(trades, key=lambda x: x['timestamp']):
            if t['side'] == "SELL":
                consecutive += 1
                if consecutive >= self.config.consecutive_bid_hits:
                    return True
            else:
                consecutive = 0
        return False

    def has_big_block(self, symbol: str) -> bool:
        avg_size = self.metrics_cache['long_term_avg_size'].get(symbol, 0)
        if avg_size == 0:
            return False

        trades = self._get_window_trades(symbol, self.config.short_window_sec)
        return any(
            t['size'] >= avg_size * self.config.block_multiplier
            and t['side'] == "SELL"
            for t in trades
        )

    def is_spread_widened(self, symbol: str) -> Tuple[bool, float]:
        min_spread = self.metrics_cache['min_spread'].get(symbol, 0)
        current_spread = self.metrics_cache['current_spread'].get(symbol, 0)

        if min_spread < 1e-9:
            return (False, 0.0)

        factor = current_spread / min_spread
        return (factor >= self.config.spread_widen_factor, factor)

    def is_one_sided_tape(self, symbol: str) -> Tuple[bool, float]:
        trades = self._get_window_trades(symbol, 2.0)
        total = sum(t['size'] for t in trades)
        sell = sum(t['size'] for t in trades if t['side'] == "SELL")
        ratio = sell / total if total > 0 else 0
        return (ratio >= self.config.sell_ratio_threshold, ratio)

    def has_selling_momentum(self, symbol: str) -> bool:
        trades = self._get_window_trades(symbol, self.config.momentum_window)
        if len(trades) < 5:
            return False

        third = self.config.momentum_window / 3
        now = time.time()

        periods = [
            [t for t in trades if t['timestamp'] > now - third],
            [t for t in trades if now - 2*third < t['timestamp'] <= now - third],
            [t for t in trades if now - self.config.momentum_window < t['timestamp'] <= now - 2*third]
        ]

        volumes = [
            sum(t['size'] for t in period if t['side'] == "SELL")
            for period in periods
        ]

        return volumes[0] > volumes[1] > volumes[2] and volumes[0] > 0

    def calculate_risk_score(self, factors: Dict) -> float:
        score = 0.0
        weights = self.config.weights

        # Heavy selling
        _, ratio = factors['heavy_selling']
        score += weights['heavy_selling'] * min(ratio / 3, 1.0)

        # Price drop
        _, drop_pct = factors['fast_drop']
        score += weights['fast_drop'] * min(drop_pct / 0.05, 1.0)  # Cap at 5% drop

        # Binary factors
        score += weights['consecutive_bids'] * factors['consecutive_bids']
        score += weights['big_block'] * factors['big_block']

        # Spread widening
        _, spread_factor = factors['spread_widen']
        score += weights['spread_widen'] * min(spread_factor / 5, 1.0)

        # One-sided tape
        _, sell_ratio = factors['one_sided']
        score += weights['one_sided'] * min(sell_ratio / 0.9, 1.0)

        # Momentum (extra weight)
        if factors['selling_momentum']:
            score *= 1.2

        return min(score, 1.0)

    # Helper methods
    def _get_window_trades(self, symbol: str, window: float, short_term: bool = True) -> List:
        store = self.trade_history_short if short_term else self.trade_history_long
        deque = store.get(symbol.upper(), deque())
        now = time.time()
        return [t for t in deque if (now - t['timestamp']) <= window]

    def _prune_deque(self, deque: deque, current_ts: float, window: float):
        cutoff = current_ts - window
        while deque and deque[0]['timestamp'] < cutoff:
            deque.popleft()

    def _update_size_metrics(self, symbol: str):
        long_trades = self.trade_history_long.get(symbol, [])
        if long_trades:
            sizes = [t['size'] for t in long_trades]
            self.metrics_cache['long_term_avg_size'][symbol] = sum(sizes) / len(sizes)

    def _update_spread_metrics(self, symbol: str):
        quotes = self.quote_history_short.get(symbol, [])
        if quotes:
            spreads = [q['spread'] for q in quotes]
            self.metrics_cache['min_spread'][symbol] = min(spreads)
            self.metrics_cache['current_spread'][symbol] = spreads[-1] if spreads else 0

    def _halted_response(self):
        return {
            'is_halted': True,
            'risk_score': 0.0,
            'factors': {},
            'metrics': {}
        }

    def is_halted(self, symbol: str) -> bool:
        trades = self._get_window_trades(symbol, self.config.short_window_sec)
        if len(trades) < self.config.halt_min_trades:
            return False

        latest_trade = max(t['timestamp'] for t in trades)
        quotes = self.quote_history_short.get(symbol, [])
        latest_quote = max(q['timestamp'] for q in quotes) if quotes else 0

        now = time.time()
        return (now - latest_trade) > self.config.halt_inactivity and \
               (now - latest_quote) > self.config.halt_inactivity

    def _current_price(self, symbol: str) -> float:
        trades = self.trade_history_short.get(symbol, deque())
        return trades[-1]['price'] if trades else 0.0

    def _get_volume_profile(self, symbol: str) -> Dict:
        trades = self._get_window_trades(symbol, self.config.short_window_sec)
        return {
            'total': sum(t['size'] for t in trades),
            'buy': sum(t['size'] for t in trades if t['side'] == "BUY"),
            'sell': sum(t['size'] for t in trades if t['side'] == "SELL")
        }