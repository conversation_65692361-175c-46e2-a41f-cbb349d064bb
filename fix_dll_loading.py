#!/usr/bin/env python3
"""
Fix DLL loading issue by adding the build directory to the DLL search path.
"""

import os
import sys
import platform

def fix_dll_loading():
    """Add the build directory to Windows DLL search path."""
    if platform.system() != "Windows":
        print("Not on Windows, no DLL path fix needed")
        return True
        
    print("=== Fixing DLL Loading ===")
    
    try:
        # Get absolute path to build directory
        build_dir = os.path.abspath(os.path.join("ocr_accelerator", "x64", "Release"))
        print(f"Build directory: {build_dir}")
        
        if not os.path.exists(build_dir):
            print("❌ Build directory not found")
            return False
            
        # On Windows, add directory to DLL search path
        if hasattr(os, 'add_dll_directory'):
            # Python 3.8+ method
            os.add_dll_directory(build_dir)
            print("✅ Added to DLL search path (Python 3.8+)")
        else:
            # Fallback: add to PATH environment variable
            current_path = os.environ.get('PATH', '')
            if build_dir not in current_path:
                os.environ['PATH'] = build_dir + os.pathsep + current_path
                print("✅ Added to PATH environment variable")
        
        # Also add to sys.path for module import
        if build_dir not in sys.path:
            sys.path.insert(0, build_dir)
            print("✅ Added to sys.path")
            
        return True
        
    except Exception as e:
        print(f"❌ Error fixing DLL loading: {e}")
        return False

def test_module_import():
    """Test importing the C++ module."""
    print("\n=== Testing Module Import ===")
    
    try:
        import ocr_accelerator
        print("✅ Module imported successfully")
        
        # Test basic function
        if hasattr(ocr_accelerator, 'test_function'):
            result = ocr_accelerator.test_function()
            print(f"✅ test_function() works: {result}")
            
        # List available functions
        functions = [attr for attr in dir(ocr_accelerator) if not attr.startswith('_')]
        print(f"✅ Available functions: {functions}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    print("TESTRADE DLL Loading Fix")
    
    # Fix DLL loading
    if fix_dll_loading():
        # Test module import
        if test_module_import():
            print("\n🎉 DLL loading fixed successfully!")
        else:
            print("\n❌ Module import still failing")
    else:
        print("\n❌ Failed to fix DLL loading")