#!/usr/bin/env python3
"""
Test the rebuilt C++ accelerator module.
"""

import sys
import os

def test_rebuilt_module():
    print("=== Testing Rebuilt C++ OCR Accelerator ===")
    
    # Add Release directory to path for DLL dependencies
    release_dir = "/mnt/c/TESTRADE/ocr_accelerator/x64/Release"
    if release_dir not in sys.path:
        sys.path.insert(0, release_dir)
    
    # Remove any cached module
    if 'ocr_accelerator' in sys.modules:
        del sys.modules['ocr_accelerator']
    
    try:
        import ocr_accelerator
        print("✅ Module imported successfully!")
        
        # Check available functions
        functions = [f for f in dir(ocr_accelerator) if not f.startswith('_')]
        print(f"Available functions: {functions}")
        
        # Test the simple test function first
        if hasattr(ocr_accelerator, 'test_function'):
            print("✅ test_function found!")
            result = ocr_accelerator.test_function()
            print(f"test_function result: {result}")
        else:
            print("❌ test_function not found")
        
        # Test the main OCR function
        if hasattr(ocr_accelerator, 'process_image_and_ocr'):
            print("✅ process_image_and_ocr found!")
            
            # Test with a simple image
            import numpy as np
            test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255  # White image
            
            try:
                result = ocr_accelerator.process_image_and_ocr(
                    test_image, 2.0, True, 1.0, 25, -2, 1.0, 1.0,
                    True, 15, 2, 2, True, 16, (0.5, 1.8), 2, 1.8, 2
                )
                print("✅ process_image_and_ocr call successful!")
                print(f"Result type: {type(result)}")
                if isinstance(result, dict):
                    print(f"Result keys: {list(result.keys())}")
                    if 'text' in result:
                        print(f"Text: '{result['text']}'")
                    if 'confidence' in result:
                        print(f"Confidence: {result['confidence']}")
                    if 't0_entry_ns' in result and 't4_tess_done_ns' in result:
                        total_time_ms = (result['t4_tess_done_ns'] - result['t0_entry_ns']) / 1_000_000
                        print(f"Total processing time: {total_time_ms:.1f}ms")
                        
            except Exception as e:
                print(f"❌ process_image_and_ocr call failed: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("❌ process_image_and_ocr not found")
            
        print("\n=== Integration Test with OCR Service ===")
        try:
            from modules.ocr.ocr_service import CPP_ACCELERATOR_AVAILABLE
            print(f"OCR Service detects C++ accelerator: {CPP_ACCELERATOR_AVAILABLE}")
        except Exception as e:
            print(f"OCR Service test failed: {e}")
            
    except Exception as e:
        print(f"❌ Module import failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_rebuilt_module()