# data_models/__init__.py

"""
TESTRADE Data Models Package

Consolidated data structures and enums for the TESTRADE trading system.
This package provides a single source of truth for all trading-related data types.
"""

# Import all enums
from .enums import (
    OrderSide,
    OrderOrigin,
    OrderLSAgentStatus,
    TradeActionType,
    OrderEventType,
    LifecycleState,
    RiskLevel,
    CommandStatus,
)

# Import all data structures
from .trading import (
    # Order Management
    FillRecord,
    StatusHistoryEntry,
    Order,
    OrderParameters,
    
    # Position Data
    BrokerSnapshotPositionData,
    
    # OCR Data
    OCRSnapshot,
    OCRParsedData,
    OcrConfigData,
    
    # Trade Management
    TradeSignal,
    
    # Risk Management
    RiskAssessment,
    
    # GUI Commands
    CommandResult,
    
    # Market Data
    MarketDataTick,
    MarketDataQuote,
)

# Note: Legacy trading_data_types.py has been consolidated into this package

__all__ = [
    # Enums
    'OrderSide',
    'OrderOrigin', 
    'OrderLSAgentStatus',
    'TradeActionType',
    'OrderEventType',
    'LifecycleState',
    'RiskLevel',
    'CommandStatus',
    
    # Data Classes
    'FillRecord',
    'StatusHistoryEntry',
    'Order',
    'OrderParameters',
    'BrokerSnapshotPositionData',
    'OCRSnapshot',
    'OCRParsedData',
    'OcrConfigData',
    'TradeSignal',
    'RiskAssessment',
    'CommandResult',
    'MarketDataTick',
    'MarketDataQuote',
    
]