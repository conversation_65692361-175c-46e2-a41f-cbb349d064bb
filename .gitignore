# Python
venv/
.venv/
__pycache__/
*.pyc
*.pyo
*.pyd
*.egg-info/
dist/
build/

# C++ Build Artifacts (Visual Studio example)
lightspeed_bridge_cpp/x64/
lightspeed_bridge_cpp/Debug/
lightspeed_bridge_cpp/Release/
lightspeed_bridge_cpp/.vs/
lightspeed_bridge_cpp/LightspeedBridge/x64/
*.obj
*.ilk
*.pdb
*.pch
*.suo
*.user
*.ncb
*.sdf
*.dll
*.exe
*.lib
*.exp
*.iobj
*.ipdb
*.tlog
*.recipe

# Logs and Runtime Data
logs/
*.log
headless_core_runner.log

# Observability and Performance Data
data/observability_logs/
data/performance_logs/
*.ndjson

# Analysis Reports
reports/
reports/lifecycle_analysis_*.txt
intellisense_hardening_results.json
intellisense_capture_logs/
intellisense_capture_sessions/
intellisense_sessions/

# Market Data and Test Data
data/friday_may30_2025/
data/test_recent/
data/*/
*.csv
# Only ignore JSON files in data directories, not documentation
data/**/*.json

# IDE specific
.idea/
*.vscode/

# OS specific
.DS_Store

# Google Drive temporary files
.tmp.driveupload/
.tmp.drivedownload/
itions*

# Backup files
*.backup
*.bak
utils/control.json.backup

# IPC Memory-mapped files (too large for git)
gui/data/ipc_mmap_buffers/
*.mmap
.venv-wsl/
