# TANK Emergency GUI

**Emergency-only interface for TANK Core when main GUI is unavailable**

## 🚨 Purpose

This is a **lightweight emergency interface** for critical situations when:
- Main TESTRADE GUI is unresponsive
- Need immediate access to emergency controls
- System monitoring during critical events
- Quick status checks and basic controls

## 🎯 Design Philosophy

**Keep it simple and focused:**
- ✅ Lightweight - minimal dependencies
- ✅ Self-contained - single HTML file with embedded CSS/JS
- ✅ Emergency-focused - only critical functions
- ✅ Direct communication - bypasses complex backend layers

## 📁 Files

```
gui/
├── emergency_gui_server.py    # Flask server with WebSocket support
├── emergency_gui_html.html    # Self-contained HTML interface
└── emergency_README.md        # This file
```

## 🚀 Quick Start

```bash
# From TESTRADE root directory
cd gui
python emergency_gui_server.py

# Access at: http://localhost:8766
```

## 🔧 Features

### **Emergency Controls:**
- 🛑 **Force Close All Positions** - Immediate position liquidation
- 📷 **Stop/Start OCR** - OCR control
- 🔄 **Refresh Status** - Manual status update
- 📐 **ROI Controls** - Emergency ROI coordinate updates

### **Real-time Monitoring:**
- 🚀 **TANK Core Status** - Operational state and uptime
- 📡 **IPC Channel Health** - Trading/System/Bulk channel status
- 💾 **Buffer Levels** - Memory buffer utilization
- 📊 **Position Summary** - Open positions and P&L

### **Alert System:**
- 🚨 **Critical Alerts** - Real-time emergency notifications
- 🔊 **Audio Alerts** - Sound notifications for critical events
- 📱 **Visual Indicators** - Color-coded status displays

## ⚙️ Configuration

The server auto-detects TANK Core ports:
- **Command Port**: 5560 (ZMQ PUSH to TANK)
- **Status Port**: 5561 (ZMQ SUB from TANK)
- **HTTP Port**: 9999 (HTTP proxy to TANK)

## 🔌 Communication

### **To TANK Core:**
- **ZMQ Commands** → Direct command sending
- **HTTP Proxy** → Status requests via TANK's HTTP interface
- **WebSocket** → Real-time updates to browser

### **From TANK Core:**
- **ZMQ Status Updates** → Real-time system status
- **ZMQ Critical Alerts** → Emergency notifications

## 🛡️ Security Notes

**Emergency GUI is intentionally minimal:**
- No authentication (emergency access)
- Local access only (localhost)
- Direct TANK communication (bypasses normal security layers)

**Use only during emergencies when normal GUI is unavailable.**

## 🔍 Troubleshooting

### **GUI Won't Load:**
```bash
# Check if HTML file exists
ls -la gui/emergency_gui_html.html

# Check server logs
python gui/emergency_gui_server.py
```

### **Can't Connect to TANK:**
```bash
# Check if TANK Core is running
netstat -an | grep 5560
netstat -an | grep 9999

# Check TANK Core logs
tail -f logs/core.log
```

### **No Real-time Updates:**
- Verify TANK Core is publishing status updates
- Check ZMQ port 5561 is accessible
- Refresh browser page

## 📋 Emergency Checklist

When using Emergency GUI:

1. ✅ **Verify TANK Core is running**
2. ✅ **Check connection status** (top-right indicator)
3. ✅ **Review system status** before taking action
4. ✅ **Use Force Close All** only in true emergencies
5. ✅ **Monitor buffer levels** for system health

## 🎯 Evolution Path

As you mentioned, this will evolve based on needs:
- **Phase 1**: Basic emergency controls ✅
- **Phase 2**: Enhanced monitoring (as needed)
- **Phase 3**: Additional emergency functions (as needed)

**Keep it lightweight and emergency-focused!**
