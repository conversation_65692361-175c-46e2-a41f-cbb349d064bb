#!/usr/bin/env python3
"""
Very simple debug script to see what's wrong.
"""

import os
import sys
import platform

print(f"Platform: {platform.system()}")
print(f"Working directory: {os.getcwd()}")
print(f"Python: {sys.version}")

# Check if build directory exists
build_dir = "ocr_accelerator/x64/Release"
print(f"Build directory exists: {os.path.exists(build_dir)}")

# Check if .pyd exists
pyd_path = os.path.join(build_dir, "ocr_accelerator.pyd")
print(f"PYD file exists: {os.path.exists(pyd_path)}")

if os.path.exists(pyd_path):
    print(f"PYD size: {os.path.getsize(pyd_path):,} bytes")

# Check critical DLLs
critical_dlls = ["tesseract55.dll", "leptonica-1.85.0.dll"]
for dll in critical_dlls:
    # Check in build directory
    build_dll = os.path.join(build_dir, dll)
    print(f"{dll} in build: {os.path.exists(build_dll)}")
    
    # Check in root
    root_dll = dll
    print(f"{dll} in root: {os.path.exists(root_dll)}")

print("\n=== DLL Directory Setup ===")
try:
    # Add DLL search paths
    abs_build = os.path.abspath(build_dir)
    print(f"Adding DLL directory: {abs_build}")
    os.add_dll_directory(abs_build)
    
    # Add to PATH for good measure
    current_path = os.environ.get('PATH', '')
    if abs_build not in current_path:
        os.environ['PATH'] = abs_build + os.pathsep + current_path
        print(f"Added to PATH: {abs_build}")
    
    # Change to build directory for import
    original_cwd = os.getcwd()
    os.chdir(abs_build)
    print(f"Changed directory to: {abs_build}")
    
    # Try import
    import ocr_accelerator
    print("✅ SUCCESS!")
    
    # Restore directory
    os.chdir(original_cwd)
    print(f"Restored directory to: {original_cwd}")
    
    # Test function
    if hasattr(ocr_accelerator, 'test_function'):
        result = ocr_accelerator.test_function()
        print(f"test_function(): {result}")
    else:
        print("❌ No test_function found")
        
    # List functions
    funcs = [f for f in dir(ocr_accelerator) if not f.startswith('_')]
    print(f"Available functions: {funcs}")
    
    # Test main function
    if hasattr(ocr_accelerator, 'process_image_and_ocr'):
        print("✅ process_image_and_ocr function found")
    else:
        print("❌ process_image_and_ocr function not found")
    
except Exception as e:
    print(f"❌ FAILED: {e}")
    print(f"Error type: {type(e).__name__}")
    
    # Show detailed error
    import traceback
    traceback.print_exc()

print("\nDone.")