#!/usr/bin/env python3
"""
Create a proper solution for DLL dependencies without polluting root
"""

import os
import sys
import shutil
import platform

def create_dll_symlinks():
    """Create symbolic links instead of copying DLLs"""
    if platform.system() != "Windows":
        print("❌ This solution requires Windows")
        return False
    
    build_dir = os.path.abspath("ocr_accelerator/x64/Release")
    
    # Critical DLLs that are likely needed
    critical_dlls = [
        "tesseract55.dll",
        "leptonica-1.85.0.dll",
        "opencv_core4.dll", 
        "opencv_imgproc4.dll"
    ]
    
    print("=== Creating DLL Symbolic Links ===")
    
    created_links = []
    
    for dll in critical_dlls:
        dll_source = os.path.join(build_dir, dll)
        dll_target = dll
        
        if os.path.exists(dll_source):
            if os.path.exists(dll_target):
                print(f"⚠️ {dll} already exists in root, skipping")
                continue
                
            try:
                # Create symbolic link (requires admin rights on Windows)
                os.symlink(dll_source, dll_target)
                created_links.append(dll_target)
                print(f"✅ Created symlink: {dll}")
                
            except OSError as e:
                print(f"❌ Failed to create symlink for {dll}: {e}")
                print("   (This may require running as administrator)")
                
                # Fallback to copying
                try:
                    shutil.copy2(dll_source, dll_target)
                    created_links.append(dll_target)
                    print(f"✅ Copied instead: {dll}")
                except Exception as e2:
                    print(f"❌ Copy also failed: {e2}")
        else:
            print(f"⚠️ Source DLL not found: {dll_source}")
    
    return created_links

def test_with_created_links(created_links):
    """Test if the module works with the created links"""
    print(f"\n=== Testing with {len(created_links)} DLL links ===")
    
    build_dir = "ocr_accelerator/x64/Release"
    abs_build = os.path.abspath(build_dir)
    original_cwd = os.getcwd()
    
    try:
        # Change to build directory for import
        os.chdir(abs_build)
        
        import ocr_accelerator
        print("✅ Module imported successfully")
        
        # Check functions
        if hasattr(ocr_accelerator, 'test_function'):
            try:
                result = ocr_accelerator.test_function()
                print(f"✅ test_function(): {result}")
                return True
            except Exception as e:
                print(f"❌ test_function() failed: {e}")
                return False
        else:
            print("❌ test_function not found")
            return False
            
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def clean_dll_links(created_links):
    """Clean up created DLL links"""
    print(f"\n=== Cleaning up {len(created_links)} DLL links ===")
    
    for dll in created_links:
        if os.path.exists(dll):
            try:
                if os.path.islink(dll):
                    os.unlink(dll)
                    print(f"✅ Removed symlink: {dll}")
                else:
                    os.remove(dll)
                    print(f"✅ Removed copy: {dll}")
            except Exception as e:
                print(f"❌ Failed to remove {dll}: {e}")

def create_startup_script():
    """Create a script that sets up DLL environment properly"""
    script_content = '''#!/usr/bin/env python3
"""
Proper DLL setup for OCR accelerator module
"""

import os
import sys
import platform

def setup_ocr_accelerator():
    """Setup OCR accelerator with proper DLL paths"""
    if platform.system() != "Windows":
        return None, "OCR accelerator requires Windows"
    
    try:
        # Add the build directory to DLL search path
        build_dir = os.path.abspath("ocr_accelerator/x64/Release")
        
        if not os.path.exists(build_dir):
            return None, f"Build directory not found: {build_dir}"
        
        # Method 1: Add DLL directory (Python 3.8+)
        if hasattr(os, 'add_dll_directory'):
            os.add_dll_directory(build_dir)
        
        # Method 2: Add to PATH
        current_path = os.environ.get('PATH', '')
        if build_dir not in current_path:
            os.environ['PATH'] = build_dir + os.pathsep + current_path
        
        # Method 3: Change directory for import (most reliable)
        original_cwd = os.getcwd()
        os.chdir(build_dir)
        
        try:
            import ocr_accelerator
            # Restore directory after successful import
            os.chdir(original_cwd)
            return ocr_accelerator, "SUCCESS"
        except Exception as e:
            os.chdir(original_cwd)
            return None, f"Import failed: {e}"
            
    except Exception as e:
        return None, f"Setup failed: {e}"

# Test the setup
if __name__ == "__main__":
    module, status = setup_ocr_accelerator()
    print(f"Status: {status}")
    
    if module:
        if hasattr(module, 'test_function'):
            try:
                result = module.test_function()
                print(f"test_function(): {result}")
            except Exception as e:
                print(f"Function call failed: {e}")
        else:
            print("No test_function found")
'''
    
    with open("setup_ocr_accelerator.py", "w") as f:
        f.write(script_content)
    
    print("✅ Created setup_ocr_accelerator.py")

def main():
    print("=== DLL Dependency Solution ===")
    
    # First, try creating symlinks/copies
    created_links = create_dll_symlinks()
    
    if created_links:
        # Test if it works
        success = test_with_created_links(created_links)
        
        if success:
            print(f"\n🎯 SUCCESS! Required DLLs: {created_links}")
            
            choice = input("\nKeep these DLL links in root? (y/n): ").lower().strip()
            if choice != 'y':
                clean_dll_links(created_links)
                print("DLL links removed. Use setup_ocr_accelerator.py instead.")
            else:
                print("DLL links kept in root directory.")
        else:
            print(f"\n❌ Still failed with DLL links")
            clean_dll_links(created_links)
    
    # Always create the proper setup script
    create_startup_script()
    print(f"\n✅ Created proper setup script: setup_ocr_accelerator.py")
    print("This script handles DLL loading without polluting root directory.")

if __name__ == "__main__":
    main()