#!/usr/bin/env python3
"""
Test for dependency resolution warnings in different modes
"""
import os
import sys
import logging
import io
import re

# Add the TESTRADE root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.dependency_injection import DIContainer
from core.di_registration import register_all_services
from utils.testrade_modes import get_current_mode, TestradeMode

def capture_warnings(mode_name):
    """Capture warnings during service registration"""
    # Create string buffer to capture logs
    log_capture = io.StringIO()
    handler = logging.StreamHandler(log_capture)
    handler.setLevel(logging.WARNING)
    
    # Add handler to root logger
    root_logger = logging.getLogger()
    root_logger.addHandler(handler)
    
    try:
        # Set mode
        os.environ['TESTRADE_MODE'] = mode_name
        
        # Create container and register services
        container = DIContainer()
        register_all_services(container)
        
        # Try to resolve some key services that might trigger warnings
        services_to_test = [
            'ICorrelationLogger',
            'IGUICommandService', 
            'ISystemHealthMonitoringService',
            'IOCRProcessManager',
            'IROIService',
            'IMasterActionFilter'
        ]
        
        for service_name in services_to_test:
            try:
                container.resolve(service_name)
            except Exception:
                pass  # We're just triggering resolutions
                
    finally:
        # Remove handler
        root_logger.removeHandler(handler)
        
    # Get captured logs
    log_contents = log_capture.getvalue()
    
    # Find dependency-related warnings
    warning_patterns = [
        r"Telemetry service required but not available",
        r"IPC client required but not available",
        r"Could not resolve.*Telemetry",
        r"Could not resolve.*IPC",
        r"ITelemetryService.*not available",
        r"IBulletproofBabysitterIPCClient.*not available",
        r"telemetry_service required but not available",
        r"ipc_client required but not available"
    ]
    
    dependency_warnings = []
    for line in log_contents.split('\n'):
        if 'WARNING' in line:
            for pattern in warning_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    dependency_warnings.append(line.strip())
                    break
                    
    return dependency_warnings

def main():
    """Test all modes for dependency warnings"""
    original_mode = os.environ.get('TESTRADE_MODE', '')
    
    print("Testing for dependency resolution warnings in different modes...\n")
    
    try:
        for mode in ['TANK_SEALED', 'TANK_BUFFERED', 'LIVE']:
            print(f"{'='*60}")
            print(f"Testing {mode} mode")
            print(f"{'='*60}")
            
            warnings = capture_warnings(mode)
            
            if warnings:
                print(f"❌ Found {len(warnings)} dependency warnings:")
                for warning in warnings:
                    print(f"  - {warning}")
            else:
                print("✅ No dependency resolution warnings found!")
                
            print()
            
    finally:
        # Restore original mode
        if original_mode:
            os.environ['TESTRADE_MODE'] = original_mode
        else:
            os.environ.pop('TESTRADE_MODE', None)

if __name__ == "__main__":
    main()