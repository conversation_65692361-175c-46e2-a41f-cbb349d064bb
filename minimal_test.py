#!/usr/bin/env python3
"""
Minimal test to verify the C++ module import works.
"""

import os
import sys

def test_minimal_import():
    """Test minimal import with directory change."""
    print("=== Minimal Import Test ===")
    
    try:
        # Build path
        build_path = os.path.abspath("ocr_accelerator/x64/Release")
        print(f"Build path: {build_path}")
        print(f"Build path exists: {os.path.exists(build_path)}")
        
        # Check PYD file
        pyd_file = os.path.join(build_path, "ocr_accelerator.pyd")
        print(f"PYD file exists: {os.path.exists(pyd_file)}")
        
        if not os.path.exists(pyd_file):
            print("❌ PYD file not found")
            return False
        
        # Save current directory
        original_dir = os.getcwd()
        print(f"Original directory: {original_dir}")
        
        try:
            # Add to sys.path
            if build_path not in sys.path:
                sys.path.insert(0, build_path)
                print("✅ Added to sys.path")
            
            # Add to DLL search path
            if hasattr(os, 'add_dll_directory'):
                os.add_dll_directory(build_path)
                print("✅ Added to DLL search path")
            
            # Change to build directory
            os.chdir(build_path)
            print(f"✅ Changed to: {os.getcwd()}")
            
            # Import
            print("🔄 Importing...")
            import ocr_accelerator
            print("✅ Import successful!")
            
            # Test function
            if hasattr(ocr_accelerator, 'test_function'):
                result = ocr_accelerator.test_function()
                print(f"✅ test_function(): {result}")
            
            return True
            
        finally:
            # Always restore directory
            os.chdir(original_dir)
            print(f"✅ Restored to: {os.getcwd()}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_minimal_import()
    if success:
        print("\n🎉 Minimal test passed!")
    else:
        print("\n❌ Minimal test failed!")