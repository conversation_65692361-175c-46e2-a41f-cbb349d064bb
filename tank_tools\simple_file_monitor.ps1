# simple_file_monitor.ps1 - Basic TESTRADE file monitor
param(
    [string]$Path = "C:\TESTRADE\data",
    [int]$Seconds = 5,
    [int]$Minutes = 5
)

Write-Host "🔍 TESTRADE File Monitor" -ForegroundColor Green
Write-Host "Path: $Path" -ForegroundColor Cyan
Write-Host "Checking every $Seconds seconds for $Minutes minutes" -ForegroundColor Cyan
Write-Host "=" * 50

$startTime = Get-Date
$endTime = $startTime.AddMinutes($Minutes)
$previousSizes = @{}
$totalGrowth = 0

while ((Get-Date) -lt $endTime) {
    $currentTime = Get-Date
    $elapsed = [math]::Round(($currentTime - $startTime).TotalMinutes, 1)
    
    # Check TESTRADE process
    $pythonProcs = Get-Process python -ErrorAction SilentlyContinue
    $testradeFound = $false
    
    if ($pythonProcs) {
        foreach ($proc in $pythonProcs) {
            try {
                $cmd = (Get-CimInstance Win32_Process -Filter "ProcessId = $($proc.Id)" -ErrorAction SilentlyContinue).CommandLine
                if ($cmd -like "*testrade*" -or $cmd -like "*ApplicationCore*") {
                    $testradeFound = $true
                    break
                }
            } catch {
                # Skip
            }
        }
    }
    
    # Check files
    $currentFiles = @{}
    $intervalGrowth = 0
    $newFiles = 0
    $modifiedFiles = 0
    
    if (Test-Path $Path) {
        try {
            Get-ChildItem -Path $Path -Recurse -File -ErrorAction SilentlyContinue | ForEach-Object {
                $currentFiles[$_.FullName] = $_.Length
                
                if ($previousSizes.ContainsKey($_.FullName)) {
                    $growth = $_.Length - $previousSizes[$_.FullName]
                    if ($growth -gt 0) {
                        $intervalGrowth += $growth
                        $modifiedFiles++
                    }
                } else {
                    $intervalGrowth += $_.Length
                    $newFiles++
                }
            }
        } catch {
            Write-Host "Error accessing files" -ForegroundColor Red
        }
    }
    
    $previousSizes = $currentFiles
    $totalGrowth += $intervalGrowth
    
    # Calculate rates
    $growthMB = [math]::Round($intervalGrowth / 1MB, 3)
    $growthKB = [math]::Round($intervalGrowth / 1KB, 1)
    $rateKBps = [math]::Round($growthKB / $Seconds, 1)
    $rateMBps = [math]::Round($growthMB / $Seconds, 3)
    
    # Display
    Clear-Host
    Write-Host "🔍 TESTRADE File Monitor - $($currentTime.ToString('HH:mm:ss'))" -ForegroundColor Green
    Write-Host "Elapsed: $elapsed minutes" -ForegroundColor Cyan
    
    if ($testradeFound) {
        Write-Host "TESTRADE: 🟢 RUNNING" -ForegroundColor Green
    } else {
        Write-Host "TESTRADE: 🔴 NOT FOUND" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "📊 INTERVAL RESULTS ($Seconds seconds):" -ForegroundColor Yellow
    Write-Host "  Growth: $growthMB MB ($growthKB KB)" -ForegroundColor White
    Write-Host "  Rate: $rateMBps MB/s ($rateKBps KB/s)" -ForegroundColor White
    Write-Host "  New files: $newFiles" -ForegroundColor White
    Write-Host "  Modified files: $modifiedFiles" -ForegroundColor White
    Write-Host "  Total files: $($currentFiles.Count)" -ForegroundColor White
    Write-Host ""
    
    $totalMB = [math]::Round($totalGrowth / 1MB, 2)
    Write-Host "📈 SESSION TOTALS:" -ForegroundColor Yellow
    Write-Host "  Total growth: $totalMB MB" -ForegroundColor White
    if ($elapsed -gt 0) {
        $sessionRate = [math]::Round($totalMB / $elapsed, 2)
        Write-Host "  Session rate: $sessionRate MB/min" -ForegroundColor White
    }
    
    # Alerts
    if ($rateMBps -gt 10) {
        Write-Host ""
        Write-Host "🚨 HIGH OUTPUT RATE!" -ForegroundColor Red
    } elseif ($rateMBps -gt 5) {
        Write-Host ""
        Write-Host "⚠️  Elevated output rate" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "Next check in $Seconds seconds... (Ctrl+C to stop)" -ForegroundColor Gray
    
    Start-Sleep $Seconds
}

Write-Host ""
Write-Host "📋 MONITORING COMPLETE" -ForegroundColor Green
$finalMB = [math]::Round($totalGrowth / 1MB, 2)
$finalMinutes = [math]::Round(((Get-Date) - $startTime).TotalMinutes, 1)
Write-Host "Total file growth: $finalMB MB in $finalMinutes minutes" -ForegroundColor White

if ($finalMinutes -gt 0) {
    $avgRate = [math]::Round($finalMB / $finalMinutes, 2)
    Write-Host "Average rate: $avgRate MB/min" -ForegroundColor White
}

Write-Host "Monitor completed successfully!" -ForegroundColor Green
