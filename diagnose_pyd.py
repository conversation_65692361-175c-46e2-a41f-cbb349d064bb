#!/usr/bin/env python3
"""
Diagnose what's wrong with the .pyd file
"""

import os
import sys
import platform
import ctypes

def main():
    print(f"Platform: {platform.system()}")
    print(f"Python: {sys.version}")
    
    # Find the .pyd file
    pyd_path = "ocr_accelerator/x64/Release/ocr_accelerator.pyd"
    
    if not os.path.exists(pyd_path):
        print(f"❌ PYD file not found: {pyd_path}")
        return
    
    size = os.path.getsize(pyd_path)
    print(f"✅ PYD file found: {size:,} bytes")
    
    if platform.system() != "Windows":
        print("❌ Cannot analyze PYD file on non-Windows platform")
        return
    
    # Try to load as DLL to see exported functions
    print("\n=== DLL Analysis ===")
    try:
        dll = ctypes.CDLL(pyd_path)
        print("✅ DLL loaded successfully")
        
        # Try to find Python module init function
        # For pybind11, it should be something like PyInit_ocr_accelerator
        try:
            init_func = dll.PyInit_ocr_accelerator
            print("✅ Found PyInit_ocr_accelerator function")
        except AttributeError:
            print("❌ No PyInit_ocr_accelerator function found")
            
            # List all exports (this is tricky without external tools)
            print("Available exports are not easily enumerable with ctypes")
        
    except Exception as e:
        print(f"❌ Failed to load as DLL: {e}")
    
    # Try Python import with detailed error handling
    print("\n=== Python Import Analysis ===")
    
    # Add directory to path
    pyd_dir = os.path.dirname(os.path.abspath(pyd_path))
    if pyd_dir not in sys.path:
        sys.path.insert(0, pyd_dir)
        print(f"Added to sys.path: {pyd_dir}")
    
    # Change directory (critical for DLL loading)
    original_cwd = os.getcwd()
    try:
        os.chdir(pyd_dir)
        print(f"Changed to directory: {pyd_dir}")
        
        # Try import
        import ocr_accelerator
        print("✅ Module imported successfully")
        
        # Check module attributes
        attrs = dir(ocr_accelerator)
        print(f"Module attributes: {attrs}")
        
        # Check for specific functions
        expected_functions = ['test_function', 'process_image_and_ocr']
        for func_name in expected_functions:
            if hasattr(ocr_accelerator, func_name):
                print(f"✅ Function '{func_name}' found")
                
                # Try to call test function
                if func_name == 'test_function':
                    try:
                        result = getattr(ocr_accelerator, func_name)()
                        print(f"  Result: {result}")
                    except Exception as e:
                        print(f"  ❌ Error calling function: {e}")
            else:
                print(f"❌ Function '{func_name}' NOT found")
        
        # Check module doc
        if hasattr(ocr_accelerator, '__doc__') and ocr_accelerator.__doc__:
            print(f"Module doc: {ocr_accelerator.__doc__}")
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        
        # Show detailed import error
        import traceback
        traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Restore directory
        os.chdir(original_cwd)
        print(f"Restored directory: {original_cwd}")

if __name__ == "__main__":
    main()