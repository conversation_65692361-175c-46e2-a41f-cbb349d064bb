/**
 * Action Configuration
 * Defines all available actions and their properties
 */

export const ActionConfig = {
    // Trading Actions
    buy: {
        endpoint: '/api/v2/order',
        method: 'POST',
        confirmation: true,
        fields: ['symbol', 'quantity', 'order_type', 'limit_price'],
        successMessage: 'Buy order placed',
        errorMessage: 'Failed to place buy order',
        validation: {
            symbol: { required: true },
            quantity: { required: true, min: 1, max: 10000 }
        }
    },
    
    sell: {
        endpoint: '/api/v2/order',
        method: 'POST',
        confirmation: true,
        fields: ['symbol', 'quantity', 'order_type', 'limit_price'],
        successMessage: 'Sell order placed',
        errorMessage: 'Failed to place sell order',
        validation: {
            symbol: { required: true },
            quantity: { required: true, min: 1, max: 10000 }
        }
    },
    
    close_position: {
        endpoint: '/api/v2/command/close_position',
        method: 'POST',
        confirmation: true,
        fields: ['symbol'],
        successMessage: 'Position closed',
        errorMessage: 'Failed to close position'
    },
    
    cancel_order: {
        endpoint: '/api/v2/command/cancel_order',
        method: 'POST',
        confirmation: true,
        fields: ['order_id'],
        successMessage: 'Order cancelled',
        errorMessage: 'Failed to cancel order'
    },
    
    // Control Actions
    start_trading: {
        endpoint: '/api/v2/command/start_trading',
        method: 'POST',
        confirmation: true,
        fields: [],
        successMessage: 'Trading started',
        errorMessage: 'Failed to start trading'
    },
    
    stop_trading: {
        endpoint: '/api/v2/command/stop_trading',
        method: 'POST',
        confirmation: true,
        fields: [],
        successMessage: 'Trading stopped',
        errorMessage: 'Failed to stop trading'
    },
    
    // Query Actions (no confirmation needed)
    refresh_positions: {
        endpoint: '/api/v2/command/get_positions',
        method: 'POST',
        confirmation: false,
        fields: [],
        successMessage: 'Positions refreshed',
        errorMessage: 'Failed to refresh positions'
    },
    
    refresh_account: {
        endpoint: '/api/v2/command/get_account',
        method: 'POST',
        confirmation: false,
        fields: [],
        successMessage: 'Account refreshed',
        errorMessage: 'Failed to refresh account'
    },
    
    refresh_orders: {
        endpoint: '/api/v2/command/get_orders',
        method: 'POST',
        confirmation: false,
        fields: [],
        successMessage: 'Orders refreshed',
        errorMessage: 'Failed to refresh orders'
    }
};

// Helper function to validate action data
export function validateActionData(action, data) {
    const config = ActionConfig[action];
    if (!config || !config.validation) return { valid: true };
    
    const errors = [];
    
    for (const [field, rules] of Object.entries(config.validation)) {
        const value = data[field];
        
        if (rules.required && !value) {
            errors.push(`${field} is required`);
        }
        
        if (rules.min && value < rules.min) {
            errors.push(`${field} must be at least ${rules.min}`);
        }
        
        if (rules.max && value > rules.max) {
            errors.push(`${field} must be at most ${rules.max}`);
        }
    }
    
    return {
        valid: errors.length === 0,
        errors
    };
}