#pragma once
#pragma once

#include <pybind11/pybind11.h>
#include <pybind11/numpy.h>
#include <pybind11/stl.h>
#include <string>
#include <utility> // For std::pair

pybind11::dict process_image_and_ocr(
    pybind11::array_t<unsigned char> input_frame,
    double upscale_factor,
    bool force_black_text_on_white,
    double unsharp_strength,
    int threshold_block_size,
    int threshold_c,
    double red_boost,
    double green_boost,
    bool apply_text_mask_cleaning,
    int text_mask_min_contour_area,
    int text_mask_min_width,
    int text_mask_min_height,
    bool enhance_small_symbols,
    int symbol_max_height_for_enhancement_upscaled,
    std::pair<double, double> period_comma_aspect_ratio_range_upscaled,
    int period_comma_draw_radius_upscaled,
    double hyphen_like_min_aspect_ratio_upscaled,
    int hyphen_like_draw_min_height_upscaled
);