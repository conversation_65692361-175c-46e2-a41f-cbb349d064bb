#pragma once

#include <pybind11/pybind11.h>
#include <pybind11/numpy.h>
#include <pybind11/stl.h>
#include <string>
#include <utility> // For std::pair
#include <memory>  // For std::unique_ptr

// Forward-declare the Tesseract API class to avoid including the heavy tesseract header here
namespace tesseract {
    class TessBaseAPI;
}

// This is the class that will hold the persistent Tesseract engine
class OcrEngine {
public:
    // Constructor: Takes language (e.g., "eng") and initializes Tesseract
    OcrEngine(const std::string& lang);

    // Destructor: Handles cleanup
    ~OcrEngine();

    // The main processing function is now a member of this class.
    // It takes the image and all 17 configurable parameters.
    pybind11::dict process_image(
        pybind11::array_t<unsigned char> input_frame,
        double upscale_factor,
        bool force_black_text_on_white,
        double unsharp_strength,
        int threshold_block_size,
        int threshold_c,
        double red_boost,
        double green_boost,
        bool apply_text_mask_cleaning,
        int text_mask_min_contour_area,
        int text_mask_min_width,
        int text_mask_min_height,
        bool enhance_small_symbols,
        int symbol_max_height_for_enhancement_upscaled,
        std::pair<double, double> period_comma_aspect_ratio_range_upscaled,
        int period_comma_draw_radius_upscaled,
        double hyphen_like_min_aspect_ratio_upscaled,
        int hyphen_like_draw_min_height_upscaled
    );

private:
    // A smart pointer to hold our single, persistent Tesseract engine instance.
    // This is the key to high performance.
    std::unique_ptr<tesseract::TessBaseAPI> tess_api_;
};