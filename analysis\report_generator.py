# TESTRADE/analysis/report_generator.py
import json
import logging
import statistics
from typing import Dict, Any, List, Optional
from pathlib import Path
from collections import defaultdict

# Assuming PerformanceBenchmarker and PipelineValidator data structures
# For PipelineValidator, data is typically:
# { uid: { "injected_at": ts, "pipeline_type": "...", "stages": { "stage_name": {"completed_at": ts, ...} }, ... } }
# For PerformanceBenchmarker, data is:
# { "metric_name": {"mean": ..., "median": ..., "p95": ..., ...} }

logger = logging.getLogger(__name__)

class ReportGenerator:
    def __init__(self,
                 pipeline_validator_results: Optional[Dict[str, Dict[str, Any]]] = None,
                 performance_benchmarker_stats: Optional[Dict[str, Dict[str, Any]]] = None,
                 observability_log_dir: Optional[str] = "data/observability_logs"):
        self.pv_results = pipeline_validator_results if pipeline_validator_results else {}
        self.pb_stats = performance_benchmarker_stats if performance_benchmarker_stats else {}
        self.obs_log_dir = Path(observability_log_dir) if observability_log_dir else None
        
        # For correlating Observability data if needed
        self.raw_ocr_data_by_frame_ts: Dict[float, List[Dict]] = defaultdict(list)
        self.cleaned_ocr_data_by_frame_ts: Dict[float, List[Dict]] = defaultdict(list)
        # Add more for market data, signals, orders if needed for deep correlation

        logger.info("ReportGenerator initialized.")

    def load_observability_data(self):
        """Loads key fields from NDJSON observability logs for correlation."""
        if not self.obs_log_dir or not self.obs_log_dir.exists():
            logger.warning(f"Observability log directory not found: {self.obs_log_dir}")
            return

        # Load Raw OCR
        raw_ocr_file = self.obs_log_dir / "raw_ocr.ndjson"
        if raw_ocr_file.exists():
            try:
                with raw_ocr_file.open('r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            data = json.loads(line)
                            if 'frame_timestamp' in data:
                                self.raw_ocr_data_by_frame_ts[data['frame_timestamp']].append(data)
                        except json.JSONDecodeError:
                            logger.warning(f"Skipping malformed JSON line in {raw_ocr_file}")
            except Exception as e:
                logger.error(f"Error loading {raw_ocr_file}: {e}", exc_info=True)
        
        # Load Cleaned OCR
        cleaned_ocr_file = self.obs_log_dir / "cleaned_ocr.ndjson"
        if cleaned_ocr_file.exists():
            try:
                with cleaned_ocr_file.open('r', encoding='utf-8') as f:
                    for line in f:
                        try:
                            data = json.loads(line)
                            # Cleaned OCR is per symbol, but might relate back to an original frame_timestamp
                            # if we can link it via original_ocr_event_id or a propagated validation_id.
                            # For now, let's assume it has a frame_timestamp or equivalent for grouping.
                            # The current `publish_cleaned_ocr` logs each symbol snapshot as a line.
                            # We need a common key. If PipelineValidator was used, the validation_id is key.
                            # If not, correlation is harder.
                            # Let's assume for now cleaned data has a 'frame_timestamp' field derived from original.
                            if 'frame_timestamp' in data: # This might need adjustment based on actual cleaned_ocr format
                                 self.cleaned_ocr_data_by_frame_ts[data['frame_timestamp']].append(data)
                        except json.JSONDecodeError:
                            logger.warning(f"Skipping malformed JSON line in {cleaned_ocr_file}")
            except Exception as e:
                logger.error(f"Error loading {cleaned_ocr_file}: {e}", exc_info=True)
        logger.info(f"Loaded {len(self.raw_ocr_data_by_frame_ts)} raw OCR frames and "
                    f"{len(self.cleaned_ocr_data_by_frame_ts)} cleaned OCR frames from observability logs.")


    def calculate_pipeline_latencies(self, pipeline_type: str = "ocr_to_signal") -> List[Dict]:
        """
        Calculates latencies for each validated item in a specific pipeline.
        Returns a list of dicts, each dict containing uid and calculated stage-to-stage latencies.
        """
        processed_items = []
        if not self.pv_results:
            logger.warning("PipelineValidator results not provided to ReportGenerator.")
            return processed_items

        for uid, data in self.pv_results.items():
            if data.get("pipeline_type") != pipeline_type or not data.get("completed_fully"):
                continue

            item_latencies = {"uid": uid, "overall_e2e_latency_ms": data.get("end_to_end_latency_sec", 0) * 1000}
            stages = data.get("stages", {})
            
            # Get sorted stage names based on expected order for this pipeline type
            # (Assumes PipelineValidator has self._expected_stages)
            # This part needs access to PipelineValidator's stage definitions.
            # For simplicity here, we'll hardcode the ocr_to_signal expected stages.
            # A better design might pass expected_stages map to ReportGenerator.
            expected_stages = []
            if pipeline_type == "ocr_to_signal":
                 expected_stages = [
                     'ocr_raw_data_received_by_appcore', 'ocr_conditioning_started',
                     'ocr_conditioning_completed', 'ocr_cleaned_data_enqueued_to_orchestrator',
                     'orchestrator_signal_logic_started', 'orchestrator_signal_logic_completed',
                     'order_request_published'
                 ]
            # Add other pipeline types here if needed

            last_stage_ts = data.get("injected_at", 0)
            for i, stage_name in enumerate(expected_stages):
                current_stage_info = stages.get(stage_name)
                if current_stage_info and current_stage_info.get('status') == 'COMPLETED':
                    current_stage_ts = current_stage_info.get('completed_at', 0)
                    if i == 0: # First defined stage, latency from injection time
                        latency_key = f"latency_inject_to_{stage_name}_ms"
                        item_latencies[latency_key] = (current_stage_ts - data.get("injected_at", 0)) * 1000
                    elif last_stage_ts > 0 : # For subsequent stages
                        prev_stage_name = expected_stages[i-1]
                        latency_key = f"latency_{prev_stage_name}_to_{stage_name}_ms"
                        item_latencies[latency_key] = (current_stage_ts - last_stage_ts) * 1000
                    
                    last_stage_ts = current_stage_ts
                else: # Stage missing or not completed
                    item_latencies[f"latency_MISSING_STAGE_{stage_name}_ms"] = None
                    last_stage_ts = 0 # Reset last_stage_ts if a stage is broken

            processed_items.append(item_latencies)
        return processed_items

    def generate_text_report(self, 
                             pipeline_type_to_analyze: str = "ocr_to_signal",
                             num_worst_latencies: int = 5) -> str:
        """Generates a text-based summary report."""
        report_lines = ["Trade Lifecycle Analysis Report"]
        report_lines.append("="*30)

        # 1. PerformanceBenchmarker Summary
        report_lines.append("\n--- PerformanceBenchmarker Statistics ---")
        if self.pb_stats:
            for metric_name, stats in self.pb_stats.items():
                if stats.get('count', 0) > 0:
                    report_lines.append(f"Metric: {metric_name}")
                    for stat_key, stat_val in stats.items():
                        if isinstance(stat_val, float):
                            report_lines.append(f"  {stat_key.capitalize()}: {stat_val:.4f} ms")
                        else:
                            report_lines.append(f"  {stat_key.capitalize()}: {stat_val}")
                    report_lines.append("") # Newline
                else:
                    report_lines.append(f"Metric: {metric_name}\n  No data collected or count is zero.\n")
        else:
            report_lines.append("No PerformanceBenchmarker data provided.")
        
        # 2. PipelineValidator Summary for the specified pipeline
        report_lines.append(f"\n--- PipelineValidator Summary for '{pipeline_type_to_analyze}' ---")
        if self.pv_results:
            # Use PipelineValidator's own analyze_pipeline_run for summary
            # This requires the ReportGenerator to have an instance of PipelineValidator or its _expected_stages
            # For now, let's assume we re-calculate some summary stats here
            pipeline_items = [data for data in self.pv_results.values() if data.get("pipeline_type") == pipeline_type_to_analyze]
            total_injected = len(pipeline_items)
            completed_all = sum(1 for data in pipeline_items if data.get("completed_fully"))
            failed = total_injected - completed_all
            
            report_lines.append(f"Total Injected for '{pipeline_type_to_analyze}': {total_injected}")
            report_lines.append(f"Completed All Stages: {completed_all}")
            report_lines.append(f"Failed/Incomplete: {failed}")

            latencies_e2e = [item["end_to_end_latency_sec"] * 1000 for item in pipeline_items if item.get("completed_fully") and "end_to_end_latency_sec" in item]
            if latencies_e2e:
                report_lines.append("\nEnd-to-End Latency (ms) for Completed Items:")
                report_lines.append(f"  Count: {len(latencies_e2e)}")
                report_lines.append(f"  Min: {min(latencies_e2e):.4f}")
                report_lines.append(f"  Max: {max(latencies_e2e):.4f}")
                report_lines.append(f"  Mean: {statistics.mean(latencies_e2e):.4f}")
                report_lines.append(f"  Median: {statistics.median(latencies_e2e):.4f}")
                if len(latencies_e2e) >= 20: # For p95/p99
                    latencies_e2e.sort()
                    report_lines.append(f"  P95: {latencies_e2e[int(len(latencies_e2e)*0.95)-1]:.4f}")
                    report_lines.append(f"  P99: {latencies_e2e[int(len(latencies_e2e)*0.99)-1]:.4f}")
            
            # Stage-to-Stage Latency Summaries
            detailed_latencies = self.calculate_pipeline_latencies(pipeline_type_to_analyze)
            stage_latency_data = defaultdict(list)
            for item_detail in detailed_latencies:
                for key, val in item_detail.items():
                    if key.startswith("latency_") and val is not None:
                        stage_latency_data[key].append(val)
            
            if stage_latency_data:
                report_lines.append("\nStage-to-Stage Latency Summaries (ms):")
                for stage_lat_name, lats in sorted(stage_latency_data.items()):
                    if lats:
                        report_lines.append(f"  {stage_lat_name}:")
                        report_lines.append(f"    Count: {len(lats)}")
                        report_lines.append(f"    Min: {min(lats):.4f}")
                        report_lines.append(f"    Max: {max(lats):.4f}")
                        report_lines.append(f"    Mean: {statistics.mean(lats):.4f}")
                        report_lines.append(f"    Median: {statistics.median(lats):.4f}")
                        if len(lats) >= 20:
                            lats.sort()
                            report_lines.append(f"    P95: {lats[int(len(lats)*0.95)-1]:.4f}")
                            report_lines.append(f"    P99: {lats[int(len(lats)*0.99)-1]:.4f}")
            
            # Worst N Latencies
            if latencies_e2e and num_worst_latencies > 0:
                report_lines.append(f"\nTop {num_worst_latencies} Worst End-to-End Latencies for '{pipeline_type_to_analyze}':")
                # Sort items by e2e latency
                sorted_items_by_latency = sorted(
                    [item for item in pipeline_items if item.get("completed_fully") and "end_to_end_latency_sec" in item],
                    key=lambda x: x["end_to_end_latency_sec"],
                    reverse=True
                )
                for i, item_data in enumerate(sorted_items_by_latency[:num_worst_latencies]):
                    report_lines.append(f"  {i+1}. UID: {item_data.get('uid', 'N/A')}, E2E Latency: {item_data['end_to_end_latency_sec']*1000:.2f} ms")
                    # Optionally print a few key stage latencies for these worst items
                    item_detail = next((d for d in detailed_latencies if d["uid"] == item_data.get('uid')), None)
                    if item_detail:
                        for stage_key, stage_lat in item_detail.items():
                            if stage_key.startswith("latency_") and stage_lat is not None and stage_lat > 10: # Show impactful stages
                                report_lines.append(f"     - {stage_key}: {stage_lat:.2f} ms")
        else:
            report_lines.append("No PipelineValidator data provided or no items matched pipeline type.")

        report_lines.append("\n--- End of Report ---")
        return "\n".join(report_lines)

    def save_report(self, report_string: str, output_file: str = "trade_lifecycle_analysis_report.txt"):
        """Saves the generated report string to a file."""
        try:
            Path(output_file).parent.mkdir(parents=True, exist_ok=True)
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(report_string)
            logger.info(f"Analysis report saved to: {output_file}")
        except Exception as e:
            logger.error(f"Failed to save analysis report to {output_file}: {e}", exc_info=True)

# Example usage (if run as a script)
if __name__ == '__main__':
    # This would be run after a test, loading data from files or a database
    # For example purposes, let's assume pv_results_data and pb_stats_data are loaded
    
    # Dummy data for illustration
    pv_results_data = {
        "uid1": {
            "injected_at": time.time() - 0.5, "pipeline_type": "ocr_to_signal", "completed_fully": True, 
            "end_to_end_latency_sec": 0.050,
            "stages": {
                "ocr_raw_data_received_by_appcore": {"completed_at": time.time() - 0.49, "status": "COMPLETED"},
                "ocr_conditioning_started": {"completed_at": time.time() - 0.48, "status": "COMPLETED"},
                "ocr_conditioning_completed": {"completed_at": time.time() - 0.40, "status": "COMPLETED"},
                "ocr_cleaned_data_enqueued_to_orchestrator": {"completed_at": time.time() - 0.39, "status": "COMPLETED"},
                "orchestrator_signal_logic_started": {"completed_at": time.time() - 0.38, "status": "COMPLETED"},
                "orchestrator_signal_logic_completed": {"completed_at": time.time() - 0.05, "status": "COMPLETED"},
                "order_request_published": {"completed_at": time.time() - 0.045, "status": "COMPLETED"}
            }
        }
    }
    pb_stats_data = {
        "ocr.conditioning_time_ms": {"count": 100, "mean": 5.0, "median": 4.0, "p95": 10.0, "p99": 15.0, "min": 1.0, "max": 20.0, "p50": 4.0, "stddev": 2.0},
        "signal_gen.ocr_cleaned_to_order_request_latency_ms": {"count": 10, "mean": 30.0, "median": 25.0, "p95": 50.0, "p99": 60.0, "min": 10.0, "max": 70.0, "p50": 25.0, "stddev": 5.0}
    }

    # Example of how the ReportGenerator might be used:
    logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(name)s: %(message)s")
    
    report_gen = ReportGenerator(
        pipeline_validator_results=pv_results_data,
        performance_benchmarker_stats=pb_stats_data
    )
    # report_gen.load_observability_data() # Optionally load more data

    text_report = report_gen.generate_text_report(pipeline_type_to_analyze="ocr_to_signal")
    print(text_report)
    report_gen.save_report(text_report, "sample_analysis_report.txt")

    # Example of getting detailed latencies for further processing
    # detailed_ocr_latencies = report_gen.calculate_pipeline_latencies("ocr_to_signal")
    # if detailed_ocr_latencies:
    #     logger.info(f"First detailed OCR latency item: {detailed_ocr_latencies[0]}")