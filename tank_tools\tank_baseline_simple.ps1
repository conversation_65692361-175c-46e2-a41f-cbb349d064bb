# tank_baseline_simple.ps1 - Simple baseline comparison tool
param(
    [string]$ProcessName = "python",
    [int]$BaselineSamples = 5,
    [int]$MonitorMinutes = 30
)

Write-Host "TANK Simple Baseline Comparison Tool" -ForegroundColor Green
Write-Host "Process: $ProcessName | Baseline Samples: $BaselineSamples | Monitor: $MonitorMinutes minutes" -ForegroundColor Cyan
Write-Host "=" * 80

# Function to create simple baseline
function New-SimpleBaseline {
    param([string]$ProcessName, [int]$Samples)
    
    Write-Host "Creating baseline with $Samples samples..." -ForegroundColor Yellow
    $measurements = @()
    
    for ($i = 1; $i -le $Samples; $i++) {
        try {
            $process = Get-Process $ProcessName -ErrorAction Stop | Select-Object -First 1
            $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
            $measurements += $memoryMB
            Write-Host "  Sample $i/$Samples`: $memoryMB MB" -ForegroundColor White
            if ($i -lt $Samples) { Start-Sleep 10 }
        } catch {
            Write-Host "  Failed to get sample $i" -ForegroundColor Red
        }
    }
    
    if ($measurements.Count -eq 0) {
        throw "No baseline measurements collected"
    }
    
    $stats = $measurements | Measure-Object -Average -Maximum -Minimum
    return @{
        Average = [math]::Round($stats.Average, 2)
        Maximum = [math]::Round($stats.Maximum, 2)
        Minimum = [math]::Round($stats.Minimum, 2)
        Samples = $measurements
        Created = Get-Date
    }
}

# Create baseline
try {
    $processes = Get-Process $ProcessName -ErrorAction Stop
    Write-Host "Found $($processes.Count) processes matching '$ProcessName'" -ForegroundColor Green
    
    $baseline = New-SimpleBaseline -ProcessName $ProcessName -Samples $BaselineSamples
    Write-Host "Baseline created successfully!" -ForegroundColor Green
    Write-Host "  Average: $($baseline.Average) MB" -ForegroundColor Cyan
    Write-Host "  Range: $($baseline.Minimum) - $($baseline.Maximum) MB" -ForegroundColor Cyan
} catch {
    Write-Host "Error creating baseline: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Starting monitoring against baseline..." -ForegroundColor Green
Write-Host "=" * 50

$startTime = Get-Date
$endTime = $startTime.AddMinutes($MonitorMinutes)
$sampleCount = 0
$alertCount = 0

while ((Get-Date) -lt $endTime) {
    try {
        $process = Get-Process $ProcessName -ErrorAction Stop | Select-Object -First 1
        $currentTime = Get-Date
        $elapsed = ($currentTime - $startTime).TotalMinutes
        $sampleCount++
        
        $currentMemoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
        $delta = $currentMemoryMB - $baseline.Average
        $deltaPercent = ($delta / $baseline.Average) * 100
        
        # Status determination
        $status = if ($deltaPercent -gt 25) { 
            $alertCount++
            "ALERT" 
        } elseif ($deltaPercent -gt 15) { 
            "WARNING" 
        } elseif ($deltaPercent -lt -15) { 
            "LOW" 
        } else { 
            "NORMAL" 
        }
        
        $statusColor = switch ($status) {
            "ALERT" { "Red" }
            "WARNING" { "Yellow" }
            "LOW" { "Cyan" }
            default { "Green" }
        }
        
        Clear-Host
        Write-Host "TANK Simple Baseline Comparison - Real Time" -ForegroundColor Green
        Write-Host "=" * 60
        Write-Host "Elapsed: $([math]::Round($elapsed, 1)) min | Remaining: $([math]::Round(($endTime - $currentTime).TotalMinutes, 1)) min" -ForegroundColor Cyan
        Write-Host "Sample: $sampleCount | Alerts: $alertCount" -ForegroundColor Cyan
        Write-Host ""
        
        Write-Host "BASELINE COMPARISON:" -ForegroundColor Yellow
        Write-Host "  Current Memory:  $currentMemoryMB MB" -ForegroundColor White
        Write-Host "  Baseline Avg:    $($baseline.Average) MB" -ForegroundColor Gray
        Write-Host "  Delta:           $(if($delta -ge 0){'+'})$([math]::Round($delta, 2)) MB ($(if($deltaPercent -ge 0){'+'})$([math]::Round($deltaPercent, 1))%)" -ForegroundColor $statusColor
        Write-Host "  Status:          $status" -ForegroundColor $statusColor
        Write-Host ""
        
        Write-Host "BASELINE DETAILS:" -ForegroundColor Yellow
        Write-Host "  Baseline Range:  $($baseline.Minimum) - $($baseline.Maximum) MB" -ForegroundColor Gray
        Write-Host "  Created:         $($baseline.Created.ToString('HH:mm:ss'))" -ForegroundColor Gray
        Write-Host "  Samples:         $($baseline.Samples.Count)" -ForegroundColor Gray
        Write-Host ""
        
        if ($status -eq "ALERT") {
            Write-Host "ALERT: Memory usage significantly above baseline!" -ForegroundColor Red -BackgroundColor Yellow
        } elseif ($status -eq "WARNING") {
            Write-Host "WARNING: Memory usage elevated above baseline" -ForegroundColor Yellow
        } elseif ($status -eq "NORMAL") {
            Write-Host "GOOD: Memory usage within normal range" -ForegroundColor Green
        }
        
        Write-Host ""
        Write-Host "Next sample in 30 seconds... (Ctrl+C to stop)" -ForegroundColor Gray
        
        Start-Sleep 30
        
    } catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        Start-Sleep 10
    }
}

Write-Host ""
Write-Host "MONITORING COMPLETE" -ForegroundColor Green
Write-Host "=" * 30
Write-Host "Total Samples: $sampleCount" -ForegroundColor White
Write-Host "Total Alerts: $alertCount" -ForegroundColor $(if($alertCount -gt 0) { "Red" } else { "Green" })

if ($alertCount -eq 0) {
    Write-Host "ASSESSMENT: Performance within baseline parameters" -ForegroundColor Green
} elseif ($alertCount -lt ($sampleCount * 0.2)) {
    Write-Host "ASSESSMENT: Occasional deviations from baseline" -ForegroundColor Yellow
} else {
    Write-Host "ASSESSMENT: Frequent deviations from baseline - investigate" -ForegroundColor Red
}

Write-Host ""
Write-Host "Monitoring complete!" -ForegroundColor Green
