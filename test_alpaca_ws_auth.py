#!/usr/bin/env python3
"""
Test Alpaca WebSocket Authentication Flow
"""
import asyncio
import websockets
import json
import sys
import os

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.global_config import GlobalConfig

async def test_alpaca_ws_auth():
    """Test the Alpaca WebSocket authentication flow"""
    
    # Load credentials
    config = GlobalConfig()
    api_key = config.ALPACA_API_KEY
    api_secret = config.ALPACA_API_SECRET
    
    print(f"Testing Alpaca WebSocket authentication...")
    print(f"API Key: {api_key[:8]}...")
    
    # Connect to Alpaca WebSocket
    ws_url = "wss://stream.data.alpaca.markets/v2/sip"
    
    try:
        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket connected to Alpaca SIP")
            
            # Send authentication message
            auth_msg = {
                "action": "auth",
                "key": api_key,
                "secret": api_secret
            }
            auth_json = json.dumps(auth_msg)
            
            print(f"📤 Sending auth message: {len(auth_json)} bytes")
            await websocket.send(auth_json)
            
            # Wait for response messages
            print("📥 Waiting for authentication response...")
            
            for i in range(5):  # Wait for up to 5 messages
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(message)
                    
                    print(f"📦 Message {i+1}: {data}")
                    
                    # Check for authentication success
                    if isinstance(data, list):
                        for item in data:
                            msg_type = item.get("T")
                            if msg_type == "success":
                                msg = item.get("msg", "")
                                if "authenticated" in msg:
                                    print("✅ Authentication successful!")
                                    return True
                                elif "connected" in msg:
                                    print("🔗 Connection established, waiting for auth confirmation...")
                    
                except asyncio.TimeoutError:
                    print(f"⏱️ Timeout waiting for message {i+1}")
                    break
                except json.JSONDecodeError as e:
                    print(f"❌ JSON decode error: {e}")
                    break
                    
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")
        return False
    
    print("❌ Authentication confirmation not received")
    return False

if __name__ == "__main__":
    result = asyncio.run(test_alpaca_ws_auth())
    sys.exit(0 if result else 1)