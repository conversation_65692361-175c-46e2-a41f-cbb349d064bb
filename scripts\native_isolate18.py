import time
import logging
import sys
import os

# Add the parent directory to sys.path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import config modules
from config import Config
from utils.global_config import load_global_config, GlobalConfig

logging.basicConfig(level=logging.ERROR, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# ---- PATCH_MAIN.PY INJECTION POINT ----

def main():
    logger.error("NATIVE_ISOLATE18: Starting. Loading config...")
    try:
        load_global_config()  # Load the global configuration
        logger.error(f"NATIVE_ISOLATE18: Config loaded.")
    except Exception as e:
        logger.error(f"NATIVE_ISOLATE18: Error loading config: {e}")

    logger.error("NATIVE_ISOLATE18: Sleeping for 15 minutes.")
    end_time = time.time() + 15 * 60 
    while time.time() < end_time:
        try: time.sleep(1) 
        except KeyboardInterrupt: break
    logger.error("NATIVE_ISOLATE18: Finished sleep.")

if __name__ == "__main__":
    logger.error("NATIVE_ISOLATE18: __main__ block entered.")
    main()
    logger.error("NATIVE_ISOLATE18: __main__ block finished.")
