# data_models/enums.py

"""
Consolidated trading enums for TESTRADE system.

This module contains all trading-related enums in a single source of truth.
Previously scattered across core/enums.py, modules/order_management/order_data_types.py,
and data_models/trading_data_types.py.
"""

from enum import Enum


class OrderSide(Enum):
    """Order side enumeration."""
    BUY = "BUY"
    SELL = "SELL"
    UNKNOWN = "UNKNOWN"

    @classmethod
    def from_str(cls, value: str):
        """Convert string to OrderSide enum with fallback to UNKNOWN."""
        try:
            return cls(value.upper())
        except ValueError:
            return cls.UNKNOWN


class OrderOrigin(Enum):
    """Order origin enumeration for tracking where orders come from."""
    PYTHON = "python"      # User/system initiated through Python
    BROKER = "broker"      # Broker-initiated (rare)
    SYNC = "sync"         # Created during position sync
    RECOVERY = "recovery"  # Created during crash recovery
    
    @property
    def requires_broker_link(self) -> bool:
        """Check if this origin type requires broker order ID linking."""
        return self in [OrderOrigin.BROKER, OrderOrigin.SYNC]


class OrderLSAgentStatus(Enum):
    """Order status enumeration for Lightspeed Agent."""
    # Internal Python States before broker interaction
    PENDING_SUBMISSION = "PendingSubmission"
    SEND_ATTEMPTED_TO_BRIDGE = "SendAttemptedToBridge"
    SEND_FAILED_TO_BRIDGE = "SendFailedToBridge"

    # States from Lightspeed C++ Bridge / LS API (or our interpretation)
    ACKNOWLEDGED_BY_BROKER = "AcknowledgedByBroker"
    SUBMITTED_TO_BROKER = "SubmittedToBroker"
    WORKING = "Working"
    PARTIALLY_FILLED = "PartiallyFilled"
    FILLED = "Filled"
    CANCEL_REQUESTED_TO_BRIDGE = "CancelRequestedToBridge"
    PENDING_CANCEL = "PendingCancel"
    CANCELLED = "Cancelled"
    REJECTED = "Rejected"
    CANCEL_REJECTED = "CancelRejected"

    # Specific Lightspeed API L_OrderChange categories
    LS_SUBMITTED = "Submitted"
    LS_ACCEPTED_BY_EXCHANGE = "Accepted"
    LS_REJECTION_FROM_BROKER = "Rejection"
    LS_KILLED = "Killed"

    # Additional terminal statuses that brokers might send
    EXPIRED = "Expired"
    DONE_FOR_DAY = "DoneForDay"
    STOPPED = "Stopped"

    # Existing statuses from OrderRepository
    PENDING = "Pending"
    SEND_FAILED = "SendFailed"
    CANCEL_REQUESTED = "CancelRequested"

    # Additional statuses found in the codebase
    SENT_TO_LIGHTSPEED = "SentToLightspeed"
    FULL_FILL = "FullFill"

    # Fallback status
    UNKNOWN = "Unknown"

    @classmethod
    def from_str(cls, value: str):
        """Convert string to OrderLSAgentStatus enum with fallback to UNKNOWN."""
        try:
            # Map common variations
            upper_value = value.upper().replace(" ", "")
            if upper_value == "CANCELED": 
                upper_value = "CANCELLED"
            if upper_value == "PARTIALLYFILLED": 
                upper_value = "PARTIALLY_FILLED"
            return cls(upper_value)
        except (ValueError, AttributeError):
            return cls.UNKNOWN


class TradeActionType(Enum):
    """Trade action type enumeration for position management."""
    OPEN_LONG = "OPEN"  # Matches old event type for now
    ADD_LONG = "ADD"
    REDUCE_PARTIAL = "REDUCE"
    REDUCE_FULL = "REDUCE_FULL"  # Full reduction but not closing position
    CLOSE_POSITION = "CLOSE"  # Standard close signal
    DO_NOTHING = "DO_NOTHING"  # Used when no action is needed
    UNKNOWN = "UNKNOWN"  # Fallback value
    # Add others like OPEN_SHORT, CLOSE_SHORT if needed


class OrderEventType(Enum):
    """Order event type enumeration."""
    FILL = "FILL"
    PARTIAL_FILL = "PARTIAL_FILL"
    CANCEL = "CANCEL"
    REJECT = "REJECT"
    
    # From TradeManagerService / SnapshotInterpreter / OrderStrategy / TradeExecutor
    OPEN_LONG = "OPEN_LONG"
    ADD_LONG = "ADD_LONG"
    CLOSE_LONG = "CLOSE_LONG"
    REDUCE_LONG = "REDUCE_LONG"
    OPEN_SHORT = "OPEN_SHORT"
    ADD_SHORT = "ADD_SHORT"
    CLOSE_SHORT = "CLOSE_SHORT"
    REDUCE_SHORT = "REDUCE_SHORT"
    
    # Extended version with additional event types specific to order management
    REDUCE_PARTIAL = "REDUCE_PARTIAL"
    CLOSE_POSITION = "CLOSE_POSITION"

    # Specific Event Types for OCR Scalping Signal Generator
    OCR_SCALPING_OPEN_LONG = "OCR_SCALPING_OPEN_LONG"
    OCR_SCALPING_ADD_LONG = "OCR_SCALPING_ADD_LONG"
    OCR_SCALPING_REDUCE_PARTIAL = "OCR_SCALPING_REDUCE_PARTIAL"
    OCR_SCALPING_CLOSE_POSITION = "OCR_SCALPING_CLOSE_POSITION"

    # From manual actions or system-level actions
    MANUAL_ADD = "MANUAL_ADD"
    MANUAL_OPEN = "MANUAL_OPEN"
    MANUAL_CLOSE = "MANUAL_CLOSE"
    FORCE_CLOSE_SYMBOL = "FORCE_CLOSE_SYMBOL"
    FORCE_CLOSE_ALL = "FORCE_CLOSE_ALL"
    LIQUIDATE_AGGRESSIVE_CHASE = "LIQUIDATE_AGGRESSIVE_CHASE"
    CRITICAL_RISK_FLATTEN = "CRITICAL_RISK_FLATTEN"

    # From OrderRepository reprice methods
    REPRICE_OPEN = "REPRICE_OPEN"
    REPRICE_ADD = "REPRICE_ADD"
    REPRICE_CLOSE = "REPRICE_CLOSE"

    # From OrderRepository handling of unlinked/manual broker orders
    MANUAL_BROKER_ORDER = "MANUAL_BROKER_ORDER"
    MANUAL_SELL_NO_POS = "MANUAL_SELL_NO_POS"

    # Specific chase event types
    CHASE_SELL_INIT_LMT = "CHASE_SELL_INIT_LMT"
    CHASE_SELL_INIT_MKT = "CHASE_SELL_INIT_MKT"
    CHASE_SELL_PEG = "CHASE_SELL_PEG"
    CHASE_SELL_FINAL_MKT = "CHASE_SELL_FINAL_MKT"

    # General "system" event types
    OPEN = "OPEN"
    ADD = "ADD"
    REDUCE = "REDUCE"
    CLOSE = "CLOSE"
    MANUAL = "MANUAL"
    LIQUIDATE_CHASE = "LIQUIDATE_CHASE"
    LIQUIDATE_CHASE_SELL_INIT = "LIQUIDATE_CHASE_SELL_INIT"

    # Broker message event types
    ORDER_STATUS = "ORDER_STATUS"
    POSITIONS_UPDATE = "POSITIONS_UPDATE"
    ACCOUNT_UPDATE = "ACCOUNT_UPDATE"
    ORDER_UPDATE_EVENT = "ORDER_UPDATE_EVENT"
    FILL_EVENT = "FILL_EVENT"
    ACCOUNT_SUMMARY_EVENT = "ACCOUNT_SUMMARY_EVENT"
    CROSS_OWN_ERROR = "CROSS_OWN_ERROR"
    PING_RESPONSE = "PING_RESPONSE"
    TRADING_STATUS = "TRADING_STATUS"

    # Force close and risk-related event types
    FORCE_CLOSE = "FORCE_CLOSE"
    REDUCE_FULL = "REDUCE_FULL"

    UNKNOWN = "UNKNOWN"


# Additional enums from other modules
class LifecycleState(Enum):
    """Lifecycle state enumeration for trade management."""
    NONE = "NONE"
    OPEN_PENDING = "OPEN_PENDING"
    OPEN_ACTIVE = "OPEN_ACTIVE"
    CLOSE_PENDING = "CLOSE_PENDING"
    CLOSED = "CLOSED"
    ERROR = "ERROR"


class RiskLevel(Enum):
    """Risk level enumeration for risk management."""
    UNKNOWN = "UNKNOWN"
    NORMAL = "NORMAL"
    ELEVATED = "ELEVATED"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"
    HALTED = "HALTED"


class CommandStatus(Enum):
    """Command status enumeration for GUI commands."""
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    WARNING = "WARNING"
    INFO = "INFO"