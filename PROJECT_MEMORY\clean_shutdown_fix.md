# Clean Shutdown Fix - June 27, 2025

## Problem
When hitting Ctrl+C, services continued logging "queue size = 0" messages because worker threads weren't properly stopped.

## Solution
Modified ApplicationCore._manual_service_shutdown() to:

1. Added services to shutdown order:
   - ocr_signal_orchestrator_service (stop first to prevent new orders)
   - risk_management_service (has PMD worker threads)

2. Modified shutdown loop to check both:
   - _services dict (old style)
   - Direct attributes like self.ocr_signal_orchestrator_service (new DI style)

3. Services already had stop() methods that:
   - Set internal stop events for worker threads
   - Drain queues
   - Shutdown thread pools

## Result
Clean shutdown with Ctrl+C:
- All worker threads stop properly
- No more "queue size = 0" spam
- Services shutdown in correct order
- Graceful termination

## Services with stop() methods:
- OCRScalpingSignalOrchestratorService
- RiskManagementService
- (Others may also have them)

The DI container's shutdown_all() should handle this automatically, but the manual shutdown provides a fallback.