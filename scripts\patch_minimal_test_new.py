#!/usr/bin/env python3
"""
Patch script for minimal_test_new.py to add tracemalloc monitoring.
This script:
1. Reads the original minimal_test_new.py
2. Injects tracemalloc code
3. Writes a new minimal_test_new.py with the tracemalloc monitoring code
"""

import os
import sys
import re
import datetime
import shutil

# Configuration
WAIT_DURATION_MINUTES = 15  # How long to wait before taking the snapshot
BACKUP_SUFFIX = '.bak'      # Suffix for the backup file

def get_timestamp():
    """Get a timestamp string for filenames."""
    return datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

def patch_minimal_test_py():
    """Patch minimal_test_new.py to add tracemalloc monitoring."""
    # Paths
    minimal_test_py_path = 'scripts/minimal_test_new.py'
    backup_path = f'{minimal_test_py_path}{BACKUP_SUFFIX}'
    
    # Check if minimal_test_new.py exists
    if not os.path.exists(minimal_test_py_path):
        print(f"Error: {minimal_test_py_path} not found.")
        return False
    
    # Create backup
    print(f"Creating backup of {minimal_test_py_path} to {backup_path}")
    shutil.copy2(minimal_test_py_path, backup_path)
    
    # Read the original file
    with open(minimal_test_py_path, 'r') as f:
        content = f.read()
    
    # Prepare the tracemalloc code to inject
    tracemalloc_import = """
import tracemalloc
import time
import threading
import datetime
import os
"""

    tracemalloc_init = """
# Initialize tracemalloc
tracemalloc.start()
print("Tracemalloc started for memory tracking")

# Function to take a memory snapshot after a delay
def take_memory_snapshot():
    # Wait for the specified duration
    print(f"Will take memory snapshot after {WAIT_DURATION_MINUTES} minutes...")
    time.sleep(WAIT_DURATION_MINUTES * 60)
    
    # Take the snapshot
    snapshot = tracemalloc.take_snapshot()
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    snapshot_path = f"tracemalloc_minimal_{timestamp}.log"
    
    print(f"Taking memory snapshot after {WAIT_DURATION_MINUTES} minutes...")
    
    # Save top statistics
    with open(snapshot_path, 'w') as f:
        f.write(f"=== Memory snapshot after {WAIT_DURATION_MINUTES} minutes ===\\n")
        f.write(f"Timestamp: {datetime.datetime.now()}\\n\\n")
        
        # Overall statistics
        f.write("=== Overall Statistics ===\\n")
        f.write(f"Current traced memory: {tracemalloc.get_traced_memory()[0] / 1024 / 1024:.2f} MB\\n")
        f.write(f"Peak traced memory: {tracemalloc.get_traced_memory()[1] / 1024 / 1024:.2f} MB\\n\\n")
        
        # Top statistics by line
        f.write("=== Top 100 memory allocations by line ===\\n")
        top_stats = snapshot.statistics('lineno')
        for i, stat in enumerate(top_stats[:100], 1):
            f.write(f"{i}. {stat.count} allocations: {stat.size / 1024:.1f} KiB\\n")
            f.write(f"   {stat.traceback.format()[0]}\\n")
        
        # Top statistics by file
        f.write("\\n=== Top 50 memory allocations by file ===\\n")
        file_stats = snapshot.statistics('filename')
        for i, stat in enumerate(file_stats[:50], 1):
            f.write(f"{i}. {stat.count} allocations: {stat.size / 1024:.1f} KiB - {os.path.basename(stat.traceback[0].filename)}\\n")
        
        # Top statistics by traceback
        f.write("\\n=== Top 50 memory allocations by traceback ===\\n")
        trace_stats = snapshot.statistics('traceback')
        for i, stat in enumerate(trace_stats[:50], 1):
            f.write(f"{i}. {stat.count} allocations: {stat.size / 1024:.1f} KiB\\n")
            for line in stat.traceback.format():
                f.write(f"   {line}\\n")
    
    print(f"Memory snapshot saved to {snapshot_path}")

# Start the snapshot thread
WAIT_DURATION_MINUTES = {WAIT_DURATION_MINUTES}  # Minutes to wait before taking snapshot
snapshot_thread = threading.Thread(target=take_memory_snapshot, daemon=True)
snapshot_thread.start()
"""

    # Check if tracemalloc is already imported
    if "import tracemalloc" in content:
        print("tracemalloc import already exists in minimal_test_new.py")
    else:
        # Add tracemalloc import after other imports
        import_pattern = r"(import .*?\n\n)"
        if re.search(import_pattern, content, re.DOTALL):
            content = re.sub(import_pattern, r"\1" + tracemalloc_import + "\n", content, count=1, flags=re.DOTALL)
        else:
            # If no clear import section, add at the top
            content = tracemalloc_import + "\n" + content

    # Add tracemalloc initialization code after imports but before main code
    if "tracemalloc.start()" in content:
        print("tracemalloc initialization already exists in minimal_test_new.py")
    else:
        # Look for a good spot to insert the initialization code
        # Try after imports and docstrings but before main code
        main_pattern = r"(if __name__ == ['\"]__main__['\"]:|def main\(\):)"
        if re.search(main_pattern, content):
            content = re.sub(main_pattern, tracemalloc_init + "\n\n" + r"\1", content, count=1)
        else:
            # If no clear entry point, add after imports
            content = re.sub(r"(import .*?\n\n)", r"\1" + tracemalloc_init + "\n\n", content, count=1, flags=re.DOTALL)

    # Write the patched file
    with open(minimal_test_py_path, 'w') as f:
        f.write(content)
    
    print(f"Successfully patched {minimal_test_py_path} with tracemalloc monitoring")
    print(f"The snapshot will be taken after {WAIT_DURATION_MINUTES} minutes")
    print(f"Original file backed up to {backup_path}")
    return True

if __name__ == "__main__":
    if patch_minimal_test_py():
        print("Patch completed successfully.")
    else:
        print("Patch failed.")
