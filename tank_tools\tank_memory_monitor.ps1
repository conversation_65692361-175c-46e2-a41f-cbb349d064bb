# tank_memory_monitor.ps1 - Real-time visual monitoring
param(
    [string]$ProcessName = "ApplicationCore",
    [int]$IntervalSeconds = 30,
    [int]$GraphWidth = 80,
    [string]$LogFile = "tank_memory_detailed.csv"
)

# Initialize
$startTime = Get-Date
$maxMemory = 0
$measurements = @()

# Create CSV header
"Timestamp,ElapsedMinutes,MemoryMB,MemoryGB,CPUPercent,HandleCount,ThreadCount,WorkingSetPeak,PagedMemory,NonPagedMemory" | Out-File $LogFile

Write-Host "🚀 TANK Memory Monitor Started" -ForegroundColor Green
Write-Host "Process: $ProcessName | Interval: $IntervalSeconds seconds" -ForegroundColor Cyan
Write-Host "Log File: $LogFile" -ForegroundColor Yellow
Write-Host "=" * 100

while ($true) {
    try {
        $process = Get-Process $ProcessName -ErrorAction Stop
        $currentTime = Get-Date
        $elapsed = ($currentTime - $startTime).TotalMinutes
        
        # Detailed memory metrics
        $memoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
        $memoryGB = [math]::Round($process.WorkingSet64 / 1GB, 3)
        $peakMB = [math]::Round($process.PeakWorkingSet64 / 1MB, 2)
        $pagedMB = [math]::Round($process.PagedMemorySize64 / 1MB, 2)
        $nonPagedMB = [math]::Round($process.NonpagedSystemMemorySize64 / 1MB, 2)
        
        # CPU usage (requires a brief sampling period)
        $cpuBefore = $process.TotalProcessorTime
        Start-Sleep -Milliseconds 100
        $process.Refresh()
        $cpuAfter = $process.TotalProcessorTime
        $cpuUsage = [math]::Round((($cpuAfter - $cpuBefore).TotalMilliseconds / 100), 2)
        
        # Update max memory
        if ($memoryMB -gt $maxMemory) { $maxMemory = $memoryMB }
        
        # Store measurement
        $measurement = [PSCustomObject]@{
            Time = $currentTime
            ElapsedMin = [math]::Round($elapsed, 1)
            MemoryMB = $memoryMB
            MemoryGB = $memoryGB
            Growth = if ($measurements.Count -gt 0) { $memoryMB - $measurements[-1].MemoryMB } else { 0 }
            CPUPercent = $cpuUsage
            Handles = $process.HandleCount
            Threads = $process.Threads.Count
        }
        $measurements += $measurement
        
        # Visual memory graph
        $graphScale = if ($maxMemory -gt 0) { [math]::Min($GraphWidth, [math]::Max(1, $memoryMB / $maxMemory * $GraphWidth)) } else { 1 }
        $graphBar = "█" * $graphScale + "░" * ($GraphWidth - $graphScale)
        
        # Color coding based on growth
        $growthColor = if ($measurement.Growth -gt 50) { "Red" } 
                      elseif ($measurement.Growth -gt 10) { "Yellow" }
                      elseif ($measurement.Growth -lt -10) { "Green" }
                      else { "White" }
        
        # Display current status
        Clear-Host
        Write-Host "🚀 TANK Memory Monitor - Real Time Analysis" -ForegroundColor Green
        Write-Host "=" * 100
        Write-Host "Current Time: $($currentTime.ToString('HH:mm:ss'))" -ForegroundColor Cyan
        Write-Host "Elapsed Time: $([math]::Round($elapsed, 1)) minutes" -ForegroundColor Cyan
        Write-Host ""
        
        Write-Host "💾 MEMORY METRICS:" -ForegroundColor Yellow
        Write-Host "  Current Memory: $memoryMB MB ($memoryGB GB)" -ForegroundColor $growthColor
        Write-Host "  Peak Memory:    $peakMB MB" -ForegroundColor White
        Write-Host "  Growth Rate:    $(if($measurement.Growth -ge 0){'+'})$($measurement.Growth) MB" -ForegroundColor $growthColor
        Write-Host "  Paged Memory:   $pagedMB MB" -ForegroundColor Gray
        Write-Host "  NonPaged Mem:   $nonPagedMB MB" -ForegroundColor Gray
        Write-Host ""
        
        Write-Host "⚡ PERFORMANCE METRICS:" -ForegroundColor Yellow
        Write-Host "  CPU Usage:      $cpuUsage%" -ForegroundColor White
        Write-Host "  Handle Count:   $($process.HandleCount)" -ForegroundColor White
        Write-Host "  Thread Count:   $($process.Threads.Count)" -ForegroundColor White
        Write-Host ""
        
        Write-Host "📊 MEMORY GRAPH (Scale: 0 - $maxMemory MB):" -ForegroundColor Yellow
        Write-Host "[$graphBar] $memoryMB MB" -ForegroundColor $growthColor
        Write-Host ""
        
        # Recent trend analysis
        if ($measurements.Count -ge 5) {
            $recent5 = $measurements | Select-Object -Last 5
            $avgGrowth = ($recent5 | Measure-Object Growth -Average).Average
            $trendText = if ($avgGrowth -gt 5) { "📈 INCREASING" }
                        elseif ($avgGrowth -lt -5) { "📉 DECREASING" }
                        else { "📊 STABLE" }
            
            Write-Host "📈 TREND ANALYSIS (Last 5 measurements):" -ForegroundColor Yellow
            Write-Host "  Average Growth: $('{0:F2}' -f $avgGrowth) MB per interval" -ForegroundColor White
            Write-Host "  Trend Status:   $trendText" -ForegroundColor $(if ($avgGrowth -gt 5) { "Red" } elseif ($avgGrowth -lt -5) { "Green" } else { "Cyan" })
            Write-Host ""
        }
        
        # Alert conditions
        if ($measurement.Growth -gt 100) {
            Write-Host "🚨 ALERT: Memory growth >100MB in $IntervalSeconds seconds!" -ForegroundColor Red -BackgroundColor Yellow
        }
        elseif ($elapsed -gt 30 -and $measurement.Growth -gt 10) {
            Write-Host "⚠️  WARNING: Continued memory growth detected" -ForegroundColor Yellow
        }
        elseif ($elapsed -gt 30 -and $measurement.Growth -eq 0) {
            Write-Host "✅ GOOD: Memory appears stable" -ForegroundColor Green
        }
        
        Write-Host ""
        Write-Host "Press Ctrl+C to stop monitoring..." -ForegroundColor Gray
        
        # Log to CSV
        "$($currentTime.ToString('yyyy-MM-dd HH:mm:ss')),$([math]::Round($elapsed,1)),$memoryMB,$memoryGB,$cpuUsage,$($process.HandleCount),$($process.Threads.Count),$peakMB,$pagedMB,$nonPagedMB" | Out-File $LogFile -Append
        
        Start-Sleep $IntervalSeconds
        
    } catch {
        Write-Host "❌ Error: Process '$ProcessName' not found or access denied" -ForegroundColor Red
        Write-Host "Available processes:" -ForegroundColor Yellow
        Get-Process | Where-Object { $_.ProcessName -like "*tank*" -or $_.ProcessName -like "*application*" } | Format-Table ProcessName, Id, WorkingSet
        Start-Sleep 5
    }
}
