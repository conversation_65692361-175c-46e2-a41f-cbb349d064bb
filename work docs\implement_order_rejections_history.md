# Implement Order Rejections in History

## Problem
Order rejections are not showing up in the history cards, even though they should be displayed as part of the trading history.

## Solution

### 1. Add Order Rejection Handler in gui_backend.py

Add this handler function to process order rejections:

```python
@dispatch_to_event_loop
async def handle_order_rejection_message(message_id: str, raw_redis_message: Dict[str, Any]):
    """Handle order rejection events and add them to history"""
    parsed_message = parse_appcore_message(raw_redis_message)
    if not parsed_message:
        return
    
    payload = parsed_message.get("payload", {})
    
    # Create a history entry for the rejection
    rejection_entry = {
        "trade_id": f"rej_{payload.get('order_id', '')}_{int(time.time())}",
        "symbol": payload.get('symbol', 'UNKNOWN'),
        "side": payload.get('side', 'UNKNOWN'),
        "quantity": payload.get('quantity', 0),
        "order_type": payload.get('order_type', 'MARKET'),
        "status": "REJECTED",
        "rejection_reason": payload.get('reason', 'Unknown reason'),
        "timestamp": int(time.time() * 1000),  # Convert to milliseconds
        "realized_pnl": 0,  # No P&L for rejections
        "is_rejection": True
    }
    
    # Broadcast as trade history update
    broadcast_message = {
        "type": "trade_history_rejection",
        "rejection": rejection_entry
    }
    
    await app_state.websocket_manager.broadcast(broadcast_message)
    logger.info(f"Order rejection broadcasted for {payload.get('symbol', 'UNKNOWN')}: {payload.get('reason', 'Unknown')}")
```

### 2. Add to Redis Stream Handlers

In the `xread_gui_streams_task` function, add this to the handlers list:

```python
{"name": "order_rejections", "stream_name": getattr(app_state.config, 'redis_stream_order_rejections', 'testrade:order-rejections'), "handler": handle_order_rejection_message},
```

### 3. Update Frontend (app.js) to Handle Rejections

Add this case to the WebSocket message handler:

```javascript
case 'trade_history_rejection':
    if (data.rejection) {
        // Add rejection to historical trades
        if (!tradeState.historicalTrades) {
            tradeState.historicalTrades = [];
        }
        
        // Format rejection for display
        const rejection = {
            ...data.rejection,
            type: 'rejection',
            display_status: 'REJECTED',
            pnl: 0,
            realized_pnl: 0
        };
        
        // Add to historical trades
        tradeState.historicalTrades.push(rejection);
        
        // Update display
        updateHistoricalTradesDisplay();
        
        // Show notification
        addSystemMessage(`Order Rejected: ${rejection.symbol} - ${rejection.rejection_reason}`, 'error');
    }
    break;
```

### 4. Update History Card Display for Rejections

Modify the `createTradeHierarchyEntryHTML` function to handle rejections:

```javascript
function createTradeHierarchyEntryHTML(trade, hierarchy = 'NONE') {
    // Check if this is a rejection
    if (trade.is_rejection || trade.type === 'rejection') {
        return `
            <div class="history-card rejection-card">
                <div class="history-card-header">
                    <span class="symbol">${trade.symbol}</span>
                    <span class="rejection-badge">REJECTED</span>
                </div>
                <div class="history-card-body">
                    <div class="trade-details">
                        <span class="side ${trade.side.toLowerCase()}">${trade.side}</span>
                        <span class="quantity">${trade.quantity} shares</span>
                    </div>
                    <div class="rejection-reason">
                        ${trade.rejection_reason || 'Order rejected by broker'}
                    </div>
                    <div class="timestamp">
                        ${new Date(trade.timestamp).toLocaleTimeString()}
                    </div>
                </div>
            </div>
        `;
    }
    
    // ... existing trade card logic ...
}
```

### 5. Add CSS for Rejection Cards

Add this to your CSS:

```css
.rejection-card {
    border-left: 3px solid var(--accent-danger);
    background-color: rgba(255, 0, 0, 0.05);
}

.rejection-badge {
    background-color: var(--accent-danger);
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
}

.rejection-reason {
    color: var(--accent-danger);
    font-size: 12px;
    margin-top: 5px;
}
```

## Testing

To test order rejections:

1. Make sure the market is closed
2. Send any order through the GUI
3. The order should be rejected by the broker
4. The rejection should appear in the history panel immediately

## Expected Behavior

When an order is rejected:
- It appears immediately in the history panel
- Shows with a red "REJECTED" badge
- Displays the rejection reason
- Is included in "Today" and "All" tabs
- Does not count as a winner or loser (P&L = 0)