# TradeExecutor Aggressive Chase Parameter Mapping

## Overview
This document provides the complete mapping between legacy TradeManagerService emergency exit parameters and the new TradeExecutor aggressive chase configuration parameters, ensuring "proven intelligence" is preserved.

## Parameter Mapping Table

| **Legacy Parameter** | **New GlobalConfig Parameter** | **Default Value** | **Description** |
|---------------------|--------------------------------|-------------------|-----------------|
| `max_attempts` | `meltdown_chase_max_duration_sec` | 30.0 | Max chase duration (converted from attempts to time-based) |
| `attempt_delay_ms` | `meltdown_chase_peg_update_interval_sec` | 0.25 | Interval between price updates (300ms → 0.25s) |
| `reprice_aggression_cents` | `meltdown_chase_peg_aggression_ticks` | 1 | Ticks away from bid (cents converted to ticks) |
| N/A (new) | `meltdown_chase_max_slippage_percent` | 2.0 | Max slippage from initial price (%) |
| N/A (new) | `meltdown_chase_final_market_order` | True | Send MKT order on timeout |
| N/A (new) | `CHASE_MAX_PENDING_LINK_TIME_SEC` | 3.0 | Max time in Pending status |
| N/A (new) | `CHASE_MAX_ACK_WAIT_TIME_SEC` | 5.0 | Max time in AcknowledgedByBroker status |

## Legacy vs New Behavior Analysis

### **Legacy Emergency Exit Behavior:**
- **Fixed attempt count** (typically 3-5 attempts)
- **Fixed delay** between attempts (300ms)
- **Cent-based aggression** (e.g., 2 cents below bid)
- **Simple retry logic** without sophisticated state management

### **New Aggressive Chase Behavior:**
- **Time-based duration** (30 seconds max)
- **Continuous price pegging** every 250ms
- **Tick-aware aggression** (1 tick below bid, adapts to price level)
- **Sophisticated order state management** with timeouts
- **Slippage protection** (max 2% from initial price)
- **Final market order fallback** during RTH only

## Configuration Usage in TradeExecutor

### **Parameter Fetching Logic:**
```python
# TradeExecutor._execute_aggressive_sell_chase()
max_chase_duration_sec = chase_config.get("max_chase_duration_sec",
                                        self._config.meltdown_chase_max_duration_sec)
peg_update_interval_sec = chase_config.get("peg_update_interval_sec",
                                         self._config.meltdown_chase_peg_update_interval_sec)
peg_aggression_ticks = chase_config.get("peg_aggression_ticks",
                                      self._config.meltdown_chase_peg_aggression_ticks)
max_chase_slippage_percent = chase_config.get("max_chase_slippage_percent",
                                            self._config.meltdown_chase_max_slippage_percent) / 100.0
final_mkt_order_on_timeout = chase_config.get("final_market_order_on_timeout",
                                            self._config.meltdown_chase_final_market_order)
```

### **Override Mechanism:**
TradeManagerService can pass `aggressive_chase_parameters` in `extra_fields` to override global defaults:
```python
extra_fields = {
    "aggressive_chase_parameters": {
        "max_chase_duration_sec": 45.0,  # Override to 45 seconds
        "peg_aggression_ticks": 2,       # More aggressive (2 ticks)
        "max_chase_slippage_percent": 3.0 # Allow 3% slippage
    }
}
```

## Proven Intelligence Preservation

### **Behavioral Equivalence:**
1. **Legacy 3 attempts @ 300ms = 900ms total**
   - **New equivalent:** 30 seconds with 250ms updates = 120 attempts
   - **Result:** Much more persistent and adaptive

2. **Legacy 2 cents aggression**
   - **New equivalent:** 1 tick (0.01 for stocks >$1, 0.0001 for <$1)
   - **Result:** More precise and price-level appropriate

3. **Legacy simple retry**
   - **New equivalent:** Sophisticated state management with timeouts
   - **Result:** Better handling of broker communication issues

### **Enhanced Capabilities:**
- **Slippage protection** prevents excessive losses
- **RTH validation** for final market orders
- **Performance tracking** integration
- **Comprehensive logging** for debugging
- **Order state timeout handling** for stuck orders

## Configuration File Integration

### **GlobalConfig Defaults (utils/global_config.py):**
```python
# New Aggressive Chase Parameters
meltdown_chase_max_duration_sec: float = 30.0
meltdown_chase_peg_update_interval_sec: float = 0.25
meltdown_chase_peg_aggression_ticks: int = 1
meltdown_chase_max_slippage_percent: float = 2.0
meltdown_chase_final_market_order: bool = True

# Chase Order State Timeout Parameters
CHASE_MAX_PENDING_LINK_TIME_SEC: float = 3.0
CHASE_MAX_ACK_WAIT_TIME_SEC: float = 5.0
```

### **JSON Configuration Override:**
```json
{
  "meltdown_chase_max_duration_sec": 45.0,
  "meltdown_chase_peg_update_interval_sec": 0.2,
  "meltdown_chase_peg_aggression_ticks": 2,
  "meltdown_chase_max_slippage_percent": 3.0,
  "meltdown_chase_final_market_order": true,
  "CHASE_MAX_PENDING_LINK_TIME_SEC": 5.0,
  "CHASE_MAX_ACK_WAIT_TIME_SEC": 8.0
}
```

## Validation and Testing

### **Parameter Validation:**
- All parameters have sensible defaults based on legacy behavior analysis
- Override mechanism allows per-trade customization
- Configuration loading includes backward compatibility

### **Testing Scenarios:**
1. **Default behavior** matches legacy emergency exit intent
2. **Override parameters** work correctly via `extra_fields`
3. **Edge cases** (no price data, broker disconnection) handled gracefully
4. **Performance tracking** captures all chase metrics

## Conclusion

The new TradeExecutor aggressive chase logic successfully preserves the "proven intelligence" of the legacy emergency exit system while providing significant enhancements:

- **Behavioral preservation** through careful parameter mapping
- **Enhanced reliability** through sophisticated state management
- **Improved safety** through slippage protection and RTH validation
- **Better observability** through comprehensive logging and performance tracking
- **Flexible configuration** through global defaults and per-trade overrides

The configuration parameter mapping ensures that the new system maintains the aggressive liquidation capabilities that were proven effective in the legacy system while adding modern safeguards and monitoring capabilities.
