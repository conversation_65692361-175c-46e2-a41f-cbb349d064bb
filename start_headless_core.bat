@echo off
REM TESTRADE Headless Core Startup Script
REM Activates virtual environment and starts run_headless_core.py

echo ========================================
echo TESTRADE Headless Core Startup
echo ========================================

REM Change to TESTRADE directory
cd /d "C:\TESTRADE"

REM Check if virtual environment exists
if not exist ".venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found at .venv\Scripts\activate.bat
    echo Please ensure the virtual environment is set up correctly.
    pause
    exit /b 1
)

echo Activating virtual environment...
call .venv\Scripts\activate.bat

REM Check if run_headless_core.py exists
if not exist "run_headless_core.py" (
    echo ERROR: run_headless_core.py not found in current directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo Starting TESTRADE Headless Core...
echo ========================================
python run_headless_core.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo ========================================
    echo TESTRADE Headless Core exited with error
    echo Error level: %errorlevel%
    echo ========================================
    pause
)

echo.
echo ========================================
echo TESTRADE Headless Core shutdown complete
echo ========================================
pause
