@echo off
echo Setting up Claude Desktop MCP connection...
echo.

REM Get the Claude config directory
set CLAUDE_CONFIG_DIR=%APPDATA%\Claude

REM Check if directory exists
if not exist "%CLAUDE_CONFIG_DIR%" (
    echo Creating Claude config directory...
    mkdir "%CLAUDE_CONFIG_DIR%"
)

REM Create config.json for Claude Desktop
echo Creating MCP configuration...
(
echo {
echo   "mcpServers": {
echo     "intellisense-trading": {
echo       "command": "python",
echo       "args": ["C:/TESTRADE/intellisense/mcp/server_runner.py"],
echo       "cwd": "C:/TESTRADE",
echo       "env": {
echo         "PYTHONPATH": "C:/TESTRADE"
echo       }
echo     }
echo   }
echo }
) > "%CLAUDE_CONFIG_DIR%\config.json"

echo.
echo ✅ Claude Desktop MCP configuration created!
echo.
echo Location: %CLAUDE_CONFIG_DIR%\config.json
echo.
echo NEXT STEPS:
echo 1. Restart Claude Desktop (if it's running)
echo 2. In <PERSON>, you should see "intellisense-trading" in the MCP servers
echo 3. You can then ask <PERSON> to use the IntelliSense trading tools!
echo.
echo Example prompts for <PERSON>:
echo - "Use the intellisense-trading MCP server to show me recent trading events"
echo - "Query the OCR snapshots from the last 30 minutes"
echo - "Run system diagnostics on the trading system"
echo.
pause