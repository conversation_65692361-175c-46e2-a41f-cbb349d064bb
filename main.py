# main.py

import sys
import os

# This ensures that no matter where you run this from, 'core', 'utils', etc. can be found.
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import logging
import time
# from logging.handlers import RotatingFileHandler  # Disabled rotation for consolidated logging

# Import the clean DI architecture
from core.dependency_injection import DIContainer
from core.di_registration import register_all_services
from interfaces.core.application import IApplicationCore as DI_ApplicationCore
from utils.global_config import load_global_config

def setup_logging(config):
    """
    Sets up a robust, consolidated logging system for the application.
    Note: Log rotation disabled for easier debugging and consolidated output.
    """
    log_level_str = getattr(config, 'LOG_LEVEL', 'INFO').upper()
    log_level = logging.getLevelName(log_level_str)
    log_dir = getattr(config, 'LOG_BASE_DIR', 'logs')
    log_file = getattr(config, 'LOG_FILE_MAIN', 'testrade_main.log')

    # Ensure the log directory exists
    os.makedirs(log_dir, exist_ok=True)
    log_path = os.path.join(log_dir, log_file)

    # Create a consolidated file handler (non-rotating for easier debugging)
    # All logs will be appended to a single file for easier analysis
    handler = logging.FileHandler(log_path, mode='a', encoding='utf-8')
    
    # Create a formatter that includes thread names
    formatter = logging.Formatter(
        '%(asctime)s - %(threadName)-15s - %(name)-30s - %(levelname)-8s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    handler.setFormatter(formatter)

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Remove any existing handlers to avoid duplicate logs
    if root_logger.hasHandlers():
        root_logger.handlers.clear()
        
    root_logger.addHandler(handler)

    # Add console logger
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(log_level)
    root_logger.addHandler(console_handler)

    logging.info("--- Logging system initialized ---")
    logging.info(f"Log Level: {log_level_str}")
    logging.info(f"Log File: {log_path}")
    
    # Reduce verbosity of noisy libraries
    logging.getLogger("websockets").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)


def main():
    """
    Main entry point for the TESTRADE application.
    """
    logging.info("--- TESTRADE Application Starting ---")
    
    # 1. Load configuration first
    try:
        config_path = "utils/control.json"
        config = load_global_config(config_path)
        setup_logging(config)
    except Exception as e:
        print(f"CRITICAL: Could not load configuration file. Error: {e}")
        return

    # 2. Create the Dependency Injection Container
    container = DIContainer()
    
    # 3. Register all services and their dependencies
    register_all_services(container)
    
    # --- PHASE 0 HANDSHAKE ---
    logging.info("MAIN: Performing prerequisite broker handshake...")
    from interfaces.broker.services import IBrokerService
    broker = container.resolve(IBrokerService)
    if not broker.establish_connection():
        logging.critical("FATAL: Could not establish connection with broker. Application will not start.")
        return  # Exit the application
    logging.info("MAIN: Broker connection successful.")
    
    # 4. Resolve the top-level application coordinator
    # The DI container will build the entire application graph from here.
    app_core = container.resolve(DI_ApplicationCore)
    
    try:
        # 5. Start the application
        app_core.start()
        
        # --- THIS IS THE CRITICAL FIX ---
        # 6. Main Execution Loop to Keep the Process Alive
        logging.info("System is running. Press Ctrl+C to stop.")
        while True:
            # We check if the application core is still considered "ready"
            # This allows for a programmatic shutdown if needed.
            if hasattr(app_core, 'is_ready') and not app_core.is_ready:
                logging.warning("ApplicationCore is no longer ready. Shutting down main loop.")
                break
            time.sleep(1)  # Block the main thread, polling every second.
        # --- END OF FIX ---
            
    except KeyboardInterrupt:
        logging.info("Keyboard interrupt received. Shutting down...")
    except Exception as e:
        logging.critical("An unhandled exception occurred in the main execution block.", exc_info=True)
    finally:
        # 7. Stop the application
        if 'app_core' in locals() and app_core:
            logging.info("Initiating application shutdown...")
            app_core.stop()
        
        logging.info("--- TESTRADE Application Shutdown Complete ---")

if __name__ == "__main__":
    main()