#!/usr/bin/env python3
"""
TANK Emergency GUI Server
Real-time emergency control interface for TANK Core

EMERGENCY USE ONLY - Bypasses normal backend infrastructure
Provides direct access to critical TANK functions when main GUI is unavailable
"""

import zmq
import json
import time
import threading
import requests
import redis
import os
import sys
from pathlib import Path
from flask import Flask, render_template_string, request, jsonify
from flask_socketio import SocketIO, emit
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add project root to path for imports
project_root = Path(__file__).parent
if project_root.name == 'gui':
    # If running from gui directory, go up one level
    project_root = project_root.parent
sys.path.insert(0, str(project_root))

class EmergencyGUIServer:
    def __init__(self, tank_status_port=5561, tank_command_port=5560, tank_http_port=9998):
        self.tank_status_port = tank_status_port
        self.tank_command_port = tank_command_port
        self.tank_http_port = tank_http_port

        # Try to load configuration from TANK if available
        try:
            from utils.global_config import config  # Use the pre-loaded global config instance
            # Override with config values if available
            self.tank_command_port = getattr(config, 'emergency_command_port', tank_command_port)
            self.tank_status_port = getattr(config, 'emergency_status_port', tank_status_port)
            self.tank_http_port = getattr(config, 'emergency_http_port', tank_http_port)
            logger.info(f"Loaded TANK configuration successfully - HTTP port: {self.tank_http_port}")
        except Exception as e:
            logger.warning(f"Could not load TANK config, using defaults: {e}")
        
        # Flask app with SocketIO
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'tank_emergency_gui_secret'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*", async_mode='threading')
        
        # ZMQ Context
        self.zmq_context = zmq.Context()
        
        # ZMQ Subscriber for real-time status from TANK
        self.status_subscriber = self.zmq_context.socket(zmq.SUB)
        self.status_subscriber.connect(f"tcp://localhost:{self.tank_status_port}")
        self.status_subscriber.setsockopt(zmq.SUBSCRIBE, b"STATUS_UPDATE")
        self.status_subscriber.setsockopt(zmq.SUBSCRIBE, b"CRITICAL_ALERT")
        self.status_subscriber.setsockopt(zmq.RCVTIMEO, 1000)  # 1 second timeout

        # ZMQ Push for commands to TANK
        self.command_socket = self.zmq_context.socket(zmq.PUSH)
        self.command_socket.connect(f"tcp://localhost:{self.tank_command_port}")
        
        # State tracking
        self.connected_clients = 0
        self.last_status = None
        self.zmq_thread_running = False
        self.tank_core_reachable = False
        self.last_tank_contact = 0
        
        # Setup routes and WebSocket handlers
        self.setup_routes()
        self.setup_websocket_handlers()
        
        logger.info("Emergency GUI Server initialized")

    def check_tank_connectivity(self):
        """Check if TANK Core is reachable"""
        try:
            # Quick HTTP health check
            response = requests.get(f'http://localhost:{self.tank_http_port}/localstatus', timeout=2)
            if response.status_code == 200:
                self.tank_core_reachable = True
                self.last_tank_contact = time.time()
                return True
        except:
            pass

        self.tank_core_reachable = False
        return False
    
    def setup_routes(self):
        """Setup HTTP routes"""
        
        @self.app.route('/')
        def dashboard():
            # Read the HTML file directly since it's self-contained
            try:
                # Try multiple possible locations for the HTML file
                possible_paths = [
                    'emergency_gui_html.html',
                    'gui/emergency_gui_html.html',
                    '../emergency_gui_html.html'
                ]
                
                for path in possible_paths:
                    try:
                        with open(path, 'r', encoding='utf-8') as f:
                            return f.read()
                    except FileNotFoundError:
                        continue
                
                # If no file found, return error
                return """
                <html><body>
                <h1>Emergency GUI HTML file not found</h1>
                <p>Searched for: emergency_gui_html.html, gui/emergency_gui_html.html</p>
                </body></html>
                """, 404
            except Exception as e:
                return f"<html><body><h1>Error loading Emergency GUI</h1><p>{e}</p></body></html>", 500
        
        @self.app.route('/localstatus')
        def get_tank_status():
            """Proxy request to TANK's HTTP status endpoint"""
            try:
                response = requests.get(f'http://localhost:{self.tank_http_port}/localstatus', timeout=3)
                return response.json()
            except requests.RequestException as e:
                logger.error(f"Failed to fetch TANK status: {e}")
                return {
                    "error": str(e), 
                    "tank_reachable": False,
                    "timestamp": time.time()
                }, 500
        
        @self.app.route('/send-command', methods=['POST'])
        def send_command():
            """Send emergency command directly to TANK Core via ZMQ (bypass Redis/Babysitter)"""
            try:
                data = request.json
                command = data['command']
                params = data.get('params', {})

                logger.info(f"Emergency GUI sending direct ZMQ command: {command} with params: {params}")

                # Send command directly to TANK Core's TankCommandReceiver (port 5562)
                # Format: [command_string, params_json]
                message_parts = [
                    command.encode('utf-8'),
                    json.dumps(params).encode('utf-8')
                ]

                self.command_socket.send_multipart(message_parts, zmq.NOBLOCK)
                logger.info(f"Emergency command sent via ZMQ: {command}")

                return {"success": True, "message": f"Emergency command {command} sent to TANK Core"}

            except zmq.Again:
                logger.error("ZMQ send failed - command socket busy")
                return {"success": False, "error": "Command socket busy, try again"}, 503
            except Exception as e:
                logger.error(f"Failed to send emergency command: {e}")
                return {"success": False, "error": str(e)}, 500
        
        # Static files not needed - HTML is self-contained
    
    def setup_websocket_handlers(self):
        """Setup WebSocket event handlers"""
        
        @self.socketio.on('connect')
        def handle_connect():
            self.connected_clients += 1
            logger.info(f"Client connected. Total clients: {self.connected_clients}")
            
            # Send initial status if available
            if self.last_status:
                emit('status_update', self.last_status)
            
            # Start ZMQ listener if not already running
            if not self.zmq_thread_running:
                self.start_zmq_listener()
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            self.connected_clients -= 1
            logger.info(f"Client disconnected. Total clients: {self.connected_clients}")
        
        @self.socketio.on('request_status')
        def handle_status_request():
            """Client requesting immediate status update"""
            try:
                # Fetch latest status from TANK
                response = requests.get(f'http://localhost:{self.tank_http_port}/localstatus', timeout=2)
                status = response.json()
                emit('status_update', status)
            except Exception as e:
                emit('error', {'message': f'Failed to fetch status: {e}'})
    
    def start_zmq_listener(self):
        """Start ZMQ listener thread for real-time updates"""
        if self.zmq_thread_running:
            return
        
        self.zmq_thread_running = True
        zmq_thread = threading.Thread(target=self._zmq_listener_loop, daemon=True)
        zmq_thread.start()
        logger.info("ZMQ listener thread started")
    
    def _zmq_listener_loop(self):
        """Listen for ZMQ messages from TANK and forward to web clients"""
        logger.info("ZMQ listener loop started")
        
        while self.zmq_thread_running and self.connected_clients > 0:
            try:
                # Receive from TANK (with timeout)
                message_parts = self.status_subscriber.recv_multipart(zmq.NOBLOCK)
                
                if len(message_parts) >= 2:
                    message_type = message_parts[0]
                    data = json.loads(message_parts[1].decode('utf-8'))
                    
                    if message_type == b"STATUS_UPDATE":
                        self.last_status = data
                        self.socketio.emit('status_update', data)
                        logger.debug("Broadcasted status update to clients")
                        
                    elif message_type == b"CRITICAL_ALERT":
                        self.socketio.emit('critical_alert', data)
                        logger.info(f"Broadcasted critical alert: {data.get('type', 'Unknown')}")
                
            except zmq.Again:
                # No message available (timeout)
                time.sleep(0.1)
                continue
            except Exception as e:
                logger.error(f"ZMQ listener error: {e}")
                time.sleep(1)
        
        self.zmq_thread_running = False
        logger.info("ZMQ listener loop stopped")
    
    def run(self, host='localhost', port=8766, debug=False):
        """Run the Emergency GUI server using production WSGI server"""
        logger.info(f"Starting Emergency GUI Server on http://{host}:{port}")
        logger.info(f"TANK Status Port: {self.tank_status_port}")
        logger.info(f"TANK Command Port: {self.tank_command_port}")
        logger.info(f"TANK HTTP Port: {self.tank_http_port}")

        try:
            # Try to use eventlet with production server for SocketIO
            try:
                import eventlet
                eventlet.monkey_patch()
                logger.info("Using eventlet production server for Emergency GUI")
                self.socketio.run(
                    self.app,
                    host=host,
                    port=port,
                    debug=debug,
                    use_reloader=False,
                    log_output=False  # Suppress development server warnings
                )
            except ImportError:
                # Fallback to development server with warnings suppressed
                logger.warning("eventlet not available, falling back to development server. Install with: pip install eventlet")
                import logging
                werkzeug_logger = logging.getLogger('werkzeug')
                werkzeug_logger.setLevel(logging.ERROR)

                self.socketio.run(self.app, host=host, port=port, debug=debug)
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Cleanup resources"""
        self.zmq_thread_running = False
        self.status_subscriber.close()
        self.command_socket.close()
        self.zmq_context.term()
        logger.info("Emergency GUI Server cleanup complete")

def main():
    """Main entry point"""
    print("""
╔══════════════════════════════════════════════════════╗
║              TANK Emergency GUI Server               ║
║                                                      ║
║  Real-time emergency control interface for TANK     ║
║  Access: http://localhost:8766                       ║
╚══════════════════════════════════════════════════════╝
    """)
    
    # Create and run server
    server = EmergencyGUIServer()
    server.run(host='localhost', port=8766, debug=False)

if __name__ == '__main__':
    main()
