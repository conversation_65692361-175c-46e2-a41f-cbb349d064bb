"""
Smart Fingerprint Service for Context-Aware Duplicate Detection

This service provides intelligent fingerprint-based duplicate detection that goes beyond
simple time-based expiry. It subscribes to order events and market conditions to make
smart decisions about when to invalidate cached fingerprints.

FUZZY'S ARCHITECTURAL NOTES:
- Stateful service managed by DI container
- Subscribes to OrderFilledEvent for smart cache invalidation
- Context-aware expiry based on market conditions
- Thread-safe for high-frequency trading environments
"""

import logging
import threading
import time
from typing import Dict, Optional, Any, Tuple
from collections import defaultdict

from interfaces.utility.services import IFingerprintService
from interfaces.core.services import IEventBus, ILifecycleService
from core.events import OrderFilledEvent, OrderStatusUpdateEvent
from utils.decorators import log_function_call


class FingerprintService(IFingerprintService, ILifecycleService):
    """
    Smart fingerprint service that provides context-aware duplicate detection.
    
    Features:
    - Order-based cache invalidation (smart, not time-based)
    - Context-aware expiry (market volatility, trading hours)
    - Thread-safe operations for high-frequency environments
    - Performance metrics and monitoring
    """
    
    def __init__(self, event_bus: IEventBus, config_service=None):
        """
        Initialize the smart fingerprint service.
        
        Args:
            event_bus: Event bus for subscribing to order events
            config_service: Configuration service for settings
        """
        self._event_bus = event_bus
        self._config_service = config_service
        self.logger = logging.getLogger(__name__)
        
        # Thread-safe cache structure: {symbol: {fingerprint: {timestamp, signal_data}}}
        self._fingerprint_cache: Dict[str, Dict[tuple, Dict[str, Any]]] = defaultdict(dict)
        self._cache_lock = threading.RLock()
        
        # Configuration with smart defaults
        self._default_expiry_seconds = 60.0  # Fallback for edge cases
        self._volatile_market_expiry = 15.0  # Shorter expiry during volatility
        self._quiet_market_expiry = 300.0   # Longer expiry during quiet periods
        
        # Performance tracking
        self._stats = {
            'duplicates_blocked': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'smart_invalidations': 0,
            'time_based_expiries': 0
        }
        self._stats_lock = threading.Lock()
        
        # Service state
        self._is_running = False
        self._subscriptions_active = False
        
        self.logger.info("FingerprintService initialized with smart cache management")
    
    @log_function_call('fingerprint_service')
    def start(self) -> None:
        """Start the fingerprint service and subscribe to events."""
        if self._is_running:
            self.logger.warning("FingerprintService already running")
            return
            
        try:
            self._subscribe_to_events()
            self._is_running = True
            self.logger.info("FingerprintService started successfully")
        except Exception as e:
            self.logger.error(f"Failed to start FingerprintService: {e}", exc_info=True)
            raise
    
    @log_function_call('fingerprint_service')
    def stop(self) -> None:
        """Stop the fingerprint service and clean up."""
        if not self._is_running:
            return
            
        try:
            self._unsubscribe_from_events()
            with self._cache_lock:
                self._fingerprint_cache.clear()
            self._is_running = False
            self.logger.info("FingerprintService stopped successfully")
        except Exception as e:
            self.logger.error(f"Error stopping FingerprintService: {e}", exc_info=True)
    
    def is_ready(self) -> bool:
        """Check if the service is ready."""
        return self._is_running and self._subscriptions_active
    
    def _subscribe_to_events(self) -> None:
        """Subscribe to order events for smart cache invalidation."""
        try:
            # Subscribe to order fill events for smart invalidation
            self._event_bus.subscribe(OrderFilledEvent, self._handle_order_filled)
            
            # Subscribe to order status updates for additional context
            self._event_bus.subscribe(OrderStatusUpdateEvent, self._handle_order_status_update)
            
            self._subscriptions_active = True
            self.logger.info("FingerprintService subscribed to order events")
        except Exception as e:
            self.logger.error(f"Failed to subscribe to events: {e}", exc_info=True)
            raise
    
    def _unsubscribe_from_events(self) -> None:
        """Unsubscribe from events during shutdown."""
        try:
            # Note: EventBus should handle cleanup automatically with WeakSet
            self._subscriptions_active = False
            self.logger.debug("FingerprintService unsubscribed from events")
        except Exception as e:
            self.logger.error(f"Error unsubscribing from events: {e}", exc_info=True)
    
    @log_function_call('fingerprint_service')
    def is_duplicate(self, fingerprint: tuple, context: Optional[dict] = None) -> bool:
        """
        Smart duplicate detection with context awareness.
        
        Args:
            fingerprint: Signal fingerprint tuple
            context: Optional context (symbol, market_volatility, etc.)
            
        Returns:
            True if this is a duplicate that should be suppressed
        """
        if not fingerprint or not isinstance(fingerprint, tuple):
            self.logger.warning(f"Invalid fingerprint for duplicate check: {fingerprint}")
            return False
        
        symbol = context.get('symbol', 'UNKNOWN') if context else 'UNKNOWN'
        current_time = time.time()
        
        with self._cache_lock:
            # Clean expired entries first
            self._cleanup_expired_fingerprints(symbol, current_time, context)
            
            # Check if fingerprint exists in cache
            symbol_cache = self._fingerprint_cache.get(symbol, {})
            if fingerprint in symbol_cache:
                cached_data = symbol_cache[fingerprint]
                age_seconds = current_time - cached_data['timestamp']
                
                with self._stats_lock:
                    self._stats['cache_hits'] += 1
                    self._stats['duplicates_blocked'] += 1
                
                self.logger.info(f"[{symbol}] SMART DUPLICATE DETECTED: {fingerprint} "
                               f"(age: {age_seconds:.1f}s)")
                return True
            else:
                with self._stats_lock:
                    self._stats['cache_misses'] += 1
                
                self.logger.debug(f"[{symbol}] New unique fingerprint: {fingerprint}")
                return False
    
    @log_function_call('fingerprint_service')
    def update(self, fingerprint: tuple, signal_data: dict) -> None:
        """
        Update cache with new fingerprint after successful signal generation.
        
        Args:
            fingerprint: Signal fingerprint tuple
            signal_data: Associated signal data for context
        """
        if not fingerprint or not isinstance(fingerprint, tuple):
            self.logger.warning(f"Invalid fingerprint for update: {fingerprint}")
            return
        
        symbol = signal_data.get('symbol', 'UNKNOWN')
        current_time = time.time()
        
        with self._cache_lock:
            if symbol not in self._fingerprint_cache:
                self._fingerprint_cache[symbol] = {}
            
            self._fingerprint_cache[symbol][fingerprint] = {
                'timestamp': current_time,
                'signal_data': signal_data.copy()
            }
        
        self.logger.info(f"[{symbol}] Updated fingerprint cache: {fingerprint}")
    
    @log_function_call('fingerprint_service')
    def invalidate_by_symbol(self, symbol: str) -> None:
        """
        Smart invalidation of all fingerprints for a symbol.
        Called when market conditions change significantly.
        
        Args:
            symbol: Symbol to invalidate fingerprints for
        """
        symbol_upper = symbol.upper()
        
        with self._cache_lock:
            if symbol_upper in self._fingerprint_cache:
                count = len(self._fingerprint_cache[symbol_upper])
                del self._fingerprint_cache[symbol_upper]
                
                with self._stats_lock:
                    self._stats['smart_invalidations'] += count
                
                self.logger.info(f"[{symbol_upper}] SMART INVALIDATION: Removed {count} fingerprints")
    
    def _cleanup_expired_fingerprints(self, symbol: str, current_time: float, context: Optional[dict] = None) -> None:
        """
        Clean up expired fingerprints with context-aware expiry times.
        MUST be called with _cache_lock held.
        """
        if symbol not in self._fingerprint_cache:
            return
        
        # Determine smart expiry time based on context
        expiry_seconds = self._get_smart_expiry_time(context)
        
        symbol_cache = self._fingerprint_cache[symbol]
        expired_fingerprints = [
            fp for fp, data in symbol_cache.items()
            if (current_time - data['timestamp']) >= expiry_seconds
        ]
        
        if expired_fingerprints:
            for fp in expired_fingerprints:
                del symbol_cache[fp]
            
            with self._stats_lock:
                self._stats['time_based_expiries'] += len(expired_fingerprints)
            
            self.logger.debug(f"[{symbol}] Cleaned {len(expired_fingerprints)} expired fingerprints "
                            f"(expiry: {expiry_seconds}s)")
        
        # Remove empty symbol cache
        if not symbol_cache:
            del self._fingerprint_cache[symbol]
    
    def _get_smart_expiry_time(self, context: Optional[dict] = None) -> float:
        """
        Determine smart expiry time based on market context.
        
        Args:
            context: Market context (volatility, trading hours, etc.)
            
        Returns:
            Expiry time in seconds
        """
        if not context:
            return self._default_expiry_seconds
        
        # Smart expiry based on market conditions
        market_volatility = context.get('market_volatility', 'NORMAL')
        market_hours = context.get('market_hours', 'REGULAR')
        
        if market_volatility == 'HIGH':
            return self._volatile_market_expiry  # 15 seconds during volatility
        elif market_hours == 'AFTER_HOURS':
            return self._quiet_market_expiry    # 5 minutes during quiet periods
        else:
            return self._default_expiry_seconds  # 60 seconds default
    
    def _handle_order_filled(self, event: OrderFilledEvent) -> None:
        """
        Handle order fill events for smart cache invalidation.
        When an order fills, invalidate related fingerprints to allow new signals.
        """
        try:
            if not event.data or not event.data.symbol:
                return
            
            symbol = event.data.symbol.upper()
            self.logger.info(f"[{symbol}] Order filled - triggering smart cache invalidation")
            
            # Smart invalidation: Remove fingerprints for this symbol
            # This allows new signals after successful order execution
            self.invalidate_by_symbol(symbol)
            
        except Exception as e:
            self.logger.error(f"Error handling order fill event: {e}", exc_info=True)
    
    def _handle_order_status_update(self, event: OrderStatusUpdateEvent) -> None:
        """
        Handle order status updates for additional context.
        """
        try:
            if not event.data or not event.data.symbol:
                return
            
            symbol = event.data.symbol.upper()
            status = event.data.status
            
            # Smart invalidation on certain status changes
            if status in ['CANCELLED', 'REJECTED']:
                # Allow new signals when orders are cancelled/rejected
                self.invalidate_by_symbol(symbol)
                self.logger.debug(f"[{symbol}] Order {status} - cache invalidated")
                
        except Exception as e:
            self.logger.error(f"Error handling order status update: {e}", exc_info=True)
    
    def get_cache_stats(self) -> dict:
        """Get performance statistics for monitoring."""
        with self._stats_lock:
            stats = self._stats.copy()
        
        with self._cache_lock:
            stats['cached_symbols'] = len(self._fingerprint_cache)
            stats['total_fingerprints'] = sum(len(fps) for fps in self._fingerprint_cache.values())
        
        return stats
