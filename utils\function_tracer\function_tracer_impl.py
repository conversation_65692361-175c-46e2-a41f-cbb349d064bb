"""
Function Tracer Implementation Module

This module provides the real implementation of function tracing utilities.
These implementations are used when function tracing is enabled.
"""

import time
import functools
import csv
import threading
import uuid
import sys # Needed to adjust path for config import
import os # Needed for path manipulation
import logging
import atexit
from datetime import datetime

# Add project root to path to find config.py
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import LOG_BASE_DIR # Import necessary paths from root config

# Global flag to enable/disable function tracing
FUNCTION_TRACING_ENABLED = False

# Thread-local storage for trace context
trace_context = threading.local()

# Logger for function tracer
logger = logging.getLogger("function_tracer")

# Ensure the logs directory exists
os.makedirs(LOG_BASE_DIR, exist_ok=True)
TRACE_FILE = os.path.join(LOG_BASE_DIR, "function_trace.csv")

# Constants for file rotation
MAX_TRACE_FILE_SIZE_MB = 10  # Maximum size in MB before rotation
MAX_TRACE_FILES = 5  # Maximum number of rotated files to keep

# Functions to enable/disable tracing
def enable_function_tracing(enabled=True):
    """
    Enable or disable function tracing globally.

    Args:
        enabled: True to enable tracing, False to disable it

    Returns:
        The new state of function tracing
    """
    global FUNCTION_TRACING_ENABLED
    old_state = FUNCTION_TRACING_ENABLED
    FUNCTION_TRACING_ENABLED = enabled

    # Log the change
    logger.info(f"Function tracing {'enabled' if enabled else 'disabled'}. Previous state: {old_state}")

    # Initialize trace file if enabling and file doesn't exist
    if enabled and not os.path.exists(TRACE_FILE):
        init_trace_file()

    return FUNCTION_TRACING_ENABLED

def is_function_tracing_enabled():
    """
    Check if function tracing is enabled.

    Returns:
        True if function tracing is enabled, False otherwise
    """
    return FUNCTION_TRACING_ENABLED

def cleanup_trace_context():
    """
    Explicitly clean up thread-local trace context.
    This should be called when a thread is about to exit.
    """
    if hasattr(trace_context, 'trace_id'):
        del trace_context.trace_id
    if hasattr(trace_context, 'call_stack'):
        del trace_context.call_stack

# Setup logging for function tracing
def setup_function_trace_logging(module_name):
    """
    Creates a new logger named '{module_name}_function_trace' that writes to a file.
    Returns the configured logger.
    """
    module_logger = logging.getLogger(f"{module_name}_function_trace")
    module_logger.setLevel(logging.DEBUG) # Log everything for function traces

    # Use LOG_BASE_DIR from the central config file
    log_file_path = os.path.join(LOG_BASE_DIR, f"{module_name}_function_trace.log")

    # Avoid adding multiple handlers if logger already exists
    if not module_logger.handlers:
        # Create a file handler
        file_handler = logging.FileHandler(log_file_path)
        file_handler.setLevel(logging.DEBUG)

        # Create a formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        # Add the handler to the logger
        module_logger.addHandler(file_handler)

    return module_logger

# File rotation functions
def check_file_size(file_path):
    """
    Check if the file size exceeds the maximum size.

    Args:
        file_path: Path to the file to check

    Returns:
        True if the file size exceeds the maximum size, False otherwise
    """
    if not os.path.exists(file_path):
        return False

    size_mb = os.path.getsize(file_path) / (1024 * 1024)  # Convert to MB
    return size_mb > MAX_TRACE_FILE_SIZE_MB

def rotate_trace_file():
    """
    Rotate the trace file if it exceeds the maximum size.
    """
    if not check_file_size(TRACE_FILE):
        return

    # Rotate existing backup files
    for i in range(MAX_TRACE_FILES - 1, 0, -1):
        old_file = f"{TRACE_FILE}.{i}"
        new_file = f"{TRACE_FILE}.{i+1}"

        if os.path.exists(old_file):
            if i == MAX_TRACE_FILES - 1:
                # Remove the oldest file if we've reached the maximum
                try:
                    os.remove(old_file)
                except Exception as e:
                    logger.error(f"Error removing old trace file {old_file}: {e}")
            else:
                # Rename the file
                try:
                    os.rename(old_file, new_file)
                except Exception as e:
                    logger.error(f"Error rotating trace file {old_file} to {new_file}: {e}")

    # Rename the current file
    try:
        if os.path.exists(TRACE_FILE):
            os.rename(TRACE_FILE, f"{TRACE_FILE}.1")
    except Exception as e:
        logger.error(f"Error rotating current trace file: {e}")

    # Create a new file
    init_trace_file()

# Initialize the trace file
def init_trace_file():
    """Initialize the trace file with headers"""
    try:
        with open(TRACE_FILE, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['trace_id', 'function', 'event', 'timestamp', 'parent', 'date_time'])
    except Exception as e:
        logger.error(f"Error initializing trace file: {e}")

# Create the trace file if it doesn't exist
if not os.path.exists(TRACE_FILE):
    init_trace_file()

def trace_function(module_name):
    """
    Decorator to trace function execution time and dependencies.

    This decorator will:
    1. Generate a unique trace ID for each top-level function call
    2. Track the call stack to identify parent-child relationships
    3. Log function entry and exit to a CSV file
    4. Rotate the trace file if it exceeds the maximum size

    Args:
        module_name: The name of the module containing the function

    Returns:
        A decorator function
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Skip tracing if disabled
            if not is_function_tracing_enabled():
                return func(*args, **kwargs)

            # Check if trace file needs rotation
            try:
                if check_file_size(TRACE_FILE):
                    rotate_trace_file()
            except Exception as e:
                logger.error(f"Error checking/rotating trace file: {e}")

            # Generate or get trace ID
            try:
                if not hasattr(trace_context, 'trace_id'):
                    trace_context.trace_id = str(uuid.uuid4())
                    trace_context.call_stack = []
            except Exception as e:
                logger.error(f"Error initializing trace context: {e}")
                # Ensure we have a valid trace context
                if not hasattr(trace_context, 'trace_id'):
                    trace_context.trace_id = str(uuid.uuid4())
                if not hasattr(trace_context, 'call_stack'):
                    trace_context.call_stack = []

            # Get parent function if any
            parent_func = None
            try:
                parent_func = trace_context.call_stack[-1] if trace_context.call_stack else None
            except Exception as e:
                logger.error(f"Error getting parent function: {e}")

            # Record function entry
            func_name = f"{module_name}.{func.__name__}"
            start_time = time.time()
            date_time_str = datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S.%f')

            # Add to call stack
            try:
                trace_context.call_stack.append(func_name)
            except Exception as e:
                logger.error(f"Error adding to call stack: {e}")
                # Ensure we have a valid call stack
                trace_context.call_stack = [func_name]

            # Log entry
            try:
                with open(TRACE_FILE, 'a', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow([
                        trace_context.trace_id,
                        func_name,
                        'start',
                        start_time,
                        parent_func or '',
                        date_time_str
                    ])
            except Exception as e:
                logger.error(f"Error logging function entry: {e}")

            # Execute the function
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                # Log the exception but don't interfere with normal exception handling
                logger.error(f"Exception in traced function {func_name}: {e}")
                raise
            finally:
                # Always execute cleanup code, even if an exception occurred
                try:
                    # Record function exit
                    end_time = time.time()
                    date_time_str = datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S.%f')

                    # Log exit
                    try:
                        with open(TRACE_FILE, 'a', newline='') as f:
                            writer = csv.writer(f)
                            writer.writerow([
                                trace_context.trace_id,
                                func_name,
                                'end',
                                end_time,
                                parent_func or '',
                                date_time_str
                            ])
                    except Exception as e:
                        logger.error(f"Error logging function exit: {e}")

                    # Remove from call stack
                    try:
                        if hasattr(trace_context, 'call_stack') and trace_context.call_stack:
                            trace_context.call_stack.pop()
                    except Exception as e:
                        logger.error(f"Error removing from call stack: {e}")
                        # Reset call stack to avoid further errors
                        trace_context.call_stack = []

                    # Clear trace context if we're back at the top level
                    try:
                        if not hasattr(trace_context, 'call_stack') or not trace_context.call_stack:
                            cleanup_trace_context()
                    except Exception as e:
                        logger.error(f"Error cleaning up trace context: {e}")
                        # Force cleanup
                        cleanup_trace_context()
                except Exception as e:
                    logger.error(f"Error in trace function cleanup: {e}")
                    # Force cleanup to avoid memory leaks
                    cleanup_trace_context()

        return wrapper
    return decorator

# Register cleanup function to be called at exit
def _cleanup_at_exit():
    """
    Cleanup function to be called when the program exits.
    This ensures that all resources are properly released.
    """
    logger.info("Cleaning up function tracer resources at exit")
    try:
        # Disable tracing to prevent new traces during shutdown
        global FUNCTION_TRACING_ENABLED
        FUNCTION_TRACING_ENABLED = False

        # Force garbage collection to help clean up resources
        import gc
        gc.collect()

        logger.info("Function tracer cleanup completed")
    except Exception as e:
        logger.error(f"Error during function tracer cleanup: {e}")

# Register the cleanup function
atexit.register(_cleanup_at_exit)

# Initialize the logger
def _init_logger():
    """Initialize the function tracer logger."""
    global logger

    # Configure the logger if it has no handlers
    if not logger.handlers:
        logger.setLevel(logging.INFO)

        # Create a file handler
        log_file_path = os.path.join(LOG_BASE_DIR, "function_tracer.log")
        file_handler = logging.FileHandler(log_file_path)
        file_handler.setLevel(logging.INFO)

        # Create a formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        # Add the handler to the logger
        logger.addHandler(file_handler)

        logger.info("Function tracer logger initialized")

# Initialize the logger
_init_logger()
