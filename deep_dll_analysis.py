#!/usr/bin/env python3
"""
Deep analysis of DLL dependencies for the C++ OCR accelerator.
"""

import os
import sys
import ctypes
from ctypes import wintypes
import subprocess

def check_python_architecture():
    """Check if Python and module architectures match."""
    print("=== Architecture Check ===")
    import platform
    import struct
    
    python_bits = struct.calcsize("P") * 8
    python_arch = platform.architecture()[0]
    
    print(f"Python architecture: {python_arch} ({python_bits}-bit)")
    
    # Check .pyd file architecture
    pyd_path = "ocr_accelerator/x64/Release/ocr_accelerator.pyd"
    if os.path.exists(pyd_path):
        try:
            # Use file command or PE header analysis
            with open(pyd_path, 'rb') as f:
                f.seek(0x3C)  # PE header offset location
                pe_offset = int.from_bytes(f.read(4), 'little')
                f.seek(pe_offset + 4)  # Machine type location
                machine_type = int.from_bytes(f.read(2), 'little')
                
                if machine_type == 0x014c:
                    pyd_arch = "32-bit (x86)"
                elif machine_type == 0x8664:
                    pyd_arch = "64-bit (x64)"
                else:
                    pyd_arch = f"Unknown (0x{machine_type:04x})"
                    
                print(f"PYD file architecture: {pyd_arch}")
                
                if python_bits == 64 and machine_type != 0x8664:
                    print("❌ ARCHITECTURE MISMATCH: 64-bit Python with 32-bit PYD")
                    return False
                elif python_bits == 32 and machine_type != 0x014c:
                    print("❌ ARCHITECTURE MISMATCH: 32-bit Python with 64-bit PYD")
                    return False
                else:
                    print("✅ Architecture match")
                    return True
                    
        except Exception as e:
            print(f"❌ Error checking PYD architecture: {e}")
            return False
    else:
        print("❌ PYD file not found")
        return False

def check_dependency_walker():
    """Use Windows tools to check dependencies."""
    print("\n=== Dependency Analysis ===")
    
    pyd_path = os.path.abspath("ocr_accelerator/x64/Release/ocr_accelerator.pyd")
    
    # Try using PowerShell to get DLL dependencies
    try:
        powershell_cmd = f'''
Add-Type -AssemblyName System.Reflection
$assembly = [System.Reflection.Assembly]::ReflectionOnlyLoadFrom("{pyd_path}")
$assembly.GetReferencedAssemblies() | ForEach-Object {{ $_.Name }}
'''
        
        result = subprocess.run([
            "powershell", "-Command", powershell_cmd
        ], capture_output=True, text=True, shell=True)
        
        if result.returncode == 0 and result.stdout.strip():
            print("✅ Referenced assemblies:")
            print(result.stdout)
        else:
            print("⚠️  PowerShell assembly analysis failed")
            
    except Exception as e:
        print(f"⚠️  PowerShell analysis error: {e}")

def manual_dll_load_test():
    """Manually test loading each DLL to find the problematic one."""
    print("\n=== Manual DLL Load Test ===")
    
    build_dir = os.path.abspath("ocr_accelerator/x64/Release")
    
    # Test loading critical DLLs one by one
    test_dlls = [
        "tesseract55.dll",
        "leptonica-1.85.0.dll",
        "libpng16.dll",
        "zlib1.dll",
        "jpeg62.dll",
        "tiff.dll",
        "libcrypto-3-x64.dll",
        "libcurl.dll"
    ]
    
    for dll in test_dlls:
        dll_path = os.path.join(build_dir, dll)
        if os.path.exists(dll_path):
            try:
                handle = ctypes.windll.kernel32.LoadLibraryW(dll_path)
                if handle:
                    print(f"✅ {dll}: Loads successfully")
                    ctypes.windll.kernel32.FreeLibrary(handle)
                else:
                    error = ctypes.windll.kernel32.GetLastError()
                    print(f"❌ {dll}: Load failed with error {error}")
            except Exception as e:
                print(f"❌ {dll}: Exception - {e}")
        else:
            print(f"⚪ {dll}: Not found")

def test_step_by_step_import():
    """Try to import step by step to isolate the issue."""
    print("\n=== Step-by-Step Import Test ===")
    
    try:
        # Step 1: Set up paths
        build_dir = os.path.abspath("ocr_accelerator/x64/Release")
        print(f"Build directory: {build_dir}")
        
        # Step 2: Add to sys.path
        if build_dir not in sys.path:
            sys.path.insert(0, build_dir)
            print("✅ Added to sys.path")
        
        # Step 3: Add to DLL search path
        if hasattr(os, 'add_dll_directory'):
            os.add_dll_directory(build_dir)
            print("✅ Added to DLL search path")
        
        # Step 4: Change to build directory (sometimes helps with relative DLL loading)
        original_cwd = os.getcwd()
        os.chdir(build_dir)
        print(f"✅ Changed to build directory: {os.getcwd()}")
        
        # Step 5: Try import
        print("🔄 Attempting import...")
        import ocr_accelerator
        print("✅ Import successful!")
        
        # Step 6: Test basic function
        if hasattr(ocr_accelerator, 'test_function'):
            result = ocr_accelerator.test_function()
            print(f"✅ test_function() works: {result}")
        
        # Restore original directory
        os.chdir(original_cwd)
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        try:
            os.chdir(original_cwd)
        except:
            pass
        return False

if __name__ == "__main__":
    print("TESTRADE Deep DLL Analysis")
    print("=" * 50)
    
    # Check architecture compatibility
    arch_ok = check_python_architecture()
    
    # Check dependencies
    check_dependency_walker()
    
    # Manual DLL loading
    manual_dll_load_test()
    
    # Step by step import
    if arch_ok:
        import_ok = test_step_by_step_import()
        
        if import_ok:
            print("\n🎉 Module loading successful!")
        else:
            print("\n❌ Module loading failed despite all checks")
    else:
        print("\n❌ Architecture mismatch - cannot proceed with import test")
    
    print("\n💡 Next steps:")
    print("1. Check if changing to build directory helps")
    print("2. Rebuild the C++ module if architecture mismatch")
    print("3. Check for missing Visual C++ runtime components")