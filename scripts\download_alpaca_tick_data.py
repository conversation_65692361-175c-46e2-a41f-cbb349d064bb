#!/usr/bin/env python3
"""
Alpaca Tick Data Downloader

Downloads tick data from Alpaca Markets Data API for specified symbols and time range.
Configured for Friday 5/30/25 from 9:45 AM to 10:45 AM EST for MSFT, AAPL, SPY, NVDA, UBER.

Usage:
    python download_alpaca_tick_data.py
    python download_alpaca_tick_data.py --symbols AAPL,MSFT --start "2025-05-30 09:45:00" --end "2025-05-30 10:45:00"
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
import pandas as pd

# Add project root to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import Alpaca credentials from GlobalConfig
try:
    from utils.global_config import GlobalConfig
    # Load config to get credentials
    config = GlobalConfig()
    ALPACA_API_KEY = config.ALPACA_API_KEY
    ALPACA_API_SECRET = config.ALPACA_API_SECRET
except ImportError as e:
    print(f"Error importing TESTRADE modules: {e}")
    sys.exit(1)

# Import Alpaca API
try:
    import alpaca_trade_api as tradeapi
    from alpaca_trade_api.rest import APIError
except ImportError:
    print("Error: alpaca-trade-api not installed. Run: pip install alpaca-trade-api")
    sys.exit(1)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AlpacaTickDataDownloader:
    """Downloads tick data from Alpaca Markets Data API."""
    
    def __init__(self, api_key: str, api_secret: str, data_url: str = "https://data.alpaca.markets"):
        """
        Initialize the Alpaca data downloader.
        
        Args:
            api_key: Alpaca API key
            api_secret: Alpaca API secret
            data_url: Alpaca data API base URL
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.data_url = data_url
        
        # Initialize Alpaca REST client for data API
        try:
            self.client = tradeapi.REST(
                key_id=api_key,
                secret_key=api_secret,
                base_url=data_url,
                api_version='v2'
            )
            logger.info("Alpaca Data API client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Alpaca client: {e}")
            raise
    
    def download_trades(self, symbols: List[str], start_time: datetime, end_time: datetime, 
                       output_dir: str = "data/alpaca_downloads") -> Dict[str, str]:
        """
        Download trade (tick) data for specified symbols and time range.
        
        Args:
            symbols: List of stock symbols to download
            start_time: Start time (timezone-aware datetime)
            end_time: End time (timezone-aware datetime)
            output_dir: Directory to save downloaded data
            
        Returns:
            Dict mapping symbol to output file path
        """
        os.makedirs(output_dir, exist_ok=True)
        downloaded_files = {}
        
        for symbol in symbols:
            logger.info(f"Downloading trade data for {symbol}...")
            
            try:
                # Convert datetime to RFC3339 format for Alpaca API
                start_rfc3339 = start_time.isoformat()
                end_rfc3339 = end_time.isoformat()

                logger.debug(f"Requesting trades for {symbol}: start={start_rfc3339}, end={end_rfc3339}")

                # Get trade data using Alpaca API
                trades = self.client.get_trades(
                    symbol=symbol,
                    start=start_rfc3339,
                    end=end_rfc3339,
                    limit=10000  # Maximum per request
                )
                
                # Convert to DataFrame for easier handling
                trade_data = []
                for trade in trades:
                    try:
                        trade_data.append({
                            'timestamp': getattr(trade, 'timestamp', getattr(trade, 't', None)),
                            'price': float(getattr(trade, 'price', getattr(trade, 'p', 0))),
                            'size': int(getattr(trade, 'size', getattr(trade, 's', 0))),
                            'exchange': getattr(trade, 'exchange', getattr(trade, 'x', None)),
                            'conditions': getattr(trade, 'conditions', getattr(trade, 'c', [])),
                            'tape': getattr(trade, 'tape', getattr(trade, 'z', None))
                        })
                    except Exception as e:
                        logger.debug(f"Error processing trade: {e}, trade object: {trade}")
                        # Try alternative approach - convert to dict if possible
                        if hasattr(trade, '_asdict'):
                            trade_dict = trade._asdict()
                        elif hasattr(trade, '__dict__'):
                            trade_dict = trade.__dict__
                        else:
                            trade_dict = {'timestamp': None, 'price': 0, 'size': 0, 'exchange': None, 'conditions': [], 'tape': None}

                        trade_data.append({
                            'timestamp': trade_dict.get('timestamp') or trade_dict.get('t'),
                            'price': float(trade_dict.get('price', trade_dict.get('p', 0))),
                            'size': int(trade_dict.get('size', trade_dict.get('s', 0))),
                            'exchange': trade_dict.get('exchange') or trade_dict.get('x'),
                            'conditions': trade_dict.get('conditions', trade_dict.get('c', [])),
                            'tape': trade_dict.get('tape') or trade_dict.get('z')
                        })
                
                if trade_data:
                    df = pd.DataFrame(trade_data)
                    
                    # Save to CSV
                    filename = f"{symbol}_trades_{start_time.strftime('%Y%m%d_%H%M')}_{end_time.strftime('%H%M')}.csv"
                    filepath = os.path.join(output_dir, filename)
                    df.to_csv(filepath, index=False)
                    
                    downloaded_files[symbol] = filepath
                    logger.info(f"Saved {len(trade_data)} trades for {symbol} to {filepath}")
                else:
                    logger.warning(f"No trade data found for {symbol}")
                    
            except APIError as e:
                logger.error(f"Alpaca API error for {symbol}: {e}")
            except Exception as e:
                logger.error(f"Error downloading {symbol}: {e}")
        
        return downloaded_files
    
    def download_quotes(self, symbols: List[str], start_time: datetime, end_time: datetime,
                       output_dir: str = "data/alpaca_downloads") -> Dict[str, str]:
        """
        Download quote (NBBO) data for specified symbols and time range.
        
        Args:
            symbols: List of stock symbols to download
            start_time: Start time (timezone-aware datetime)
            end_time: End time (timezone-aware datetime)
            output_dir: Directory to save downloaded data
            
        Returns:
            Dict mapping symbol to output file path
        """
        os.makedirs(output_dir, exist_ok=True)
        downloaded_files = {}
        
        for symbol in symbols:
            logger.info(f"Downloading quote data for {symbol}...")
            
            try:
                # Convert datetime to RFC3339 format for Alpaca API
                start_rfc3339 = start_time.isoformat()
                end_rfc3339 = end_time.isoformat()

                # Get quote data using Alpaca API
                quotes = self.client.get_quotes(
                    symbol=symbol,
                    start=start_rfc3339,
                    end=end_rfc3339,
                    limit=10000  # Maximum per request
                )
                
                # Convert to DataFrame
                quote_data = []
                for quote in quotes:
                    try:
                        quote_data.append({
                            'timestamp': getattr(quote, 'timestamp', getattr(quote, 't', None)),
                            'bid_price': float(getattr(quote, 'bid_price', getattr(quote, 'bp', 0))),
                            'bid_size': int(getattr(quote, 'bid_size', getattr(quote, 'bs', 0))),
                            'ask_price': float(getattr(quote, 'ask_price', getattr(quote, 'ap', 0))),
                            'ask_size': int(getattr(quote, 'ask_size', getattr(quote, 'as', 0))),
                            'bid_exchange': getattr(quote, 'bid_exchange', getattr(quote, 'bx', None)),
                            'ask_exchange': getattr(quote, 'ask_exchange', getattr(quote, 'ax', None)),
                            'conditions': getattr(quote, 'conditions', getattr(quote, 'c', [])),
                            'tape': getattr(quote, 'tape', getattr(quote, 'z', None))
                        })
                    except Exception as e:
                        logger.debug(f"Error processing quote: {e}, quote object: {quote}")
                        # Try alternative approach - convert to dict if possible
                        if hasattr(quote, '_asdict'):
                            quote_dict = quote._asdict()
                        elif hasattr(quote, '__dict__'):
                            quote_dict = quote.__dict__
                        else:
                            quote_dict = {'timestamp': None, 'bid_price': 0, 'bid_size': 0, 'ask_price': 0, 'ask_size': 0, 'bid_exchange': None, 'ask_exchange': None, 'conditions': [], 'tape': None}

                        quote_data.append({
                            'timestamp': quote_dict.get('timestamp') or quote_dict.get('t'),
                            'bid_price': float(quote_dict.get('bid_price', quote_dict.get('bp', 0))),
                            'bid_size': int(quote_dict.get('bid_size', quote_dict.get('bs', 0))),
                            'ask_price': float(quote_dict.get('ask_price', quote_dict.get('ap', 0))),
                            'ask_size': int(quote_dict.get('ask_size', quote_dict.get('as', 0))),
                            'bid_exchange': quote_dict.get('bid_exchange') or quote_dict.get('bx'),
                            'ask_exchange': quote_dict.get('ask_exchange') or quote_dict.get('ax'),
                            'conditions': quote_dict.get('conditions', quote_dict.get('c', [])),
                            'tape': quote_dict.get('tape') or quote_dict.get('z')
                        })
                
                if quote_data:
                    df = pd.DataFrame(quote_data)
                    
                    # Save to CSV
                    filename = f"{symbol}_quotes_{start_time.strftime('%Y%m%d_%H%M')}_{end_time.strftime('%H%M')}.csv"
                    filepath = os.path.join(output_dir, filename)
                    df.to_csv(filepath, index=False)
                    
                    downloaded_files[symbol] = filepath
                    logger.info(f"Saved {len(quote_data)} quotes for {symbol} to {filepath}")
                else:
                    logger.warning(f"No quote data found for {symbol}")
                    
            except APIError as e:
                logger.error(f"Alpaca API error for {symbol}: {e}")
            except Exception as e:
                logger.error(f"Error downloading {symbol}: {e}")
        
        return downloaded_files

def parse_datetime(date_str: str) -> datetime:
    """Parse datetime string and ensure it's timezone-aware (EDT) in RFC3339 format."""
    try:
        # Parse the datetime string
        dt = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")

        # Make it timezone-aware (EDT = UTC-4 for May 30, 2025)
        from datetime import timezone, timedelta
        edt = timezone(timedelta(hours=-4))
        dt = dt.replace(tzinfo=edt)

        # Verify the format will work with Alpaca API
        rfc3339_str = dt.isoformat()
        logger.debug(f"Parsed datetime '{date_str}' to RFC3339: '{rfc3339_str}'")

        return dt
    except ValueError as e:
        logger.error(f"Error parsing datetime '{date_str}': {e}")
        raise

def main():
    """Main function to download Alpaca tick data."""
    parser = argparse.ArgumentParser(description='Download tick data from Alpaca Markets')
    parser.add_argument('--symbols', default='MSFT,AAPL,SPY,NVDA,UBER',
                       help='Comma-separated list of symbols (default: MSFT,AAPL,SPY,NVDA,UBER)')
    parser.add_argument('--start', default='2025-05-30 09:45:00',
                       help='Start time in YYYY-MM-DD HH:MM:SS format (default: 2025-05-30 09:45:00)')
    parser.add_argument('--end', default='2025-05-30 10:45:00',
                       help='End time in YYYY-MM-DD HH:MM:SS format (default: 2025-05-30 10:45:00)')
    parser.add_argument('--output-dir', default='data/alpaca_downloads',
                       help='Output directory for downloaded files (default: data/alpaca_downloads)')
    parser.add_argument('--trades-only', action='store_true',
                       help='Download only trade data (skip quotes)')
    parser.add_argument('--quotes-only', action='store_true',
                       help='Download only quote data (skip trades)')
    
    args = parser.parse_args()
    
    # Parse symbols
    symbols = [s.strip().upper() for s in args.symbols.split(',')]
    
    # Parse datetime strings
    start_time = parse_datetime(args.start)
    end_time = parse_datetime(args.end)
    
    logger.info(f"Downloading data for symbols: {symbols}")
    logger.info(f"Time range: {start_time} to {end_time}")
    logger.info(f"Output directory: {args.output_dir}")
    
    # Initialize downloader with TESTRADE credentials
    try:
        downloader = AlpacaTickDataDownloader(
            api_key=ALPACA_API_KEY,
            api_secret=ALPACA_API_SECRET
        )
    except Exception as e:
        logger.error(f"Failed to initialize downloader: {e}")
        return 1
    
    # Download data
    downloaded_files = {}
    
    if not args.quotes_only:
        logger.info("Downloading trade data...")
        trade_files = downloader.download_trades(symbols, start_time, end_time, args.output_dir)
        downloaded_files.update({f"{k}_trades": v for k, v in trade_files.items()})
    
    if not args.trades_only:
        logger.info("Downloading quote data...")
        quote_files = downloader.download_quotes(symbols, start_time, end_time, args.output_dir)
        downloaded_files.update({f"{k}_quotes": v for k, v in quote_files.items()})
    
    # Summary
    logger.info("Download completed!")
    logger.info(f"Downloaded {len(downloaded_files)} files:")
    for data_type, filepath in downloaded_files.items():
        logger.info(f"  {data_type}: {filepath}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
