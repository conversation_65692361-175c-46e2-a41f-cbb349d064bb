#pragma once
#if !defined(LS_L_THREAD_H)
#define LS_L_THREAD_H

// Copyright (c) 2001-2018 Lightspeed Financial, Inc. All rights reserved.

#include "L_Application.h"
#include "L_Memory.h"

#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
#include <process.h>
#endif

#if !defined(_MINWINBASE_) && !defined(_WINDOWS_)
extern "C"
{
	struct _OVERLAPPED
	{
		win_ulong_ptr Internal;
		win_ulong_ptr InternalHigh;
		union
		{
			struct
			{
				unsigned long Offset;
				unsigned long OffsetHigh;
			};
			void *Pointer;
		};

		win_os_handle hEvent;
	};
} // extern "C"
#endif // defined(_MINWINBASE_) || defined(_WINDOWS_)

#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
	__declspec(dllimport) win_os_handle __stdcall CreateIoCompletionPort(win_os_handle, win_os_handle, win_ulong_ptr, unsigned long);
	__declspec(dllimport) int __stdcall GetQueuedCompletionStatus(win_os_handle, unsigned long *, win_ulong_ptr *, _OVERLAPPED **, unsigned long);
	__declspec(dllimport) int __stdcall PostQueuedCompletionStatus(win_os_handle, unsigned long, win_ulong_ptr, _OVERLAPPED *);
#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

namespace LightspeedTrader
{


class L_Port;
class L_Thread;
class L_Strand;
class L_StrandStorageId;
class L_Buffer;

class L_Strand
{
public:
	virtual L_Port *L_GetPort() const = 0;
};

class L_Thread
{
public:
	virtual unsigned __int32 L_ThreadId() const = 0;
	virtual L_Strand *L_StrandInProgress() const = 0;
};

class L_Work : public _OVERLAPPED
{
public:
	L_Work()
	{
		Internal = 0;
		InternalHigh = 0;
		Pointer = 0;
		hEvent = 0;
	}

	virtual void DoWork(L_Thread *currentThread = 0, int opResult = true, unsigned long numberOfBytes = 0, win_ulong_ptr completionKey = 0) = 0;
};

class L_Port
{
public:
	virtual bool QueueWork(L_Work *work, unsigned long numberOfBytes = 0, win_ulong_ptr completionKey = 0) = 0;

	L_Buffer *l_Buffer;
};

#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
template<typename T>
class C_StrandLocalPtr
{
public:
	typedef T element_type;
	typedef T value_type;
	typedef T *pointer;
	typedef T &reference;

	typedef L_StrandStorageId *Id;

	void reset(pointer p, void (*deleter)(pointer) = C_StdDelete<T>)
	{
		if (id_ == 0)
		{
			id_ = L_AllocStrandStorageId();
		}
		if (id_ != 0)
		{
			L_SetStrandStorage(id_, p, reinterpret_cast<void (*)(void *)>(deleter));
		}
	}

	reference operator*() const
	{
		pointer r = get();
		assert(r != 0);
		return *r;
	}

	pointer operator->() const
	{
		pointer r = get();
		assert(r != 0);
		return r;
	}

	pointer get() const
	{
		pointer r = 0;
		if (id_ != 0)
		{
			r = reinterpret_cast<T *>(L_GetStrandStorage(id_));
		}
		return r;
	}

	bool operator!() const
	{
		return id_ == 0 || get() == 0;
	}

	C_StrandLocalPtr() : id_(L_AllocStrandStorageId())
	{
	}

#if !(defined(_MSC_VER) && _MSC_VER < 1900)

	C_StrandLocalPtr(C_StrandLocalPtr &&rhs) noexcept : id_(rhs.id_)
	{
		rhs.id_ = 0;
	}
	C_StrandLocalPtr &operator=(C_StrandLocalPtr &&rhs)
	{
		if (this != &rhs)
		{
			id_ = rhs.id_;
			rhs.id_ = 0;
		}
	}
	explicit operator bool() const
	{
		return id_ != 0 && get() != 0;
	}

#else

	typedef Id C_StrandLocalPtr::*unspecified_bool_type;

	operator unspecified_bool_type() const
	{
		return id_ == 0 ? 0 : ( get() == 0 ? 0 : &C_StrandLocalPtr::id_ );
	}

#endif // !(defined(_MSC_VER) && _MSC_VER < 1900)

	~C_StrandLocalPtr()
	{
		if (id_)
		{
			L_FreeStrandStorageId(id_);
		}
	}

private:
#if !(defined(_MSC_VER) && _MSC_VER < 1800)
	C_StrandLocalPtr(C_StrandLocalPtr const &rhs) = delete;
	C_StrandLocalPtr &operator=(C_StrandLocalPtr const &rhs) = delete;
#else
	C_StrandLocalPtr(C_StrandLocalPtr const &rhs);
	C_StrandLocalPtr &operator=(C_StrandLocalPtr const &rhs);
#endif // !(defined(_MSC_VER) && _MSC_VER < 1800) 

	Id id_;
};


template<typename portPtr>
L_Strand *C_CreateStrand(portPtr port)
{
	return L_CreateStrand(get_pointer(port));
}

template<typename strandPtr>
void C_DestroyStrand(strandPtr strand)
{
	L_DestroyStrand(get_pointer(strand));
}

class C_LibPort : public L_Port
{
public:
	typedef L_Port alloc_interface_type;
	enum : long { id = L_BufferType::LibCompletionPort };

	class C_StopWork : public L_Work
	{
	public:
		typedef L_Work alloc_interface_type;
		enum : long { id = L_BufferType::LibThreadLoopWork };

		// Assumes hCompletionPort is a valid handle
		C_StopWork(win_os_handle hCompletionPort)
			: h(hCompletionPort)
		{
		}

		~C_StopWork()
		{
			CloseHandle(h);
		}

		virtual void DoWork(L_Thread * = 0, int = true, unsigned long = 0, win_ulong_ptr = 0)
		{
			// Just a method to trigger breaking out of StartProc's while loop.
		}

		L_Buffer *l_Buffer;
		win_os_handle h;
	};

	C_LibPort(unsigned long NumberOfConcurrentThreads = 1)
		: h(CreateIoCompletionPort(
				reinterpret_cast<win_os_handle>(size_t(-1)),
				0,
				0,
				NumberOfConcurrentThreads
				))
		, numberOfConcurrentThreads(NumberOfConcurrentThreads)
		, numRunning(0)
	{
		if (h != 0)
		{
			stopWork = C_Alloc<C_StopWork>(h);
			if (!stopWork)
			{
				CloseHandle(h);
				h = 0;
			}
		}
	}

	~C_LibPort()
	{
		if (stopWork)
		{
			for (long i = 0; i < numRunning; ++i)
			{
				QueueWork(GetThreadStopObject());
			}
		}
	}

	static C_Ptr<C_LibPort> CreatePort(unsigned long NumberOfConcurrentThreads)
	{
		return C_Alloc<C_LibPort>(NumberOfConcurrentThreads);
	}

	void IncRunningThreadCount()
	{
		GetThreadStopObject()->l_Buffer->L_IncRef();
		_InterlockedIncrement(&numRunning);
	}

	C_StopWork *GetThreadStopObject()
	{
		return stopWork.get();
	}

	static unsigned int __stdcall StartProc(void *param)
	{
		C_StopWork *stopWork(reinterpret_cast<C_StopWork *>(param));
		win_os_handle hPort = stopWork->h;
		unsigned long numberOfBytes = 0;
		win_ulong_ptr completionKey = 0;
		_OVERLAPPED *pOverlapped = 0;
		int res;
		L_Thread *currentThread(L_GetCurrentThread());

		while (true)
		{
			res = GetQueuedCompletionStatus(hPort, &numberOfBytes, &completionKey, &pOverlapped, 0xFFFFFFFF);
			if (res || pOverlapped)
			{
				if (pOverlapped)
				{
					L_Work *p = static_cast<L_Work *>(pOverlapped);
					p->DoWork(currentThread, res, numberOfBytes, completionKey);
					if (p == stopWork)
					{
						break;
					}
				}
			}
			else
			{
				assert(false);
				break;
			}
			pOverlapped = 0;
		}
		stopWork->l_Buffer->L_DecRef();

		return 0;
	}

	unsigned long StartCRTThreads(unsigned long numThreads)
	{
		unsigned long res = 0;
		if (h != 0)
		{
			for (unsigned long i = 0; i < numThreads; ++i)
			{
				win_os_handle hThread = reinterpret_cast<win_os_handle *>(_beginthreadex(NULL, 0, StartProc, GetThreadStopObject(), 0, NULL));
				// Threads can be created in other ways than _beginthreadex--so long as:
				// 1) On successfull creation IncRunningThreadCount is called, as below.
				// 2) StartProc is called with GetThreadStopObject() as argument from that thread
				//    (the running thread count will be decremented on the way out of StartProc, which is triggered by
				//    the destruction of this C_LibPort object).

				if (hThread != 0)
				{
					IncRunningThreadCount();

					// Successfully created thread--close this handle to it as we aren't storing it anywhere.  Thread will remain running.

					CloseHandle(hThread);
					++res;
				}
			}
		}
		return res;
	}

	unsigned long GetNumberOfConcurrentThreads() const
	{
		return numberOfConcurrentThreads;
	}

	bool Associate(win_os_handle hFile, win_ulong_ptr completionKey)
	{
		return CreateIoCompletionPort(hFile, h, completionKey, 0) != NULL;
	}

	win_os_handle Handle() const
	{
		return h;
	}

	///
	// L_Port
	virtual bool QueueWork(
		L_Work *work,
		unsigned long numberOfBytes = 0,
		win_ulong_ptr completionKey = 0
		)
	{
		if (h != 0)
		{
			return PostQueuedCompletionStatus(h, numberOfBytes, completionKey, work) != 0;
		}
		return false;
	}
	// L_Port
	///

private:
	win_os_handle h;
	unsigned long numberOfConcurrentThreads;
	C_Ptr<C_StopWork> stopWork;
#if defined(_MSC_VER) && _MSC_VER < 1910
	__declspec(align(16)) long numRunning;
#else
	alignas(16) long numRunning;
#endif
};

inline L_Strand *C_CreateSingleThreadStrand()
{
	L_Strand *result = 0;
	C_Ptr<C_LibPort> port(C_LibPort::CreatePort(1));
	if (port)
	{
		if (port->StartCRTThreads(1) == 1)
		{
			result = C_CreateStrand(port);
		}
	}
	return result;
}


#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

// Built-in port implementation to support windows threads, in particular the main extension thread.
class L_EventPort
{
public:
	virtual win_os_handle L_GetHandle() const = 0;
	virtual void L_DoEvent(L_Thread *currentThread) = 0;
	virtual L_Port *L_GetPortInterface() = 0;
};

} // namespace LightspeedTrader

#endif // !defined(LS_L_THREAD_H)
