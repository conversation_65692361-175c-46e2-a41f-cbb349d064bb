#!/usr/bin/env python3
"""
Test the .pyd file directly to isolate the exact issue.
"""

import os
import sys
import ctypes
from ctypes import wintypes

def test_pyd_as_dll():
    """Test loading the .pyd file as a DLL directly."""
    print("=== Testing PYD as DLL ===")
    
    # Change to build directory
    build_dir = os.path.abspath("ocr_accelerator/x64/Release")
    original_cwd = os.getcwd()
    
    try:
        os.chdir(build_dir)
        pyd_path = "ocr_accelerator.pyd"
        
        if not os.path.exists(pyd_path):
            print("❌ PYD file not found in build directory")
            return False
        
        print(f"🔄 Attempting to load {pyd_path} as DLL...")
        
        # Try loading the .pyd as a DLL
        handle = ctypes.windll.kernel32.LoadLibraryW(pyd_path)
        if handle:
            print("✅ PYD loads as DLL successfully")
            
            # Try to get the PyInit function (required for Python modules)
            try:
                init_func = ctypes.windll.kernel32.GetProcAddress(handle, b"PyInit_ocr_accelerator")
                if init_func:
                    print("✅ PyInit_ocr_accelerator function found")
                else:
                    print("❌ PyInit_ocr_accelerator function not found")
            except:
                print("⚠️  Could not check for PyInit function")
            
            ctypes.windll.kernel32.FreeLibrary(handle)
            
            # Now try Python import from this directory
            print("🔄 Trying Python import from build directory...")
            
            # Add current directory to Python path
            if "." not in sys.path:
                sys.path.insert(0, ".")
            
            try:
                import ocr_accelerator
                print("✅ Python import successful from build directory!")
                return True
            except Exception as e:
                print(f"❌ Python import still failed: {e}")
                return False
                
        else:
            error = ctypes.windll.kernel32.GetLastError()
            print(f"❌ PYD failed to load as DLL, error code: {error}")
            
            # Common error codes
            if error == 126:
                print("   This usually means a dependent DLL is missing")
            elif error == 193:
                print("   This means architecture mismatch")
            elif error == 1114:
                print("   This means DLL initialization failed")
            
            return False
            
    finally:
        os.chdir(original_cwd)

def check_missing_runtime_dlls():
    """Check for missing runtime DLLs that might not be in our test list."""
    print("\n=== Checking for Missing Runtime DLLs ===")
    
    # Additional runtime DLLs that might be missing
    runtime_dlls = [
        "msvcp140.dll",
        "vcruntime140.dll", 
        "vcruntime140_1.dll",
        "concrt140.dll",
        "vccorlib140.dll",
        "api-ms-win-crt-heap-l1-1-0.dll",
        "api-ms-win-crt-runtime-l1-1-0.dll",
        "api-ms-win-crt-stdio-l1-1-0.dll",
        "api-ms-win-crt-string-l1-1-0.dll"
    ]
    
    build_dir = "ocr_accelerator/x64/Release"
    
    for dll in runtime_dlls:
        # Check if it's in build directory
        local_path = os.path.join(build_dir, dll)
        if os.path.exists(local_path):
            print(f"✅ {dll}: Found in build directory")
            continue
            
        # Check if it's in system
        try:
            handle = ctypes.windll.kernel32.LoadLibraryW(dll)
            if handle:
                print(f"✅ {dll}: Found in system")
                ctypes.windll.kernel32.FreeLibrary(handle)
            else:
                print(f"❌ {dll}: Not found in system")
        except:
            print(f"❌ {dll}: Load test failed")

def try_alternative_import():
    """Try alternative import methods."""
    print("\n=== Alternative Import Methods ===")
    
    build_dir = os.path.abspath("ocr_accelerator/x64/Release")
    
    # Method 1: Direct importlib
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("ocr_accelerator", 
                                                     os.path.join(build_dir, "ocr_accelerator.pyd"))
        if spec and spec.loader:
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            print("✅ Alternative import successful!")
            return True
    except Exception as e:
        print(f"❌ Alternative import failed: {e}")
    
    return False

if __name__ == "__main__":
    print("TESTRADE PYD Direct Test")
    print("=" * 40)
    
    # Test PYD as DLL
    pyd_ok = test_pyd_as_dll()
    
    # Check runtime DLLs
    check_missing_runtime_dlls()
    
    # Try alternative import
    if not pyd_ok:
        alt_ok = try_alternative_import()
        if alt_ok:
            print("\n🎉 Alternative import method worked!")
        else:
            print("\n❌ All import methods failed")
    
    print("\n💡 If all tests fail, the C++ module may need to be rebuilt with different settings.")