# interfaces/data/services.py

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from data_models.pricing import PriceData
from interfaces.core.services import ILifecycleService

class IMarketDataReceiver(ABC):
    @abstractmethod
    def on_trade(self, symbol: str, price: float, size: int, timestamp: float, **kwargs): pass
    @abstractmethod
    def on_quote(self, symbol: str, bid: float, ask: float, bid_size: int, ask_size: int, timestamp: float, **kwargs): pass

class IPriceProvider(ILifecycleService, IMarketDataReceiver):
    """
    Canonical interface for the JIT Price Oracle.
    """
    
    # --- Lifecycle Methods (from ILifecycleService) ---
    # start(), stop(), and is_ready are implicitly required by inheriting ILifecycleService.
    # No need to declare them here again unless you want to add specific docstrings.

    # --- Modern JIT Oracle Methods ---
    
    @abstractmethod
    def get_price_data(self, symbol: str, max_age_ns: int, correlation_id: Optional[str] = None) -> PriceData:
        """
        Gets the freshest price from the unified cache.
        """
        pass

    @abstractmethod
    def get_price_blocking(self, symbol: str, timeout_sec: float = 2.0, correlation_id: Optional[str] = None) -> PriceData:
        """
        Blocks to wait for a fresh API response if the cache fails.
        """
        pass

    # --- Legacy Compatibility Layer ---

    @abstractmethod
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """[COMPATIBILITY] Gets the latest price as a float."""
        pass

    @abstractmethod
    def get_latest_price_legacy(self, symbol: str) -> Optional[float]:
        """[COMPATIBILITY] Legacy method for backward compatibility."""
        pass
        
    @abstractmethod
    def get_bid_price(self, symbol: str) -> Optional[float]:
        """[COMPATIBILITY] Gets the latest bid price."""
        pass
        
    @abstractmethod
    def get_ask_price(self, symbol: str) -> Optional[float]:
        """[COMPATIBILITY] Gets the latest ask price."""
        pass

    @abstractmethod
    def get_reliable_price(self, symbol: str, side: Optional[str] = None, allow_api_fallback: bool = True, correlation_id: Optional[str] = None) -> Optional[float]:
        """[COMPATIBILITY] Gets a reliable price, potentially blocking."""
        pass
        
    @abstractmethod
    def get_reference_price(self, symbol: str, perf_timestamps: Optional[Dict] = None) -> Optional[float]:
        """[COMPATIBILITY] Gets a suitable reference price for OCR Handler."""
        pass
        
    @abstractmethod
    async def get_latest_price_async(self, symbol: str) -> Optional[float]:
        """[COMPATIBILITY] Async wrapper for legacy compatibility."""
        pass

    @abstractmethod
    def is_price_stale(self, symbol: str, max_age_seconds: float) -> bool:
        """[COMPATIBILITY] Legacy method for staleness check."""
        pass

    @abstractmethod
    def is_subscribed(self, symbol: str) -> bool:
        """[COMPATIBILITY] Checks if the provider has data for the symbol."""
        pass

    @abstractmethod
    def subscribe_symbol(self, symbol: str, interval: float = 0.0, fast_first: bool = False, window_secs: float = 5.0) -> None:
        """[COMPATIBILITY] Signals intent to receive data for a symbol."""
        pass

    @abstractmethod
    def update_cache_manually(self, symbol: str, bid: Optional[float] = None, ask: Optional[float] = None, last: Optional[float] = None):
        """[COMPATIBILITY] Manually update the repository's cache."""
        pass

class IMarketDataPublisher(ABC):
    """Interface for publishing market data to downstream consumers."""
    
    @abstractmethod
    def publish_price_update(self, symbol: str, price_data: Dict[str, Any]) -> None:
        """Publishes a price update to the telemetry system."""
        pass