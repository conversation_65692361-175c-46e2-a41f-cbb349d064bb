#!/usr/bin/env python3
"""
Final metadata verification for Agent 3's TANK SEALING operation.
"""

import re

def check_event_metadata(file_path, event_name):
    """Check if an event has proper metadata in its payload."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find the event
    event_pattern = rf'event_name\s*=\s*["\']{event_name}["\']'
    matches = list(re.finditer(event_pattern, content))
    
    if not matches:
        return f"❌ Event '{event_name}' not found"
    
    results = []
    for match in matches:
        # Find the payload for this event (look backwards and forwards)
        start = max(0, match.start() - 500)
        end = min(len(content), match.end() + 500)
        context = content[start:end]
        
        # Check for critical metadata
        has_correlation = 'correlation_id' in context
        has_timestamp = 'timestamp' in context or 'origin_timestamp_s' in context
        has_payload = 'payload' in context
        
        if has_correlation and has_payload:
            results.append(f"✅ Event '{event_name}' has correlation_id and payload")
        else:
            missing = []
            if not has_correlation:
                missing.append("correlation_id")
            if not has_payload:
                missing.append("payload")
            results.append(f"⚠️  Event '{event_name}' missing: {', '.join(missing)}")
    
    return '\n'.join(results)

def main():
    services = [
        {
            'path': 'modules/ocr/ocr_service.py',
            'events': ['OcrCaptureStarted', 'OcrCaptureFailure', 'OcrCaptureMilestones', 'ServiceHealthStatus']
        },
        {
            'path': 'modules/ocr/ocr_data_conditioning_service.py',
            'events': ['OcrConditioningLatency', 'CleanedOcrSnapshot', 'NoTradeData', 'OcrConditioningError', 'ServiceHealthStatus']
        },
        {
            'path': 'modules/trade_management/ocr_scalping_signal_orchestrator_service.py',
            'events': ['SignalDecision', 'SignalSuppression', 'SignalValidationFailure', 'SignalGenerationFailure', 'OrchestrationMilestones']
        }
    ]
    
    print("=== Final Metadata Verification ===\n")
    
    for service in services:
        print(f"📄 {service['path']}")
        print("-" * 50)
        
        for event in service['events']:
            result = check_event_metadata(service['path'], event)
            print(result)
        
        print()

if __name__ == "__main__":
    main()