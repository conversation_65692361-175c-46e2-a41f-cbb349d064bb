# tank_baseline_compare.ps1 - Compare against baseline performance with enhanced analysis
param(
    [string]$ProcessName = "ApplicationCore",
    [string]$BaselineFile = "tank_baseline.json",
    [int]$MonitorMinutes = 60,
    [string]$ComparisonLogFile = "tank_baseline_comparison.csv",
    [switch]$EnableCSVLogging = $true,
    [switch]$CreateNewBaseline = $false,
    [switch]$ShowDetailedStats = $true
)

Write-Host "🔍 TANK Baseline Performance Comparison Tool" -ForegroundColor Green
Write-Host "Process: $ProcessName | Monitor Duration: $MonitorMinutes minutes" -ForegroundColor Cyan
Write-Host "Baseline File: $BaselineFile | CSV Logging: $EnableCSVLogging" -ForegroundColor Yellow
Write-Host "=" * 100

# Function to create comprehensive baseline
function New-Baseline {
    param([string]$ProcessName, [string]$OutputFile)
    
    Write-Host "🔄 Creating comprehensive baseline measurements..." -ForegroundColor Yellow
    Write-Host "This will take approximately 5 minutes (10 samples, 30 seconds apart)" -ForegroundColor Gray
    
    $measurements = @()
    $startTime = Get-Date
    
    for ($i = 1; $i -le 10; $i++) {
        try {
            $process = Get-Process $ProcessName -ErrorAction Stop
            
            # Collect comprehensive metrics
            $measurement = @{
                Timestamp = Get-Date
                MemoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
                MemoryGB = [math]::Round($process.WorkingSet64 / 1GB, 3)
                CPUTime = $process.TotalProcessorTime.TotalMilliseconds
                HandleCount = $process.HandleCount
                ThreadCount = $process.Threads.Count
                PagedMemoryMB = [math]::Round($process.PagedMemorySize64 / 1MB, 2)
                NonPagedMemoryMB = [math]::Round($process.NonpagedSystemMemorySize64 / 1MB, 2)
                PeakMemoryMB = [math]::Round($process.PeakWorkingSet64 / 1MB, 2)
            }
            $measurements += $measurement
            
            Write-Host "  Sample $($i.ToString().PadLeft(2))/10: $($measurement.MemoryMB) MB | Handles: $($measurement.HandleCount) | Threads: $($measurement.ThreadCount)" -ForegroundColor White
            
            if ($i -lt 10) { Start-Sleep 30 }
        } catch {
            Write-Host "  ❌ Failed to get baseline sample $i`: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    if ($measurements.Count -eq 0) {
        throw "Failed to collect any baseline measurements"
    }
    
    # Calculate comprehensive baseline statistics
    $memoryStats = $measurements | Measure-Object MemoryMB -Average -Maximum -Minimum -StandardDeviation
    $handleStats = $measurements | Measure-Object HandleCount -Average -Maximum -Minimum
    $threadStats = $measurements | Measure-Object ThreadCount -Average -Maximum -Minimum
    
    $baseline = @{
        CreatedDate = Get-Date
        ProcessName = $ProcessName
        SampleCount = $measurements.Count
        DurationMinutes = [math]::Round(((Get-Date) - $startTime).TotalMinutes, 1)
        
        # Memory statistics
        AverageMemoryMB = [math]::Round($memoryStats.Average, 2)
        MinMemoryMB = [math]::Round($memoryStats.Minimum, 2)
        MaxMemoryMB = [math]::Round($memoryStats.Maximum, 2)
        MemoryStdDev = [math]::Round($memoryStats.StandardDeviation, 2)
        MemoryVolatility = [math]::Round(($memoryStats.StandardDeviation / $memoryStats.Average) * 100, 1)
        
        # Resource statistics
        AverageHandles = [math]::Round($handleStats.Average, 0)
        MinHandles = $handleStats.Minimum
        MaxHandles = $handleStats.Maximum
        AverageThreads = [math]::Round($threadStats.Average, 0)
        MinThreads = $threadStats.Minimum
        MaxThreads = $threadStats.Maximum
        
        # Performance thresholds (for alerting)
        MemoryAlertThresholdMB = [math]::Round($memoryStats.Average * 1.2, 2)  # 20% above average
        MemoryWarningThresholdMB = [math]::Round($memoryStats.Average * 1.1, 2)  # 10% above average
        HandleAlertThreshold = [math]::Round($handleStats.Average * 1.3, 0)  # 30% above average
        
        # Raw samples for detailed analysis
        Samples = $measurements
    }
    
    $baseline | ConvertTo-Json -Depth 3 | Out-File $OutputFile -Encoding UTF8
    Write-Host "✅ Comprehensive baseline saved to $OutputFile" -ForegroundColor Green
    Write-Host "   Average Memory: $($baseline.AverageMemoryMB) MB (±$($baseline.MemoryStdDev) MB)" -ForegroundColor Cyan
    Write-Host "   Memory Range: $($baseline.MinMemoryMB) - $($baseline.MaxMemoryMB) MB" -ForegroundColor Cyan
    Write-Host "   Volatility: $($baseline.MemoryVolatility)%" -ForegroundColor Cyan
    
    return $baseline
}

# Load or create baseline
if ($CreateNewBaseline -or -not (Test-Path $BaselineFile)) {
    if ($CreateNewBaseline) {
        Write-Host "🔄 Creating new baseline as requested..." -ForegroundColor Yellow
    } else {
        Write-Host "📋 No baseline found. Creating new baseline..." -ForegroundColor Yellow
    }
    
    try {
        $baseline = New-Baseline -ProcessName $ProcessName -OutputFile $BaselineFile
    } catch {
        Write-Host "❌ Failed to create baseline: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    try {
        $baseline = Get-Content $BaselineFile -Encoding UTF8 | ConvertFrom-Json
        Write-Host "✅ Loaded baseline from $BaselineFile" -ForegroundColor Green
        Write-Host "   Created: $($baseline.CreatedDate)" -ForegroundColor Cyan
        Write-Host "   Average Memory: $($baseline.AverageMemoryMB) MB (±$($baseline.MemoryStdDev) MB)" -ForegroundColor Cyan
        Write-Host "   Samples: $($baseline.SampleCount) over $($baseline.DurationMinutes) minutes" -ForegroundColor Cyan
        Write-Host "   Volatility: $($baseline.MemoryVolatility)%" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ Failed to load baseline: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Creating new baseline..." -ForegroundColor Yellow
        $baseline = New-Baseline -ProcessName $ProcessName -OutputFile $BaselineFile
    }
}

# Create CSV header if logging enabled
if ($EnableCSVLogging) {
    "Timestamp,ElapsedMinutes,CurrentMemoryMB,BaselineMemoryMB,MemoryDeltaMB,MemoryDeltaPercent,CurrentHandles,BaselineHandles,HandleDelta,Status,AlertLevel" | Out-File $ComparisonLogFile -Encoding UTF8
}

Write-Host ""
Write-Host "🔄 Starting performance comparison monitoring..." -ForegroundColor Green
Write-Host "Monitoring for $MonitorMinutes minutes against baseline" -ForegroundColor Cyan
Write-Host "=" * 100

$startTime = Get-Date
$endTime = $startTime.AddMinutes($MonitorMinutes)
$currentMeasurements = @()
$alertCount = 0

while ((Get-Date) -lt $endTime) {
    try {
        $process = Get-Process $ProcessName -ErrorAction Stop
        $currentTime = Get-Date
        $elapsed = ($currentTime - $startTime).TotalMinutes
        
        $current = @{
            Timestamp = $currentTime
            MemoryMB = [math]::Round($process.WorkingSet64 / 1MB, 2)
            MemoryGB = [math]::Round($process.WorkingSet64 / 1GB, 3)
            HandleCount = $process.HandleCount
            ThreadCount = $process.Threads.Count
            PagedMemoryMB = [math]::Round($process.PagedMemorySize64 / 1MB, 2)
        }
        $currentMeasurements += $current
        
        # Compare to baseline
        $memoryDelta = $current.MemoryMB - $baseline.AverageMemoryMB
        $memoryDeltaPercent = ($memoryDelta / $baseline.AverageMemoryMB) * 100
        $handleDelta = $current.HandleCount - $baseline.AverageHandles
        $threadDelta = $current.ThreadCount - $baseline.AverageThreads
        
        # Enhanced status determination
        $alertLevel = "NORMAL"
        $status = "🟢 NORMAL"
        
        if ($current.MemoryMB -gt $baseline.MemoryAlertThresholdMB) {
            $alertLevel = "ALERT"
            $status = "🔴 ALERT"
            $alertCount++
        } elseif ($current.MemoryMB -gt $baseline.MemoryWarningThresholdMB) {
            $alertLevel = "WARNING"
            $status = "🟡 WARNING"
        } elseif ($memoryDeltaPercent -lt -15) {
            $alertLevel = "LOW"
            $status = "🔵 LOW"
        }
        
        # Display results
        Clear-Host
        Write-Host "🔍 TANK Performance vs Baseline Comparison" -ForegroundColor Green
        Write-Host "=" * 100
        Write-Host "Elapsed: $([math]::Round($elapsed, 1)) min | Remaining: $([math]::Round(($endTime - $currentTime).TotalMinutes, 1)) min | Alerts: $alertCount" -ForegroundColor Cyan
        Write-Host ""
        
        Write-Host "📊 BASELINE COMPARISON:" -ForegroundColor Yellow
        Write-Host "  Current Memory:    $($current.MemoryMB) MB" -ForegroundColor White
        Write-Host "  Baseline Memory:   $($baseline.AverageMemoryMB) MB (±$($baseline.MemoryStdDev) MB)" -ForegroundColor Gray
        Write-Host "  Delta:             $(if($memoryDelta -ge 0){'+'})$([math]::Round($memoryDelta, 2)) MB ($(if($memoryDeltaPercent -ge 0){'+'})$([math]::Round($memoryDeltaPercent, 1))%)" -ForegroundColor $(
            if ($memoryDeltaPercent -gt 20) { "Red" } elseif ($memoryDeltaPercent -gt 10) { "Yellow" } else { "Green" }
        )
        Write-Host "  Status:            $status" -ForegroundColor White
        Write-Host "  Alert Threshold:   $($baseline.MemoryAlertThresholdMB) MB (+20%)" -ForegroundColor Gray
        Write-Host "  Warning Threshold: $($baseline.MemoryWarningThresholdMB) MB (+10%)" -ForegroundColor Gray
        Write-Host ""
        
        Write-Host "🔧 RESOURCE COMPARISON:" -ForegroundColor Yellow
        Write-Host "  Handles: $($current.HandleCount) (baseline: $($baseline.AverageHandles), delta: $(if($handleDelta -ge 0){'+'})$handleDelta)" -ForegroundColor $(
            if ([math]::Abs($handleDelta) -gt ($baseline.AverageHandles * 0.2)) { "Yellow" } else { "White" }
        )
        Write-Host "  Threads: $($current.ThreadCount) (baseline: $($baseline.AverageThreads), delta: $(if($threadDelta -ge 0){'+'})$threadDelta)" -ForegroundColor $(
            if ([math]::Abs($threadDelta) -gt ($baseline.AverageThreads * 0.2)) { "Yellow" } else { "White" }
        )
        Write-Host "  Paged Memory: $($current.PagedMemoryMB) MB" -ForegroundColor White
        Write-Host ""
        
        # Session trend analysis
        if ($currentMeasurements.Count -ge 5) {
            $sessionGrowth = $current.MemoryMB - $currentMeasurements[0].MemoryMB
            $sessionGrowthRate = if ($elapsed -gt 0) { $sessionGrowth / $elapsed } else { 0 }
            
            # Calculate session volatility
            $sessionMemoryValues = $currentMeasurements | ForEach-Object { $_.MemoryMB }
            $sessionAvg = ($sessionMemoryValues | Measure-Object -Average).Average
            $sessionStdDev = if ($sessionMemoryValues.Count -gt 1) {
                [math]::Sqrt((($sessionMemoryValues | ForEach-Object { ($_ - $sessionAvg) * ($_ - $sessionAvg) }) | Measure-Object -Sum).Sum / ($sessionMemoryValues.Count - 1))
            } else { 0 }
            $sessionVolatility = if ($sessionAvg -gt 0) { ($sessionStdDev / $sessionAvg) * 100 } else { 0 }
            
            Write-Host "📈 SESSION ANALYSIS:" -ForegroundColor Yellow
            Write-Host "  Session Growth:    $(if($sessionGrowth -ge 0){'+'})$([math]::Round($sessionGrowth, 2)) MB" -ForegroundColor $(
                if ($sessionGrowth -gt 100) { "Red" } elseif ($sessionGrowth -gt 50) { "Yellow" } else { "Green" }
            )
            Write-Host "  Growth Rate:       $([math]::Round($sessionGrowthRate, 2)) MB/min" -ForegroundColor White
            Write-Host "  Session Volatility: $([math]::Round($sessionVolatility, 1))% (baseline: $($baseline.MemoryVolatility)%)" -ForegroundColor $(
                if ($sessionVolatility -gt ($baseline.MemoryVolatility * 1.5)) { "Yellow" } else { "White" }
            )
            
            if ($sessionGrowthRate -gt 5) {
                Write-Host "  🚨 WARNING: High growth rate detected!" -ForegroundColor Red -BackgroundColor Yellow
            } elseif ($sessionGrowthRate -lt 0.5 -and $alertLevel -eq "NORMAL") {
                Write-Host "  ✅ GOOD: Memory usage appears stable" -ForegroundColor Green
            }
        }
        
        if ($ShowDetailedStats -and $currentMeasurements.Count -ge 3) {
            Write-Host ""
            Write-Host "📊 DETAILED STATISTICS:" -ForegroundColor Yellow
            $recentAvg = ($currentMeasurements | Select-Object -Last 5 | Measure-Object MemoryMB -Average).Average
            Write-Host "  Recent Average:    $([math]::Round($recentAvg, 2)) MB (last 5 samples)" -ForegroundColor White
            Write-Host "  vs Baseline:       $(if(($recentAvg - $baseline.AverageMemoryMB) -ge 0){'+'})$([math]::Round($recentAvg - $baseline.AverageMemoryMB, 2)) MB" -ForegroundColor White
            Write-Host "  Sample Count:      $($currentMeasurements.Count)" -ForegroundColor White
        }
        
        # CSV Logging
        if ($EnableCSVLogging) {
            "$($currentTime.ToString('yyyy-MM-dd HH:mm:ss')),$([math]::Round($elapsed, 2)),$($current.MemoryMB),$($baseline.AverageMemoryMB),$([math]::Round($memoryDelta, 2)),$([math]::Round($memoryDeltaPercent, 2)),$($current.HandleCount),$($baseline.AverageHandles),$handleDelta,$status,$alertLevel" | Out-File $ComparisonLogFile -Append -Encoding UTF8
        }
        
        Write-Host ""
        Write-Host "Next sample in 60 seconds... (Ctrl+C to stop early)" -ForegroundColor Gray
        if ($EnableCSVLogging) {
            Write-Host "Data logged to: $ComparisonLogFile" -ForegroundColor Gray
        }
        
        Start-Sleep 60
        
    } catch {
        Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        Start-Sleep 10
    }
}

Write-Host ""
Write-Host "🎯 MONITORING COMPLETE - FINAL ANALYSIS" -ForegroundColor Green
Write-Host "=" * 60

if ($currentMeasurements.Count -gt 0) {
    $finalMemory = $currentMeasurements[-1].MemoryMB
    $initialMemory = $currentMeasurements[0].MemoryMB
    $totalGrowth = $finalMemory - $initialMemory
    $avgMemory = ($currentMeasurements | Measure-Object MemoryMB -Average).Average
    $maxMemory = ($currentMeasurements | Measure-Object MemoryMB -Maximum).Maximum
    $minMemory = ($currentMeasurements | Measure-Object MemoryMB -Minimum).Minimum
    
    Write-Host "📊 SESSION SUMMARY:" -ForegroundColor Yellow
    Write-Host "  Initial Memory:    $initialMemory MB" -ForegroundColor White
    Write-Host "  Final Memory:      $finalMemory MB" -ForegroundColor White
    Write-Host "  Total Growth:      $(if($totalGrowth -ge 0){'+'})$([math]::Round($totalGrowth, 2)) MB" -ForegroundColor $(
        if ($totalGrowth -gt 100) { "Red" } elseif ($totalGrowth -gt 50) { "Yellow" } else { "Green" }
    )
    Write-Host "  Session Average:   $([math]::Round($avgMemory, 2)) MB" -ForegroundColor White
    Write-Host "  Session Range:     $([math]::Round($minMemory, 2)) - $([math]::Round($maxMemory, 2)) MB" -ForegroundColor White
    Write-Host ""
    
    $baselineDelta = $avgMemory - $baseline.AverageMemoryMB
    $baselineDeltaPercent = ($baselineDelta / $baseline.AverageMemoryMB) * 100
    
    Write-Host "📈 BASELINE COMPARISON SUMMARY:" -ForegroundColor Yellow
    Write-Host "  vs Baseline Avg:   $(if($baselineDelta -ge 0){'+'})$([math]::Round($baselineDelta, 2)) MB ($(if($baselineDeltaPercent -ge 0){'+'})$([math]::Round($baselineDeltaPercent, 1))%)" -ForegroundColor $(
        if ($baselineDeltaPercent -gt 20) { "Red" } elseif ($baselineDeltaPercent -gt 10) { "Yellow" } else { "Green" }
    )
    Write-Host "  Alert Count:       $alertCount" -ForegroundColor $(if($alertCount -gt 0) { "Red" } else { "Green" })
    Write-Host "  Samples Collected: $($currentMeasurements.Count)" -ForegroundColor White
    
    # Performance assessment
    Write-Host ""
    Write-Host "🏥 PERFORMANCE ASSESSMENT:" -ForegroundColor Yellow
    
    if ($alertCount -gt ($currentMeasurements.Count * 0.2)) {
        Write-Host "  🔴 POOR: Frequent alerts detected ($alertCount alerts)" -ForegroundColor Red
        Write-Host "  • Consider investigating memory usage patterns" -ForegroundColor Red
        Write-Host "  • Review recent system changes" -ForegroundColor Red
    } elseif ($baselineDeltaPercent -gt 15) {
        Write-Host "  🟡 ELEVATED: Memory usage significantly above baseline" -ForegroundColor Yellow
        Write-Host "  • Monitor for continued growth" -ForegroundColor Yellow
        Write-Host "  • Consider updating baseline if this is new normal" -ForegroundColor Yellow
    } elseif ($totalGrowth -gt 50) {
        Write-Host "  🟡 CONCERNING: Significant memory growth during session" -ForegroundColor Yellow
        Write-Host "  • Continue monitoring for leak patterns" -ForegroundColor Yellow
    } else {
        Write-Host "  🟢 GOOD: Performance within acceptable parameters" -ForegroundColor Green
        Write-Host "  • System operating normally compared to baseline" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "Analysis complete! 🎉" -ForegroundColor Green
