# testrade_project_root/utils/event_queue.py
import logging
import time
from queue import SimpleQueue, Full, Empty # SimpleQueue is thread-safe and unbounded by default
from typing import Any, Optional

logger = logging.getLogger(__name__)

class AsyncPublishQueue:
    """
    A simple wrapper around queue.SimpleQueue for enqueuing items (typically
    serialized JSON strings) to be published asynchronously. Includes a soft
    maximum size for monitoring and a strategy to drop oldest items if exceeded.
    """
    def __init__(self, max_size: int, name: str = "DefaultAsyncPubQueue"):
        """
        Args:
            max_size: The soft maximum size of the queue. If exceeded, oldest items are dropped.
            name: A descriptive name for this queue instance (for logging).
        """
        if not isinstance(max_size, int) or max_size <= 0:
            raise ValueError("max_size must be a positive integer.")

        self._queue = SimpleQueue()
        self._max_size = max_size
        self.name = name
        self._dropped_items_count = 0
        self._items_added_count = 0
        self._items_retrieved_count = 0
        self.logger = logger # Instance logger

        self.logger.info(f"AsyncPublishQueue '{self.name}' initialized with soft max_size: {self._max_size}")

    def put(self, item: Any) -> bool:
        """
        Adds an item to the queue. If the current size >= max_size,
        it discards the oldest item to make space.

        Args:
            item: The item to add to the queue.

        Returns:
            True if the item was successfully added (potentially after dropping an old one),
            False if an error occurred during the process.
        """
        try:
            current_size = self._queue.qsize() # qsize() is approximate for SimpleQueue
            if current_size >= self._max_size:
                try:
                    self._queue.get_nowait() # Remove oldest item
                    self._dropped_items_count += 1
                    self.logger.warning(f"Queue '{self.name}' is at/over capacity (size {current_size}/{self._max_size}). Dropped oldest item.")
                except Empty:
                    # This case is unlikely if qsize reported >= max_size, but good for robustness
                    self.logger.debug(f"Queue '{self.name}' reported full but was empty on get_nowait() before put. Proceeding to put.")
                except Exception as e_drop:
                    self.logger.error(f"Queue '{self.name}': Error dropping oldest item from full queue: {e_drop}", exc_info=True)
                    # Decide if we should still try to put or return False.
                    # If dropping failed, putting might also fail or exceed intended capacity.
                    # For now, let's try to put if dropping failed but queue isn't actually "stuck".
                    pass

            self._queue.put_nowait(item) # SimpleQueue's put_nowait is equivalent to put
            self._items_added_count += 1
            return True
        except Exception as e_put:
            self.logger.error(f"Queue '{self.name}': Error putting item onto queue: {e_put}", exc_info=True)
            return False

    def get(self, block: bool = True, timeout: Optional[float] = None) -> Any:
        """
        Removes and returns an item from the queue.

        Args:
            block: If True (default), block until an item is available.
            timeout: If block is True, how long to wait for an item in seconds.
                     If None, block indefinitely. Not directly supported by SimpleQueue.get(),
                     so a polling loop is used if timeout is specified.

        Raises:
            queue.Empty: If non-blocking and queue is empty, or if timeout occurs.

        Returns:
            The item from the queue.
        """
        try:
            if not block:
                item = self._queue.get_nowait()
            elif timeout is not None:
                # Polling implementation for timeout with SimpleQueue
                start_time = time.monotonic()
                while True:
                    try:
                        item = self._queue.get_nowait()
                        break
                    except Empty:
                        if (time.monotonic() - start_time) >= timeout:
                            raise # Re-raise Empty as timeout occurred
                        time.sleep(0.001) # Small sleep to yield, avoid busy-wait
            else: # block=True, timeout=None
                item = self._queue.get() # This will block indefinitely if queue is empty

            self._items_retrieved_count += 1
            return item
        except Empty: # Re-raise Empty if it occurs from get_nowait or our timeout logic
            raise
        except Exception as e:
            self.logger.error(f"Queue '{self.name}': Unexpected error during get: {e}", exc_info=True)
            raise # Re-raise other unexpected errors

    def qsize(self) -> int:
        """Return the approximate size of the queue."""
        return self._queue.qsize()

    def empty(self) -> bool:
        """Return True if the queue is empty, False otherwise."""
        return self._queue.empty()

    def get_stats(self) -> dict:
        """Returns statistics about the queue."""
        return {
            "name": self.name,
            "current_size": self.qsize(),
            "max_size_soft": self._max_size,
            "items_added_total": self._items_added_count,
            "items_retrieved_total": self._items_retrieved_count,
            "items_dropped_total": self._dropped_items_count
        }
