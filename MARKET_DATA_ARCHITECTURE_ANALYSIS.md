# TESTRADE Market Data Flow Architecture Analysis

## Current Architecture Overview

### Data Flow Path (Current)

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                         ALPACA WEBSOCKET (SIP FEED)                          │
│                               All Symbols                                     │
└─────────────────────────────────────┬───────────────────────────────────────┘
                                      │
                                      ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                        PriceFetchingService                                   │
│  - Connects to Alpaca WebSocket                                              │
│  - Subscribes to initial_symbols (from ActiveSymbolsService)                 │
│  - Receives ALL market data for subscribed symbols                           │
│  - Has receiver: MarketDataFilterService                                     │
└─────────────────────────────────────┬───────────────────────────────────────┘
                                      │
                                      ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                       MarketDataFilterService                                 │
│  - "The Bouncer" - First line of defense                                     │
│  - Checks: is_relevant_for_publishing(symbol)                                │
│  - If TRUE: forwards to PriceRepository                                      │
│  - If FALSE: drops silently (no logging)                                     │
│  - Has receiver: PriceRepository                                             │
└─────────────────────────────────────┬───────────────────────────────────────┘
                                      │ (Only relevant symbols)
                                      ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                          PriceRepository                                      │
│  - "JIT Price Oracle" - Unified cache                                        │
│  - Updates internal cache with filtered data                                 │
│  - Provides get_price_data() with staleness checks                          │
│  - Has REST API fallback for stale/missing data                             │
│  - Currently: DOES NOT publish to Redis/IPC                                  │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                       ActiveSymbolsService                                    │
│  - Central authority for "relevant" symbols                                   │
│  - Tracks: _active_symbols (positions) + _pending_open_symbols (orders)      │
│  - Provides: is_relevant_for_publishing(symbol) → bool                       │
│  - Listens to: PositionUpdateEvent, OpenOrderSubmittedEvent, etc.           │
│  - Bootstrap: Loads from PositionManager + initial config symbols            │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                    FilteredMarketDataPublisher                                │
│  - Currently UNUSED in the data flow!                                        │
│  - Designed to publish filtered data to Redis/IPC                           │
│  - Has publish_market_data() method                                         │
│  - Uses TelemetryService for buffered delivery                              │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Key Findings

### 1. **Missing Connection**
The PriceRepository is NOT wired to FilteredMarketDataPublisher. This means:
- Filtered market data reaches PriceRepository but goes no further
- Redis streams `testrade:filtered:market-trades` and `testrade:filtered:market-quotes` are empty
- GUI receives no filtered market data updates

### 2. **Current Wiring in DI Container**

```python
# PriceFetchingService → MarketDataFilterService
market_data_filter = container.resolve('MarketDataFilterService')
PriceFetchingService(receiver=market_data_filter, ...)

# MarketDataFilterService → PriceRepository  
price_repository = container.resolve(DI_IPriceProvider)
MarketDataFilterService(price_repository=price_repository, ...)

# FilteredMarketDataPublisher (standalone - not connected!)
FilteredMarketDataPublisher(telemetry_service=..., config_service=...)
```

### 3. **ActiveSymbolsService Role**
- Maintains the "allowlist" of symbols
- Updated by trading events (positions, orders)
- MarketDataFilterService queries it for every price update
- Critical for performance (drops irrelevant symbols early)

### 4. **Data Flow Decision Points**

1. **PriceFetchingService**: Which symbols to subscribe to?
   - Currently: Uses initial_symbols from ActiveSymbolsService
   - Dynamic subscription attempts exist but are commented out

2. **MarketDataFilterService**: Which data to forward?
   - Uses ActiveSymbolsService.is_relevant_for_publishing()
   - Binary decision: forward or drop

3. **PriceRepository**: What to do with received data?
   - Currently: Only updates internal cache
   - Missing: Forward to FilteredMarketDataPublisher

## Proposed Warehouse Model Changes

### Option 1: Direct Wiring (Minimal Change)
```
PriceRepository → FilteredMarketDataPublisher
```
- PriceRepository would need to import and use FilteredMarketDataPublisher
- Add publish calls in on_trade() and on_quote() methods
- Requires injecting publisher into PriceRepository

### Option 2: Event-Based (Clean Architecture)
```
PriceRepository → EventBus → FilteredMarketDataPublisher
```
- PriceRepository publishes events for filtered data
- FilteredMarketDataPublisher subscribes to these events
- Maintains separation of concerns

### Option 3: Composite Receiver Pattern
```
MarketDataFilterService → CompositeReceiver[PriceRepository, FilteredMarketDataPublisher]
```
- Both services receive filtered data in parallel
- No changes to PriceRepository needed
- Clean separation of caching vs publishing

## Current Service Dependencies

### PriceFetchingService
- Depends on: MarketDataFilterService (as receiver)
- Optional: REST client, TelemetryService

### MarketDataFilterService  
- Depends on: ActiveSymbolsService, PriceRepository (as receiver)
- Optional: FilteredMarketDataPublisher (currently injected but unused)

### PriceRepository
- Depends on: Config, REST client
- Missing: FilteredMarketDataPublisher dependency

### FilteredMarketDataPublisher
- Depends on: Config, TelemetryService
- Missing: No one sends it data!

### ActiveSymbolsService
- Depends on: EventBus, PositionManager (ref), PriceFetchingService (ref)
- Provides: is_relevant_for_publishing() to filter service

## Implementation Recommendations

1. **Immediate Fix**: Wire PriceRepository to publish to FilteredMarketDataPublisher
2. **Consider**: Moving filtering logic entirely to PriceRepository (warehouse model)
3. **Optimize**: Batch publish operations to reduce Redis/IPC overhead
4. **Monitor**: Add metrics for filtering effectiveness (dropped vs forwarded ratio)

## Performance Considerations

- Current design minimizes memory by dropping irrelevant data early
- Publishing adds IPC overhead - consider batching
- ActiveSymbolsService lookups are O(1) set operations - very fast
- Consider caching the publisher's formatted messages briefly