#!/usr/bin/env python3
"""
Safe DLL cleanup script for TESTRADE root directory.
Removes duplicate DLLs that are already in the proper build directory.
"""

import os
import sys

def cleanup_duplicate_dlls():
    """Remove duplicate DLLs from root directory."""
    print("=== TESTRADE DLL Cleanup ===")
    
    # DLLs that are duplicated between root and build directory
    duplicate_dlls = [
        "archive.dll",
        "bz2.dll", 
        "gif.dll",
        "jpeg62.dll",
        "leptonica-1.85.0.dll",
        "libcrypto-3-x64.dll",
        "libcurl.dll",
        "liblzma.dll",
        "libpng16.dll",
        "libsharpyuv.dll",
        "libwebp.dll",
        "libwebpmux.dll",
        "lz4.dll",
        "opencv_core4.dll",         # No longer needed
        "opencv_imgcodecs4.dll",    # No longer needed
        "opencv_imgproc4.dll",      # No longer needed
        "openjp2.dll",
        "tesseract55.dll",
        "tiff.dll",
        "zlib1.dll",
        "zstd.dll"
    ]
    
    # OCR accelerator build directory
    build_dir = os.path.join("ocr_accelerator", "x64", "Release")
    
    # Verify build directory exists
    if not os.path.exists(build_dir):
        print(f"❌ Build directory not found: {build_dir}")
        return False
    
    # Verify critical DLLs exist in build directory
    critical_dlls = ["tesseract55.dll", "leptonica-1.85.0.dll"]
    for dll in critical_dlls:
        build_dll_path = os.path.join(build_dir, dll)
        if not os.path.exists(build_dll_path):
            print(f"❌ Critical DLL missing in build directory: {dll}")
            return False
    
    print(f"✅ Build directory verified: {os.path.abspath(build_dir)}")
    
    # Remove duplicate DLLs from root
    removed_count = 0
    for dll in duplicate_dlls:
        root_dll_path = dll
        if os.path.exists(root_dll_path):
            try:
                os.remove(root_dll_path)
                print(f"✅ Removed: {dll}")
                removed_count += 1
            except Exception as e:
                print(f"❌ Failed to remove {dll}: {e}")
        else:
            print(f"⚪ Not found: {dll}")
    
    # Also remove opencv DLLs from build directory (no longer needed)
    opencv_dlls = ["opencv_core4.dll", "opencv_imgproc4.dll"]
    for dll in opencv_dlls:
        build_dll_path = os.path.join(build_dir, dll)
        if os.path.exists(build_dll_path):
            try:
                os.remove(build_dll_path)
                print(f"✅ Removed from build: {dll}")
                removed_count += 1
            except Exception as e:
                print(f"❌ Failed to remove from build {dll}: {e}")
    
    print(f"\n🎉 Cleanup complete! Removed {removed_count} files")
    print(f"✅ C++ OCR accelerator will load DLLs from: {os.path.abspath(build_dir)}")
    return True

def verify_module_loading():
    """Verify the C++ module can still be loaded after cleanup."""
    print("\n=== Verifying Module Loading ===")
    
    try:
        # Simulate the OCR service import logic
        import sys
        import platform
        
        if platform.system() == "Windows":
            # Add the C++ module build path to sys.path
            cpp_module_path = os.path.join("ocr_accelerator", "x64", "Release")
            if os.path.exists(cpp_module_path) and cpp_module_path not in sys.path:
                sys.path.insert(0, cpp_module_path)
            
            import ocr_accelerator
            print("✅ C++ module imported successfully")
            
            # Test basic function
            if hasattr(ocr_accelerator, 'test_function'):
                result = ocr_accelerator.test_function()
                print(f"✅ test_function() works: {result}")
            
            return True
        else:
            print("⚪ Not on Windows - module loading verification skipped")
            return True
            
    except ImportError as e:
        print(f"❌ Module import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Module test failed: {e}")
        return False

if __name__ == "__main__":
    print("TESTRADE DLL Cleanup Tool")
    print("This will remove duplicate DLLs from the root directory.")
    print("DLLs will remain in the proper build directory.")
    print()
    
    # Get user confirmation
    response = input("Proceed with cleanup? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Cleanup cancelled.")
        sys.exit(0)
    
    # Perform cleanup
    success = cleanup_duplicate_dlls()
    
    if success:
        # Verify module still works (on Windows)
        verify_module_loading()
        print("\n✅ DLL cleanup completed successfully!")
    else:
        print("\n❌ DLL cleanup failed!")
        sys.exit(1)