# tank_monitor_analyzer.ps1 - Analyze TANK monitoring data
param(
    [string]$CsvFile = "tank_memory_detailed.csv",
    [switch]$GenerateReport = $true,
    [switch]$ShowGraphs = $true,
    [string]$ReportFile = "tank_analysis_report.html"
)

Write-Host "🔍 TANK Memory Monitor Data Analyzer" -ForegroundColor Green
Write-Host "Analyzing: $CsvFile" -ForegroundColor Cyan
Write-Host "=" * 80

if (-not (Test-Path $CsvFile)) {
    Write-Host "❌ Error: CSV file '$CsvFile' not found!" -ForegroundColor Red
    exit 1
}

# Import data
try {
    $data = Import-Csv $CsvFile
    Write-Host "✅ Loaded $($data.Count) data points" -ForegroundColor Green
} catch {
    Write-Host "❌ Error reading CSV file: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

if ($data.Count -eq 0) {
    Write-Host "❌ No data found in CSV file!" -ForegroundColor Red
    exit 1
}

# Convert numeric columns
$data | ForEach-Object {
    $_.MemoryMB = [double]$_.MemoryMB
    $_.MemoryGB = [double]$_.MemoryGB
    $_.ElapsedMinutes = [double]$_.ElapsedMinutes
    $_.CPUPercent = [double]$_.CPUPercent
    $_.HandleCount = [int]$_.HandleCount
    $_.ThreadCount = [int]$_.ThreadCount
}

# Calculate statistics
$memoryStats = $data | Measure-Object MemoryMB -Average -Maximum -Minimum -Sum
$cpuStats = $data | Measure-Object CPUPercent -Average -Maximum -Minimum
$durationMinutes = ($data | Measure-Object ElapsedMinutes -Maximum).Maximum

# Memory growth analysis
$growthRates = @()
for ($i = 1; $i -lt $data.Count; $i++) {
    $growth = $data[$i].MemoryMB - $data[$i-1].MemoryMB
    $growthRates += $growth
}
$avgGrowthRate = if ($growthRates.Count -gt 0) { ($growthRates | Measure-Object -Average).Average } else { 0 }

# Detect memory leaks (consistent growth over time)
$leakDetection = $false
$leakSeverity = "None"
if ($avgGrowthRate -gt 5) {
    $leakDetection = $true
    $leakSeverity = if ($avgGrowthRate -gt 20) { "Severe" } elseif ($avgGrowthRate -gt 10) { "Moderate" } else { "Minor" }
}

# Peak usage periods
$peakUsage = $data | Sort-Object MemoryMB -Descending | Select-Object -First 5
$lowUsage = $data | Sort-Object MemoryMB | Select-Object -First 5

Write-Host ""
Write-Host "📊 MEMORY ANALYSIS SUMMARY" -ForegroundColor Yellow
Write-Host "=" * 50
Write-Host "Duration:           $([math]::Round($durationMinutes, 1)) minutes" -ForegroundColor White
Write-Host "Data Points:        $($data.Count)" -ForegroundColor White
Write-Host "Average Memory:     $([math]::Round($memoryStats.Average, 2)) MB" -ForegroundColor White
Write-Host "Peak Memory:        $([math]::Round($memoryStats.Maximum, 2)) MB" -ForegroundColor White
Write-Host "Minimum Memory:     $([math]::Round($memoryStats.Minimum, 2)) MB" -ForegroundColor White
Write-Host "Memory Range:       $([math]::Round($memoryStats.Maximum - $memoryStats.Minimum, 2)) MB" -ForegroundColor White
Write-Host "Avg Growth Rate:    $([math]::Round($avgGrowthRate, 3)) MB per interval" -ForegroundColor $(if($avgGrowthRate -gt 5) { "Red" } elseif($avgGrowthRate -gt 1) { "Yellow" } else { "Green" })

Write-Host ""
Write-Host "⚡ CPU ANALYSIS" -ForegroundColor Yellow
Write-Host "=" * 30
Write-Host "Average CPU:        $([math]::Round($cpuStats.Average, 2))%" -ForegroundColor White
Write-Host "Peak CPU:           $([math]::Round($cpuStats.Maximum, 2))%" -ForegroundColor White
Write-Host "Minimum CPU:        $([math]::Round($cpuStats.Minimum, 2))%" -ForegroundColor White

Write-Host ""
Write-Host "🔍 MEMORY LEAK ANALYSIS" -ForegroundColor Yellow
Write-Host "=" * 40
Write-Host "Leak Detected:      $(if($leakDetection) { '⚠️ YES' } else { '✅ NO' })" -ForegroundColor $(if($leakDetection) { "Red" } else { "Green" })
Write-Host "Leak Severity:      $leakSeverity" -ForegroundColor $(if($leakSeverity -eq "Severe") { "Red" } elseif($leakSeverity -eq "Moderate") { "Yellow" } else { "Green" })

if ($leakDetection) {
    Write-Host ""
    Write-Host "🚨 MEMORY LEAK RECOMMENDATIONS:" -ForegroundColor Red
    Write-Host "  • Monitor for extended periods to confirm trend" -ForegroundColor Yellow
    Write-Host "  • Check for unclosed resources or growing caches" -ForegroundColor Yellow
    Write-Host "  • Review recent code changes for memory management" -ForegroundColor Yellow
    Write-Host "  • Consider implementing memory profiling" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📈 PEAK USAGE PERIODS" -ForegroundColor Yellow
Write-Host "=" * 40
$peakUsage | ForEach-Object {
    Write-Host "  $($_.Timestamp): $($_.MemoryMB) MB (CPU: $($_.CPUPercent)%)" -ForegroundColor White
}

Write-Host ""
Write-Host "📉 LOW USAGE PERIODS" -ForegroundColor Yellow
Write-Host "=" * 40
$lowUsage | ForEach-Object {
    Write-Host "  $($_.Timestamp): $($_.MemoryMB) MB (CPU: $($_.CPUPercent)%)" -ForegroundColor White
}

# Stability analysis
$stablePoints = $growthRates | Where-Object { [math]::Abs($_) -lt 5 }
$stabilityPercentage = if ($growthRates.Count -gt 0) { ($stablePoints.Count / $growthRates.Count) * 100 } else { 0 }

Write-Host ""
Write-Host "📊 STABILITY ANALYSIS" -ForegroundColor Yellow
Write-Host "=" * 40
Write-Host "Stable Intervals:   $($stablePoints.Count)/$($growthRates.Count) ($([math]::Round($stabilityPercentage, 1))%)" -ForegroundColor $(if($stabilityPercentage -gt 80) { "Green" } elseif($stabilityPercentage -gt 60) { "Yellow" } else { "Red" })
Write-Host "Stability Rating:   $(if($stabilityPercentage -gt 90) { 'Excellent' } elseif($stabilityPercentage -gt 80) { 'Good' } elseif($stabilityPercentage -gt 60) { 'Fair' } else { 'Poor' })" -ForegroundColor $(if($stabilityPercentage -gt 80) { "Green" } elseif($stabilityPercentage -gt 60) { "Yellow" } else { "Red" })

# Generate HTML report
if ($GenerateReport) {
    Write-Host ""
    Write-Host "📄 Generating HTML Report..." -ForegroundColor Cyan
    
    $htmlContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>TANK Memory Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 3px; }
        .alert { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 3px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 TANK Memory Analysis Report</h1>
        <p>Generated: $(Get-Date)</p>
        <p>Data Source: $CsvFile</p>
    </div>
    
    <div class="section">
        <h2>📊 Summary Statistics</h2>
        <div class="metric"><strong>Duration:</strong> $([math]::Round($durationMinutes, 1)) minutes</div>
        <div class="metric"><strong>Data Points:</strong> $($data.Count)</div>
        <div class="metric"><strong>Average Memory:</strong> $([math]::Round($memoryStats.Average, 2)) MB</div>
        <div class="metric"><strong>Peak Memory:</strong> $([math]::Round($memoryStats.Maximum, 2)) MB</div>
        <div class="metric"><strong>Memory Range:</strong> $([math]::Round($memoryStats.Maximum - $memoryStats.Minimum, 2)) MB</div>
        <div class="metric"><strong>Avg Growth Rate:</strong> $([math]::Round($avgGrowthRate, 3)) MB/interval</div>
    </div>
    
    <div class="section">
        <h2>🔍 Memory Leak Analysis</h2>
        $(if($leakDetection) { 
            "<div class='alert'><strong>⚠️ Memory Leak Detected!</strong><br>Severity: $leakSeverity<br>Average Growth: $([math]::Round($avgGrowthRate, 3)) MB per interval</div>"
        } else {
            "<div class='success'><strong>✅ No Memory Leak Detected</strong><br>Memory usage appears stable</div>"
        })
    </div>
    
    <div class="section">
        <h2>📊 Stability Analysis</h2>
        <div class="metric"><strong>Stable Intervals:</strong> $($stablePoints.Count)/$($growthRates.Count) ($([math]::Round($stabilityPercentage, 1))%)</div>
        <div class="metric"><strong>Stability Rating:</strong> $(if($stabilityPercentage -gt 90) { 'Excellent' } elseif($stabilityPercentage -gt 80) { 'Good' } elseif($stabilityPercentage -gt 60) { 'Fair' } else { 'Poor' })</div>
    </div>
    
    <div class="section">
        <h2>📈 Peak Usage Periods</h2>
        <table>
            <tr><th>Timestamp</th><th>Memory (MB)</th><th>CPU (%)</th></tr>
            $($peakUsage | ForEach-Object { "<tr><td>$($_.Timestamp)</td><td>$($_.MemoryMB)</td><td>$($_.CPUPercent)</td></tr>" } | Out-String)
        </table>
    </div>
</body>
</html>
"@
    
    $htmlContent | Out-File $ReportFile -Encoding UTF8
    Write-Host "✅ HTML report saved to: $ReportFile" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎯 RECOMMENDATIONS" -ForegroundColor Yellow
Write-Host "=" * 40

if ($leakDetection) {
    Write-Host "  🚨 URGENT: Investigate memory leak" -ForegroundColor Red
    Write-Host "  • Run extended monitoring sessions" -ForegroundColor Yellow
    Write-Host "  • Enable detailed logging in ApplicationCore" -ForegroundColor Yellow
    Write-Host "  • Check mmap buffer growth patterns" -ForegroundColor Yellow
} elseif ($stabilityPercentage -lt 70) {
    Write-Host "  ⚠️  Memory usage is unstable" -ForegroundColor Yellow
    Write-Host "  • Monitor during different load conditions" -ForegroundColor Yellow
    Write-Host "  • Check for periodic cleanup processes" -ForegroundColor Yellow
} else {
    Write-Host "  ✅ Memory usage appears healthy" -ForegroundColor Green
    Write-Host "  • Continue periodic monitoring" -ForegroundColor Green
    Write-Host "  • Consider baseline establishment" -ForegroundColor Green
}

Write-Host ""
Write-Host "Analysis complete! 🎉" -ForegroundColor Green
