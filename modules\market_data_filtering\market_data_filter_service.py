#
# --- FILE: /modules/market_data_filtering/market_data_filter_service.py ---
#
import logging
from typing import List, Optional

# Import interfaces from centralized location
from interfaces.data.services import IMarketDataReceiver
from modules.active_symbols.active_symbols_service import ActiveSymbolsService

logger = logging.getLogger(__name__)

class MarketDataFilterService(IMarketDataReceiver):
    """
    Acts as a gatekeeper for raw market data. It receives all data from the
    PriceFetchingService, performs a quick "allow list" check using the
    ActiveSymbolsService, and forwards only the relevant data to the
    PriceRepository for deeper processing.
    """

    def __init__(self,
                 active_symbols_service: ActiveSymbolsService,
                 price_repository: IMarketDataReceiver,
                 streaming_consumers: Optional[List[IMarketDataReceiver]] = None):
        self.active_symbols_service = active_symbols_service
        self.price_repository = price_repository
        self.streaming_consumers = streaming_consumers or []
        self.logger = logger
        
        if not all([active_symbols_service, price_repository]):
            raise ValueError("MarketDataFilterService requires both ActiveSymbolsService and PriceRepository.")
            
        self.logger.info(f"MarketDataFilterService initialized with {len(self.streaming_consumers)} streaming consumers.")

    def on_trade(self, symbol: str, price: float, size: int, timestamp: float, conditions: Optional[List[str]] = None, **kwargs):
        """
        Receives raw trade data, filters it, and forwards if relevant.
        """
        symbol_upper = symbol.upper()
        
        # The single, efficient check.
        if self.active_symbols_service.is_relevant_for_publishing(symbol_upper):
            # If relevant, pass it on to the next stage (PriceRepository).
            self.price_repository.on_trade(symbol, price, size, timestamp, conditions, **kwargs)
            
            # Also forward to any streaming consumers (e.g., RiskManagementService)
            for consumer in self.streaming_consumers:
                try:
                    consumer.on_trade(symbol, price, size, timestamp, conditions, **kwargs)
                except Exception as e:
                    self.logger.error(f"Error forwarding trade to streaming consumer {consumer.__class__.__name__}: {e}")
        # If not relevant, the method simply returns. The data is dropped silently to avoid log spam.

    def on_quote(self, symbol: str, bid: float, ask: float, bid_size: int, ask_size: int, timestamp: float, conditions: Optional[List[str]] = None, **kwargs):
        """
        Receives raw quote data, filters it, and forwards if relevant.
        """
        symbol_upper = symbol.upper()

        # The single, efficient check.
        if self.active_symbols_service.is_relevant_for_publishing(symbol_upper):
            # If relevant, pass it on to the next stage (PriceRepository).
            self.price_repository.on_quote(symbol, bid, ask, bid_size, ask_size, timestamp, conditions, **kwargs)
            
            # Also forward to any streaming consumers (e.g., RiskManagementService)
            for consumer in self.streaming_consumers:
                try:
                    consumer.on_quote(symbol, bid, ask, bid_size, ask_size, timestamp, conditions, **kwargs)
                except Exception as e:
                    self.logger.error(f"Error forwarding quote to streaming consumer {consumer.__class__.__name__}: {e}")
        # If not relevant, the method simply returns. The data is dropped silently.