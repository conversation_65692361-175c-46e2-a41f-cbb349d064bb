@echo off
title TESTRADE Babysitter Service
color 0A

echo.
echo =========================================
echo    TESTRADE Babysitter Service
echo =========================================
echo.

:: Check if virtual environment exists
if not exist ".venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found at .venv\Scripts\python.exe
    echo Please make sure you have created a virtual environment in the .venv folder
    pause
    exit /b 1
)

:: Check if babysitter service file exists
if not exist "core\babysitter_service.py" (
    echo ERROR: Babysitter service not found at core\babysitter_service.py
    echo Please make sure you are running this from the TESTRADE root directory
    pause
    exit /b 1
)

echo Starting Babysitter Service...
echo Using Python: .venv\Scripts\python.exe
echo Service file: core\babysitter_service.py
echo.
echo Press Ctrl+C to stop the service
echo.

:: Start the babysitter service using the virtual environment Python
".venv\Scripts\python.exe" "core\babysitter_service.py"

:: If the service exits, show message
echo.
echo Babysitter service has stopped.
pause