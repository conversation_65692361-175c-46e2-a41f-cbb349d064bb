{"last_updated": "2025-07-07", "updated_by": "<PERSON> Assistant", "version": "2.2.0", "changelog": {"2025-07-07": "NANOSECOND PRECISION OCR PIPELINE: Updated OCR pipeline to 4-stage architecture with nanosecond timing. Added IMAGE_GRABS (T0) and RAW_OCR_DATA (T2) streams. Updated OCR_RAW (T3) and OCR_CLEANED (T4) with precise timing. Removed legacy testrade:ocr-raw references. All OCR streams now track T0->T4 pipeline latency with correlation ID consistency.", "2025-07-03": "COMPLETE VERIFICATION: All streams now verified active with bulletproof IPC. Moved ROI_UPDATES, CONFIG_CHANGES, ENRICHED_POSITION_UPDATES, TRADE_LIFECYCLE_EVENTS, TRADING_STATE_CHANGES from inactive/config-only to verified_active. Fixed MAF_DECISIONS TANK mode support. All streams now have non-blocking Redis publishing.", "2025-01-03": "Added SIGNAL_DECISIONS and EXECUTION_DECISIONS streams. Removed outdated ORDER_REQUESTS and VALIDATED_ORDERS. Added CONFIG_CHANGES stream. Updated categories to reflect new bulletproof IPC implementation.", "previous": "Initial stream mappings for IntelliSense developer onboarding"}, "status_notes": {"verified_active": ["IMAGE_GRABS", "RAW_OCR_DATA", "OCR_RAW", "OCR_CLEANED", "ORDER_FILLS", "ORDER_STATUS", "ORDER_REJECTIONS", "POSITION_UPDATES", "ACCOUNT_UPDATES", "RISK_ACTIONS", "BROKER_RAW_MESSAGES", "BROKER_ERRORS", "SIGNAL_DECISIONS", "EXECUTION_DECISIONS", "FILTERED_MARKET_TRADES", "FILTERED_MARKET_QUOTES", "MAF_DECISIONS", "ROI_UPDATES", "CONFIG_CHANGES", "ENRICHED_POSITION_UPDATES", "TRADE_LIFECYCLE_EVENTS", "TRADING_STATE_CHANGES"], "potentially_inactive": [], "configuration_only": [], "infrastructure": ["CORE_HEALTH", "BABYSITTER_HEALTH", "REDIS_INSTANCE_HEALTH", "GUI_COMMANDS", "GUI_RESPONSES"]}, "stream_mappings": {"IMAGE_GRABS": {"stream_name": "testrade:image-grabs", "source_component": "ApplicationCore", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "core/application_core.py:1749", "event_type": "TESTRADE_RAW_IMAGE_GRAB", "trigger": "Screen capture for OCR processing", "data_content": "Raw screen images for visual replay", "intellisense_priority": "MEDIUM - Visual debugging"}, "RAW_OCR_DATA": {"stream_name": "testrade:raw-ocr-data", "source_component": "ApplicationCore.ProcessedImageListener", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "core/application_core.py:1190", "event_type": "TESTRADE_PROCESSED_IMAGE_DATA", "trigger": "T2 - 17-step image processing complete", "data_content": "Base64 encoded processed images with T0-T2 timestamps and correlation ID", "intellisense_priority": "MEDIUM - Image processing verification"}, "OCR_RAW": {"stream_name": "testrade:raw-ocr-events", "source_component": "ApplicationCore.OCRPipeListener", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "core/application_core.py:1216", "event_type": "TESTRADE_RAW_OCR_DATA", "trigger": "T3 - Tesseract OCR text extraction complete", "data_content": "Raw OCR text with all nanosecond timestamps T0-T3 and correlation ID", "intellisense_priority": "HIGH - Core OCR pipeline data"}, "OCR_CLEANED": {"stream_name": "testrade:cleaned-ocr-snapshots", "source_component": "OCRDataConditioningService", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/ocr/python_ocr_data_conditioner.py:1320", "event_type": "TESTRADE_CLEANED_OCR_DATA", "trigger": "T4 - Text conditioning and trade detection complete", "data_content": "Cleaned OCR snapshots with nanosecond T0-T4 pipeline timing and trading signals", "intellisense_priority": "CRITICAL - Final trading signals with complete pipeline latency"}, "SIGNAL_DECISIONS": {"stream_name": "testrade:signal-decisions", "source_component": "OCRScalpingSignalOrchestratorService", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/trade_management/ocr_scalping_signal_orchestrator_service.py:1139", "event_type": "TESTRADE_SIGNAL_DECISION, TESTRADE_SIGNAL_SUPPRESSION", "trigger": "Signal generation decisions (both generated and suppressed)", "data_content": "Signal decisions with correlation tracking - includes order generation, no-change detection, duplicate suppression", "intellisense_priority": "CRITICAL - Signal analysis and intelligence"}, "EXECUTION_DECISIONS": {"stream_name": "testrade:execution-decisions", "source_component": "TradeExecutor", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/trade_management/trade_executor.py:944", "event_type": "TESTRADE_EXECUTION_DECISION, TESTRADE_EXECUTION_REJECTION", "trigger": "Trade execution decisions (both sent to broker and rejected)", "data_content": "Execution decisions with order parameters - includes broker submissions, trading disabled rejections, risk failures", "intellisense_priority": "CRITICAL - Execution analysis and intelligence"}, "ORDER_FILLS": {"stream_name": "testrade:order-fills", "source_component": "OrderRepository", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/order_management/order_repository.py:4028", "event_type": "TESTRADE_ORDER_FILL", "trigger": "Broker fill confirmation received", "data_content": "Execution confirmations with fill price/quantity", "intellisense_priority": "CRITICAL - Execution confirmation"}, "ORDER_STATUS": {"stream_name": "testrade:order-status", "source_component": "OrderRepository", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/order_management/order_repository.py:multiple", "event_type": "TESTRADE_ORDER_STATUS_UPDATE", "trigger": "Order state changes (submitted, filled, rejected)", "data_content": "Order status updates throughout lifecycle", "intellisense_priority": "HIGH - Order tracking"}, "ORDER_REJECTIONS": {"stream_name": "testrade:order-rejections", "source_component": "OrderRepository", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/order_management/order_repository.py:multiple", "event_type": "TESTRADE_ORDER_REJECTION", "trigger": "Broker rejection received", "data_content": "Rejection reasons and failed order details", "intellisense_priority": "HIGH - Error analysis"}, "POSITION_UPDATES": {"stream_name": "testrade:position-updates", "source_component": "PositionManager", "publishing_method": "babysitter_ipc_client.send_trading_data()", "file_location": "modules/trade_management/position_manager.py:379,831,884", "event_type": "TESTRADE_POSITION_UPDATE", "trigger": "Position changes from fills or broker updates", "data_content": "Current positions by symbol with P&L", "intellisense_priority": "CRITICAL - Portfolio state"}, "ENRICHED_POSITION_UPDATES": {"stream_name": "testrade:enriched-position-updates", "source_component": "PositionEnrichmentService", "publishing_method": "babysitter_ipc_client.send_trading_data()", "file_location": "modules/trade_management/position_enrichment_service.py:250", "event_type": "TESTRADE_ENRICHED_POSITION_UPDATE", "trigger": "Position data enriched with market data", "data_content": "Positions with current market prices and P&L", "intellisense_priority": "HIGH - Enhanced portfolio analytics"}, "ACCOUNT_UPDATES": {"stream_name": "testrade:account-updates", "source_component": "RiskManagementService", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/risk_management/risk_service.py:multiple", "event_type": "TESTRADE_ACCOUNT_UPDATE", "trigger": "Account balance and P&L updates", "data_content": "Live account equity, buying power, realized/unrealized P&L, risk status", "intellisense_priority": "HIGH - Account monitoring"}, "CORE_HEALTH": {"stream_name": "testrade:health:core", "source_component": "ApplicationCore", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "core/application_core.py:427", "event_type": "TESTRADE_CORE_HEALTH", "trigger": "Periodic health monitoring", "data_content": "Core system health metrics and status", "intellisense_priority": "LOW - System monitoring"}, "BABYSITTER_HEALTH": {"stream_name": "testrade:health:babysitter", "source_component": "BabysitterService", "publishing_method": "redis_client.xadd()", "file_location": "core/babysitter_service.py:multiple", "event_type": "TESTRADE_BABYSITTER_HEALTH", "trigger": "Babysitter service health monitoring", "data_content": "IPC and Redis publishing health status", "intellisense_priority": "LOW - Infrastructure monitoring"}, "GUI_COMMANDS": {"stream_name": "testrade:commands:from_gui", "source_component": "GUIBackend", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "gui/gui_backend.py:2225", "event_type": "TESTRADE_GUI_COMMAND", "trigger": "User actions from GUI interface", "data_content": "Commands like start/stop trading, config changes", "intellisense_priority": "MEDIUM - User interaction tracking"}, "GUI_RESPONSES": {"stream_name": "testrade:responses:to_gui", "source_component": "ApplicationCore", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "core/application_core.py:multiple", "event_type": "TESTRADE_GUI_RESPONSE", "trigger": "Responses to GUI commands", "data_content": "Command execution results and status updates", "intellisense_priority": "LOW - GUI feedback"}, "RISK_ACTIONS": {"stream_name": "testrade:risk-actions", "source_component": "RiskManagementService", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/risk_management/risk_service.py:1195,1218,1252", "event_type": "TESTRADE_RISK_TRADING_STOPPED, TESTRADE_RISK_REJECTION", "trigger": "All risk suppressions - quantity limits, position limits, spread conditions, market halts", "data_content": "Risk rejections (quantity/position/spread), risk stops, meltdown alerts, market condition warnings", "intellisense_priority": "HIGH - Risk management"}, "ROI_UPDATES": {"stream_name": "testrade:roi-updates", "source_component": "ROIService", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/roi/roi_service.py:100", "event_type": "TESTRADE_ROI_UPDATE", "trigger": "ROI calculations and updates", "data_content": "Return on investment metrics", "intellisense_priority": "MEDIUM - Performance analytics"}, "BROKER_RAW_MESSAGES": {"stream_name": "testrade:broker-raw-messages", "source_component": "LightspeedBroker", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/broker_bridge/lightspeed_broker.py:285", "event_type": "TESTRADE_BROKER_RAW_MESSAGE", "trigger": "Raw C++ broker message received", "data_content": "Unprocessed broker communications with metadata", "intellisense_priority": "HIGH - Broker communication debugging"}, "BROKER_ERRORS": {"stream_name": "testrade:broker-errors", "source_component": "LightspeedBroker", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/broker_bridge/lightspeed_broker.py:325", "event_type": "TESTRADE_BROKER_ERROR", "trigger": "Broker connection or communication error", "data_content": "Error messages, details, and criticality flags", "intellisense_priority": "CRITICAL - System health monitoring"}, "TRADE_LIFECYCLE_EVENTS": {"stream_name": "testrade:trade-lifecycle-events", "source_component": "TradeLifecycleManagerService", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/trade_management/trade_lifecycle_manager_service.py:73", "event_type": "TESTRADE_TRADE_LIFECYCLE_EVENT", "trigger": "Trade creation, state transitions, closure decisions", "data_content": "Trade ID, symbol, lifecycle state, correlation ID, origin", "intellisense_priority": "CRITICAL - End-to-end trade tracking"}, "MAF_DECISIONS": {"stream_name": "testrade:maf-decisions", "source_component": "MasterActionFilterService", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/trade_management/master_action_filter_service.py:455", "event_type": "TESTRADE_MAF_SIGNAL_SUPPRESSED, TESTRADE_MAF_SIGNAL_OVERRIDE", "trigger": "MAF settling period suppression or override conditions", "data_content": "Symbol, action (ADD/REDUCE), decision (SUPPRESSED/OVERRIDE), settling details, thresholds", "intellisense_priority": "CRITICAL - Signal filtering analysis"}, "TRADING_STATE_CHANGES": {"stream_name": "testrade:trading-state-changes", "source_component": "TradeManagerService", "publishing_method": "_queue_ipc_publish() via async worker", "file_location": "modules/trade_management/trade_manager_service.py:265,934", "event_type": "TESTRADE_TRADING_STATE_CHANGE", "trigger": "set_trading_enabled() calls, force_close_all(), meltdown conditions", "data_content": "state_type (TRADING_ENABLED), enabled bool, changed_by, previous_state, timestamp", "intellisense_priority": "HIGH - Operational state monitoring"}, "REDIS_INSTANCE_HEALTH": {"stream_name": "testrade:health:redis_instance", "source_component": "BabysitterService", "publishing_method": "redis_client.xadd()", "file_location": "core/babysitter_service.py:multiple", "event_type": "TESTRADE_REDIS_INSTANCE_HEALTH", "trigger": "Redis instance health monitoring", "data_content": "Redis connection status and performance metrics", "intellisense_priority": "LOW - Infrastructure monitoring"}, "FILTERED_MARKET_TRADES": {"stream_name": "testrade:filtered:market-trades", "source_component": "FilteredMarketDataPublisher", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/market_data_publishing/filtered_market_data_publisher.py:multiple", "event_type": "TESTRADE_MARKET_TRADE_FILTERED", "trigger": "Alpaca WebSocket → PriceFetchingService → MarketDataFilterService → PriceRepository", "data_content": "Live trade data filtered for active symbols with correlation tracking", "intellisense_priority": "HIGH - Live filtered market data"}, "FILTERED_MARKET_QUOTES": {"stream_name": "testrade:filtered:market-quotes", "source_component": "FilteredMarketDataPublisher", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "modules/market_data_publishing/filtered_market_data_publisher.py:multiple", "event_type": "TESTRADE_MARKET_QUOTE_FILTERED", "trigger": "Alpaca WebSocket → PriceFetchingService → MarketDataFilterService → PriceRepository", "data_content": "Live quote data filtered for active symbols with correlation tracking", "intellisense_priority": "HIGH - Live filtered market data"}, "CONFIG_CHANGES": {"stream_name": "testrade:config-changes", "source_component": "GlobalConfig", "publishing_method": "babysitter_ipc_client.send_data()", "file_location": "utils/global_config.py:multiple", "event_type": "TESTRADE_CONFIG_CHANGE", "trigger": "Configuration updates and changes", "data_content": "Configuration changes with before/after values", "intellisense_priority": "MEDIUM - Configuration tracking"}}, "intellisense_categories": {"CRITICAL_TRADING_PATH": ["SIGNAL_DECISIONS", "MAF_DECISIONS", "EXECUTION_DECISIONS", "ORDER_FILLS", "POSITION_UPDATES", "TRADE_LIFECYCLE_EVENTS", "BROKER_ERRORS"], "HIGH_PRIORITY_DATA": ["OCR_RAW", "OCR_CLEANED", "ORDER_STATUS", "ORDER_REJECTIONS", "ACCOUNT_UPDATES", "FILTERED_MARKET_TRADES", "FILTERED_MARKET_QUOTES", "ENRICHED_POSITION_UPDATES", "RISK_ACTIONS", "BROKER_RAW_MESSAGES", "TRADING_STATE_CHANGES", "CONFIG_CHANGES"], "MEDIUM_PRIORITY_DATA": ["GUI_COMMANDS", "ROI_UPDATES", "IMAGE_GRABS", "RAW_OCR_DATA"], "LOW_PRIORITY_MONITORING": ["CORE_HEALTH", "BABYSITTER_HEALTH", "GUI_RESPONSES", "REDIS_INSTANCE_HEALTH"]}, "gui_field_mappings": {"actual_position_fields": {"symbol": "Symbol", "quantity": "Quantity", "average_price": "Average Price", "avg_price": "Avg Price (Alt)", "current_price": "Current Price", "last_price": "Last Price", "unrealized_pnl": "Unrealized P&L", "realized_pnl": "Realized P&L", "realized_pnl_session": "Session P&L", "bid_price": "<PERSON><PERSON>", "ask_price": "Ask Price", "bid": "Bid (Alt)", "ask": "Ask (Alt)", "market_value": "Market Value", "total_volume": "Total Volume", "volume": "Volume", "day_volume": "Day Volume", "total_fills_count": "Fill Count", "commission_total": "Total Commission", "commission": "Commission", "signal_to_fill_latency_ms": "Signal Latency", "order_to_fill_latency_ms": "Order Latency", "fill_duration_ms": "Fill Duration", "position_opened_timestamp": "Position Opened Time", "last_update_timestamp": "Last Update Time", "last_fill_timestamp": "Last Fill Time", "timestamp": "Timestamp", "trade_id": "Trade ID", "parent_trade_id": "Parent Trade ID", "order_id": "Order ID", "local_order_id": "Local Order ID", "is_parent": "Is Parent", "is_child": "Is Child", "status": "Status", "strategy": "Strategy", "is_open": "Is Open", "open": "Open", "action": "Action", "side": "Side", "price": "Price", "pnl_per_share": "P&L Per Share", "causing_correlation_id": "Correlation ID", "correlation_id": "Correlation ID (Alt)", "update_source_trigger": "Update Source", "session_buy_quantity": "Session Buy Qty", "session_sell_quantity": "Session <PERSON><PERSON>", "session_buy_value": "Session Buy Value", "session_sell_value": "Session Sell Value", "market_data_available": "Market Data Available", "hierarchy_data_available": "Hierarchy Available", "position_uuid": "Position UUID", "master_correlation_id": "Master Correlation ID", "lcm_parent_trade_id": "LCM Parent Trade ID", "total_shares": "Total Shares", "total_avg_price": "Total Avg Price", "overall_realized_pnl": "Overall Realized P&L", "startup_shares": "Startup Shares", "startup_avg_price": "Startup Avg Price", "last_sync_details": "Last Sync Details", "last_update_source": "Last Update Source", "trading_status": "Trading Status", "luld_high": "LULD High", "luld_low": "LULD Low", "last_broker_fingerprint": "Last Broker Fingerprint", "retry_count": "Retry Count", "cooldown_until": "Cooldown Until", "potential_avg_price": "Potential Avg Price", "session_system_fills_ledger": "System Fills Ledger", "lifecycle_state": "Lifecycle State", "shares": "Shares", "start_time": "Start Time", "end_time": "End Time", "local_orders": "Local Orders", "fills": "Fills", "origin": "Origin", "comment": "Comment", "child_trades": "Child Trades", "ocr_confidence": "OCR Confidence", "master_trader_snapshot": "Master Trader S<PERSON><PERSON>", "cost_basis": "<PERSON><PERSON>", "strategy_hint": "Strategy Hint"}, "order_fields": {"order_id": "Order ID", "symbol": "Symbol", "side": "Buy/Sell", "quantity": "Quantity", "price": "Price", "order_type": "Order Type", "time_in_force": "Time in Force", "status": "Order Status", "filled_quantity": "Filled <PERSON><PERSON>", "remaining_quantity": "Remaining <PERSON>ty", "average_fill_price": "Avg <PERSON><PERSON>", "commission": "Commission", "timestamp": "Order Time", "fill_timestamp": "Fill Time", "rejection_reason": "Rejection Reason"}, "market_data_fields": {"symbol": "Symbol", "bid": "<PERSON><PERSON>", "ask": "Ask Price", "last": "Last Price", "volume": "Volume", "timestamp": "Quote Time", "bid_size": "<PERSON><PERSON>", "ask_size": "<PERSON>"}}, "hash_stability_analysis": {"stable_fields": {"description": "Fields that rarely change and are good for hash calculation", "fields": ["symbol", "trade_id", "parent_trade_id", "quantity", "average_price", "total_avg_price", "is_open", "open", "is_parent", "is_child", "strategy", "position_uuid", "lcm_parent_trade_id", "total_shares", "lifecycle_state", "origin", "action", "side"]}, "volatile_fields": {"description": "Fields that change frequently and cause hash instability - EXCLUDE from hash", "fields": ["unrealized_pnl", "last_price", "current_price", "bid_price", "ask_price", "bid", "ask", "market_value", "last_update_timestamp", "last_fill_timestamp", "timestamp", "position_opened_timestamp", "total_volume", "volume", "day_volume", "signal_to_fill_latency_ms", "order_to_fill_latency_ms", "fill_duration_ms", "pnl_per_share", "market_data_available", "hierarchy_data_available", "trading_status", "retry_count", "cooldown_until", "last_broker_fingerprint", "ocr_confidence"]}, "recommended_hash_fields": ["symbol", "trade_id", "quantity", "average_price", "is_open", "strategy", "action"], "improved_hash_function": "function createStableTradesHash(trades) { if (!trades || !Array.isArray(trades)) return 'empty'; return trades.map(trade => `${trade.symbol}-${trade.trade_id}-${trade.quantity}-${trade.average_price}-${trade.is_open}-${trade.strategy}`).sort().join('|'); }", "current_problem": "The existing hash uses 'unrealized_pnl' and 'status' which change frequently with market data updates", "solution": "Replace volatile fields with stable identity fields that only change when position structure changes"}}