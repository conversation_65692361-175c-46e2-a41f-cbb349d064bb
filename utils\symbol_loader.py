# utils/symbol_loader.py

import csv
import re
import logging
import os
import time
import threading

# Lazy-loaded symbol sets (private)
_tradeable_symbols = None
_non_tradeable_symbols = None
_symbols_loaded = False
_loader_lock = threading.Lock()

# Legacy tracking variables (kept for compatibility)
_last_loaded_path = None
_last_loaded_timestamp = 0

logger = logging.getLogger(__name__)

# New: Create separate logger instance instead of reusing
symbol_loader_logger = logging.getLogger("symbol_loader")
symbol_loader_logger.propagate = True

def _load_symbols_if_needed():
    """
    Internal function to load symbols lazily with thread safety.
    Only loads symbols once, using double-checked locking pattern.
    """
    global _tradeable_symbols, _non_tradeable_symbols, _symbols_loaded

    if not _symbols_loaded:  # First check without lock
        with _loader_lock:
            if not _symbols_loaded:  # Double-check after acquiring lock
                symbol_loader_logger.debug("Loading symbols lazily from CSV...")

                # Initialize empty sets
                loaded_tradeable = set()
                loaded_non_tradeable = set()

                try:
                    # Calculate path to CSV file
                    csv_file_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'tradeable_symbols.csv')
                    abs_csv_path = os.path.abspath(csv_file_path)

                    if os.path.exists(abs_csv_path):
                        symbol_loader_logger.debug(f"Loading symbols from: {abs_csv_path}")

                        # Read and parse CSV file
                        with open(abs_csv_path, 'r', encoding='utf-8-sig') as csvfile:
                            # Auto-detect CSV dialect
                            try:
                                dialect = csv.Sniffer().sniff(csvfile.read(1024))
                                csvfile.seek(0)
                            except:
                                dialect = csv.excel

                            reader = csv.DictReader(csvfile, dialect=dialect)
                            headers = [h.strip() for h in reader.fieldnames] if reader.fieldnames else []

                            # Case-insensitive header mapping
                            header_map = {col.lower(): col for col in headers}

                            if 'symbol' in header_map and 'tradeable' in header_map:
                                symbol_col = header_map['symbol']
                                tradeable_col = header_map['tradeable']

                                for row in reader:
                                    raw_symbol = row.get(symbol_col, '').strip().upper()
                                    if raw_symbol:
                                        symbol = re.sub(r'[^A-Z0-9]', '', raw_symbol)
                                        if symbol:
                                            raw_value = str(row.get(tradeable_col, '')).strip().lower()
                                            is_tradeable = raw_value in {'y', 'yes', 'true', '1'}

                                            if is_tradeable:
                                                loaded_tradeable.add(symbol)
                                            else:
                                                loaded_non_tradeable.add(symbol)

                        # Update global variables
                        _tradeable_symbols = loaded_tradeable
                        _non_tradeable_symbols = loaded_non_tradeable
                        _symbols_loaded = True

                        symbol_loader_logger.info(f"Symbols loaded lazily: {len(_tradeable_symbols)} tradeable, {len(_non_tradeable_symbols)} non-tradeable")

                    else:
                        symbol_loader_logger.error(f"Symbol CSV file not found: {abs_csv_path}")
                        _tradeable_symbols = set()
                        _non_tradeable_symbols = set()
                        _symbols_loaded = True  # Mark as loaded to prevent re-attempts

                except Exception as e:
                    symbol_loader_logger.exception(f"Error loading symbols lazily: {e}")
                    _tradeable_symbols = set()
                    _non_tradeable_symbols = set()
                    _symbols_loaded = True  # Mark as loaded to prevent re-attempts

def get_tradeable_symbols() -> set:
    """
    Get the set of tradeable symbols, loading them lazily if needed.

    Returns:
        Set of tradeable symbol strings
    """
    _load_symbols_if_needed()
    return _tradeable_symbols if _tradeable_symbols is not None else set()

def get_non_tradeable_symbols() -> set:
    """
    Get the set of non-tradeable symbols, loading them lazily if needed.

    Returns:
        Set of non-tradeable symbol strings
    """
    _load_symbols_if_needed()
    return _non_tradeable_symbols if _non_tradeable_symbols is not None else set()

# Legacy compatibility: create a class to provide module-level variables with lazy loading
class _LazySymbolSet:
    """Helper class to provide lazy-loaded symbol sets as module-level variables."""

    @property
    def tradeable_symbols(self):
        return get_tradeable_symbols()

    @property
    def non_tradeable_symbols(self):
        return get_non_tradeable_symbols()

# Create instance for module-level access
_lazy_symbols = _LazySymbolSet()
tradeable_symbols = _lazy_symbols.tradeable_symbols
non_tradeable_symbols = _lazy_symbols.non_tradeable_symbols

def load_symbols_from_csv(csv_path: str, fix_bom: bool = True, force_reload: bool = False) -> None:
    """
    Legacy function to load symbols from a CSV file.
    Now delegates to the lazy loading system but forces a reload.

    Args:
        csv_path (str): Path to the CSV file (currently ignored, uses default path).
        fix_bom (bool): If True (default), use 'utf-8-sig' to remove BOM.
        force_reload (bool): If True, reload symbols even if already loaded.
    """
    global _symbols_loaded, _last_loaded_path, _last_loaded_timestamp

    symbol_loader_logger.debug(f"ENTERED load_symbols_from_csv: Path='{csv_path}', force_reload={force_reload}")

    if force_reload:
        # Reset the loaded flag to force a reload
        with _loader_lock:
            _symbols_loaded = False

    # Trigger lazy loading
    _load_symbols_if_needed()

    # Update legacy tracking variables for compatibility
    _last_loaded_path = csv_path
    try:
        abs_path = os.path.abspath(csv_path)
        if os.path.exists(abs_path):
            _last_loaded_timestamp = os.path.getmtime(abs_path)
    except OSError:
        _last_loaded_timestamp = 0
