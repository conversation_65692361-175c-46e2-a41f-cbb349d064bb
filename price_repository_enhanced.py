# Enhanced PriceRepository with JIT Price Oracle pattern
# This implementation will be integrated into the existing PriceRepository

import time
import logging
from threading import RLock
from typing import Dict, Optional
from data_models.pricing import PriceData, StalePriceError

logger = logging.getLogger(__name__)

class JITPriceOracle:
    """
    Mixin class that provides the clean JIT Price Oracle interface
    on top of the existing robust PriceRepository infrastructure.
    
    This leverages the existing worker-based fallback mechanism while
    providing a clean, high-precision interface for high-speed trading.
    """
    
    def __init__(self):
        # Enhanced cache using PriceData objects with nanosecond precision
        self._jit_cache_lock = RLock()
        self._jit_price_cache: Dict[str, PriceData] = {}
    
    def get_latest_price_jit(self, symbol: str, max_age_ns: int, critical: bool = False) -> PriceData:
        """
        JIT Price Oracle: Gets the most recent price with nanosecond precision.
        
        This is the primary method for all price consumers in high-speed trading.
        Uses the existing robust worker-based fallback infrastructure.
        
        Args:
            symbol: The stock symbol
            max_age_ns: Maximum acceptable age in nanoseconds (e.g., 50_000_000 = 50ms)
            critical: If True, blocks until fresh data or raises StalePriceError
                     If False, may return stale data with clear age indication
        
        Returns:
            PriceData object with nanosecond precision
            
        Raises:
            StalePriceError: If no price available or critical=True and data too stale
        """
        symbol_upper = symbol.upper()
        now_ns = time.perf_counter_ns()
        
        # Step 1: Check JIT cache for fresh data
        with self._jit_cache_lock:
            cached_data = self._jit_price_cache.get(symbol_upper)
        
        if cached_data:
            age_ns = now_ns - cached_data.timestamp_ns
            
            # If data is fresh enough, return immediately (fastest path)
            if age_ns <= max_age_ns:
                logger.debug(f"JIT: Fresh price for {symbol_upper} (age: {age_ns/1e6:.1f}ms)")
                return cached_data
            
            # Data exists but is stale
            logger.debug(f"JIT: Stale cache for {symbol_upper} (age: {age_ns/1e6:.1f}ms > limit: {max_age_ns/1e6:.1f}ms)")
            
            # For non-critical requests, return stale data while triggering background refresh
            if not critical:
                self._trigger_background_refresh(symbol_upper)
                logger.warning(f"JIT: Returning STALE price for non-critical request: {symbol_upper}")
                return cached_data
        else:
            logger.warning(f"JIT: No cache for {symbol_upper}, triggering fetch")
        
        # Step 2: Critical request or no data - use robust fallback mechanism
        return self._get_fresh_price_blocking(symbol_upper, max_age_ns)
    
    def _trigger_background_refresh(self, symbol: str):
        """
        Trigger non-blocking background refresh using existing infrastructure.
        This leverages the existing worker queue system.
        """
        try:
            # Use existing non-blocking API fallback
            # This calls get_latest_price with wait_for_fallback=False
            self.get_latest_price(symbol, allow_api_fallback=True, wait_for_fallback=False)
        except Exception as e:
            logger.debug(f"Background refresh failed for {symbol}: {e}")
    
    def _get_fresh_price_blocking(self, symbol: str, max_age_ns: int) -> PriceData:
        """
        Get fresh price using blocking fallback mechanism.
        Leverages existing robust worker-based API fallback.
        """
        try:
            # Use existing robust blocking fallback
            price = self.get_latest_price(symbol, allow_api_fallback=True, wait_for_fallback=True)
            
            if price is None or price <= 0:
                raise StalePriceError(f"API returned invalid price for {symbol}: {price}")
            
            # Convert to PriceData with current timestamp
            now_ns = time.perf_counter_ns()
            price_data = PriceData(
                symbol=symbol,
                price=float(price),
                timestamp_ns=now_ns,
                source="api_fallback_blocking"
            )
            
            # Update JIT cache
            with self._jit_cache_lock:
                self._jit_price_cache[symbol] = price_data
            
            logger.info(f"JIT: Fresh price obtained for {symbol}: ${price:.4f}")
            return price_data
            
        except Exception as e:
            raise StalePriceError(f"All sources failed for {symbol}: {e}", symbol=symbol)
    
    def update_jit_cache_from_stream(self, symbol: str, price: float, source: str):
        """
        Update JIT cache from streaming data with nanosecond precision.
        Called from on_trade and on_quote methods.
        """
        if price is None or price <= 0:
            return
            
        symbol_upper = symbol.upper()
        now_ns = time.perf_counter_ns()
        
        price_data = PriceData(
            symbol=symbol_upper,
            price=float(price),
            timestamp_ns=now_ns,
            source=source
        )
        
        with self._jit_cache_lock:
            self._jit_price_cache[symbol_upper] = price_data
        
        logger.debug(f"JIT: Updated {symbol_upper} from {source}: ${price:.4f}")


# Usage example showing how to integrate this into existing PriceRepository:
"""
class PriceRepository(IMarketDataReceiver, IPriceProvider, JITPriceOracle):
    def __init__(self, ...):
        super().__init__()  # Initialize existing infrastructure
        JITPriceOracle.__init__(self)  # Initialize JIT oracle
        # ... existing init code ...
    
    def on_trade(self, symbol: str, price: float, size: int, timestamp: float, **kwargs):
        # ... existing on_trade logic ...
        
        # Update JIT cache
        self.update_jit_cache_from_stream(symbol, price, "stream_trade")
    
    def on_quote(self, symbol: str, bid: float, ask: float, **kwargs):
        # ... existing on_quote logic ...
        
        # Update JIT cache with ask price (better for entry signals)
        self.update_jit_cache_from_stream(symbol, ask, "stream_ask")


# Consumer usage:
try:
    # For critical operations (blocking until fresh)
    price_data = price_repo.get_latest_price_jit("AAPL", max_age_ns=50_000_000, critical=True)
    
    # For non-critical operations (may return stale data)
    price_data = price_repo.get_latest_price_jit("AAPL", max_age_ns=100_000_000, critical=False)
    
    logger.info(f"Price: ${price_data.price:.4f}, Age: {price_data.age_ms:.1f}ms, Source: {price_data.source}")
    
except StalePriceError as e:
    logger.error(f"Price unavailable: {e}")
    # Handle failure case explicitly
"""