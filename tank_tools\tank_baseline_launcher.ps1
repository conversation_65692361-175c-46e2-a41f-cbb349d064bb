# tank_baseline_launcher.ps1 - Comprehensive baseline comparison launcher
param(
    [string]$Mode = "single",  # single, multi, create-single, create-multi, analyze
    [string]$ProcessName = "ApplicationCore",
    [string]$ProcessPattern = "python",
    [int]$MonitorMinutes = 60,
    [switch]$ShowHelp = $false
)

if ($ShowHelp) {
    Write-Host "🔍 TANK Baseline Comparison Suite" -ForegroundColor Green
    Write-Host "=" * 60
    Write-Host ""
    Write-Host "MODES:" -ForegroundColor Yellow
    Write-Host "  single        - Single process baseline comparison" -ForegroundColor White
    Write-Host "  multi         - Multi-process baseline comparison" -ForegroundColor White
    Write-Host "  create-single - Create new single process baseline" -ForegroundColor White
    Write-Host "  create-multi  - Create new multi-process baseline" -ForegroundColor White
    Write-Host "  analyze       - Analyze baseline comparison data" -ForegroundColor White
    Write-Host ""
    Write-Host "EXAMPLES:" -ForegroundColor Cyan
    Write-Host "  .\tank_baseline_launcher.ps1 -Mode single" -ForegroundColor Gray
    Write-Host "  .\tank_baseline_launcher.ps1 -Mode multi" -ForegroundColor Gray
    Write-Host "  .\tank_baseline_launcher.ps1 -Mode create-single -ProcessName python" -ForegroundColor Gray
    Write-Host "  .\tank_baseline_launcher.ps1 -Mode analyze" -ForegroundColor Gray
    exit 0
}

Write-Host "🚀 TANK Baseline Comparison Suite Launcher" -ForegroundColor Green
Write-Host "=" * 70

# Function to check for processes
function Test-ProcessAvailability {
    param([string]$ProcessName, [string]$ProcessPattern, [string]$Mode)
    
    if ($Mode -like "*single*") {
        $processes = Get-Process $ProcessName -ErrorAction SilentlyContinue
        if ($processes.Count -eq 0) {
            Write-Host "❌ No process found with name '$ProcessName'!" -ForegroundColor Red
            return $false
        }
        Write-Host "✅ Found process: $ProcessName (PID: $($processes[0].Id))" -ForegroundColor Green
    } else {
        $processes = Get-Process $ProcessPattern -ErrorAction SilentlyContinue
        if ($processes.Count -eq 0) {
            Write-Host "❌ No processes found matching pattern '$ProcessPattern'!" -ForegroundColor Red
            return $false
        }
        Write-Host "✅ Found $($processes.Count) processes matching '$ProcessPattern'" -ForegroundColor Green
    }
    return $true
}

switch ($Mode.ToLower()) {
    "single" {
        Write-Host "📊 Starting Single Process Baseline Comparison..." -ForegroundColor Cyan
        Write-Host "Process: $ProcessName | Duration: $MonitorMinutes minutes" -ForegroundColor Yellow
        Write-Host ""
        
        if (-not (Test-ProcessAvailability -ProcessName $ProcessName -ProcessPattern $ProcessPattern -Mode $Mode)) {
            Write-Host ""
            Write-Host "Available processes:" -ForegroundColor Yellow
            Get-Process | Where-Object { 
                $_.ProcessName -like "*python*" -or 
                $_.ProcessName -like "*application*" -or 
                $_.ProcessName -like "*core*"
            } | Format-Table ProcessName, Id, @{Name='MemoryMB';Expression={[math]::Round($_.WorkingSet64/1MB,2)}} -AutoSize
            exit 1
        }
        
        Write-Host "Starting single process baseline comparison..." -ForegroundColor Cyan
        & .\tank_baseline_compare.ps1 -ProcessName $ProcessName -MonitorMinutes $MonitorMinutes -EnableCSVLogging -ShowDetailedStats
    }
    
    "multi" {
        Write-Host "🌐 Starting Multi-Process Baseline Comparison..." -ForegroundColor Cyan
        Write-Host "Process Pattern: $ProcessPattern | Duration: $MonitorMinutes minutes" -ForegroundColor Yellow
        Write-Host ""
        
        if (-not (Test-ProcessAvailability -ProcessName $ProcessName -ProcessPattern $ProcessPattern -Mode $Mode)) {
            Write-Host ""
            Write-Host "Available processes:" -ForegroundColor Yellow
            Get-Process | Where-Object { 
                $_.ProcessName -like "*python*" -or 
                $_.ProcessName -like "*application*" -or 
                $_.ProcessName -like "*core*"
            } | Format-Table ProcessName, Id, @{Name='MemoryMB';Expression={[math]::Round($_.WorkingSet64/1MB,2)}} -AutoSize
            exit 1
        }
        
        Write-Host "Starting multi-process baseline comparison..." -ForegroundColor Cyan
        & .\tank_multi_baseline_compare.ps1 -ProcessPattern $ProcessPattern -MonitorMinutes $MonitorMinutes -EnableCSVLogging
    }
    
    "create-single" {
        Write-Host "🔧 Creating New Single Process Baseline..." -ForegroundColor Cyan
        Write-Host "Process: $ProcessName" -ForegroundColor Yellow
        Write-Host "This will take approximately 5 minutes" -ForegroundColor Gray
        Write-Host ""
        
        if (-not (Test-ProcessAvailability -ProcessName $ProcessName -ProcessPattern $ProcessPattern -Mode $Mode)) {
            exit 1
        }
        
        Write-Host "Creating new single process baseline..." -ForegroundColor Cyan
        & .\tank_baseline_compare.ps1 -ProcessName $ProcessName -CreateNewBaseline -MonitorMinutes 1
    }
    
    "create-multi" {
        Write-Host "🔧 Creating New Multi-Process Baseline..." -ForegroundColor Cyan
        Write-Host "Process Pattern: $ProcessPattern" -ForegroundColor Yellow
        Write-Host "This will take approximately 5 minutes" -ForegroundColor Gray
        Write-Host ""
        
        if (-not (Test-ProcessAvailability -ProcessName $ProcessName -ProcessPattern $ProcessPattern -Mode $Mode)) {
            exit 1
        }
        
        Write-Host "Creating new multi-process baseline..." -ForegroundColor Cyan
        & .\tank_multi_baseline_compare.ps1 -ProcessPattern $ProcessPattern -CreateNewBaseline -MonitorMinutes 1
    }
    
    "analyze" {
        Write-Host "📈 Analyzing Baseline Comparison Data..." -ForegroundColor Cyan
        
        $dataFiles = @()
        if (Test-Path "tank_baseline_comparison.csv") {
            $dataFiles += "tank_baseline_comparison.csv"
        }
        if (Test-Path "tank_multi_baseline_comparison.csv") {
            $dataFiles += "tank_multi_baseline_comparison.csv"
        }
        
        if ($dataFiles.Count -eq 0) {
            Write-Host "❌ No baseline comparison data found!" -ForegroundColor Red
            Write-Host ""
            Write-Host "Available data files to analyze:" -ForegroundColor Yellow
            Write-Host "  • tank_baseline_comparison.csv (from single process comparison)" -ForegroundColor Gray
            Write-Host "  • tank_multi_baseline_comparison.csv (from multi-process comparison)" -ForegroundColor Gray
            Write-Host ""
            Write-Host "Run baseline comparison first:" -ForegroundColor Cyan
            Write-Host "  .\tank_baseline_launcher.ps1 -Mode single" -ForegroundColor Yellow
            Write-Host "  .\tank_baseline_launcher.ps1 -Mode multi" -ForegroundColor Yellow
            exit 1
        }
        
        Write-Host "✅ Found baseline comparison data files:" -ForegroundColor Green
        $dataFiles | ForEach-Object {
            $fileInfo = Get-Item $_
            Write-Host "  • $_ ($([math]::Round($fileInfo.Length / 1KB, 1)) KB, modified: $($fileInfo.LastWriteTime.ToString('HH:mm:ss')))" -ForegroundColor White
        }
        Write-Host ""
        
        # Analyze each data file
        foreach ($dataFile in $dataFiles) {
            Write-Host "📊 Analyzing $dataFile..." -ForegroundColor Cyan
            
            try {
                $data = Import-Csv $dataFile
                Write-Host "✅ Loaded $($data.Count) data points from $dataFile" -ForegroundColor Green
                
                if ($dataFile -like "*multi_baseline*") {
                    # Multi-process baseline analysis
                    $data | ForEach-Object {
                        $_.CurrentTotalMemoryMB = [double]$_.CurrentTotalMemoryMB
                        $_.TotalMemoryDeltaPercent = [double]$_.TotalMemoryDeltaPercent
                        $_.CurrentProcessCount = [int]$_.CurrentProcessCount
                    }
                    
                    $memoryDeltas = $data | Measure-Object TotalMemoryDeltaPercent -Average -Maximum -Minimum
                    $processCountStats = $data | Measure-Object CurrentProcessCount -Average -Maximum -Minimum
                    $alertCount = ($data | Where-Object { $_.AlertLevel -ne "NORMAL" }).Count
                    
                    Write-Host "  📊 Multi-Process Baseline Analysis:" -ForegroundColor Yellow
                    Write-Host "    Duration: $([math]::Round(((Get-Date) - [datetime]$data[0].Timestamp).TotalHours, 1)) hours" -ForegroundColor White
                    Write-Host "    Avg Memory Delta: $([math]::Round($memoryDeltas.Average, 1))%" -ForegroundColor White
                    Write-Host "    Max Memory Delta: $([math]::Round($memoryDeltas.Maximum, 1))%" -ForegroundColor White
                    Write-Host "    Avg Process Count: $([math]::Round($processCountStats.Average, 1))" -ForegroundColor White
                    Write-Host "    Alert Rate: $([math]::Round(($alertCount / $data.Count) * 100, 1))%" -ForegroundColor $(if($alertCount -gt 0) { "Red" } else { "Green" })
                    
                } else {
                    # Single process baseline analysis
                    $data | ForEach-Object {
                        $_.CurrentMemoryMB = [double]$_.CurrentMemoryMB
                        $_.MemoryDeltaPercent = [double]$_.MemoryDeltaPercent
                    }
                    
                    $memoryDeltas = $data | Measure-Object MemoryDeltaPercent -Average -Maximum -Minimum
                    $alertCount = ($data | Where-Object { $_.AlertLevel -ne "NORMAL" }).Count
                    
                    Write-Host "  📊 Single Process Baseline Analysis:" -ForegroundColor Yellow
                    Write-Host "    Duration: $([math]::Round(((Get-Date) - [datetime]$data[0].Timestamp).TotalHours, 1)) hours" -ForegroundColor White
                    Write-Host "    Avg Memory Delta: $([math]::Round($memoryDeltas.Average, 1))%" -ForegroundColor White
                    Write-Host "    Max Memory Delta: $([math]::Round($memoryDeltas.Maximum, 1))%" -ForegroundColor White
                    Write-Host "    Alert Rate: $([math]::Round(($alertCount / $data.Count) * 100, 1))%" -ForegroundColor $(if($alertCount -gt 0) { "Red" } else { "Green" })
                }
                
                # Performance assessment
                if ($alertCount -eq 0) {
                    Write-Host "    Assessment: ✅ Performance within baseline parameters" -ForegroundColor Green
                } elseif ($alertCount -lt ($data.Count * 0.1)) {
                    Write-Host "    Assessment: ⚠️ Occasional deviations from baseline" -ForegroundColor Yellow
                } else {
                    Write-Host "    Assessment: 🔴 Frequent deviations from baseline" -ForegroundColor Red
                }
                
            } catch {
                Write-Host "  ❌ Error analyzing $dataFile`: $($_.Exception.Message)" -ForegroundColor Red
            }
            Write-Host ""
        }
        
        # Check for baseline files
        Write-Host "📋 Baseline Files Status:" -ForegroundColor Cyan
        
        if (Test-Path "tank_baseline.json") {
            $singleBaseline = Get-Content "tank_baseline.json" | ConvertFrom-Json
            Write-Host "  ✅ Single Process Baseline: Created $($singleBaseline.CreatedDate)" -ForegroundColor Green
            Write-Host "     Average Memory: $($singleBaseline.AverageMemoryMB) MB" -ForegroundColor White
        } else {
            Write-Host "  ❌ Single Process Baseline: Not found" -ForegroundColor Red
            Write-Host "     Create with: .\tank_baseline_launcher.ps1 -Mode create-single" -ForegroundColor Yellow
        }
        
        if (Test-Path "tank_multi_baseline.json") {
            $multiBaseline = Get-Content "tank_multi_baseline.json" | ConvertFrom-Json
            Write-Host "  ✅ Multi-Process Baseline: Created $($multiBaseline.CreatedDate)" -ForegroundColor Green
            Write-Host "     Average Total Memory: $($multiBaseline.AverageTotalMemoryMB) MB" -ForegroundColor White
            Write-Host "     Average Process Count: $($multiBaseline.AverageProcessCount)" -ForegroundColor White
        } else {
            Write-Host "  ❌ Multi-Process Baseline: Not found" -ForegroundColor Red
            Write-Host "     Create with: .\tank_baseline_launcher.ps1 -Mode create-multi" -ForegroundColor Yellow
        }
    }
    
    default {
        Write-Host "❌ Invalid mode: $Mode" -ForegroundColor Red
        Write-Host ""
        Write-Host "Available modes:" -ForegroundColor Yellow
        Write-Host "  single        - Single process baseline comparison" -ForegroundColor White
        Write-Host "  multi         - Multi-process baseline comparison" -ForegroundColor White
        Write-Host "  create-single - Create new single process baseline" -ForegroundColor White
        Write-Host "  create-multi  - Create new multi-process baseline" -ForegroundColor White
        Write-Host "  analyze       - Analyze baseline comparison data" -ForegroundColor White
        Write-Host ""
        Write-Host "For detailed help:" -ForegroundColor Cyan
        Write-Host "  .\tank_baseline_launcher.ps1 -ShowHelp" -ForegroundColor Gray
        exit 1
    }
}
