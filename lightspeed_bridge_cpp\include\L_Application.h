#pragma once
#if !defined(LS_L_APPLICATION_H)
#define LS_L_APPLICATION_H

// Copyright (c) 2001-2018 Lightspeed Financial, Inc. All rights reserved.
#include "L_Version.h"

#include <time.h>
#include <stddef.h>
#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
#include <intrin.h>
#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

namespace LightspeedTrader
{
	typedef void *win_os_handle;
#if defined(_M_IX86)
	typedef unsigned long win_ulong_ptr;
#else
	typedef unsigned long long win_ulong_ptr;
#endif
	class L_ObserverDelete;
	class L_ObservableImpl;
	class L_ObserverImpl;
	class L_Observer;
	class L_Thread;
	class L_Strand;
	class L_Port;
	class L_StrandStorageId;
	class L_FullQuote;
	class L_Summary;
	class L_Account;
	class L_Sort;
	class L_SortOperator;
	class L_SymbolFilterStatic;
	class L_EventPort;
	class L_Buffer;
}

using LightspeedTrader::win_os_handle;
using LightspeedTrader::win_ulong_ptr;

extern "C"
{
	struct HWND__;
	struct HINSTANCE__;
	struct tagMSG;

#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
	__declspec(dllimport) void __stdcall Sleep(unsigned long);
	__declspec(dllimport) int __stdcall CloseHandle(win_os_handle);
	__declspec(dllimport) unsigned long __stdcall GetCurrentProcessId();
	__declspec(dllimport) int __stdcall GetModuleHandleExW(unsigned long, wchar_t const *, HINSTANCE__ **);
	__declspec(dllimport) win_os_handle __stdcall OpenFileMappingW(unsigned long, int, wchar_t const *);
	__declspec(dllimport) void * __stdcall MapViewOfFile(win_os_handle, unsigned long dwDesiredAccess, unsigned long, unsigned long, win_ulong_ptr);
	__declspec(dllimport) int __stdcall UnmapViewOfFile(void const *);
#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)
} // extern "C"



namespace LightspeedTrader
{

class L_Application
{
public:
	virtual char const *L_GetLightspeedVersion() const = 0;
	virtual HWND__ *L_GetMainWnd() const = 0;
	virtual L_Account *L_GetAccount() = 0;
	virtual void L_LogAppMessage(char const *message) = 0;
	virtual void L_LogExtensionMessage(char const *message) = 0;
	virtual void L_AddMessageToAppWnd(char const *message) = 0;
	virtual void L_AddMessageToExtensionWnd(char const *message, unsigned long colorref = 0xffffff) = 0;

	virtual L_Summary *L_CreateSummary(char const *symbol) = 0;
	virtual void L_DestroySummary(L_Summary *sum) = 0;

	virtual bool L_IsListed(char const *symbol) const = 0;

	virtual void L_GetUSEasternTime(unsigned int &h, unsigned int &m, unsigned int &s) = 0;
	virtual void L_GetUSEasternTime(tm &t) = 0;
	virtual time_t L_GetUSEasternMidnight() = 0;
	virtual void L_MillisToTm(long millis, tm &t) = 0;

	virtual char const *L_GetFocusedSymbol() = 0;
	virtual void L_SetFocusedSymbol(char const *symbol) = 0;

	virtual void L_AttachToAppNotifier(L_Observer *dest) = 0;
	virtual void L_DetachFromAppNotifier(L_Observer *dest) = 0;

	virtual void L_SubscribeToOrderImbalances(L_Observer *dest) = 0;
	virtual void L_UnsubscribeFromOrderImbalances(L_Observer *dest) = 0;

	virtual void L_SubscribeToLevel1(char const *symbol, L_Observer* dest) = 0;
	virtual void L_UnsubscribeFromLevel1(char const *symbol, L_Observer* dest) = 0;

	virtual void L_SubscribeToTrades(char const *symbol, L_Observer *dest) = 0;
	virtual void L_UnsubscribeFromTrades(char const *symbol, L_Observer *dest) = 0;

	virtual void L_SubscribeToLevel2(char const *symbol, L_Observer* dest) = 0;
	virtual void L_UnsubscribeFromLevel2(char const *symbol, L_Observer* dest) = 0;

	virtual void L_SubscribeToECNList(L_Observer *dest) = 0;
	virtual void L_UnsubscribeFromECNList(L_Observer *dest) = 0;

	virtual void L_SubscribeToECN(char const *ecn, char const *symbol, L_Observer *dest) = 0;
	virtual void L_UnsubscribeFromECN(char const *ecn, char const *symbol, L_Observer *dest) = 0;

	virtual void L_SubscribeToMarketStatus(L_Observer *dest) = 0;
	virtual void L_UnsubscribeFromMarketStatus(L_Observer *dest) = 0;

	virtual L_FullQuote *L_CreateFullQuote(char const *symbol) = 0;
	virtual void L_DestroyFullQuote(L_FullQuote *fullQuote) = 0;

	virtual bool L_IsExecutorConnected() const = 0;
	virtual bool L_IsQuoteConnected() const = 0;

	virtual char L_MarketStatus() const = 0;

	virtual bool L_IsMarketConnected() const = 0;

	virtual void L_SubscribeToIndications(L_Observer *dest) = 0;
	virtual void L_UnsubscribeFromIndications(L_Observer *dest) = 0;

	virtual L_Summary *L_TryCreateSummary(char const *symbol) = 0;
	virtual L_FullQuote *L_TryCreateFullQuote(char const *symbol) = 0;

	virtual long L_SummaryCount(char const *symbol) = 0;
	virtual long L_L1SubscriptionCount(char const *symbol) = 0;
	virtual long L_FullQuoteCount(char const *symbol) = 0;
	virtual long L_TradesSubscriptionCount(char const *symbol) = 0;
	virtual long L_L2SubscriptionCount(char const *symbol) = 0;
	virtual long L_ECNSubscriptionCount(char const *ecn, char const *symbol) = 0;

	virtual L_Buffer *L_Alloc(size_t size) = 0;
	virtual void L_QueueDelete(L_ObserverImpl *impl) = 0;
	virtual L_ObserverImpl *L_CreateObserverImpl(L_Observer *ob) = 0;
	virtual L_ObserverImpl *L_CreateObserverImplOnStrand(L_Observer *ob, L_Strand *s, L_ObserverDelete *deleter) = 0;
	virtual L_ObservableImpl *L_CreateObservableImpl(L_Strand *strand) = 0;
	virtual void L_DestroyObservableImpl(L_ObservableImpl *impl) = 0;
	virtual L_Strand *L_GetExtensionStrand() = 0;
	virtual L_Thread *L_GetCurrentThread() = 0;
	virtual L_Strand *L_GetCurrentStrand() = 0;
	virtual L_Strand *L_CreateStrand(L_Port *port) = 0;
	virtual void L_DestroyStrand(L_Strand *strand) = 0;
	virtual L_StrandStorageId *L_AllocStrandStorageId() = 0;
	virtual void L_FreeStrandStorageId(L_StrandStorageId *id) = 0;
	virtual void *L_GetStrandStorage(L_StrandStorageId *id) = 0;
	virtual void L_SetStrandStorage(L_StrandStorageId *id, void *val, void (*deleter)(void *)) = 0;

	virtual L_Sort *L_CreateSort(L_SortOperator *sortOp, long elementCount, long startingRank, L_Strand *strand, size_t indexSize, size_t indexAlign, size_t ioffset, void (*indexConstruct)(void * mem, L_Sort *sort, size_t param), void (*indexDestruct)(void *)) = 0;
	virtual void L_DestroySort(L_Sort *sort) = 0;
	virtual void L_AddMarketSummaryElements(L_Sort *sort, L_SymbolFilterStatic const *filt) = 0;
	virtual L_Summary *L_CreateClientSummary(L_Summary const *sourceSummary, L_Strand *clientStrand) = 0;
	virtual long long L_GetHNUSEasternMidnight() const = 0;

	virtual L_EventPort *L_GetMainEventPort() = 0;
	virtual L_EventPort *L_CreateEventPort() = 0;
	virtual void L_DestroyEventPort(L_EventPort *port) = 0;

	virtual bool L_HasMarketSubscription() const = 0;
	virtual bool L_IsLan() const = 0;
};


class L_ApiHost
{
public:
	virtual L_Application *L_GetApplication(HINSTANCE__ *) = 0;
	virtual void L_ExitExtension(HINSTANCE__ *, unsigned int exitCode) = 0;
	virtual void *L_COIDeprecated(void *) = 0;
	virtual void L_DestroyObserverImpl(L_ObserverImpl *impl) = 0;
	virtual void L_PrePumpMessage() = 0;
	virtual void L_PreProcessMessageFilter(int code, tagMSG *lpMsg) = 0;
};


#if !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)

inline L_ApiHost *GetApiHost()
{
	unsigned long const c_filemapread = 0x0004;

	wchar_t szName[256];
	win_os_handle hMapFile = 0;
	L_ApiHost **ppHost = 0;
	L_ApiHost *result = 0;

	{
		unsigned long processId = GetCurrentProcessId();
		wchar_t *buf = szName;
		while (processId > 0)
		{
			*buf = wchar_t(processId % 10) + L'0'; // backwards
			processId = processId / 10;
			++buf;
		}
		{
			static wchar_t const suffix[] = L"LightspeedTraderHost";
			for (wchar_t const *i = &suffix[0]; *i; ++i)
			{
				*buf = *i;
				++buf;
			}
			*buf = L'\0';
		}
	}
	hMapFile = OpenFileMappingW(
				c_filemapread,
				0,
				szName
				);
 
	if (hMapFile == NULL) 
	{
		ppHost = 0;
	}
	else
	{
 
		ppHost = reinterpret_cast<L_ApiHost **>(
			MapViewOfFile(
				hMapFile,
				c_filemapread,
				0,
				0,
				sizeof(L_ApiHost *)
				)
				);
	}

	if (ppHost)
	{
		result = *ppHost;
		UnmapViewOfFile(ppHost);
	}
	if (hMapFile)
	{
		CloseHandle(hMapFile);
	}

	return result;
}

inline long *extension_spinlockflag()
{
#if defined(_MSC_VER) && _MSC_VER < 1910
	__declspec(align(16)) static long l_;
#else
	alignas(16) static long l_;
#endif
	return &l_;
}

inline void extension_lock()
{
	unsigned long sleepPeriod = 0;
	long *pl = extension_spinlockflag();
	do
	{
		long i = 4000;
		for (; i > 0; --i)
		{
			if (*pl == 0)
			{
				_ReadWriteBarrier();
				break;
			}
			_ReadWriteBarrier();
			_mm_pause();
		}
		if (i == 0)
		{
			Sleep(sleepPeriod);
			sleepPeriod = 1;
		}
	} while (_InterlockedExchange(pl, 1));
}

inline void extension_unlock()
{
	_ReadWriteBarrier();
	*extension_spinlockflag() = 0;
}

inline L_ApiHost *L_GetHost()
{
	static long init;
	static L_ApiHost *host;

	if (!init)
	{
		extension_lock();
		if (!init)
		{
			host = GetApiHost();
			init = 1;
		}
		extension_unlock();
	}

	return host;
}

inline HINSTANCE__ *L_GetExtensionModule()
{
	unsigned long const c_fromaddress = 0x00000004;
	unsigned long const c_unchangedrefcount = 0x00000002;
	static HINSTANCE__ *hExtension;
	if (hExtension == 0)
	{
		extension_lock();
		if (hExtension == 0)
		{
			GetModuleHandleExW(
				c_unchangedrefcount | c_fromaddress,
				reinterpret_cast<wchar_t const *>(&L_GetExtensionModule),
				&hExtension
				);
		}
		extension_unlock();
	}

	return hExtension;
}

inline L_Application *L_GetApplication()
{
	static long init;
	static L_Application *app;

	if (!init)
	{
		L_ApiHost *host = L_GetHost();
		HINSTANCE__ *hModule = L_GetExtensionModule();
		extension_lock();
		if (!init)
		{
			app = host->L_GetApplication(hModule);
			init = 1;
		}
		extension_unlock();
	}

	return app;
}

inline void L_ExitLightspeedExtension(unsigned int exitCode)
{
	L_GetHost()->L_ExitExtension(L_GetExtensionModule(), exitCode);
}

inline void L_PrePumpMessage()
{
	L_GetHost()->L_PrePumpMessage();
}

inline void L_PreProcessMessageFilter(int code, tagMSG *lpMsg)
{
	L_GetHost()->L_PreProcessMessageFilter(code, lpMsg);
}

inline char const *L_GetLightspeedVersion()
{
	return L_GetApplication()->L_GetLightspeedVersion();
}
inline HWND__ *L_GetMainWnd()
{
	return L_GetApplication()->L_GetMainWnd();
}
inline L_Account *L_GetAccount()
{
	return L_GetApplication()->L_GetAccount();
}
inline void L_LogAppMessage(char const *message)
{
	L_GetApplication()->L_LogAppMessage(message);
}
inline void L_LogExtensionMessage(char const *message)
{
	L_GetApplication()->L_LogExtensionMessage(message);
}
inline void L_AddMessageToAppWnd(char const *message)
{
	L_GetApplication()->L_AddMessageToAppWnd(message);
}
inline void L_AddMessageToExtensionWnd(char const *message, unsigned long color = 0xffffff)
{
	L_GetApplication()->L_AddMessageToExtensionWnd(message, color);
}

inline L_Summary *L_CreateSummary(char const *symbol)
{
	return L_GetApplication()->L_CreateSummary(symbol);
}
inline L_Summary *L_TryCreateSummary(char const *symbol)
{
	return L_GetApplication()->L_TryCreateSummary(symbol);
}
inline void L_DestroySummary(L_Summary *sum)
{
	return L_GetApplication()->L_DestroySummary(sum);
}

inline L_FullQuote *L_CreateFullQuote(char const *symbol)
{
	return L_GetApplication()->L_CreateFullQuote(symbol);
}

inline L_FullQuote *L_TryCreateFullQuote(char const *symbol)
{
	return L_GetApplication()->L_TryCreateFullQuote(symbol);
}

inline void L_DestroyFullQuote(L_FullQuote *fullQuote)
{
	return L_GetApplication()->L_DestroyFullQuote(fullQuote);
}

inline char const *L_GetFocusedSymbol()
{
	return L_GetApplication()->L_GetFocusedSymbol();
}

inline void L_SetFocusedSymbol(char const *symbol)
{
	L_GetApplication()->L_SetFocusedSymbol(symbol);
}

inline void L_AttachToAppNotifier(L_Observer *dest)
{
	L_GetApplication()->L_AttachToAppNotifier(dest);
}

inline void L_DetachFromAppNotifier(L_Observer *dest)
{
	L_GetApplication()->L_DetachFromAppNotifier(dest);
}

inline void L_SubscribeToOrderImbalances(L_Observer *dest)
{
	L_GetApplication()->L_SubscribeToOrderImbalances(dest);
}
inline void L_UnsubscribeFromOrderImbalances(L_Observer *dest)
{
	L_GetApplication()->L_UnsubscribeFromOrderImbalances(dest);
}

inline void L_SubscribeToIndications(L_Observer *dest)
{
	L_GetApplication()->L_SubscribeToIndications(dest);
}
inline void L_UnsubscribeFromIndications(L_Observer *dest)
{
	L_GetApplication()->L_UnsubscribeFromIndications(dest);
}

inline void L_SubscribeToTrades(char const *symbol, L_Observer *dest)
{
	L_GetApplication()->L_SubscribeToTrades(symbol, dest);
}
inline void L_UnsubscribeFromTrades(char const *symbol, L_Observer *dest)
{
	L_GetApplication()->L_UnsubscribeFromTrades(symbol, dest);
}

inline void L_SubscribeToECNList(L_Observer *dest)
{
	L_GetApplication()->L_SubscribeToECNList(dest);
}
inline void L_UnsubscribeFromECNList(L_Observer *dest)
{
	L_GetApplication()->L_UnsubscribeFromECNList(dest);
}

inline void L_SubscribeToLevel1(char const *symbol, L_Observer* dest)
{
	L_GetApplication()->L_SubscribeToLevel1(symbol, dest);
}
inline void L_UnsubscribeFromLevel1(char const *symbol, L_Observer* dest)
{
	L_GetApplication()->L_UnsubscribeFromLevel1(symbol, dest);
}


inline void L_SubscribeToLevel2(char const *symbol, L_Observer* dest)
{
	L_GetApplication()->L_SubscribeToLevel2(symbol, dest);
}
inline void L_UnsubscribeFromLevel2(char const *symbol, L_Observer* dest)
{
	L_GetApplication()->L_UnsubscribeFromLevel2(symbol, dest);
}


inline void L_SubscribeToECN(char const *ecn, char const *symbol, L_Observer *dest)
{
	L_GetApplication()->L_SubscribeToECN(ecn, symbol, dest);
}
inline void L_UnsubscribeFromECN(char const *ecn, char const *symbol, L_Observer *dest)
{
	L_GetApplication()->L_UnsubscribeFromECN(ecn, symbol, dest);
}
inline void L_SubscribeToMarketStatus(L_Observer *dest)
{
	L_GetApplication()->L_SubscribeToMarketStatus(dest);
}
inline void L_UnsubscribeFromMarketStatus(L_Observer *dest)
{
	L_GetApplication()->L_UnsubscribeFromMarketStatus(dest);
}

inline long L_SummaryCount(char const *symbol)
{
	return L_GetApplication()->L_SummaryCount(symbol);
}
inline long L_L1SubscriptionCount(char const *symbol)
{
	return L_GetApplication()->L_L1SubscriptionCount(symbol);
}
inline long L_FullQuoteCount(char const *symbol)
{
	return L_GetApplication()->L_FullQuoteCount(symbol);
}
inline long L_TradesSubscriptionCount(char const *symbol)
{
	return L_GetApplication()->L_TradesSubscriptionCount(symbol);
}
inline long L_L2SubscriptionCount(char const *symbol)
{
	return L_GetApplication()->L_L2SubscriptionCount(symbol);
}
inline long L_ECNSubscriptionCount(char const *ecn, char const *symbol)
{
	return L_GetApplication()->L_ECNSubscriptionCount(ecn, symbol);
}

inline bool L_IsExecutorConnected()
{
	return L_GetApplication()->L_IsExecutorConnected();
}
inline bool L_IsQuoteConnected()
{
	return L_GetApplication()->L_IsQuoteConnected();
}
inline bool L_IsMarketConnected()
{
	return L_GetApplication()->L_IsMarketConnected();
}

inline char L_MarketStatus()
{
	return L_GetApplication()->L_MarketStatus();
}

inline L_Buffer *L_Alloc(size_t size)
{
	return L_GetApplication()->L_Alloc(size);
}

inline L_Strand *L_GetExtensionStrand()
{
	return L_GetApplication()->L_GetExtensionStrand();
}

inline L_Thread *L_GetCurrentThread()
{
	return L_GetApplication()->L_GetCurrentThread();
}

inline L_Strand *L_GetCurrentStrand()
{
	return L_GetApplication()->L_GetCurrentStrand();
}

inline L_Strand *L_CreateStrand(L_Port *port)
{
	return L_GetApplication()->L_CreateStrand(port);
}

inline void L_DestroyStrand(L_Strand *strand)
{
	L_GetApplication()->L_DestroyStrand(strand);
}

inline L_StrandStorageId *L_AllocStrandStorageId()
{
	return L_GetApplication()->L_AllocStrandStorageId();
}
inline void L_FreeStrandStorageId(L_StrandStorageId *id)
{
	L_GetApplication()->L_FreeStrandStorageId(id);
}
inline void *L_GetStrandStorage(L_StrandStorageId *id)
{
	return L_GetApplication()->L_GetStrandStorage(id);
}
inline void L_SetStrandStorage(L_StrandStorageId *id, void *val, void (*deleter)(void *))
{
	L_GetApplication()->L_SetStrandStorage(id, val, deleter);
}

inline L_Sort *L_CreateSort(L_SortOperator *sortOp, long elementCount, long startingRank, L_Strand *strand, size_t indexSize, size_t indexAlign, size_t ioffset, void (*indexConstruct)(void * mem, L_Sort *sort, size_t param), void (*indexDestruct)(void *))
{
	return L_GetApplication()->L_CreateSort(sortOp, elementCount, startingRank, strand, indexSize, indexAlign, ioffset, indexConstruct, indexDestruct);
}

inline void L_DestroySort(L_Sort *sort)
{
	return L_GetApplication()->L_DestroySort(sort);
}

inline void L_AddMarketSummaryElements(L_Sort *sort, L_SymbolFilterStatic const *filt)
{
	L_GetApplication()->L_AddMarketSummaryElements(sort, filt);
}

inline L_Summary *L_CreateClientSummary(L_Summary const *sourceSummary, L_Strand *clientStrand)
{
	return L_GetApplication()->L_CreateClientSummary(sourceSummary, clientStrand);
}

inline bool L_HasMarketSubscription()
{
	return L_GetApplication()->L_HasMarketSubscription();
}

inline bool L_IsLan()
{
	return L_GetApplication()->L_IsLan();
}

inline L_EventPort *L_GetMainEventPort()
{
	return L_GetApplication()->L_GetMainEventPort();
}

inline L_EventPort *L_CreateEventPort()
{
	return L_GetApplication()->L_CreateEventPort();
}

inline void L_DestroyEventPort(L_EventPort *port)
{
	return L_GetApplication()->L_DestroyEventPort(port);
}

inline bool L_IsListed(char const *symbol)
{
	return L_GetApplication()->L_IsListed(symbol);
}

#endif // !defined(LS_EXCLUDE_CLIENT_COMPILER_SPECIFIC)


} // namespace LightspeedTrader

#endif // !defined(LS_L_APPLICATION_H)

