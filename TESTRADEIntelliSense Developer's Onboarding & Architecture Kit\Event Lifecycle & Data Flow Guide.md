Document 1 of 4: The Event Lifecycle & Data Flow Guide
Purpose: This document provides a canonical, unambiguous guide to how data and commands move through the TESTRADE system. All modifications or new features must respect these data flow patterns. This is the single source of truth for "how does X get from A to B?"
Trace 1: The Lifecycle of a Live Trading Event (The "Horseshoe")
This traces data flowing out of the core trading engine.
Example Event: OCRParsedData generated by the OCR service.
Origin: The event is created inside the TESTRADE ApplicationCore.
Egress: The ApplicationCore does not call ZMQ directly. It hands the event to its internal instance of the BulletproofBabysitterIPCClient by calling self.babysitter_ipc_client.send_data(target_stream, data_json).
The correlationId is generated by ApplicationCore and is included in the data_json metadata. This ID is preserved end-to-end.
Transport 1 (Local IPC): The BulletproofBabysitterIPCClient uses a ZMQ PUSH socket to send the event to the babysitter_service.py process. The specific port depends on the data type (bulk on 5555, trading on 5556, system on 5557).
Resilience Note: If this connection fails, the BulletproofBabysitterIPCClient automatically buffers the event to a memory-mapped disk file and a background thread handles retries. The ApplicationCore is never blocked.
Hop 1 (The Router): The babysitter_service.py process receives the event on its ZMQ PULL socket. Its only job is to act as a router.
Transport 2 (Network): The babysitter_service.py process immediately connects to the main Redis server (**************:6379).
Destination: The event is written to the appropriate Redis Stream, as defined in control.json (e.g., testrade:raw-ocr-events).
Consumption: All consumers on the "left side" of the horseshoe (GUI Backend, Intellisense in Data Capture Mode) connect only to Redis to receive these events.
Trace 2: The Lifecycle of a GUI Command (The "Reverse Horseshoe")
This traces a command flowing into the core trading engine.
Example Command: A ForceCloseAll request from the GUI.
Origin: A user clicks a button, triggering an API call to the GUI Backend server.
Egress: The GUI Backend does not call the ApplicationCore directly. It has its own instance of the BulletproofBabysitterIPCClient. It formats the command as a JSON message and calls self.babysitter_ipc_client.send_data(...).
Transport 1 (Local IPC): The GUI Backend's BulletproofBabysitterIPCClient uses its ZMQ PUSH socket to send the command to the babysitter_service.py process, typically on the 'system' channel.
Hop 1 (The Router): The babysitter_service.py process receives the command on its ZMQ PULL socket. It recognizes it as a command and forwards it.
Transport 2 (Local IPC): The babysitter_service.py process sends the command to a different ZMQ PUSH socket, one that connects to the ApplicationCore's dedicated command port (e.g., tcp://127.0.0.1:5560).
Destination: The TESTRADE ApplicationCore is listening for commands on its ZMQ PULL socket (_core_command_receive_loop). It receives the command and executes the corresponding logic.
Response: The ApplicationCore sends a response back along the same path in reverse: ApplicationCore -> Baby Sitter -> Redis Stream (e.g., testrade:responses:to_gui) -> GUI Backend.