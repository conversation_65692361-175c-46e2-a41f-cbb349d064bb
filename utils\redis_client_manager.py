# testrade_project_root/utils/redis_client_manager.py
import redis # Using redis-py library: pip install redis
import logging
import time
import threading # For _lock and _connection_lock
from typing import Optional, Any

# Attempt to import global_config, with a fallback for standalone testing/development
try:
    from utils.global_config import config as global_app_config
except ImportError:
    logging.warning("RedisClientManager: Could not import global_app_config from utils.global_config. Using internal mock for defaults.")
    class MockGlobalConfig: # Minimal mock for required attributes
        REDIS_HOST = "127.0.0.1"  # Changed from "localhost" to avoid DNS resolution latency
        REDIS_PORT = 6379
        REDIS_DB = 0
        REDIS_PASSWORD = None
        redis_connect_timeout_sec = 2 # Shorter for mock
        redis_socket_timeout_sec = 2  # Shorter for mock
        redis_retry_on_timeout = False # Don't retry for mock usually
        redis_health_check_interval_sec = 10 # Example
    global_app_config = MockGlobalConfig()


logger = logging.getLogger(__name__)

class RedisClientManager:
    """
    Manages a connection to a Redis server, providing a client instance
    and handling reconnections. Designed to be a singleton.
    """
    _instance: Optional['RedisClientManager'] = None
    _singleton_lock = threading.Lock() # Lock for singleton instantiation

    def __init__(self, config: Optional[Any] = None, auto_connect: bool = True):
        """
        Initializes the RedisClientManager.
        Args:
            config: Configuration object (e.g., GlobalConfig). If None, uses global_app_config.
            auto_connect: If True, attempts to connect upon instantiation.
        """
        if RedisClientManager._instance is not None:
            # This enforces singleton behavior more strictly.
            # If an instance already exists, subsequent __init__ calls on the class
            # effectively become no-ops if you want to ensure only one config is used.
            # Or, raise an error:
            # raise RuntimeError("RedisClientManager is a singleton and already instantiated.")
            # For this pattern, we assume get_instance() is the primary way to get it.
            # If __init__ is called directly again, it would re-initialize the existing _instance's attributes.
            pass

        self._config = config if config is not None else global_app_config

        self.host = getattr(self._config, 'REDIS_HOST', '127.0.0.1')  # Changed default from 'localhost'
        # Replace 'localhost' with direct IP address to avoid DNS resolution latency
        if self.host == "localhost":
            self.host = "127.0.0.1"
        self.port = int(getattr(self._config, 'REDIS_PORT', 6379))
        self.db = int(getattr(self._config, 'REDIS_DB', 0))
        self.password = getattr(self._config, 'REDIS_PASSWORD', None)

        self.connect_timeout = float(getattr(self._config, 'redis_connect_timeout_sec', 2.0))
        self.socket_timeout = float(getattr(self._config, 'redis_socket_timeout_sec', 2.0))
        # redis-py's retry_on_timeout is a boolean for the client itself.
        # We can add our own retry logic in connect() if needed.
        self.client_retry_on_timeout = bool(getattr(self._config, 'redis_client_retry_on_timeout_bool', True))

        self.redis_client: Optional[redis.Redis] = None
        self._is_connected: bool = False
        self._connection_attempt_lock = threading.Lock() # Lock for connect/reconnect attempts
        self.logger = logger # Use instance logger for clarity

        if auto_connect:
            self.connect()

    @classmethod
    def get_instance(cls, config: Optional[Any] = None) -> 'RedisClientManager':
        """
        Provides a thread-safe singleton instance of RedisClientManager.
        The first call creates the instance; subsequent calls return the existing one.
        If 'config' is provided on first call, it's used; otherwise, global_app_config is used.
        """
        if cls._instance is None:
            with cls._singleton_lock:
                if cls._instance is None:
                    logger.info("Creating new RedisClientManager singleton instance.")
                    # Pass config to __init__ only if it's the first time.
                    # Auto-connect might be better handled after instance is fully assigned to _instance.
                    instance_config = config if config is not None else global_app_config
                    cls._instance = cls(config=instance_config, auto_connect=False)
                    cls._instance.connect() # Connect after instance is set
        elif config is not None and id(cls._instance._config) != id(config):
             logger.warning("RedisClientManager singleton already exists. Provided config differs. "
                                "Returning existing instance with its original config.")
        return cls._instance

    def connect(self, max_retries: int = 3, retry_delay: float = 1.0) -> bool:
        """
        Establishes connection to Redis with retries.
        Returns True if successful, False otherwise.
        """
        if self._is_connected and self.redis_client:
            try:
                if self.redis_client.ping():
                    self.logger.debug("Redis connection already active and ping successful.")
                    return True
            except (redis.exceptions.ConnectionError, redis.exceptions.TimeoutError):
                self.logger.warning("Existing Redis connection failed ping. Attempting to reconnect.")
                self._is_connected = False # Mark as not connected before trying again
                self.redis_client = None

        with self._connection_attempt_lock: # Prevent multiple concurrent connect attempts
            if self._is_connected: # Double check after acquiring lock
                return True

            current_retry = 0
            while current_retry <= max_retries:
                try:
                    self.logger.info(f"Attempting to connect to Redis: {self.host}:{self.port} DB {self.db} (Attempt {current_retry + 1}/{max_retries + 1})")
                    self.redis_client = redis.Redis(
                        host=self.host, port=self.port, db=self.db, password=self.password,
                        decode_responses=True, # Crucial for string data
                        socket_connect_timeout=self.connect_timeout,
                        socket_timeout=self.socket_timeout,
                        retry_on_timeout=self.client_retry_on_timeout
                    )
                    self.redis_client.ping()
                    self._is_connected = True
                    self.logger.info(f"Successfully connected to Redis: {self.host}:{self.port} DB {self.db}")
                    return True
                except (redis.exceptions.ConnectionError, redis.exceptions.TimeoutError) as e:
                    self.logger.error(f"Redis connection attempt {current_retry + 1} failed: {e}")
                    current_retry += 1
                    if current_retry <= max_retries:
                        self.logger.info(f"Retrying Redis connection in {retry_delay} seconds...")
                        time.sleep(retry_delay)
                    else:
                        self.logger.error("Max Redis connection retries reached. Connection failed.")
                        self.redis_client = None
                        self._is_connected = False
                        return False
                except Exception as e_unexp: # Catch other unexpected errors
                    self.logger.error(f"Unexpected error during Redis connection attempt {current_retry + 1}: {e_unexp}", exc_info=True)
                    self.redis_client = None
                    self._is_connected = False
                    return False # Do not retry on unexpected errors
            return False # Should be unreachable if loop logic is correct
    
    def get_client(self) -> Optional[redis.Redis]:
        """
        Returns the active Redis client. Attempts to connect if not already connected.
        """
        if not self._is_connected or not self.redis_client:
            self.logger.warning("Redis client not available or not connected. Attempting connect().")
            if not self.connect(): # connect() handles logging of success/failure
                return None

        # Optional: Add a ping here to ensure connection is still live before returning
        # if self.redis_client and not self.is_healthy(): # is_healthy() includes a ping
        #     self.logger.warning("Redis connection unhealthy during get_client(). Attempting to re-establish.")
        #     if not self.connect(): return None

        return self.redis_client

    def is_healthy(self) -> bool:
        """Checks current connection health by pinging."""
        if not self.redis_client:
            self._is_connected = False # Ensure consistent state
            return False

        try:
            is_ok = self.redis_client.ping()
            if not is_ok and self._is_connected: # Was connected, but ping failed
                self.logger.warning("Redis ping failed on an active connection. Marking as disconnected.")
            self._is_connected = is_ok # Update status based on ping
            return is_ok
        except (redis.exceptions.ConnectionError, redis.exceptions.TimeoutError) as e:
            self.logger.warning("Redis ping failed due to connection/timeout error. Marking as disconnected.")
            self._is_connected = False
            return False
        except Exception as e: # Catch other redis-py exceptions or unexpected ones
            self.logger.error(f"Unexpected error during Redis health check (ping): {e}", exc_info=True)
            self._is_connected = False
            return False

    def close(self) -> None:
        """Closes the Redis connection."""
        # This method might be called by multiple threads during shutdown.
        # The lock ensures only one thread tries to close the client.
        with self._connection_attempt_lock: # Use same lock as connect to serialize operations
            if self.redis_client:
                try:
                    self.redis_client.close() # redis-py's close method
                    self.logger.info("Redis connection closed via RedisClientManager.")
                except Exception as e:
                    self.logger.error(f"Error closing Redis connection: {e}", exc_info=True)
                finally: # Ensure state is updated regardless of close() success
                    self.redis_client = None
                    self._is_connected = False
            else:
                self.logger.info("RedisClientManager.close() called, but no active client to close.")
