"""
Performance Tracker Package

This package provides utilities for tracking performance metrics throughout the application.
It includes both real and dummy implementations of performance tracking functionality.

The real implementation is used when performance tracking is enabled, while the dummy
implementation is used when performance tracking is disabled to minimize memory usage.

Usage:
    # Import the interface module to get the appropriate implementation
    from utils.performance_tracker.interface import (
        create_timestamp_dict, add_timestamp, calculate_durations,
        enable_performance_tracking, is_performance_tracking_enabled
    )
    
    # Or import the package and use the interface module
    from utils import performance_tracker
    timestamps = performance_tracker.interface.create_timestamp_dict()
"""

# Import the interface module to make it available at the package level
# NOTE: Only import interface - it will conditionally import real or dummy as needed
from . import interface

# Re-export the interface functions for convenience
from .interface import (
    enable_performance_tracking,
    is_performance_tracking_enabled,
    create_timestamp_dict,
    add_timestamp,
    calculate_durations,
    add_to_stats,
    get_performance_stats,
    reset_performance_stats,
    reset_all_trackers,
    log_performance_durations,
    export_performance_stats_to_csv,
    get_tracker,
    track_performance,
    PerformanceTracker,
    DummyTracker,
    update_global_config_with_performance_tracking,
    # Benchmarker integration functions
    initialize_global_benchmarker,
    get_global_benchmarker,
    capture_metric_with_benchmarker,
    add_timestamp_with_capture,
    finalize_performance_scope,
    capture_queue_size,
    capture_throughput_metric,
    capture_cpu_usage
)

# Log that the performance_tracker package has been initialized
import logging
logger = logging.getLogger(__name__)
logger.info("performance_tracker package initialized")
