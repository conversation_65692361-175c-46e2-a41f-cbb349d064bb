#!/usr/bin/env python3
"""
Proper fix for C++ module loading without polluting root directory.
"""

import os
import sys
import ctypes
from ctypes import wintypes

def setup_proper_dll_loading():
    """Set up proper DLL loading environment for the C++ module."""
    print("=== Setting Up Proper DLL Loading ===")
    
    build_dir = os.path.abspath("ocr_accelerator/x64/Release")
    
    if not os.path.exists(build_dir):
        print(f"❌ Build directory not found: {build_dir}")
        return False
    
    print(f"Build directory: {build_dir}")
    
    try:
        # Method 1: Use SetDllDirectory (Windows API)
        if hasattr(ctypes.windll.kernel32, 'SetDllDirectoryW'):
            result = ctypes.windll.kernel32.SetDllDirectoryW(build_dir)
            if result:
                print("✅ Set DLL directory using SetDllDirectoryW")
            else:
                print("⚠️  SetDllDirectoryW failed")
        
        # Method 2: Add to DLL search path (Python 3.8+)
        if hasattr(os, 'add_dll_directory'):
            os.add_dll_directory(build_dir)
            print("✅ Added to DLL search path")
        
        # Method 3: Modify PATH temporarily
        original_path = os.environ.get('PATH', '')
        if build_dir not in original_path:
            os.environ['PATH'] = build_dir + os.pathsep + original_path
            print("✅ Added to PATH environment variable")
        
        # Method 4: Pre-load critical DLLs from build directory
        critical_dlls = ['tesseract55.dll', 'leptonica-1.85.0.dll']
        loaded_handles = []
        
        for dll in critical_dlls:
            dll_path = os.path.join(build_dir, dll)
            if os.path.exists(dll_path):
                try:
                    handle = ctypes.windll.kernel32.LoadLibraryW(dll_path)
                    if handle:
                        loaded_handles.append(handle)
                        print(f"✅ Pre-loaded: {dll}")
                    else:
                        print(f"⚠️  Failed to pre-load: {dll}")
                except Exception as e:
                    print(f"⚠️  Error pre-loading {dll}: {e}")
        
        # Method 5: Add build directory to sys.path
        if build_dir not in sys.path:
            sys.path.insert(0, build_dir)
            print("✅ Added to sys.path")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up DLL loading: {e}")
        return False

def test_module_import():
    """Test importing the module with proper DLL setup."""
    print("\n=== Testing Module Import ===")
    
    try:
        # Import from current directory (should work with proper DLL setup)
        import ocr_accelerator
        print("✅ Module imported successfully!")
        
        # Test basic functionality
        if hasattr(ocr_accelerator, 'test_function'):
            result = ocr_accelerator.test_function()
            print(f"✅ test_function(): {result}")
        
        # List available functions
        functions = [attr for attr in dir(ocr_accelerator) if not attr.startswith('_')]
        print(f"✅ Available functions: {functions}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_rebuild_options():
    """Suggest options for rebuilding the module properly."""
    print("\n=== Suggestions for Proper Module Build ===")
    print("1. 🔧 Rebuild with static linking:")
    print("   - Link Tesseract and dependencies statically")
    print("   - Use /MT instead of /MD in Visual Studio")
    print("   - This embeds all dependencies in the .pyd file")
    print()
    print("2. 🔧 Rebuild with proper DLL search paths:")
    print("   - Use delay-loaded DLLs with proper search paths")
    print("   - Configure the module to search in its own directory")
    print()
    print("3. 🔧 Use vcpkg with static libraries:")
    print("   - vcpkg install tesseract:x64-windows-static")
    print("   - This avoids DLL dependencies entirely")
    print()
    print("4. 🔧 Configure the build to embed DLL search paths:")
    print("   - Use CMAKE_INSTALL_RPATH or similar")
    print("   - Set the module to search in relative paths")

if __name__ == "__main__":
    print("TESTRADE Proper DLL Fix (No Root Pollution)")
    print("=" * 50)
    
    # Set up proper DLL loading
    setup_ok = setup_proper_dll_loading()
    
    if setup_ok:
        # Test import
        import_ok = test_module_import()
        
        if import_ok:
            print("\n🎉 Success! Module works with proper DLL setup.")
            print("✅ No root directory pollution")
            print("✅ All DLLs remain in build directory")
        else:
            print("\n❌ Module still fails to import.")
            print("💡 The C++ module may need to be rebuilt properly.")
            suggest_rebuild_options()
    else:
        print("\n❌ Failed to set up DLL loading environment.")
        suggest_rebuild_options()
    
    print("\n📝 This approach keeps your root directory clean!")