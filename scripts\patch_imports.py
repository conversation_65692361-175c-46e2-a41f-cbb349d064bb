import sys
import logging

# Configure logging
logging.basicConfig(level=logging.ERROR, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# Create stub classes for cv2 and pytesseract
class StubCV2:
    """Stub implementation of cv2 module to prevent loading OpenCV's native libraries."""

    # Constants
    COLOR_BGR2GRAY = 6
    COLOR_RGB2BGR = 4
    THRESH_BINARY = 0
    THRESH_BINARY_INV = 1
    THRESH_OTSU = 8
    ADAPTIVE_THRESH_GAUSSIAN_C = 1

    def __init__(self):
        logger.error("NATIVE-ISOLATE19: Initialized stub cv2 module")

    def resize(self, *args, **kwargs):
        return args[0]

    def cvtColor(self, *args, **kwargs):
        return args[0]

    def GaussianBlur(self, *args, **kwargs):
        return args[0]

    def adaptiveThreshold(self, *args, **kwargs):
        return args[0]

    def threshold(self, *args, **kwargs):
        return (args[0], args[0])

    def VideoWriter(self, *args, **kwargs):
        return self

    def write(self, *args, **kwargs):
        pass

    def release(self, *args, **kwargs):
        pass

class StubPytesseract:
    """Stub implementation of pytesseract module to prevent loading Tesseract's native libraries."""

    class Output:
        DICT = 'dict'
        STRING = 'string'
        BYTES = 'bytes'
        DATAFRAME = 'data.frame'

    def __init__(self):
        logger.error("NATIVE-ISOLATE19: Initialized stub pytesseract module")
        self.pytesseract = self
        self.tesseract_cmd = None

    def image_to_string(self, *args, **kwargs):
        return "DUMMY OCR TEXT"

    def image_to_data(self, *args, **kwargs):
        output_type = kwargs.get('output_type', self.Output.STRING)
        if output_type == self.Output.DICT:
            return {
                "level": [1, 2, 3],
                "page_num": [1, 1, 1],
                "block_num": [0, 1, 1],
                "par_num": [0, 0, 1],
                "line_num": [0, 0, 1],
                "word_num": [0, 0, 1],
                "left": [0, 0, 10],
                "top": [0, 0, 10],
                "width": [0, 0, 50],
                "height": [0, 0, 20],
                "conf": [0, 0, 95],
                "text": ["", "", "DUMMY"]
            }
        return "DUMMY OCR DATA"

# Store the original import function
original_import = __import__

# Custom import function to prevent cv2 and pytesseract from being imported
def custom_import(name, globals=None, locals=None, fromlist=(), level=0):
    if name == 'cv2':
        logger.error(f"NATIVE-ISOLATE19: Prevented import of cv2")
        if 'cv2' not in sys.modules:
            sys.modules['cv2'] = StubCV2()
        return sys.modules['cv2']
    elif name == 'pytesseract':
        logger.error(f"NATIVE-ISOLATE19: Prevented import of pytesseract")
        if 'pytesseract' not in sys.modules:
            sys.modules['pytesseract'] = StubPytesseract()
        return sys.modules['pytesseract']
    return original_import(name, globals, locals, fromlist, level)

# Add the stubs to sys.modules directly instead of replacing the import function
sys.modules['cv2'] = StubCV2()
sys.modules['pytesseract'] = StubPytesseract()

logger.error("NATIVE-ISOLATE19: Added stub modules to sys.modules")

logger.error("NATIVE-ISOLATE19: Import patching complete")
