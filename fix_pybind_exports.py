#!/usr/bin/env python3
"""
Fix pybind11 exports issue by removing ModuleDefinitionFile from Visual Studio project.

The exports.def file is preventing pybind11 functions from being exported properly.
This script will comment out the ModuleDefinitionFile line to allow pybind11 to handle
exports automatically.
"""

import os
import shutil
from datetime import datetime

def fix_pybind_exports():
    """Fix the pybind11 exports issue in the Visual Studio project."""
    print("=== Fixing pybind11 Exports Issue ===")
    
    project_file = "ocr_accelerator/ocr_accelerator.vcxproj"
    
    if not os.path.exists(project_file):
        print(f"❌ Project file not found: {project_file}")
        return False
    
    print(f"📄 Reading project file: {project_file}")
    
    # Create backup
    backup_file = f"{project_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(project_file, backup_file)
    print(f"💾 Created backup: {backup_file}")
    
    # Read project file
    with open(project_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if the problematic line exists
    module_def_line = "<ModuleDefinitionFile>exports.def</ModuleDefinitionFile>"
    if module_def_line not in content:
        print("ℹ️  ModuleDefinitionFile line not found - nothing to fix")
        return True
    
    print("🔍 Found problematic ModuleDefinitionFile line")
    print("    This line prevents pybind11 from exporting functions automatically")
    
    # Comment out the line instead of removing it entirely
    commented_line = "      <!-- <ModuleDefinitionFile>exports.def</ModuleDefinitionFile> COMMENTED OUT TO FIX PYBIND11 EXPORTS -->"
    modified_content = content.replace(module_def_line, commented_line)
    
    # Verify the change was made
    if commented_line in modified_content:
        print("✅ Successfully commented out ModuleDefinitionFile line")
        
        # Write the modified content back
        with open(project_file, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print("📝 Project file updated successfully")
        print()
        print("🔧 NEXT STEPS:")
        print("   1. Rebuild the project in Visual Studio (Release x64)")
        print("   2. The new .pyd file should have working pybind11 function exports")
        print("   3. Test with: python test_cpp_ocr_accelerator.py")
        print()
        print("💡 EXPLANATION:")
        print("   - exports.def was only exporting PyInit_ocr_accelerator")
        print("   - This prevented pybind11 from exporting test_function and process_image_and_ocr")
        print("   - Now pybind11 will handle all exports automatically")
        
        return True
    else:
        print("❌ Failed to modify project file")
        return False

if __name__ == "__main__":
    success = fix_pybind_exports()
    if success:
        print("\n🎉 Fix applied successfully! Please rebuild the project.")
    else:
        print("\n❌ Fix failed. Check the error messages above.")