#!/usr/bin/env python3
# run_gui_backend.py - GUI Backend launcher from project root

"""
GUI Backend Launcher
====================
Launches the GUI backend from the project root directory to ensure
all imports work correctly.
"""

import os
import sys

# Ensure we're in the right directory
project_root = os.path.dirname(os.path.abspath(__file__))
os.chdir(project_root)

# Add current directory to Python path
sys.path.insert(0, project_root)

# Add GUI directory to Python path for internal imports
gui_dir = os.path.join(project_root, 'gui')
sys.path.insert(0, gui_dir)

# Import and run the GUI backend
from gui.gui_backend import app
import uvicorn

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")